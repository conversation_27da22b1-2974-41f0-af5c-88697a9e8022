﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    class CardConnectStatusCheckResponseDTO
    {
        public string resptext { get; set; }
        public string orderId { get; set; }
        public string setlstat { get; set; }
        public string capturedate { get; set; }
        public string acctid { get; set; }
        public string respcode { get; set; }
        public string merchid { get; set; }
        public string respproc { get; set; }
        public string bintype { get; set; }
        public string profileid { get; set; }
        public string voidable { get; set; }
        public string currency { get; set; }
        public string refundable { get; set; }
        public string settledate { get; set; }
        public string expiry { get; set; }
        public string retref { get; set; }
        public string respstat { get; set; }
        public string amount { get; set; }
        public string batchid { get; set; }
        public string entrymode { get; set; }
        public string token { get; set; }
        public string authcode { get; set; }
        public string authdate { get; set; }
        public string userfields { get; set; }
        public string lastfour { get; set; }
        public string name { get; set; }
        public string account { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
