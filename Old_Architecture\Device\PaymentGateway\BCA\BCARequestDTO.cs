﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.BCA
{
    class BCARequestDTO
    {
        public string Version { get; set; }
        public string TransactionType { get; set; }
        public string TransactionAmount { get; set; }
        public string OtherAmount { get; set; }
        public string PAN { get; set; }
        public string ExpiryDate { get; set; }
        public string CancelReason { get; set; }
        public string InvoiceNumber { get; set; }
        public string AuthCode { get; set; }
        public string InstallmentFlag { get; set; }
        public string RedeemFlag { get; set; }
        public string DCCFlag { get; set; }
        public string InstallmentPlan { get; set; }
        public string InstallmentTenor { get; set; }
        public string GenericData { get; set; }
        public string ReffNumber { get; set; }
        public string OriginalDate { get; set; }
        public string Filler { get; set; }

        // Constructor with Default Values
        public BCARequestDTO()
        {
            Version = "03";                     // Default Version
            TransactionType = "00";             // Default Type (to be overridden)
            TransactionAmount = "000000000000"; // Default Amount
            OtherAmount = "000000000000";       // Default Other Amount
            PAN = "".PadRight(19, ' ');         // Default PAN (Blank)
            ExpiryDate = "2803";                // Default Expiry Date
            CancelReason = "00";                // Default Cancel Reason
            InvoiceNumber = "000000";           // Default Invoice Number
            AuthCode = "".PadLeft(6, '0');      // Default Auth Code (Blank)
            InstallmentFlag = " ".PadRight(1, ' ');            // Default Installment Flag (Blank)
            RedeemFlag = " ".PadRight(1, ' ');                   // Default Redeem Flag (Blank)
            DCCFlag = "N";                      // Default DCC Flag (No DCC)
            InstallmentPlan = "".PadRight(3, ' '); // Default Installment Plan (Blank)
            InstallmentTenor = "".PadRight(2, ' '); // Default Installment Tenor (Blank)
            GenericData = "".PadRight(12, ' ');    // Default Generic Data (Blank)
            ReffNumber = "".PadRight(12, ' ');     // Default Reference Number (Blank)
            OriginalDate = "0000";                 // Default Original Date
            Filler = "".PadRight(50, ' ');         // Default Filler (Blank)
        }

    }
}
