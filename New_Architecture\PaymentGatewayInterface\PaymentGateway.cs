
/********************************************************************************************
 * Project Name - Payment GatewayInterface
 * Description  - PaymentGateway Class 
 * 
 **************
 **Version Log
 **************
 *Version     Date              Modified By                    Remarks          
 *********************************************************************************************
 * 2.190.0    24-Sep-2024       Amrutha                        Created
 *******************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public abstract class PaymentGateway : IPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public virtual bool IsSaleSupported
        {
            get
            {
                return false;
            }

        }
        public virtual bool IsRefundSupported
        {
            get
            {
                return false;
            }
        }
        public virtual bool IsStatusCheckSupported
        {
            get
            {
                return false;
            }
        }
        public virtual bool IsAuthSupported
        {
            get
            {
                return false;
            }

        }

        public virtual bool IsPaymentHistoryListRequired
        {
            get
            {
                return false;
            }
       }
        public virtual bool CanCreateMultipleInstances
        {
            get
            {
                return false;
            }
        }

        public virtual bool IsTipAdjustmentAllowed
        {
            get
            {
                return false;
            }
        }
 
        public virtual bool LastTransactionCheckAllowed
        {
            get
            {
                return false;
            }
        }
        public virtual bool SupportsFullStatusCheck
        {
            get
            {
                return false;
            }
        }

        public virtual bool IsCustomerInfoRequired
        {
            get
            {
                return false;
            }
        }

        public virtual bool IsProductsInfoRequired
        {
            get
            {
                return false;
            }
        }

        public virtual bool RedirectResponseToWebsite
        {
            get
            {
                return true;
            }
        }

        public virtual string CallbackResponseMessage
        {
            get
            {
                return "OK";
            }
        }

        public virtual Task<PaymentResponseDTO> Auth(PaymentRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            throw new AuthorizationNotSupportedException(paymentMessages.GetMessage(5736)); //Authorization Not Supported
        }
        public virtual string GetCustomerCopy(PaymentPrintAttribute paymentPrintAttributes, PaymentResponseDTO paymentResponse)
        {
            throw new CustomerCopyNotSupportedException(paymentMessages.GetMessage(5862)); //Customer receipt is not supported.
        }
        public virtual string GetMerchantCopy(PaymentPrintAttribute paymentPrintAttributes, PaymentResponseDTO paymentResponse)
        {
            throw new MerchantCopyNotSupportedException(paymentMessages.GetMessage(5863)); //Merchant receipt is not supported.
        }
        public virtual Task<PaymentResponseDTO> PerformSettlement(SettlementRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            throw new SettlementNotSupportedException(paymentMessages.GetMessage(5864)); //Settlement Not Supported
        }
        public virtual Task<PaymentResponseDTO> Refund(RefundRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            throw new RefundNotSupportedException(paymentMessages.GetMessage(5865));
        }
        public virtual Task<PaymentResponseDTO> Sale(PaymentRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            throw new SaleNotSupportedException(paymentMessages.GetMessage(5737)); //Sale Not Supported
        }
        public virtual Task<PaymentResponseDTO> StatusCheck(StatusCheckRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken,string errorMsg)
        {
            throw new StatusCheckNotSupportedException(paymentMessages.GetMessage(5866)); //Status Check Not Supported
        }
        public virtual Task<PaymentResponseDTO> PreAuth(PaymentRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            throw new PreAuthorizationNotSupportedException(paymentMessages.GetMessage(5735)); //PreAuthorization Not Supported
        }
        public virtual Task<PaymentResponseDTO> SaveDonationResponse(PaymentResponseDTO paymentResponseDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            throw new DonationNotSupportedException(paymentMessages.GetMessage(5861)); //Donation not supported 
        }
        public virtual bool IsTipEnabled(PaymentResponseDTO paymentResponseDTO, List<PaymentResponseDTO> paymentResponseDTOList)
        {
            return false;
        }
        public virtual bool IsDonationSupported(PaymentResponseDTO paymentResponseDTO, List<PaymentResponseDTO> paymentResponseDTOList)
        {
            return false;
        }
        public virtual void ValidateConfiguration()
        {
           throw new ConfigurationDataNotValidException(paymentMessages.GetMessage(5868)); //Configuration data Not Valid
        }
        public virtual Task<PaymentSessionDTO> CreatePaymentSessionDTO(PaymentRequestDTO paymentRequestDTO)
        {
            throw new PaymentSessionNotSupportedException(paymentMessages.GetMessage(5979));
        }
        public virtual Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO)
        {
            throw new ProcessPaymentResponseNotSupportedException(paymentMessages.GetMessage(5981));
        }

        public virtual Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO, List<PaymentResponseDTO> paymentHistoryList)
        {
            // Default implementation: ignore payment history and call the standard method
            return ProcessPaymentResponse(paymentGatewayResponseDTO);
        }
        public virtual Task<string> GetPaymentIdentifier(string paymentResponse)
        {
            throw new GetPaymentIdentifierNotSupportedException(paymentMessages.GetMessage(5983));
        }
        

        protected PaymentConfiguration paymentConfiguration;
        protected PaymentMessages paymentMessages;
        protected PaymentPrintAttribute paymentPrintAttribute;

        protected PaymentGateway(PaymentConfiguration paymentConfiguration, PaymentMessages paymentMessages)
        {
            this.paymentConfiguration = paymentConfiguration;
            this.paymentMessages = paymentMessages;
        }

        public enum Alignment
        {
            Left,
            Right,
            Center
        }

        protected string AllignText(string text, Alignment align)
        {
            log.LogMethodEntry(text, align);

            int pageWidth = 70;
            string res;
            if (align.Equals(Alignment.Right))
            {
                string returnValueNew = text.PadLeft(pageWidth, ' ');
                log.LogMethodExit(returnValueNew);
                return returnValueNew;
            }
            else if (align.Equals(Alignment.Center))
            {
                int len = (pageWidth - text.Length);
                int len2 = len / 2;
                len2 = len2 + text.Length;
                res = text.PadLeft(len2);
                if (res.Length > pageWidth && res.Length > text.Length)
                {
                    res = res.Substring(res.Length - pageWidth);
                }

                log.LogMethodExit(res);
                return res;
            }
            else
            {
                log.LogMethodExit(text);
                return text;
            }
        }
        protected string GetMaskedResponseField(string responseField)
        {
            log.LogMethodEntry();
            responseField = responseField.Trim();//triming the Merchant id
            string maskedResponseField = string.Empty;
            if (!string.IsNullOrWhiteSpace(responseField))
            {
                maskedResponseField = responseField.Length > 4 ? new string('X', responseField.Length - 4) + responseField.Substring(responseField.Length - 4) : responseField;
            }
            log.LogMethodExit(maskedResponseField);
            return maskedResponseField;
        }
        protected static string GetMaskedCardNumber(string responseField)
        {
            log.LogMethodEntry();
            //triming the Merchant id
            string maskedResponseField = string.Empty;
            if (!string.IsNullOrWhiteSpace(responseField))
            {
                responseField = responseField.Trim();
                maskedResponseField = responseField.Length > 4 ? new string('X', responseField.Length - 4) + responseField.Substring(responseField.Length - 4) : responseField;
            }
            log.LogMethodExit(maskedResponseField);
            return maskedResponseField;
        }

        protected static string GetTranCode (StatusCheckRequestDTO statusCheckRequestDTO)
        {
            log.LogMethodEntry(statusCheckRequestDTO);
            string tranCode;

            if (statusCheckRequestDTO.PaymentStatus == "SALE_INITIATED")

                tranCode = "SALE";

            else if (statusCheckRequestDTO.PaymentStatus == "AUTHORIZATION_INITIATED")

                tranCode = "AUTHORIZATION";

            else if (statusCheckRequestDTO.PaymentStatus == "PRE_AUTHORIZATION_INITIATED")

                tranCode = "TATokenRequest";

            else if (statusCheckRequestDTO.PaymentStatus == "SETTLEMENT_INITIATED")

                tranCode = "CAPTURE";
            else if (statusCheckRequestDTO.PaymentStatus == "REFUND_INITIATED" || statusCheckRequestDTO.PaymentStatus == "VOID_INITIATED" || statusCheckRequestDTO.PaymentStatus == "REVERSE_INITIATED")

                tranCode = "REFUND";
            else
                tranCode = null;

            log.LogMethodExit(tranCode);
            return tranCode;
        }
        protected PaymentResponseDTO BuildPaymentTransactionResponseDTO(string requestIdentifier, string trxType, decimal amount, decimal tipAmount, string msg, string errorMessage)
        {
            log.LogMethodEntry(requestIdentifier, trxType, amount, tipAmount);
            return new PaymentResponseDTO(requestIdentifier, string.Empty, string.Empty, null, errorMessage, string.Empty,
                                                                                string.Empty, trxType, string.Empty, string.Empty,
                                                                                string.Empty, DateTime.Now, string.Empty, string.Empty,
                                                                                string.Empty, string.Empty, string.Empty, string.Empty, tipAmount.ToString(), string.Empty,
                                                                                string.Empty, string.Empty, msg, string.Empty, string.Empty, string.Empty, amount);



        }

        protected static string GetCardTypeHelper(string startingTwoDigitOfCC)
        {
            log.LogMethodEntry();
            string cardType = "";

            if (string.IsNullOrWhiteSpace(startingTwoDigitOfCC))
            {
                log.Error("No card token provided. Unable to identify card type");
                return "";
            }

            log.Debug("Card first two digits: " + startingTwoDigitOfCC);

            if (startingTwoDigitOfCC.StartsWith("4"))
            {
                cardType = "Visa";
            }
            else if (startingTwoDigitOfCC.StartsWith("2") || startingTwoDigitOfCC.StartsWith("5"))
            {
                cardType = "Mastercard";
            }
            else if (startingTwoDigitOfCC.StartsWith("34") || startingTwoDigitOfCC.StartsWith("37"))
            {
                cardType = "AMEX";
            }
            else if (startingTwoDigitOfCC.StartsWith("6"))
            {
                cardType = "Discover";
            }
            else if (startingTwoDigitOfCC.StartsWith("36"))
            {
                cardType = "Diners Club";
            }
            else if (startingTwoDigitOfCC.StartsWith("35"))
            {
                cardType = "JCB";
            }
            else
            {
                cardType = "";
            }

            log.Debug("Card type: " + cardType);

            log.LogMethodExit(cardType);
            return cardType;
        }
    }
}
