﻿/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Payment Gateway Factory
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.190.0     24-Sep-2024         Amrutha      Created
 ********************************************************************************************************/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;

namespace Semnox.Parafait.PaymentGateway
{
    internal class ParafaitPaymentGatewayFactory
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        protected static Dictionary<PaymentGateways, IPaymentGateway> paymentGateways;

        public static IPaymentGateway GetPaymentGateway(PaymentGateways gateway, ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO, bool isUnattended)
        {
            log.LogMethodEntry(gateway);
            IPaymentGateway paymentGateway = null;
            if (paymentGateways == null)
            {
                paymentGateways = new Dictionary<PaymentGateways, IPaymentGateway>();
            }
            if (paymentGateways.ContainsKey(gateway) == false)
            {
                paymentGateway = CreatePaymentGateway(gateway, executionContext, paymentModeContainerDTO, isUnattended);
                if (paymentGateway.CanCreateMultipleInstances == false)
                {
                    paymentGateways.Add(gateway, paymentGateway);
                }
            }
            else
            {
                paymentGateway = paymentGateways[gateway];
            }

            log.LogMethodExit(paymentGateway);
            return paymentGateway;
        }
        protected static IPaymentGateway CreatePaymentGateway(PaymentGateways gateway, ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO, bool isUnattended)
        {
            log.LogMethodEntry(gateway);
            IPaymentGateway paymentGateway = null;
            switch (gateway)
            {
                case PaymentGateways.AdyenPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new ParafaitAdyenPaymentConfigurations(executionContext, paymentModeContainerDTO, isUnattended);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new AdyenPaymentGateway(paymentConfiguration, parafaitPaymentMessages);
                        break;
                    }
                case PaymentGateways.Geidea: {
                        PaymentConfiguration paymentConfiguration = new ParafaitGeideaPaymentConfigurations(executionContext, paymentModeContainerDTO);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new GeideaPaymentGateway(paymentConfiguration, parafaitPaymentMessages);
                        break;
                    }
                case PaymentGateways.CCAvenueHostedPayment:
                case PaymentGateways.CCAvenueCallbackHostedPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new CCAvenueWebPaymentConfiguration(executionContext, paymentModeContainerDTO);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new CCAvenueWebPaymentGateway(paymentConfiguration, parafaitPaymentMessages, LogManager.GetLogger(executionContext, typeof(CCAvenueWebPaymentGateway)));
                        break;
                    }
                case PaymentGateways.WPCyberSourceHostedPayment:
                case PaymentGateways.WPCyberSourceCallbackHostedPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new WPCyberSourceWebPaymentConfiguration(executionContext, paymentModeContainerDTO);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new WPCyberSourceWebPaymentGateway(paymentConfiguration, parafaitPaymentMessages, LogManager.GetLogger(executionContext, typeof(WPCyberSourceWebPaymentGateway)));
                        break;
                    }
                case PaymentGateways.AdyenHostedPayment:
                case PaymentGateways.AdyenCallbackHostedPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new AdyenWebPaymentConfiguration(executionContext, paymentModeContainerDTO);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new AdyenWebPaymentGateway(paymentConfiguration, parafaitPaymentMessages, LogManager.GetLogger(executionContext, typeof(AdyenWebPaymentGateway)));
                        break;
                    }
                case PaymentGateways.CardConnectHostedPayment:
                case PaymentGateways.CardConnectCallbackHostedPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new CardConnectWebPaymentConfiguration(executionContext, paymentModeContainerDTO);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new CardConnectWebPaymentGateway(paymentConfiguration, parafaitPaymentMessages, LogManager.GetLogger(executionContext, typeof(CardConnectWebPaymentGateway)));
                        break;
                    }
                case PaymentGateways.PaytmDQRPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new ParafaitPayTMDQRPaymentConfigurations(executionContext, paymentModeContainerDTO);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new PayTMDQRPaymentGateway(paymentConfiguration, parafaitPaymentMessages);
                        break;
                    }
                case PaymentGateways.CardConnect:
                    {
                        PaymentConfiguration paymentConfiguration = new ParafaitCardConnectPaymentConfigurations(executionContext, paymentModeContainerDTO, isUnattended);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new CardConnectPaymentGateway(paymentConfiguration, parafaitPaymentMessages);
                        break;
                    }
                case PaymentGateways.PineLabsCardPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new ParafaitPinelabsPaymentConfiguration(executionContext, paymentModeContainerDTO, isUnattended);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new PinelabsPaymentGateway(paymentConfiguration, parafaitPaymentMessages);
                        break;
                    }
                case PaymentGateways.PineLabsQRPayment:
                    {
                        PaymentConfiguration paymentConfiguration = new ParafaitPinelabsPaymentConfiguration(executionContext, paymentModeContainerDTO, isUnattended);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new PinelabsPaymentGateway(paymentConfiguration, parafaitPaymentMessages);
                        break;
                    }
                case PaymentGateways.Mashreq:
                    {
                        PaymentConfiguration paymentConfiguration = new ParafaitMashreqPaymentConfiguration(executionContext, paymentModeContainerDTO, isUnattended);
                        ParafaitPaymentMessages parafaitPaymentMessages = new ParafaitPaymentMessages(executionContext.SiteId, executionContext.LanguageId);
                        paymentGateway = new MashreqPaymentGateway(paymentConfiguration, parafaitPaymentMessages);
                        break;
                    }
                default:
                    throw new PaymentGatewayException("Invalid payment gateway");
            }

            log.LogMethodExit(paymentGateway);
            return paymentGateway;
        }
    }
}
