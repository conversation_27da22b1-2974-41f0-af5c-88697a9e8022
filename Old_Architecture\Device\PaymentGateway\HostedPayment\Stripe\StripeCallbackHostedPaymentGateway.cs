﻿/********************************************************************************************
 * Project Name -  Stripe Hosted Payment Gateway                                                                     
 * Description  - Class to handle the payment of Stripe Payment Gateway
 *
 **************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 *********************************************************************************************
  *2.152.0    11-Nov-2023    Yashodhara C H                 Created for Website 
 ********************************************************************************************/
using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Stripe;
using Stripe.Checkout;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Stripe
{
    class StripeCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        string secretKey;
        string publishableKey;
        string baseURL;
        string successUrl;
        string cancelUrl;
        string currencyCode;
        string sessionid;
        private string post_url;
        private HostedGatewayDTO hostedGatewayDTO;
        private const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        private string paymentPageLink;

        public StripeCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            this.hostedGatewayDTO = new HostedGatewayDTO();
            this.Initialize();
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();
            secretKey = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            publishableKey = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            baseURL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            currencyCode = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");
            //post_url = "/account/Stripe";

            //secretKey = utilities.getParafaitDefaults("GOOGLE_RECAPTCHA_SECRET_KEY");
            //clientID = utilities.getParafaitDefaults("GOOGLE_RECAPTCHA_CLIENT_ID");
            //url = utilities.getParafaitDefaults("GOOGLE_RECAPTCHA_URL");
            //isRecaptchaEnabled = utilities.getParafaitDefaults("ENABLE_GOOGLE_RECAPTCHA") == "Y" ? true : false;

            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";

            if (string.IsNullOrWhiteSpace(secretKey))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            else if (string.IsNullOrWhiteSpace(publishableKey))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            }
            else if (string.IsNullOrWhiteSpace(baseURL))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            this.hostedGatewayDTO.PGSuccessResponseMessage = "RECEIVEOK";
            this.hostedGatewayDTO.PGFailedResponseMessage = "RECEIVEOK";

            String apiSite = "";
            String webSite = "";

            apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;
            //log.Debug("apiSite " + apiSite);

            webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;
            //log.Debug("webSite " + webSite);

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null )
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.StripeCallbackHostedPayment.ToString());
                //log.Debug("successResponseAPIURL " + successResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.StripeCallbackHostedPayment.ToString());
                //log.Debug("failureResponseAPIURL " + failureResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null )
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.StripeCallbackHostedPayment.ToString());
                //log.Debug("cancelResponseAPIURL " + cancelResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null )
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.StripeCallbackHostedPayment.ToString());
                //log.Debug("callbackResponseAPIURL " + callbackResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null )
            {
                this.hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.StripeCallbackHostedPayment.ToString());
                //log.Debug("successRedirectURL " + this.hostedGatewayDTO.SuccessURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null )
            {
                this.hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.StripeCallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.StripeCallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;
            this.successUrl = this.hostedGatewayDTO.SuccessURL;
            this.cancelUrl = this.hostedGatewayDTO.FailureURL;
            paymentPageLink = webSite + "/products/stripecallback";

            log.LogMethodExit();
        }


        private IDictionary<string, string> SetPostParameters(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCRequestPGWDTO);
            IDictionary<string, string> postparamslist = new Dictionary<string, string>();

            postparamslist.Clear();
            postparamslist.Add("sessionId", sessionid);
            postparamslist.Add("publishableKey", publishableKey);

            log.LogMethodExit(postparamslist);
            return postparamslist;
        }

        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<style>.ProgressWaiting {position: fixed; background-color: #333; opacity: 0.8;overflow: hidden; font-size:20px;" +
                "text-align: center; top: 0; left: 0; height: 100%; width: 100%; padding-top: 20%; z-index: 2147483647 !important;" +
                "-webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease;" +
                " transition: all 0.3s ease; color: ActiveBorder; }</style>");
            builder.Append("<meta http-equiv=\"refresh\" content=\"1; URL =");
            builder.Append(URL);
            builder.Append("\"/>");
            builder.Append("</head><body>");
            builder.Append(string.Format("<div id=\"pnlPaymentProcess\" style=\"padding: 50px; \"> <div class=\"ProgressWaiting\" style=\"padding-bottom: 50px;\">" +
                "<p>Redirecting to payment gateway. Do not click on back button or close the browser. </p>" +
                "<img src = \"/images/img/loading-indicator.svg\" alt=\"Please wait... \" /></div></div>"));
            builder.Append("</body></html>");

            return builder.ToString();
        }

        //private string loadStripeCheckOut(string sessionId)
        //{
        //    log.LogMethodEntry(sessionId);

        //    StringBuilder builder = new StringBuilder();
        //    builder.Clear();
        //    builder.Append("<html><head><script src =\"https://js.stripe.com/v3/\"></script></head>");
        //    builder.Append("<body onload =\"document.getElementById('btnPay').click()\">");
        //    builder.Append("<form><button id =\"btnPay\" onclick=\"checkout()\">Checkout</button></form>");
        //    builder.Append("<script>");
        //    builder.Append("var stripe = Stripe('"+ publishableKey + "');");
        //    builder.Append("var element = document.getElementById('btnPay');");
        //    builder.Append("function checkout(){");
        //    builder.Append("stripe.redirectToCheckout({");
        //    builder.Append("sessionId:'"+ sessionid+"'" );
        //    builder.Append("}).then(function (result) { }); } </script></body></html>");

        //    log.LogMethodExit(builder.ToString());
        //    return builder.ToString();
        //}
        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);

            try
            {
                CCRequestPGWDTO cCRequestPGWDTO = this.CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());
                StripeConfiguration.ApiKey = secretKey;
                StripeConfiguration.MaxNetworkRetries = 2;
                List<string> paymentOption = new List<string>();
                List<LookupValuesDTO> lookupValuesDTOList1;
                LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
                List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> lookUpValueSearchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
                lookUpValueSearchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "STRIPE_PAYMENT_MODES"));
                lookUpValueSearchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                lookupValuesDTOList1 = lookupValuesList.GetAllLookupValues(lookUpValueSearchParameters);
                if (lookupValuesDTOList1 != null && lookupValuesDTOList1.Any())
                {
                    foreach (LookupValuesDTO element in lookupValuesDTOList1)
                    {
                        paymentOption.Add(element.Description);
                    }
                }


                var options = new SessionCreateOptions
                {
                    CustomerEmail = transactionPaymentsDTO.NameOnCreditCard,
                    PaymentMethodTypes = paymentOption,
                    LineItems = new List<SessionLineItemOptions> {
                                    new SessionLineItemOptions {
                                        PriceData = new SessionLineItemPriceDataOptions
                                        {
                                            UnitAmount = Convert.ToInt64(transactionPaymentsDTO.Amount * 100),
                                            Currency = currencyCode.ToLower(),
                                            ProductData = new SessionLineItemPriceDataProductDataOptions
                                            {
                                                Name = "Online Purchase",
                                            },
                                        },
                                        Quantity = 1,
                                    },
                                },
                    Metadata = new Dictionary<string, string>{
                              { "trxId",  transactionPaymentsDTO.TransactionId.ToString()},
                              { "customerName", transactionPaymentsDTO.CreditCardName },
                              { "paymentModeId", transactionPaymentsDTO.PaymentModeId.ToString() },
                           },
                    Mode = "payment",
                    SuccessUrl = successResponseAPIURL + "?session_id={CHECKOUT_SESSION_ID}",
                    CancelUrl = failureResponseAPIURL,
                };
                var sessionService = new SessionService();
                var service = new SessionService();
                Session session = service.Create(options);
                log.Debug("seesion: " + session);

                sessionid = session.Id;
                cCRequestPGWDTO.ReferenceNo = sessionid;
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestPGWDTO);
                cCRequestPGWBL.Save();

                //this.hostedGatewayDTO.PrivateKey = sessionid;
                this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO));
                this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO),
                                                                paymentPageLink + "?payload=" + Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(this.hostedGatewayDTO.GatewayRequestString)),
                                                                "frmStripeForm");
                //this.hostedGatewayDTO.GatewayRequestString = loadStripeCheckOut(sessionid);
                log.Debug(this.hostedGatewayDTO.GatewayRequestString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }
            log.LogMethodExit(this.hostedGatewayDTO);
            return this.hostedGatewayDTO;
        }



        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            StripeConfiguration.ApiKey = secretKey;

            if (this.hostedGatewayDTO == null)
            {
                this.hostedGatewayDTO = new HostedGatewayDTO();
            }

            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            log.Debug("DeserializeObject Initiated");

            dynamic response = JsonConvert.DeserializeObject(gatewayResponse);
            if (response != null)
            {
                try
                {
                    string sessionId = string.Empty;
                    if (response["id"] != null)
                    {
                        log.Debug("callback:");
                        dynamic data = response["data"]["object"];
                        sessionId = data["id"];
                    }
                    else
                    {
                        log.Debug("callsuccess:");
                        sessionId = response["session_id"].ToString();
                    }
                    log.Debug("sessionid: " + sessionId);
                    var sessionService = new SessionService();
                    Session session = sessionService.Get(sessionId);
                    if(session != null)
                    {
                        dynamic resData = Newtonsoft.Json.JsonConvert.DeserializeObject(session.StripeResponse.Content);
                        hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = resData["metadata"]["trxId"];
                        hostedGatewayDTO.TrxId = resData["metadata"]["trxId"];
                        if (resData["payment_status"] == "paid")
                        {
                            var service = new PaymentIntentService();
                            var intent = service.Get(session.PaymentIntentId);
                            dynamic intentData = Newtonsoft.Json.JsonConvert.DeserializeObject(intent.StripeResponse.Content);
                            hostedGatewayDTO.GatewayReferenceNumber = intentData["id"];
                        }
                    }
                    else
                    {
                        log.Error("Response for Sale Transaction doesn't contain TrxId.");
                        throw new Exception("Error processing your payment");
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw;
                }

            }
            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            string paymentStatus = "";
            this.hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();

            dynamic response = JsonConvert.DeserializeObject(gatewayResponse);
            try
            {
                string sessionId = string.Empty;
                if (response["id"] != null)
                {
                    log.Debug("callback:");
                    dynamic data = response["data"]["object"];
                    sessionId = data["id"];
                }
                else
                {
                    log.Debug("callsuccess:");
                    sessionId = response["session_id"].ToString();
                }
                log.Debug("sessionid: " + sessionId);
                var sessionService = new SessionService();
                Session session = sessionService.Get(sessionId);
                dynamic resData = Newtonsoft.Json.JsonConvert.DeserializeObject(session.StripeResponse.Content);

                if (resData["payment_status"].ToString().ToLower() == "paid")
                {
                    var service = new PaymentIntentService();
                    var intent = service.Get(session.PaymentIntentId);
                    dynamic intentData = Newtonsoft.Json.JsonConvert.DeserializeObject(intent.StripeResponse.Content);
                    log.Debug("PaymentIntent Data");
                    log.Debug(intentData);
                    log.Debug("End PaymentIntent Data");
                    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = intentData["id"] == null ? "" : intentData["id"];
                    hostedGatewayDTO.TransactionPaymentsDTO.Amount = intentData["amount_received"] == null ? "" : (Convert.ToDouble(intentData["amount_received"]) / 100).ToString();
                    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = (intent.Charges.Data[0].PaymentMethodDetails.Card.Last4) == null ? "" : intent.Charges.Data[0].PaymentMethodDetails.Card.Last4;
                    hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = (resData["metadata"]["paymentModeId"]) == null ? "" : resData["metadata"]["paymentModeId"];
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = resData["metadata"]["trxId"] == null ? "" : resData["metadata"]["trxId"];
                    hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = resData["currency"] == null ? "" : resData["currency"];
                    hostedGatewayDTO.TransactionPaymentsDTO.Reference = intentData["id"] == null ? "" : intentData["id"];
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                    paymentStatus = "APPROVED_";
                }
                else
                {
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = (resData["metadata"]["trxId"]) == null ? "" : resData["metadata"]["trxId"];
                    hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = resData["currency"] == null ? "" : resData["currency"];
                    hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = (resData["metadata"]["paymentModeId"]) == null ? "" : resData["metadata"]["paymentModeId"];
                    paymentStatus = "FAILED_";
                    log.Debug("Payment Failed");
                }

                
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                this.TransactionSiteId = cCRequestsPGWDTO.SiteId;

                log.Debug("Trying to update the CC request to payment processing status");
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);

                //TODO: Changed PaymentProcessStatusType.PAYMENT_COMPLETED to hostedGatewayDTO.PaymentProcessStatus removed isProcessPsyment
                int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(PaymentProcessStatusType.PAYMENT_COMPLETED.ToString(), hostedGatewayDTO.PaymentProcessStatus.ToString());

                if (rowsUpdated == 0)
                {
                    log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                }
                else
                {
                    log.Debug("CC request updated to " + hostedGatewayDTO.PaymentProcessStatus.ToString());
                }


                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = null;
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                
                if (cCTransactionsPGWDTOList == null)
                {
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.Authorize = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.Purchase = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.AcctNo = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber;
                    cCTransactionsPGWDTO.TextResponse = string.Concat(paymentStatus, hostedGatewayDTO.PaymentStatus);
                    cCTransactionsPGWDTO.DSIXReturnCode = hostedGatewayDTO.PaymentStatusMessage;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();

                    this.hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }
            }
            catch (Exception ex)
            {
                log.Error("Last transaction check failed", ex);
                throw;
            }


            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;

        }
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            StripeConfiguration.ApiKey = secretKey;

            if (transactionPaymentsDTO != null)
            {
                try
                {
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
                    CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                    if (transactionPaymentsDTO.CCResponseId > -1)
                    {
                        CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                        List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                        searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                        List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters);

                        ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    }

                    var options = new RefundCreateOptions
                    {
                        PaymentIntent = transactionPaymentsDTO.Reference,
                        Amount = Convert.ToInt64(transactionPaymentsDTO.Amount * 100),
                    };
                    var service = new RefundService();
                    var refund = service.Create(options);

                    dynamic data = Newtonsoft.Json.JsonConvert.DeserializeObject(refund.StripeResponse.Content);

                    log.Debug("data: " + data);

                    if (data["status"].ToString().ToLower() == "succeeded")
                    {
                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                        ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                        ccTransactionsPGWDTO.Authorize = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString();
                        ccTransactionsPGWDTO.TextResponse = data["status"] == null ? "" : data["status"];
                        ccTransactionsPGWDTO.AuthCode = data["payment_intent"] == null ? "" : data["payment_intent"];
                        ccTransactionsPGWDTO.AcqRefData = data["id"] == null ? "" : data["id"];
                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                        ccTransactionsPGWBL.Save();
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        log.Debug("Refund Success");
                    }
                    else
                    {
                        log.Error("refund failed");
                        throw new Exception(utilities.MessageUtils.getMessage(2203));
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw;
                }
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry();

            string sessionId;
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;
            try
            {
                //ExecutionContext executionContext = ExecutionContext.GetExecutionContext();
                //StripeConfiguration.ApiKey = ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");

                StripeConfiguration.ApiKey = secretKey;

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                sessionId = cCRequestsPGWDTO.ReferenceNo;

                var sessionService = new SessionService();
                Session session = sessionService.Get(sessionId);
                resData = Newtonsoft.Json.JsonConvert.DeserializeObject(session.StripeResponse.Content);
                log.Debug("Session Data");
                log.Debug(resData);
                log.Debug("End Session Data");

                if (resData["payment_status"] == "paid")
                {
                    var service = new PaymentIntentService();
                    var intent = service.Get(session.PaymentIntentId);
                    dynamic intentData = Newtonsoft.Json.JsonConvert.DeserializeObject(intent.StripeResponse.Content);
                    log.Debug("PaymentIntent Data");
                    log.Debug(intentData);
                    log.Debug("End PaymentIntent Data");

                    intentData["amount_received"] = (Convert.ToDouble(intentData["amount_received"]) / 100).ToString();

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    if (intentData["id"] != null)
                    {
                        cCTransactionsPGWDTO.RecordNo = intentData["id"];
                    }
                    else
                    {
                        log.Error("Response for refund Transaction doesn't contain TrxId.");
                        throw new Exception("Error processing your payment");
                    }
                    cCTransactionsPGWDTO.InvoiceNo = (resData["metadata"]["trxId"]) == null? "" : resData["metadata"]["trxId"].ToString();
                    cCTransactionsPGWDTO.AuthCode = intentData["id"] == null? "" : intentData["id"];
                    cCTransactionsPGWDTO.Authorize = resData["amount"] == null ? "" : resData["amount"];
                    cCTransactionsPGWDTO.Purchase = resData["amount"] == null ? "" : resData["amount"];
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.RefNo = resData["id"] == null ? "" : resData["id"];
                    cCTransactionsPGWDTO.AcctNo = intent.Charges.Data[0].PaymentMethodDetails.Card.Last4;
                    cCTransactionsPGWDTO.TextResponse = resData["status"] == null ? "" : resData["status"];
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                    ccTransactionsPGWBL.Save();

                    dict.Add("status", "1");
                    dict.Add("message", "success");
                    dict.Add("retref", intentData["id"]);
                    dict.Add("amount", intentData["amount_received"]);
                    dict.Add("orderId", resData["metadata"]["trxId"]);
                    dict.Add("acctNo", intent.Charges.Data[0].PaymentMethodDetails.Card.Last4);

                    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                }
                else
                {
                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", cCRequestsPGWDTO.InvoiceNo);
                    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                }

                log.LogMethodExit();
                return resData;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }
    }
}
