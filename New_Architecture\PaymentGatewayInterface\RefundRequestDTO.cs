﻿
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class RefundRequestDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        private decimal amount;
        private string requestIdentifier;
        private List<PaymentResponseDTO> paymentResponseHistory;
        private PaymentResponseDTO paymentResponses;
        private decimal tipAmount;
        private int intRequestIdentifier;
        private DateTime requestDate;
        private DateTime bussStartTime;
        private DateTime bussEndTime;




        /// <summary>
        /// Constructor with all the data fields
        /// </summary>
        public RefundRequestDTO(decimal amount, string requestIdentifier, int intRequestIdentifier ,decimal tipAmount, PaymentResponseDTO paymentResponses, List<PaymentResponseDTO> paymentResponseHistory, DateTime requestDate, DateTime bussStartTime,DateTime bussEndTime)

        {
            log.LogMethodEntry(amount, requestIdentifier);
            this.amount = amount;
            this.paymentResponses = paymentResponses;
            this.requestIdentifier = requestIdentifier;
            this.intRequestIdentifier = intRequestIdentifier;
            this.tipAmount = tipAmount;
            this.requestDate = requestDate;
            this.bussStartTime = bussStartTime;
            this.bussEndTime = bussEndTime;
            this.paymentResponseHistory = paymentResponseHistory;
            log.LogMethodExit();
        }

        /// <summary>
        /// Get/Set method of the Amount field
        /// </summary>
        public decimal Amount
        {
            get
            {
                return amount;
            }

            set
            {
                //IsChanged = true;
                amount = value;
            }
        }
        public decimal TipAmount
        {
            get
            {
                return tipAmount;
            }

            set
            {
                //IsChanged = true;
                tipAmount = value;
            }
        }
        public string RequestIdentifier
        {
            get
            {
                return requestIdentifier;
            }

            set
            {
                //IsChanged = true;
                requestIdentifier = value;
            }
        }
        public int IntRequestIdentifier
        {
            get
            {
                return intRequestIdentifier;
            }

            set
            {
                //IsChanged = true;
                intRequestIdentifier = value;
            }
        }
        /// <summary>
        /// Get method of the requestDate field
        /// </summary>
        public DateTime RequestDate
        {
            get
            {
                return requestDate;
            }
            set
            {
                // this.IsChanged = true;
                requestDate = value;
            }
        }
        public DateTime BussStartTime
        {
            get
            {
                return bussStartTime;
            }
            set
            {
                // this.IsChanged = true;
                bussStartTime = value;
            }
        }
        public DateTime BussEndTime
        {
            get
            {
                return bussEndTime;
            }
            set
            {
                // this.IsChanged = true;
                bussEndTime = value;
            }
        }

        public List<PaymentResponseDTO> PaymentResponseHistory
        {
            get { return paymentResponseHistory; }
            set { paymentResponseHistory = value; }
        }
        public PaymentResponseDTO PaymentResponses
        {
            get { return paymentResponses; }
            set { paymentResponses = value; }
        }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
