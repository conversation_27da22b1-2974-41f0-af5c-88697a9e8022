﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Midtrans
{

    #region MidtransRefundRequestDTO
    /// <summary>
    /// MidtransRefundRequestDTO
    /// </summary>
    public class MidtransRefundRequestDTO
    {
        public class MidtransRefundRequest
        {
            public string refund_key { get; set; }
            public long amount { get; set; }
            public string reason { get; set; }
        }
    }
    /// <summary>
    /// Midtrans Get Token Request
    /// </summary>
    public class MidtransGetTokenRequestDTO
    {
        public class GetTokenRequest
        {
            public TransactionDetails transaction_details { get; set; }
            public CreditCard credit_card { get; set; }
            public CustomerDetails customer_details { get; set; }
        }

        public class TransactionDetails
        {
            public string order_id { get; set; }
            public Int32 gross_amount { get; set; }
        }
        public class CreditCard
        {
            public bool secure { get; set; }
        }
        public class CustomerDetails
        {
            public string first_name { get; set; }
            public string last_name { get; set; }
            public string email { get; set; }
            public string phone { get; set; }
        }
    }

    #endregion MidtransRefundRequestDTO

}
