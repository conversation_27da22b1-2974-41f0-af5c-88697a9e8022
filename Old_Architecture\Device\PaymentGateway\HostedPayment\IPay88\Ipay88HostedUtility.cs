using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.IPay88
{
    class IPay88HostedUtility : HostedPaymentGatewayUtility
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public IPay88HostedUtility() : base()
        {
            log.LogMethodEntry();
            Initialize();
            log.LogMethodExit();
        }

        public string currencyCode = "MYR";
        public string amount;
        public string trxSearchUrl;
        private void Initialize()
        {
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_API_URL", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_BASE_URL", "");
            PaymentCredentailsList.Add("TRANSACTION_AMOUNT", "");

        }

        /// <summary>
        /// Initializes and returns an ChillpayHostedCommandHandler instance.
        /// </summary>
        /// <param name="paymentCredentialsList">The payment credentials list.</param>
        /// <returns>An instance of ChillpayHostedCommandHandler.</returns>
        public Ipay88HostedCommandHandler InitializeCommandHandler(Dictionary<string, string> paymentCredentialsList)
        {
            if (paymentCredentialsList == null)
            {
                throw new ArgumentNullException("paymentCredentialsList", "The payment credentials list cannot be null.");
            }
            string baseUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_BASE_URL", out baseUrl) || string.IsNullOrWhiteSpace(baseUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_BASE_URL'.");
            }
            string postUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_API_URL", out postUrl) || string.IsNullOrWhiteSpace(postUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_API_URL'.");
            }
            if (!paymentCredentialsList.TryGetValue("TRANSACTION_AMOUNT", out amount) || string.IsNullOrWhiteSpace(amount))
            {
                throw new ArgumentException("Please enter a valid 'TRANSACTION_AMOUNT'.");
            }
            if (baseUrl.EndsWith("/"))
            {
                baseUrl = baseUrl.Remove(baseUrl.Length - 1);
            }

            string merchantKey;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", out merchantKey) || string.IsNullOrWhiteSpace(merchantKey))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY'.");
            }

            string merchantId;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", out merchantId) || string.IsNullOrWhiteSpace(merchantId))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_MERCHANT_ID'.");
            }
            
            //if (!paymentCredentialsList.TryGetValue("CURRENCY_CODE", out currencyCode) || string.IsNullOrEmpty(currencyCode))
            //{
            //    throw new ArgumentException("Please enter a valid 'CURRENCY_CODE'.");
            //}

            trxSearchUrl = baseUrl + "/ePayment/Webservice/TxInquiryCardDetails/TxDetailsInquiry.asmx";


            return new Ipay88HostedCommandHandler(merchantKey, merchantId, currencyCode);
        }
        /// <summary>
        /// Retrieves the payment status search result for a transaction ID.
        /// </summary>
        /// <param name="trxId">The transaction ID to search for.</param>
        /// <returns>The payment status search result.</returns>
        public override TrxSearchUtilityDTO GetPaymentStatusSearch(string trxId)
        {
            log.LogMethodEntry(trxId);
            TrxSearchUtilityDTO trxSearchResult = new TrxSearchUtilityDTO
            {
                TransactionId = trxId
            };

            if (string.IsNullOrWhiteSpace(trxId))
            {
                log.Error("Invalid transaction ID.");
                trxSearchResult.ErrorMessage = "Invalid transaction ID.";
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.PaymentStatus = "Failed";
                trxSearchResult.TransactionId = "null";
                return trxSearchResult;
            }

            try
            {
                Ipay88HostedCommandHandler ipayCmdHandler = InitializeCommandHandler(PaymentCredentailsList);
                if (ipayCmdHandler == null)
                {
                    log.Error("CommandHandler instance is null");
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    return trxSearchResult;
                }

                TxDetailsInquiryCardInfoResponse txDetailsInquiryCardInfoResponse = ipayCmdHandler.RequeryPayment(trxId, amount, trxSearchUrl);
                TxDetailsInquiryCardInfoResponseTxDetailsInquiryCardInfoResult transactionInquiry = txDetailsInquiryCardInfoResponse.TxDetailsInquiryCardInfoResult;

                if (transactionInquiry == null || string.IsNullOrEmpty(transactionInquiry.Status))
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    return trxSearchResult;
                }
                log.LogVariableState($"TxSearch Response for TrxId: {trxId}: ", transactionInquiry);

                log.Debug("response status: " + transactionInquiry.Status);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(transactionInquiry.Status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                string formattedJson = JsonConvert.SerializeObject(transactionInquiry, Formatting.Indented);
                trxSearchResult.FormattedResponse = formattedJson;
                trxSearchResult.PaymentStatus = salePaymentStatus.ToString();
            }
            catch (Exception ex)
            {
                log.Error("Error searching transaction details for trxId: " + trxId);
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.ErrorMessage = ex.Message;
                trxSearchResult.PaymentStatus = "Failed"; // Set payment status to "Failed"
            }
            log.LogMethodExit(trxSearchResult.ToString());
            return trxSearchResult;
        }

        /// <summary>
        /// Maps the payment status from raw response to Semnox payment status.
        /// </summary>
        /// <param name="rawPaymentGatewayStatus">The raw payment gateway status.</param>
        /// <param name="pgwTrxType">The type of payment gateway transaction.</param>
        /// <returns>The mapped payment status type.</returns>
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType defaultStatus = PaymentStatusType.FAILED; //default status
            PaymentStatusType paymentStatusType = defaultStatus;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            {"1", PaymentStatusType.SUCCESS},
                            {"0", PaymentStatusType.FAILED},
                        };
                        break;
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            {"1", PaymentStatusType.SUCCESS},
                            {"0", PaymentStatusType.FAILED},
                            {"6", PaymentStatusType.PENDING},
                            {"20", PaymentStatusType.PENDING},
                        };
                        break;
                    case PaymentGatewayTransactionType.VOID:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            {"0", PaymentStatusType.SUCCESS},
                            {"1", PaymentStatusType.FAILED},
                            {"3", PaymentStatusType.FAILED},
                            {"4", PaymentStatusType.FAILED},
                            {"5", PaymentStatusType.FAILED},
                            {"6", PaymentStatusType.FAILED},
                            {"7", PaymentStatusType.FAILED},
                            {"12", PaymentStatusType.FAILED},
                            {"13", PaymentStatusType.FAILED},
                            {"14", PaymentStatusType.FAILED},
                            {"15", PaymentStatusType.FAILED},
                            {"19", PaymentStatusType.FAILED},
                            {"20", PaymentStatusType.FAILED},
                            {"21", PaymentStatusType.FAILED},
                            {"22", PaymentStatusType.FAILED},
                            {"30", PaymentStatusType.FAILED},
                            {"33", PaymentStatusType.FAILED},
                            {"34", PaymentStatusType.FAILED},
                            {"36", PaymentStatusType.FAILED},
                            {"41", PaymentStatusType.FAILED},
                            {"43", PaymentStatusType.FAILED},
                            {"51", PaymentStatusType.FAILED},
                            {"54", PaymentStatusType.FAILED},
                            {"59", PaymentStatusType.FAILED},
                            {"61", PaymentStatusType.FAILED},
                            {"62", PaymentStatusType.FAILED},
                            {"63", PaymentStatusType.FAILED},
                            {"65", PaymentStatusType.FAILED},
                            {"91", PaymentStatusType.FAILED},
                            {"96", PaymentStatusType.FAILED},
                            {"1001", PaymentStatusType.FAILED},
                            {"1002", PaymentStatusType.FAILED},
                            {"1003", PaymentStatusType.FAILED},
                            {"1004", PaymentStatusType.FAILED},
                            {"1005", PaymentStatusType.FAILED},
                            {"1006", PaymentStatusType.FAILED},
                            {"1007", PaymentStatusType.FAILED},
                            {"1008", PaymentStatusType.FAILED},
                            {"1009", PaymentStatusType.FAILED},
                            {"1010", PaymentStatusType.FAILED},
                            {"1011", PaymentStatusType.FAILED},
                            {"1012", PaymentStatusType.FAILED},
                            {"9999", PaymentStatusType.FAILED}
                        };
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            {"5", PaymentStatusType.SUCCESS},
                            {"0", PaymentStatusType.FAILED},
                            {"1", PaymentStatusType.FAILED},
                            {"2", PaymentStatusType.FAILED},
                            {"3", PaymentStatusType.FAILED},
                            {"4", PaymentStatusType.FAILED},
                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to failed.");
                    paymentStatusType = defaultStatus;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentStatusType = defaultStatus;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

    }
}
