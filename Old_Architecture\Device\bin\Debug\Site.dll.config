﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="Semnox.Parafait.Site.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <Semnox.Parafait.Site.Properties.Settings>
            <setting name="Site_ParafaitGateway_Service" serializeAs="String">
                <value>https://localhost:444/Service.asmx</value>
            </setting>
        </Semnox.Parafait.Site.Properties.Settings>
    </applicationSettings>
</configuration>