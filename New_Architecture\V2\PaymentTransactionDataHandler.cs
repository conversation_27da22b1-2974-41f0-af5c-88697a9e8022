/********************************************************************************************
 * Project Name - Transaction
 * Description  - PaymentTransactionDataHandler
 **************
 **Version Log
 **************
 *Version     Date             Modified By         Remarks          
 *********************************************************************************************
 *2.140.0     15-Dec-2021      Fiona               Created 
 ********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using Microsoft.SqlServer.Server;
using Semnox.Core.Utilities;
namespace Semnox.Parafait.Transaction.V2
{
    /// <summary
    ///  PaymentTransaction Data Handler - Handles insert, update and select of  PaymentTransaction objects
    /// </summary>
    public class PaymentTransactionDataHandler
    {
        private Semnox.Parafait.logging.Logger log;
        private static readonly Dictionary<PaymentTransactionDTO.SearchByParameters, string> DBSearchParameters = new Dictionary<PaymentTransactionDTO.SearchByParameters, string>
            {
                {PaymentTransactionDTO.SearchByParameters.RESPONSE_ID, "Tr.ResponseID"},
                {PaymentTransactionDTO.SearchByParameters.INVOICE_NUMBER, "Tr.InvoiceNo"},
                {PaymentTransactionDTO.SearchByParameters.TRAN_CODE, "Tr.TranCode"},
                {PaymentTransactionDTO.SearchByParameters.MASTER_ENTITY_ID, "Tr.MasterEntityId"},
                {PaymentTransactionDTO.SearchByParameters.SITE_ID, "Tr.site_id"},
                {PaymentTransactionDTO.SearchByParameters.REF_NO,"Tr.RefNo" },
                {PaymentTransactionDTO.SearchByParameters.AUTH_CODE,"Tr.AuthCode" },
                {PaymentTransactionDTO.SearchByParameters.RESPONSE_ORIGIN,"Tr.ResponseOrigin" },
                {PaymentTransactionDTO.SearchByParameters.PAYMENT_GUID,"Tr.TrxPaymentGuid" },
                {PaymentTransactionDTO.SearchByParameters.PARENT_RESPONSE_ID,"Tr.ParentResponseId" },
                {PaymentTransactionDTO.SearchByParameters.PARENT_RESPONSE_ID_OR_RESPONSE_ID,"Tr.ParentResponseId" }
            };

        private UnitOfWork unitOfWork;
        private const string SELECT_QUERY = @"SELECT Tr.* FROM CCTransactionsPGW as Tr ";
        #region MERGE QUERY
        private const string MERGE_QUERY = @"DECLARE @Output AS PaymentTransactionType;
                                            MERGE INTO CCTransactionsPGW tr
                                            USING @PaymentTransactionList AS src
                                            ON src.ResponseID = tr.ResponseID
                                            WHEN MATCHED THEN
                                            UPDATE SET
                                            TransactionId=(SELECT TOP 1 TrxId FROM trxPayments WHERE Guid = src.TrxPaymentGuid),
                                            InvoiceNo=src.InvoiceNo,
                                            TokenID=src.TokenID,
                                            RecordNo=src.RecordNo,
                                            DSIXReturnCode=src.DSIXReturnCode,
                                            StatusID=src.StatusID,
                                            TextResponse=src.TextResponse,
                                            AcctNo=src.AcctNo,
                                            CardType=src.CardType,
                                            TranCode=src.TranCode,
                                            RefNo=src.RefNo,
                                            Purchase=src.Purchase,
                                            Authorize=src.Authorize,
                                            TransactionDatetime=src.TransactionDatetime,
                                            AuthCode=src.AuthCode,
                                            ProcessData=src.ProcessData,
                                            ResponseOrigin=src.ResponseOrigin,
                                            UserTraceData=src.UserTraceData,
                                            CaptureStatus=src.CaptureStatus,
                                            AcqRefData=src.AcqRefData,
                                            TipAmount=src.TipAmount,
                                            MerchantCopy=src.MerchantCopy,
                                            CustomerCopy=src.CustomerCopy,
                                            CustomerCardProfileId=src.CustomerCardProfileId,
                                            TrxPaymentGuid=src.TrxPaymentGuid,
                                            IsActive=src.IsActive,
                                            Status=src.Status,
                                            CreditCardName=src.CreditCardName,
                                            NameOnCreditCard=src.NameOnCreditCard,
                                            CreditCardExpiry=src.CreditCardExpiry,
                                            Amount=src.Amount,
                                            LastUpdatedBy = src.LastUpdatedBy,
                                            LastUpdateDate = GETDATE(),
                                            MasterEntityId = src.MasterEntityId,
											ParentResponseId=src.ParentResponseId
                                            WHEN NOT MATCHED THEN INSERT (
                                            TransactionId,
                                            InvoiceNo,
                                            TokenID,
                                            RecordNo,
                                            DSIXReturnCode,
                                            StatusID,
                                            TextResponse,
                                            AcctNo,
                                            CardType,
                                            TranCode,
                                            RefNo,
                                            Purchase,
                                            Authorize,
                                            TransactionDatetime,
                                            AuthCode,
                                            ProcessData,
                                            ResponseOrigin,
                                            UserTraceData,
                                            CaptureStatus,
                                            AcqRefData,
                                            TipAmount,
                                            MerchantCopy,
                                            CustomerCopy,
                                            CustomerCardProfileId,
                                            TrxPaymentGuid,
                                            IsActive,
                                            Status,
                                            CreditCardName,
                                            NameOnCreditCard,
                                            CreditCardExpiry,
                                            Amount,
                                            Guid,
                                            site_id,
                                            MasterEntityId,
                                            CreatedBy,
                                            CreationDate,
                                            LastUpdatedBy,
                                            LastUpdateDate,
                                            ParentResponseId
                                            )
                                            VALUES (
                                            (SELECT TOP 1 TrxId FROM trxPayments WHERE Guid = src.TrxPaymentGuid),
                                            src.InvoiceNo,
                                            src.TokenID,
                                            src.RecordNo,
                                            src.DSIXReturnCode,
                                            src.StatusID,
                                            src.TextResponse,
                                            src.AcctNo,
                                            src.CardType,
                                            src.TranCode,
                                            src.RefNo,
                                            src.Purchase,
                                            src.Authorize,
                                            src.TransactionDatetime,
                                            src.AuthCode,
                                            src.ProcessData,
                                            src.ResponseOrigin,
                                            src.UserTraceData,
                                            src.CaptureStatus,
                                            src.AcqRefData,
                                            src.TipAmount,
                                            src.MerchantCopy,
                                            src.CustomerCopy,
                                            src.CustomerCardProfileId,
                                            src.TrxPaymentGuid,
                                            src.IsActive,
                                            src.Status,
                                            src.CreditCardName,
                                            src.NameOnCreditCard,
                                            src.CreditCardExpiry,
                                            src.Amount,
                                            src.Guid,
                                            src.site_id,
                                            src.MasterEntityId,
                                            src.CreatedBy,
                                            GETDATE(),
                                            src.LastUpdatedBy,                             
                                            GETDATE(),
											src.ParentResponseId
                                            )
                                            OUTPUT
                                            inserted.ResponseID,
                                            inserted.CreatedBy,
                                            inserted.CreationDate,
                                            inserted.LastUpdateDate,
                                            inserted.LastUpdatedBy,
                                            inserted.site_id,
                                            inserted.Guid
                                            INTO @Output(
                                            ResponseID,
                                            CreatedBy, 
                                            CreationDate, 
                                            LastUpdateDate, 
                                            LastUpdatedBy, 
                                            site_id, 
                                            Guid);
                                            SELECT * FROM @Output;";
        #endregion
        private DataAccessHandler dataAccessHandler;
        private ExecutionContext executionContext;
        private List<SqlParameter> parameters = new List<SqlParameter>();

        /// <summary>
        /// Default constructor of PaymentTransactionDataHandler class
        /// </summary>
        /// <param name="sqlTransaction">sqlTransaction</param>
        public PaymentTransactionDataHandler(ExecutionContext executionContext, UnitOfWork unitOfWork)
        {

            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry();
            this.executionContext = executionContext;
            dataAccessHandler = new DataAccessHandler(executionContext);
            this.unitOfWork = unitOfWork;
            log.LogMethodExit();
        }

        /// <summary>
        /// ParameterHelper method for building SQL parameters for query
        /// </summary>
        /// <param name="parameters">parameters</param>
        /// <param name="parameterName">parameterName</param>
        /// <param name="value">value</param>
        /// <param name="negetiveValueNull">negetiveValueNull</param>
        //private void ParameterHelper(List<SqlParameter> parameters, string parameterName, object value, bool negetiveValueNull = false)
        //{
        //    log.LogMethodEntry(parameters, parameterName, value, negetiveValueNull);
        //    if (parameters != null && !string.IsNullOrEmpty(parameterName))
        //    {
        //        if (value is int)
        //        {
        //            if (negetiveValueNull && ((int)value) < 0)
        //            {
        //                parameters.Add(new SqlParameter(parameterName, DBNull.Value));
        //            }
        //            else
        //            {
        //                parameters.Add(new SqlParameter(parameterName, value));
        //            }
        //        }
        //        else if (value is string)
        //        {
        //            if (string.IsNullOrEmpty(value as string))
        //            {
        //                parameters.Add(new SqlParameter(parameterName, DBNull.Value));
        //            }
        //            else
        //            {
        //                parameters.Add(new SqlParameter(parameterName, value));
        //            }
        //        }
        //        else
        //        {
        //            if (value == null)
        //            {
        //                parameters.Add(new SqlParameter(parameterName, DBNull.Value));
        //            }
        //            else
        //            {
        //                parameters.Add(new SqlParameter(parameterName, value));
        //            }
        //        }
        //    }
        //    log.LogMethodExit();
        //}


        
        /// <summary>
        /// Converts the Data row object to PaymentTransactionDTO class type
        /// </summary>
        /// <param name="dataRow">DataRow</param>
        /// <returns>Returns PaymentTransactionDTO</returns>
        private PaymentTransactionDTO GetPaymentTransactionDTO(DataRow dataRow)
        {
            log.LogMethodEntry(dataRow);
            PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO(Convert.ToInt32(dataRow["ResponseID"]),
                                            dataRow["TransactionId"] == DBNull.Value ? -1 : Convert.ToInt32(dataRow["TransactionId"]),
                                            dataRow["InvoiceNo"] == DBNull.Value ? string.Empty : dataRow["InvoiceNo"].ToString(),
                                            dataRow["TokenID"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["TokenID"]),
                                            dataRow["RecordNo"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["RecordNo"]),
                                            dataRow["DSIXReturnCode"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["DSIXReturnCode"]),
                                            dataRow["StatusID"] == DBNull.Value ? -1 : Convert.ToInt32(dataRow["StatusID"]),
                                            dataRow["TextResponse"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["TextResponse"]),
                                            dataRow["AcctNo"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["AcctNo"]),
                                            dataRow["CardType"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["CardType"]),
                                            dataRow["TranCode"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["TranCode"]),
                                            dataRow["RefNo"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["RefNo"]),
                                            dataRow["Purchase"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["Purchase"]),
                                            dataRow["Authorize"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["Authorize"]),
                                            dataRow["TransactionDatetime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(dataRow["TransactionDatetime"]),
                                            dataRow["AuthCode"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["AuthCode"]),
                                            dataRow["ProcessData"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["ProcessData"]),
                                            dataRow["ResponseOrigin"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["ResponseOrigin"]),
                                            dataRow["UserTraceData"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["UserTraceData"]),
                                            dataRow["CaptureStatus"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["CaptureStatus"]),
                                            dataRow["AcqRefData"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["AcqRefData"]),
                                            dataRow["Guid"] == DBNull.Value ? string.Empty : dataRow["Guid"].ToString(),
                                            dataRow["SynchStatus"] == DBNull.Value ? false : Convert.ToBoolean(dataRow["SynchStatus"]),
                                            dataRow["site_id"] == DBNull.Value ? -1 : Convert.ToInt32(dataRow["site_id"]),
                                            dataRow["TipAmount"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["TipAmount"]),
                                            dataRow["MasterEntityId"] == DBNull.Value ? -1 : Convert.ToInt32(dataRow["MasterEntityId"]),
                                            dataRow["CreatedBy"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["CreatedBy"]),
                                            dataRow["CreationDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(dataRow["CreationDate"]),
                                            dataRow["LastUpdatedBy"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["LastUpdatedBy"]),
                                            dataRow["LastUpdateDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(dataRow["LastUpdateDate"]),
                                            dataRow["CustomerCopy"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["CustomerCopy"]),
                                            dataRow["MerchantCopy"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["MerchantCopy"]),
                                            dataRow["CustomerCardProfileId"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["CustomerCardProfileId"]),
                                            dataRow["TrxPaymentGuid"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["TrxPaymentGuid"]),
                                            dataRow["IsActive"] == DBNull.Value ? true : Convert.ToBoolean(dataRow["IsActive"]),
                                            dataRow["Status"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["Status"]),
                                            dataRow["CreditCardName"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["CreditCardName"]),
                                            dataRow["NameOnCreditCard"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["NameOnCreditCard"]),
                                            dataRow["CreditCardExpiry"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["CreditCardExpiry"]),
                                            dataRow["Amount"] == DBNull.Value ? 0 : Convert.ToDecimal(dataRow["Amount"]),
                                            dataRow["ParentResponseId"] == DBNull.Value ? -1 : Convert.ToInt32(dataRow["ParentResponseId"])
                                            );

            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        /// <summary>
        /// Gets the PaymentTransaction data of passed transactionsId
        /// </summary>
        /// <param name="responseId">integer type parameter</param>
        /// <returns>Returns PaymentTransactionDTO</returns>
        public PaymentTransactionDTO GetPaymentTransactionDTO(int responseId)
        {
            log.LogMethodEntry(responseId);
            PaymentTransactionDTO returnValue = null;
            string query = SELECT_QUERY + "    WHERE Tr.ResponseID = @ResponseID";
            SqlParameter parameter = new SqlParameter("@ResponseID", responseId);
            DataTable dataTable = dataAccessHandler.executeSelectQuery(query, new SqlParameter[] { parameter }, unitOfWork.SQLTrx);
            if (dataTable.Rows.Count > 0)
            {
                returnValue = GetPaymentTransactionDTO(dataTable.Rows[0]);
            }
            log.LogMethodExit(returnValue);
            return returnValue;
        }

        /// <summary>
        /// To Build the Query String
        /// </summary>
        /// <param name="searchParameters"></param>
        /// <returns>Query String object</returns>
        private string BuildQueryString(List<KeyValuePair<PaymentTransactionDTO.SearchByParameters, string>> searchParameters)
        {
            log.LogMethodEntry(searchParameters);
            int count = 0;
            string joiner = string.Empty;
            StringBuilder query = new StringBuilder(" where ");         
            parameters = new List<SqlParameter>();
            foreach (KeyValuePair<PaymentTransactionDTO.SearchByParameters, string> searchParameter in searchParameters)
            {
                if (DBSearchParameters.ContainsKey(searchParameter.Key))
                {
                    joiner = (count == 0) ? string.Empty : " and ";
                    if (searchParameter.Key == PaymentTransactionDTO.SearchByParameters.RESPONSE_ID ||
                       searchParameter.Key == PaymentTransactionDTO.SearchByParameters.MASTER_ENTITY_ID ||
                       searchParameter.Key == PaymentTransactionDTO.SearchByParameters.PARENT_RESPONSE_ID)
                    {
                        query.Append(joiner + DBSearchParameters[searchParameter.Key] + "=" + dataAccessHandler.GetParameterName(searchParameter.Key));
                        parameters.Add(new SqlParameter(dataAccessHandler.GetParameterName(searchParameter.Key), Convert.ToInt32(searchParameter.Value)));
                    }
                    else if (searchParameter.Key == PaymentTransactionDTO.SearchByParameters.PARENT_RESPONSE_ID_OR_RESPONSE_ID)
                    {
                        query.Append(joiner + @"(Tr.ParentResponseId =" + dataAccessHandler.GetParameterName(searchParameter.Key) + " OR Tr.ResponseID = " + dataAccessHandler.GetParameterName(searchParameter.Key) + ")");
                        parameters.Add(new SqlParameter(dataAccessHandler.GetParameterName(searchParameter.Key), Convert.ToInt32(searchParameter.Value)));
                    }
                    else if (searchParameter.Key == PaymentTransactionDTO.SearchByParameters.SITE_ID)
                    {
                        query.Append(joiner + "(" + DBSearchParameters[searchParameter.Key] + "=" + dataAccessHandler.GetParameterName(searchParameter.Key) + " or " + dataAccessHandler.GetParameterName(searchParameter.Key) + " =-1)");
                        parameters.Add(new SqlParameter(dataAccessHandler.GetParameterName(searchParameter.Key), searchParameter.Value));
                    }
                    else if (searchParameter.Key == PaymentTransactionDTO.SearchByParameters.INVOICE_NUMBER ||
                            searchParameter.Key == PaymentTransactionDTO.SearchByParameters.TRAN_CODE ||
                            searchParameter.Key == PaymentTransactionDTO.SearchByParameters.AUTH_CODE ||
                            searchParameter.Key == PaymentTransactionDTO.SearchByParameters.REF_NO ||
                            searchParameter.Key == PaymentTransactionDTO.SearchByParameters.RESPONSE_ORIGIN ||
                            searchParameter.Key == PaymentTransactionDTO.SearchByParameters.PAYMENT_GUID)
                    {
                        query.Append(joiner + DBSearchParameters[searchParameter.Key] + "=" + dataAccessHandler.GetParameterName(searchParameter.Key));
                        parameters.Add(new SqlParameter(dataAccessHandler.GetParameterName(searchParameter.Key), searchParameter.Value));
                    }
                    else
                    {
                        query.Append(joiner + "Isnull(" + DBSearchParameters[searchParameter.Key] + ",'') like  " + "N'%'+" + dataAccessHandler.GetParameterName(searchParameter.Key) + "+'%'");
                        parameters.Add(new SqlParameter(dataAccessHandler.GetParameterName(searchParameter.Key), searchParameter.Value));
                    }
                    count++;
                }
                else
                {
                    string message = "The query parameter does not exist " + searchParameter.Key;
                    log.LogVariableState("searchParameter.Key", searchParameter.Key);
                    log.LogMethodExit(null, "Throwing exception -" + message);
                    throw new Exception(message);
                }
            }

            log.LogMethodExit(query.ToString());
            return query.ToString();
        }

        /// <summary>
        /// Gets the PaymentTransactionDTO list matching the search key
        /// </summary>
        /// <param name="searchParameters">List of search parameters</param>
        /// <returns>Returns the list of PaymentTransactionDTO matching the search criteria</returns>
        public List<PaymentTransactionDTO> GetPaymentTransactionDTOList(List<KeyValuePair<PaymentTransactionDTO.SearchByParameters, string>> searchParameters)
        {
            log.LogMethodEntry(searchParameters);
            List<PaymentTransactionDTO> list = null;

            string selectQuery = SELECT_QUERY;
            if ((searchParameters != null) && (searchParameters.Count > 0))
            {

                selectQuery = selectQuery + BuildQueryString(searchParameters);
            }        
            log.Debug("Search query: " + selectQuery);
            DataTable dataTable = dataAccessHandler.executeSelectQuery(selectQuery, parameters.ToArray(), unitOfWork.SQLTrx);
            if (dataTable.Rows.Count > 0)
            {
                list = new List<PaymentTransactionDTO>();
                foreach (DataRow dataRow in dataTable.Rows)
                {
                    PaymentTransactionDTO paymentTransactionDTO = GetPaymentTransactionDTO(dataRow);
                    list.Add(paymentTransactionDTO);
                }
            }
            log.LogMethodExit(list);
            return list;
        }

        /// <summary>
        /// Gets the PaymentTransactionDTO list matching the search key
        /// </summary>
        /// <param name="searchParameters">List of search parameters</param>
        /// <returns>Returns the list of Non Reversed PaymentTransactionDTO matching the search criteria</returns>
        public List<PaymentTransactionDTO> GetNonReversedPaymentTransactionDTOList(List<KeyValuePair<PaymentTransactionDTO.SearchByParameters, string>> searchParameters)
        {
            log.LogMethodEntry(searchParameters);
            List<PaymentTransactionDTO> list = null;

            string selectQuery = SELECT_QUERY;
            KeyValuePair<PaymentTransactionDTO.SearchByParameters, string>? splitIdParameter = null;
            KeyValuePair<PaymentTransactionDTO.SearchByParameters, string>? transactionIdParameter = null;
            if ((searchParameters != null) && (searchParameters.Count > 0))
            {
                foreach (var item in searchParameters)
                {
                    if (item.Key == PaymentTransactionDTO.SearchByParameters.SPLIT_ID)
                    {
                        splitIdParameter = item;
                    }
                    if (item.Key == PaymentTransactionDTO.SearchByParameters.TRANSACTION_ID)
                    {
                        transactionIdParameter = item;
                    }
                }
                if (splitIdParameter != null)
                {
                    searchParameters.Remove(splitIdParameter.Value);
                }
                if (transactionIdParameter != null)
                {
                    searchParameters.Remove(transactionIdParameter.Value);
                }
                selectQuery = selectQuery + BuildQueryString(searchParameters) + " AND ";
            }
            else
            {
                selectQuery = selectQuery + " WHERE ";
            }
            StringBuilder transactionAndSplitIdCondition = new StringBuilder();
            if (transactionIdParameter != null)
            {
                transactionAndSplitIdCondition.Append(" tp.TrxId=");
                transactionAndSplitIdCondition.Append(transactionIdParameter.Value.Value);
                transactionAndSplitIdCondition.Append(" AND ");
            }
            if (splitIdParameter != null)
            {
                transactionAndSplitIdCondition.Append(" tp.SplitId=");
                transactionAndSplitIdCondition.Append(splitIdParameter.Value.Value);
                transactionAndSplitIdCondition.Append(" AND ");
            }
            selectQuery = selectQuery + " EXISTS(SELECT * FROM TrxPayments tp WHERE " + transactionAndSplitIdCondition.ToString() + " tp.CCResponseId = Tr.ResponseID AND tp.ParentPaymentId IS NULL AND NOT EXISTS(SELECT * FROM TrxPayments WHERE TrxPayments.ParentPaymentId = tp.PaymentId)) ";
            log.Debug("Search query: " + selectQuery);
            DataTable dataTable = dataAccessHandler.executeSelectQuery(selectQuery, parameters.ToArray(), unitOfWork.SQLTrx);
            if (dataTable.Rows.Count > 0)
            {
                list = new List<PaymentTransactionDTO>();
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    PaymentTransactionDTO paymentTransactionDTO = GetPaymentTransactionDTO(dataTable.Rows[i]);
                    list.Add(paymentTransactionDTO);
                }
            }
            log.LogMethodExit(list);
            return list;
        }
        /// <summary>
        /// GetPaymentTransactionDTOListOfTransactionPayments
        /// </summary>
        /// <param name="transactionIdList"></param>
        /// <returns></returns>
        public List<PaymentTransactionDTO> GetPaymentTransactionDTOListOfTransactions(List<int> transactionIdList, bool isActive)
        {
            log.LogMethodEntry(transactionIdList);
            List<PaymentTransactionDTO> list = new List<PaymentTransactionDTO>();
            string query = SELECT_QUERY + @" INNER JOIN @transactionIdList List
                            ON Tr.TransactionId = List.Id ";
            if (isActive)
            {
                query += " WHERE ISNULL(Tr.IsActive, 1) = 1 ";
            }
            DataTable table = dataAccessHandler.BatchSelect(query, "@transactionIdList", transactionIdList, null, unitOfWork.SQLTrx);
            if (table != null && table.Rows.Count > 0)
            {
                foreach (DataRow row in table.Rows)
                {
                    list.Add(GetPaymentTransactionDTO(row));
                }
            }
            log.LogMethodExit(list);
            return list;
        }

        public List<PaymentTransactionDTO> GetPaymentTransactionDTOListOfTransactionPayments(List<string> transactionPaymentGuidList, bool isActive)
        {
            log.LogMethodEntry(transactionPaymentGuidList);
            List<PaymentTransactionDTO> list = new List<PaymentTransactionDTO>();
            string query = SELECT_QUERY + @" INNER JOIN @transactionPaymentGuidList List
                            ON Tr.TrxpaymentGuid = List.Value ";
            if (isActive)
            {
                query += " WHERE ISNULL(Tr.IsActive, 1) = 1 ";
            }
            DataTable table = dataAccessHandler.BatchSelect(query, "@transactionPaymentGuidList", transactionPaymentGuidList, null, unitOfWork.SQLTrx);
            if (table != null && table.Rows.Count > 0)
            {
                foreach (DataRow row in table.Rows)
                {
                    list.Add(GetPaymentTransactionDTO(row));
                }
            }
            log.LogMethodExit(list);
            return list;
        }
        private Dictionary<string, PaymentTransactionDTO> GetPaymentTransactionDTOGuidMap(List<PaymentTransactionDTO> paymentTransactionDTOList)
        {
            Dictionary<string, PaymentTransactionDTO> result = new Dictionary<string, PaymentTransactionDTO>();
            for (int i = 0; i < paymentTransactionDTOList.Count; i++)
            {
                if (string.IsNullOrWhiteSpace(paymentTransactionDTOList[i].Guid))
                {
                    paymentTransactionDTOList[i].Guid = Guid.NewGuid().ToString();
                }
                result.Add(paymentTransactionDTOList[i].Guid, paymentTransactionDTOList[i]);
            }
            return result;
        }

        private void Update(Dictionary<string, PaymentTransactionDTO> paymentTransactionDTOGuidMap, DataTable table)
        {
            foreach (DataRow row in table.Rows)
            {
                PaymentTransactionDTO paymentTransactionDTO = paymentTransactionDTOGuidMap[Convert.ToString(row["Guid"])];
                paymentTransactionDTO.ResponseID = row["ResponseID"] == DBNull.Value ? -1 : Convert.ToInt32(row["ResponseID"]);
                paymentTransactionDTO.CreatedBy = row["CreatedBy"] == DBNull.Value ? string.Empty : Convert.ToString(row["CreatedBy"]);
                paymentTransactionDTO.CreationDate = row["CreationDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(row["CreationDate"]);
                paymentTransactionDTO.LastUpdatedBy = row["LastUpdatedBy"] == DBNull.Value ? string.Empty : Convert.ToString(row["LastUpdatedBy"]);
                paymentTransactionDTO.LastUpdateDate = row["LastUpdateDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(row["LastUpdateDate"]);
                paymentTransactionDTO.SiteId = row["site_id"] == DBNull.Value ? -1 : Convert.ToInt32(row["site_id"]);
                paymentTransactionDTO.AcceptChanges();
            }
        }

        public void Save(PaymentTransactionDTO paymentTransactionDTO)
        {
            log.LogMethodEntry(paymentTransactionDTO);
            Save(new List<PaymentTransactionDTO>() { paymentTransactionDTO });
            log.LogMethodExit();
        }
        public void Save(List<PaymentTransactionDTO> paymentTransactionDTOList)
        {
            log.LogMethodEntry(paymentTransactionDTOList);
            Dictionary<string, PaymentTransactionDTO> transactionAccountDTOGuidMap = GetPaymentTransactionDTOGuidMap(paymentTransactionDTOList);
            List<SqlDataRecord> sqlDataRecords = GetSqlDataRecords(paymentTransactionDTOList);
            DataTable dataTable = dataAccessHandler.BatchSave(sqlDataRecords,
                                                                unitOfWork.SQLTrx,
                                                                MERGE_QUERY,
                                                                "PaymentTransactionType",
                                                                "@PaymentTransactionList");
            Update(transactionAccountDTOGuidMap, dataTable);
            log.LogMethodExit();
        }

        private List<SqlDataRecord> GetSqlDataRecords(List<PaymentTransactionDTO> paymentTransactionDTOList)
        {
            log.LogMethodEntry(paymentTransactionDTOList);
            List<SqlDataRecord> result = new List<SqlDataRecord>();
            int col = 0;
            SqlMetaData[] columnStructures = new SqlMetaData[41];
            columnStructures[col++] = new SqlMetaData("ResponseID", SqlDbType.Int);
            columnStructures[col++] = new SqlMetaData("TransactionId", SqlDbType.Int);
            columnStructures[col++] = new SqlMetaData("InvoiceNo", SqlDbType.NVarChar,50);
            columnStructures[col++] = new SqlMetaData("TokenID", SqlDbType.NVarChar, 500);
            columnStructures[col++] = new SqlMetaData("RecordNo", SqlDbType.NVarChar,400);
            columnStructures[col++] = new SqlMetaData("DSIXReturnCode", SqlDbType.NVarChar,500);
            columnStructures[col++] = new SqlMetaData("StatusID", SqlDbType.Int);
            columnStructures[col++] = new SqlMetaData("TextResponse", SqlDbType.NVarChar,200);
            columnStructures[col++] = new SqlMetaData("AcctNo", SqlDbType.NVarChar, 400);
            columnStructures[col++] = new SqlMetaData("CardType", SqlDbType.NVarChar,50);
            columnStructures[col++] = new SqlMetaData("TranCode", SqlDbType.NVarChar,50);
            columnStructures[col++] = new SqlMetaData("RefNo", SqlDbType.NVarChar, 50);
            columnStructures[col++] = new SqlMetaData("Purchase", SqlDbType.NVarChar, 15);
            columnStructures[col++] = new SqlMetaData("Authorize", SqlDbType.NVarChar, 15);
            columnStructures[col++] = new SqlMetaData("TransactionDatetime", SqlDbType.DateTime);
            columnStructures[col++] = new SqlMetaData("AuthCode", SqlDbType.NVarChar,400);
            columnStructures[col++] = new SqlMetaData("ProcessData", SqlDbType.NVarChar, SqlMetaData.Max);
            columnStructures[col++] = new SqlMetaData("ResponseOrigin", SqlDbType.NVarChar, SqlMetaData.Max);
            columnStructures[col++] = new SqlMetaData("UserTraceData", SqlDbType.NVarChar, 50);
            columnStructures[col++] = new SqlMetaData("CaptureStatus", SqlDbType.NVarChar, 50);
            columnStructures[col++] = new SqlMetaData("AcqRefData", SqlDbType.NVarChar, 400);
            columnStructures[col++] = new SqlMetaData("TipAmount", SqlDbType.NVarChar, 15);
            columnStructures[col++] = new SqlMetaData("MerchantCopy", SqlDbType.NVarChar, SqlMetaData.Max);
            columnStructures[col++] = new SqlMetaData("CustomerCopy", SqlDbType.NVarChar, SqlMetaData.Max);
            columnStructures[col++] = new SqlMetaData("CustomerCardProfileId", SqlDbType.NVarChar, 500);
            columnStructures[col++] = new SqlMetaData("TrxPaymentGuid", SqlDbType.UniqueIdentifier);
            columnStructures[col++] = new SqlMetaData("IsActive", SqlDbType.Bit);
            columnStructures[col++] = new SqlMetaData("Status", SqlDbType.NVarChar, 400);
            columnStructures[col++] = new SqlMetaData("CreditCardName", SqlDbType.NVarChar, 400);
            columnStructures[col++] = new SqlMetaData("NameOnCreditCard", SqlDbType.NVarChar, 400);
            columnStructures[col++] = new SqlMetaData("CreditCardExpiry", SqlDbType.NVarChar, 400);
            columnStructures[col++] = new SqlMetaData("Amount", SqlDbType.Decimal, 18,4);
            columnStructures[col++] = new SqlMetaData("ParentResponseId", SqlDbType.Int);
            columnStructures[col++] = new SqlMetaData("Guid", SqlDbType.UniqueIdentifier);
            columnStructures[col++] = new SqlMetaData("SynchStatus", SqlDbType.Bit);
            columnStructures[col++] = new SqlMetaData("site_id", SqlDbType.Int);
            columnStructures[col++] = new SqlMetaData("MasterEntityId", SqlDbType.Int);
            columnStructures[col++] = new SqlMetaData("CreatedBy", SqlDbType.NVarChar,50);
            columnStructures[col++] = new SqlMetaData("CreationDate", SqlDbType.DateTime);
            columnStructures[col++] = new SqlMetaData("LastUpdatedBy", SqlDbType.NVarChar, 50);
            columnStructures[col++] = new SqlMetaData("LastUpdateDate", SqlDbType.DateTime);
            for (int i = 0; i < paymentTransactionDTOList.Count; i++)
            {
                col = 0;
                SqlDataRecord dataRecord = new SqlDataRecord(columnStructures);
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].ResponseID, true));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].TransactionId, true));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].InvoiceNo));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].TokenID));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].RecordNo));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].DSIXReturnCode));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].StatusID, true));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].TextResponse));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].AcctNo));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].CardType));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].TranCode));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].RefNo));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].Purchase));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].Authorize));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].TransactionDatetime));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].AuthCode));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].ProcessData));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].ResponseOrigin));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].UserTraceData));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].CaptureStatus));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].AcqRefData));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].TipAmount));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].MerchantCopy));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].CustomerCopy));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].CustomerCardProfileId));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(Guid.Parse(paymentTransactionDTOList[i].TransactionPaymentGuid)));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].IsActive));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].Status));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].CreditCardName));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].NameOnCreditCard));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].CreditCardExpiry));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].Amount));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].ParentResponseId,true));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(Guid.Parse(paymentTransactionDTOList[i].Guid)));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].SynchStatus));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(executionContext.SiteId, true));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].MasterEntityId, true));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(executionContext.UserId));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].CreationDate));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(executionContext.UserId));
                dataRecord.SetValue(col++, dataAccessHandler.GetParameterValue(paymentTransactionDTOList[i].LastUpdateDate));
                result.Add(dataRecord);
            }
            log.LogMethodExit(result);
            return result;
        }


    }
}
