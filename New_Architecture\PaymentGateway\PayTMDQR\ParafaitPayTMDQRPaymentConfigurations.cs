﻿using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Site;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace Semnox.Parafait.PaymentGateway
{
    class ParafaitPayTMDQRPaymentConfigurations : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        int adyenTransactionTimeout;

        public ParafaitPayTMDQRPaymentConfigurations(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            var posId = executionContext.POSMachineName;
            var siteContainerDTO = SiteViewContainerList.GetCurrentSiteContainerDTO(executionContext);
            var posVersion = string.Empty;
            if (siteContainerDTO != null)
            {
                posVersion = siteContainerDTO.Version;
            }

            OperatingSystem os = Environment.OSVersion;
            string osVersion = Convert.ToString(os.Version.Major);
            string operationSystem = os.ToString();

            SetConfiguration("CREDIT_CARD_TERMINAL_PORT_NO", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_PORT_NO"));
            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
            SetConfiguration("CREDIT_CARD_HOST_USERNAME", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_HOST_USERNAME"));
            SetConfiguration("CREDIT_CARD_STORE_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_STORE_ID"));
            SetConfiguration("CREDIT_CARD_HOST_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_HOST_URL"));
            SetConfiguration("MAXIMUM_WAIT_PERIOD_IN_MINIUTES", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "MAXIMUM_WAIT_PERIOD_IN_MINIUTES"));
            SetConfiguration("AUTO_CHECK_IN_MINIUTES", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "AUTO_CHECK_IN_MINIUTES"));
            SetConfiguration("CREDIT_CARD_TERMINAL_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_ID"));
            SetConfiguration("HIDE_SHIFT_OPEN_CLOSE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HIDE_SHIFT_OPEN_CLOSE"));
            SetConfiguration("ALLOW_CREDIT_CARD_AUTHORIZATION", Convert.ToString(paymentModeContainerDTO.AllowCreditCardAuthorization));
            SetConfiguration("CREDIT_CARD_TERMINAL_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_ID"));
            SetConfiguration("CC_PAYMENT_RECEIPT_PRINT", paymentModeContainerDTO.CcPaymentReceiptPrint);

        }
    }

}
