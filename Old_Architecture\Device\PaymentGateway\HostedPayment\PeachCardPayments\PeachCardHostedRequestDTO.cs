﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PeachCardPayments
{
    public class PeachPaymentsHostedRequestDTO
    {
        public string AuthenticationEntityId { get; set; }
        public string MerchantTransactionId { get; set; }
        public int Amount { get; set; }
        public string PaymentType { get; set; }
        public string Currency { get; set; }
        public string Nonce { get; set; }
        public string Signature { get; set; }
        public string ShopperResultUrl { get; set; }

    }
    public class PeachPaymentsTrxSearchRequestDTO
    {
        public string AuthenticationEntityId { get; set; }
        public string MerchantTransactionId { get; set; }
        public string Signature { get; set; }
    }

    public class PeachPaymentsRefundRequestDTO
    {
        public string AuthenticationEntityId { get; set; }
        public string Id { get; set; }
        public string Signature { get; set; }
        public string Currency { get; set; }
        public string Amount { get; set; }
        public string PaymentType { get; set; }

    }

    public class PeachPaymentsAuthRequestDTO
    {
        public string clientId { get; set; }
        public string clientSecret { get; set; }
        public string merchantId { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class PeachPaymentsCheckoutRequestDTO
    {
        public AuthenticationDto Authentication { get; set; }
        public string Amount { get; set; }
        public string Currency { get; set; }
        public string ShopperResultUrl { get; set; }
        public string MerchantTransactionId { get; set; }
        public string Nonce { get; set; }
        public string NotificationUrl { get; set; }
        public string CancelUrl { get; set; }
        public string MerchantInvoiceId { get; set; }
        public string DefaultPaymentMethod { get; set; }
        public bool ForceDefaultMethod { get; set; }
        public bool CreateRegistration { get; set; }
        public string[] CardTokens { get; set; } = new string[0]; // Initialized as an empty array
        public bool AllowStoringDetails { get; set; }
        public CustomParamsDto CustomParameters { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class AuthenticationDto
    {
        public string EntityId { get; set; }
    }

    public class CustomParamsDto
    {
        public bool EnableTestMode { get; set; }
    }
}
