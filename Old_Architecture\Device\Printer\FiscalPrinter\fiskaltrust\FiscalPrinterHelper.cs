﻿using Semnox.Core.Utilities;
using Semnox.Parafait.Communication;
using Semnox.Parafait.JobUtils;
using Semnox.Parafait.Languages;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace Semnox.Parafait.Device.Printer.FiscalPrinter.fiskaltrust
{
    /// <summary>
    /// FiscalTrustHelper
    /// </summary>
    public class FiscalTrustHelper
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private static string NORMAL_STATUS = "Normal";
        private static string EMAILMESSAGINGCHANELTYPE = "E";
        private static string FAILED = "FAILED";

        /// <summary>
        /// Generate Concurrent Request Summary
        /// </summary> 
        public static void EmailConcurrentRequestSummary(ExecutionContext executionContext, string fromProgram, string reportSubject,
            string elementName, int requestId, int programId,string status,int siteId, SqlTransaction sqlTrx)
        {
            log.LogMethodEntry(executionContext, fromProgram, reportSubject, elementName, requestId, programId, sqlTrx);
            try
            {
                if (requestId > -1 && programId > -1)
                {
                    ConcurrentProgramList concurrentProgramList = new ConcurrentProgramList(executionContext);
                    List<KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>> searchParameters = new List<KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>>();
                    searchParameters.Add(new KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>(ConcurrentProgramsDTO.SearchByProgramsParameters.PROGRAM_ID, programId.ToString()));
                    List<ConcurrentProgramsDTO> concurrentProgramsDTOList = concurrentProgramList.GetAllConcurrentPrograms(searchParameters);
                    if (concurrentProgramsDTOList != null && concurrentProgramsDTOList.Any())
                    {
                        ConcurrentProgramsDTO concurrentProgramsDTO = concurrentProgramsDTOList[0];
                        if (status == NORMAL_STATUS && string.IsNullOrWhiteSpace(concurrentProgramsDTO.SuccessNotificationMailId))
                        {
                            log.Error("Unable to send email, SuccessNotificationMailId is not set for " + concurrentProgramsDTO.ProgramName);
                        }
                        else if (status != NORMAL_STATUS && string.IsNullOrWhiteSpace(concurrentProgramsDTO.ErrorNotificationMailId))
                        {
                            log.Error("Unable to send email, ErrorNotificationMailId is not set for " + concurrentProgramsDTO.ProgramName);
                        }
                        else
                        {
                            string emailId = (status == NORMAL_STATUS) ? concurrentProgramsDTO.SuccessNotificationMailId : concurrentProgramsDTO.ErrorNotificationMailId;
                            FiscalTrustDataHandler fiscalTrustDataHandler = new FiscalTrustDataHandler();
                            List<FiscalTrustDTO> fiscalTrustDTOs = fiscalTrustDataHandler.GetFiskalTrustDailyOperationList(requestId, programId, siteId);
                            EmailConcurrentSummaryReport(executionContext: executionContext, fromProgram: fromProgram, emailId: emailId, reportSubject: reportSubject,
                                elementName: elementName, fiscalTrustDTOs: fiscalTrustDTOs, sqlTrx: sqlTrx);
                        }
                    }
                    else
                    {
                        log.Error("Unable to find concurrent program details for program id: " + programId.ToString());
                    }
                }
                else
                {
                    log.Error("Unable to find concurrent program request details for request id: " + requestId.ToString());
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }
            log.LogMethodExit();
        }

        private static void EmailConcurrentSummaryReport(ExecutionContext executionContext, string fromProgram, string emailId, string reportSubject, string elementName, List<FiscalTrustDTO> fiscalTrustDTOs, SqlTransaction sqlTrx)
        {
            log.LogMethodEntry(executionContext, fromProgram, emailId, reportSubject, fiscalTrustDTOs, sqlTrx);
            if (string.IsNullOrWhiteSpace(emailId) == false)
            {
                string msgBody = GenerateLogSummary(executionContext: executionContext, reportTitle: reportSubject, elementName: elementName,
                    fromProgram: fromProgram, fiscalTrustDTOs: fiscalTrustDTOs);
                MessagingRequestDTO messagingRequestDTO = new MessagingRequestDTO(-1, -1, fromProgram, EMAILMESSAGINGCHANELTYPE, emailId, null, null, null, null, null, null, reportSubject,
                                                                                  msgBody, -1, null, null, true, null, null, -1, false, null, false);

                SaveMessageRequest(executionContext, messagingRequestDTO, sqlTrx);
            }
            log.LogMethodExit();
        }

        private static string GenerateLogSummary(ExecutionContext executionContext, string reportTitle, string elementName, string fromProgram, List<FiscalTrustDTO> fiscalTrustDTOs)
        {
            log.LogMethodEntry(executionContext, reportTitle, elementName, fiscalTrustDTOs);
            StringBuilder reportData = new StringBuilder();
            int totalRecordCount = 0;
            int totalFailedRecordCount = 0;
            if (fiscalTrustDTOs == null)
            {
                fiscalTrustDTOs = new List<FiscalTrustDTO>();
            }
            string dateTimeFormat = ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "DATETIME_FORMAT");
            string numberFormat = ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "NUMBER_FORMAT");

            totalRecordCount = (fiscalTrustDTOs != null ? fiscalTrustDTOs.Count : 0);
            totalFailedRecordCount = (fiscalTrustDTOs != null ?
                                                fiscalTrustDTOs.Where(exs => exs.Status == FAILED).ToList().Count() : 0);
            reportData.Append("<!DOCTYPE html>");
            reportData.Append("<html>");
            reportData.Append(@"<head>
                                 <title>" + reportTitle +
                             @" </title></head> ");
            reportData.Append("<body>");
            reportData.Append(@"<h1>" + "FiskalTrust Trust Daily Opearation" +
                             @" </h1>");
            reportData.Append(@"<h1> Current Time: " + ServerDateTime.Now.ToString(dateTimeFormat) +
                             @" </h1>");
            reportData.Append(@"<h2>" + MessageContainerList.GetMessage(executionContext, " Total Number of &1 processed: &2", elementName, (totalRecordCount == 0 ? "0" : totalRecordCount.ToString(numberFormat))) +
                           @" </h2>");
            reportData.Append(@"<h2>" + MessageContainerList.GetMessage(executionContext, " Total Number of failed &1: &2", elementName, (totalFailedRecordCount == 0 ? "0" : totalFailedRecordCount.ToString(numberFormat))) +
                            @" </h2>");
            reportData.Append(@"</BR>");
            reportData.Append(@"<table border=1>
                                  <tr>
                                    <th>Request Id</th> 
                                    <th>POS ID</th> 
                                    <th>POS Name</th>
                                    <th>Computer Name</th> 
                                    <th>status</th>
                                    <th>Data</th>
                                    <th>Remarks</th>
                                  </tr>");
            if (fiscalTrustDTOs != null)
            {
                foreach (FiscalTrustDTO logDetails in fiscalTrustDTOs)
                {
                    reportData.Append(@"<tr><td>" + logDetails.RequestId + "</td><td>"
                                                  + logDetails.POSId + "</td><td>"
                                                  + logDetails.POSName + "</td><td>"
                                                  + logDetails.ComputerName + "</td><td>"
                                                  + logDetails.Status + "</td><td>"
                                                  + logDetails.Data + "</td><td>"
                                                  + logDetails.Remarks + "</td></tr>");
                }
            }
            reportData.Append(@"</table>");
            reportData.Append("</body>");
            reportData.Append("</HTML>");
            string reportDataString = reportData.ToString();
            log.LogMethodExit(reportDataString);
            return reportDataString;
        }

        private static void SaveMessageRequest(ExecutionContext executionContext, MessagingRequestDTO messagingRequestDTO, SqlTransaction sqlTrx)
        {
            log.LogMethodEntry(executionContext, messagingRequestDTO, sqlTrx);
            MessagingRequestBL messagingRequestBL = new MessagingRequestBL(executionContext, messagingRequestDTO);
            messagingRequestBL.Save(sqlTrx);
            log.LogMethodExit();
        }
    }
}
