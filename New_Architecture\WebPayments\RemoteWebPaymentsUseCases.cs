﻿/********************************************************************************************
* Project Name - Web Payments
 * Description - Remote implementation of the use cases
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Semnox.Parafait.Transaction.V2;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentGateway;
using Semnox.Parafait.ViewContainer;

namespace Semnox.Parafait.WebPayments
{
    public class RemoteWebPaymentsUseCases : RemoteUseCases, IWebPaymentsUseCases
    {
        private readonly Semnox.Parafait.logging.Logger log;
        private const string START_WEBPAYMENT_URL = "api/Transaction/WebPayment/{TransactionId}/StartWebPayment";
        private const string COMPLETE_WEBPAYMENT_URL = "api/Transaction/WebPayment/{TransactionId}/CompleteWebPayment";
        private const string REVERSE_WEBPAYMENT_URL = "api/Transaction/WebPayment/{TransactionId}/ReverseWebPayment";
        private const string PROCESS_GATEWAY_RESPONSE_URL = "api/Transaction/WebPayments/{PaymentGateway}/ProcessResponse/{caller}";
        private const string PAYMENT_SESSION_URL = "api/Transaction/WebPayment/Session";

        /// <summary>
        /// RemoteWebPaymentsUseCases
        /// </summary>
        /// <param name="executionContext"></param>
        public RemoteWebPaymentsUseCases(ExecutionContext executionContext, string requestGuid)
            : base(executionContext, requestGuid)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, requestGuid);
            log.LogMethodExit();
        }

        public async Task<WebPaymentDTO> StartWebPayment(int TransactionId, TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry(TransactionId, transactionPaymentDTO);
            try
            {
                String localURL = START_WEBPAYMENT_URL.Replace("{TransactionId}", TransactionId.ToString());
                WebPaymentDTO result = await Post<WebPaymentDTO>(localURL, transactionPaymentDTO);
                log.LogMethodExit(result);
                return result;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public async Task<WebPaymentDTO> CompleteWebPayment(int transactionId, CompleteWebPaymentDTO completeWebPaymentDTO)
        {
            log.LogMethodEntry(transactionId, completeWebPaymentDTO);
            try
            {
                String localURL = COMPLETE_WEBPAYMENT_URL.Replace("{TransactionId}", transactionId.ToString());
                WebPaymentDTO result = await Post<WebPaymentDTO>(localURL, completeWebPaymentDTO);
                log.LogMethodExit(result);
                return result;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public async Task<WebPaymentDTO> ReverseWebPayment(int transactionId, ReverseWebPaymentDTO reverseWebPaymentDTO)
        {
            log.LogMethodEntry(transactionId, reverseWebPaymentDTO);
            try
            {
                String localURL = COMPLETE_WEBPAYMENT_URL.Replace("{TransactionId}", transactionId.ToString());
                WebPaymentDTO result = await Post<WebPaymentDTO>(localURL, reverseWebPaymentDTO);
                log.LogMethodExit(result);
                return result;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public async Task<HostedPaymentResponseDTO> ProcessGatewayResponse(string paymentGateway, string paymentGatewayResponse, string caller)
        {
            log.LogMethodEntry(paymentGateway, paymentGatewayResponse);
            try
            {
                String localURL = PROCESS_GATEWAY_RESPONSE_URL.Replace("{PaymentGateway}", paymentGateway);
                HostedPaymentResponseDTO result = await Post<HostedPaymentResponseDTO>(localURL, paymentGatewayResponse);
                log.LogMethodExit(result);
                return result;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public async Task<PaymentSessionDTO> GetPaymentSession(PaymentRequestDTO paymentRequestDTO)
        {
            throw new PaymentSessionNotSupportedException(MessageViewContainerList.GetMessage(executionContext.SiteId, executionContext.LanguageId, 5979));
        }
    }
}
