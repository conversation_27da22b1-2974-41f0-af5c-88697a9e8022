﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PeachWalletPayments
{
    class PeachWalletHostedUtility : HostedPaymentGatewayUtility
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Constructor for SSLCommerzUtility.
        /// </summary>
        public PeachWalletHostedUtility() : base()
        {
            log.LogMethodEntry();
            Initialize();
            log.LogMethodExit();
        }

        // Initializes payment credentials list with default values
        private void Initialize()
        {
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", "");
            //PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", "");
            //PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_BASE_URL", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_API_URL", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_REQUERY_URL", "");

        }
        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
{
    { "000.000.000", PaymentStatusType.SUCCESS }, // Transaction succeeded
    { "000.000.100", PaymentStatusType.SUCCESS }, // successful request
    { "000.100.105", PaymentStatusType.SUCCESS }, // Chargeback Representment is successful
    { "000.100.106", PaymentStatusType.SUCCESS }, // Chargeback Representment cancellation is successful
    { "000.100.110", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Integrator Test Mode'
    { "000.100.111", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Validator Test Mode'
    { "000.100.112", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Connector Test Mode'
    { "000.300.000", PaymentStatusType.SUCCESS }, // Two-step transaction succeeded
    { "000.300.100", PaymentStatusType.SUCCESS }, // Risk check successful
    { "000.300.101", PaymentStatusType.SUCCESS }, // Risk bank account check successful
    { "000.300.102", PaymentStatusType.SUCCESS }, // Risk report successful
    { "000.300.103", PaymentStatusType.SUCCESS }, // Exemption check successful
    { "000.310.100", PaymentStatusType.SUCCESS }, // Account updated
    { "000.310.101", PaymentStatusType.SUCCESS }, // Account updated (Credit card expired)
    { "000.310.110", PaymentStatusType.SUCCESS }, // No updates found, but account is valid
    { "000.400.110", PaymentStatusType.SUCCESS }, // Authentication successful (frictionless flow)
    { "000.400.120", PaymentStatusType.SUCCESS }, // Authentication successful (data only flow)
    { "000.600.000", PaymentStatusType.SUCCESS }, // transaction succeeded due to external update

    { "000.400.000", PaymentStatusType.PENDING }, // Transaction succeeded (review for fraud suspicion)
    { "000.400.010", PaymentStatusType.PENDING }, // Transaction succeeded (review for AVS return code)
    { "000.400.020", PaymentStatusType.PENDING }, // Transaction succeeded (review for CVV return code)
    { "000.400.040", PaymentStatusType.PENDING }, // Transaction succeeded (review for amount mismatch)
    { "000.400.050", PaymentStatusType.PENDING }, // Transaction succeeded (pending)
    { "000.400.060", PaymentStatusType.PENDING }, // Transaction succeeded (merchant’s risk)
    { "000.400.070", PaymentStatusType.PENDING }, // Transaction succeeded (external risk review)
    { "000.400.080", PaymentStatusType.PENDING }, // Transaction succeeded (service unavailable)
    { "000.400.081", PaymentStatusType.PENDING }, // Transaction succeeded (network timeout)
    { "000.400.082", PaymentStatusType.PENDING }, // Transaction succeeded (processing timeout)
    { "000.400.090", PaymentStatusType.PENDING }, // Transaction succeeded (external risk check)
    { "000.400.100", PaymentStatusType.PENDING }, // Transaction succeeded, risk after payment rejected

    { "000.200.000", PaymentStatusType.PENDING }, // Transaction pending
    { "000.200.001", PaymentStatusType.PENDING }, // Transaction pending for acquirer
    { "000.200.100", PaymentStatusType.PENDING }, // successfully created checkout
    { "000.200.101", PaymentStatusType.PENDING }, // successfully updated checkout
    { "000.200.102", PaymentStatusType.PENDING }, // successfully deleted checkout
    { "000.200.103", PaymentStatusType.PENDING }, // checkout is pending
    { "000.200.200", PaymentStatusType.PENDING }, // Transaction initialized
    { "000.200.201", PaymentStatusType.PENDING }, // QR Scanned/Link Clicked
    { "100.400.500", PaymentStatusType.PENDING }, // waiting for external risk
    { "800.400.500", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant payment
    { "800.400.501", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant debit
    { "800.400.502", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant refund

    { "000.400.101", PaymentStatusType.FAILED }, // card not participating/authentication unavailable
    { "000.400.102", PaymentStatusType.FAILED }, // user not enrolled
    { "000.400.103", PaymentStatusType.FAILED }, // Technical Error in 3D system
    { "000.400.104", PaymentStatusType.FAILED }, // Missing or malformed 3DSecure Configuration for Channel
    { "000.400.105", PaymentStatusType.FAILED }, // Unsupported User Device
    { "000.400.106", PaymentStatusType.FAILED }, // invalid payer authentication response
    { "000.400.107", PaymentStatusType.FAILED }, // Communication Error to Scheme Directory Server
    { "000.400.108", PaymentStatusType.FAILED }, // Cardholder Not Found
    { "000.400.109", PaymentStatusType.FAILED }, // Card not enrolled for 3DS version 2
    { "000.400.111", PaymentStatusType.FAILED }, // Data Only request failed
    { "000.400.112", PaymentStatusType.FAILED }, // 3RI transaction not permitted
    { "000.400.200", PaymentStatusType.FAILED }, // risk management check communication error
};

        /// <summary>
        /// Initializes and returns an SSLCommerzHostedCommandHandler instance.
        /// </summary>
        /// <param name="paymentCredentialsList">The payment credentials list.</param>
        /// <returns>An instance of SSLCommerzHostedCommandHandler.</returns>
        public PeachWalletHostedCommandHandler InitializeCommandHandler(Dictionary<string, string> paymentCredentialsList)
        {
            if (paymentCredentialsList == null)
            {
                throw new ArgumentNullException("paymentCredentialsList", "The payment credentials list cannot be null.");
            }

            string merchantId;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", out merchantId) || string.IsNullOrEmpty(merchantId))
            {
                throw new ArgumentException("Please enter a valid ''%HOSTED_PAYMENT_GATEWAY_MERCHANT_ID'.");
            }

            string merchantKey;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", out merchantKey) || string.IsNullOrEmpty(merchantKey))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY'.");
            }

            string secretKey;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", out secretKey) || string.IsNullOrEmpty(secretKey))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_SECRET_KEY'.");
            }

            //string publicKey;
            //if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", out publicKey) || string.IsNullOrEmpty(publicKey))
            //{
            //    throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY'.");
            //}
            //string publishableKey;
            //if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY", out publishableKey) || string.IsNullOrEmpty(publishableKey))
            //{
            //    throw new ArgumentException("Please enter a valid ''%HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY'.");
            //}

            string apiUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_API_URL", out apiUrl) || string.IsNullOrEmpty(apiUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_API_URL'.");
            }

            string baseUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_BASE_URL", out baseUrl) || string.IsNullOrEmpty(baseUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_BASE_URL'.");
            }

            string requeryUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_REQUERY_URL", out requeryUrl) || string.IsNullOrEmpty(requeryUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_REQUERY_URL'.");
            }
            if (baseUrl.EndsWith("/"))
            {
                baseUrl = baseUrl.Remove(baseUrl.Length - 1);
            }
            if (apiUrl.EndsWith("/"))
            {
                apiUrl = apiUrl.Remove(apiUrl.Length - 1);
            }
            if (requeryUrl.EndsWith("/"))
            {
                requeryUrl = requeryUrl.Remove(requeryUrl.Length - 1);
            }

            return new PeachWalletHostedCommandHandler(merchantKey, secretKey, baseUrl, apiUrl, requeryUrl);

        }

        /// <summary>
        /// Retrieves the payment status search result for a transaction ID.
        /// </summary>
        /// <param name="trxId">The transaction ID to search for.</param>
        /// <returns>The payment status search result.</returns>
        public override TrxSearchUtilityDTO GetPaymentStatusSearch(string trxId)
        {
            log.LogMethodEntry(trxId);
            TrxSearchUtilityDTO trxSearchResult = new TrxSearchUtilityDTO
            {
                TransactionId = trxId
            };

            if (string.IsNullOrWhiteSpace(trxId))
            {
                log.Error("Invalid transaction ID.");
                trxSearchResult.ErrorMessage = "Invalid transaction ID.";
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.PaymentStatus = "Failed";
                trxSearchResult.TransactionId = "null";
                return trxSearchResult;
            }

            try
            {
                PeachWalletHostedCommandHandler peachWalletHostedCommandHandler = InitializeCommandHandler(PaymentCredentailsList);
                if (peachWalletHostedCommandHandler == null)
                {
                    log.Error("CommandHandler instance is null");
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    return trxSearchResult;
                }

                PeachPaymentsTrxSeachResponseDTO response = peachWalletHostedCommandHandler.VerifyPayment(trxId);
                if (response == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    return trxSearchResult;
                }
                log.Debug("response status: " + response.ResultCode);
                // Map payment status
                PaymentStatusType salePaymentStatus = MapPaymentStatus(response.ResultCode, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                // Format response
                string formattedJson = JsonConvert.SerializeObject(response, Formatting.Indented);
                trxSearchResult.FormattedResponse = formattedJson;
                trxSearchResult.PaymentStatus = salePaymentStatus.ToString();
            }
            catch (Exception ex)
            {
                log.Error("Error searching transaction details for trxId: " + trxId);
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.ErrorMessage = ex.Message; // todo: some cases msg is: String reference not set to an instance of a String.
                trxSearchResult.PaymentStatus = "Failed"; // Set payment status to "Failed"
            }

            log.LogMethodExit(trxSearchResult);
            return trxSearchResult;
        }

        /// <summary>
        /// Maps the payment status from raw response to Semnox payment status.
        /// </summary>
        /// <param name="rawPaymentGatewayStatus">The raw payment gateway status.</param>
        /// <param name="pgwTrxType">The type of payment gateway transaction.</param>
        /// <returns>The mapped payment status type.</returns>
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                    case PaymentGatewayTransactionType.REFUND:

                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

    }
}
