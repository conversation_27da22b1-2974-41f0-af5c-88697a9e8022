﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Semnox.Parafait.PaymentGateway.WorldPayCyberSource
{
    public class WorldPayRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public Processinginformation processingInformation { get; set; }
        public Paymentinformation paymentInformation { get; set; }
        public Orderinformation orderInformation { get; set; }
        public string paymentId { get; set; }
    }


    public class TxSearchRequestDTO
    {
        public string query { get; set; }
        public string sort { get; set; }

    }


    public class RefundRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public Orderinformation orderInformation { get; set; }
    }

    public class VoidRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
    }
}