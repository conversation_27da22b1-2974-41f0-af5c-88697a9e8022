﻿/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Mashreq Payment Configurations
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.200.0     07-Apr-2025         Amrutha      Created
 ********************************************************************************************************/

using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    class ParafaitMashreqPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public ParafaitMashreqPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO, bool isUnattended)
        {
            SetConfiguration("ALLOW_CREDIT_CARD_AUTHORIZATION", paymentModeContainerDTO.AllowCreditCardAuthorization? "Y" : "N");
            SetConfiguration("CREDIT_CARD_TERMINAL_PORT_NO", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_PORT_NO"));
            SetConfiguration("ENABLE_AUTO_CREDIT_CARD_AUTHORIZATION", paymentModeContainerDTO.EnableAutoCreditcardAuthorization? "Y" : "N");
            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
        }
    }
}
