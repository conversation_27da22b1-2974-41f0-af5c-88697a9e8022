﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Kount
{

    public class KountRisResponseDTO
    {
        public string VERS { get; set; }
        public string MODE { get; set; }
        public string TRAN { get; set; }
        public string MERC { get; set; }
        public string SESS { get; set; }
        public string ORDR { get; set; }
        public string AUTO { get; set; }
        public string SCOR { get; set; }
        public string GEOX { get; set; }
        public string BRND { get; set; }
        public object REGN { get; set; }
        public string NETW { get; set; }
        public string KAPT { get; set; }
        public string CARDS { get; set; }
        public string DEVICES { get; set; }
        public string EMAILS { get; set; }
        public string VELO { get; set; }
        public string VMAX { get; set; }
        public string SITE { get; set; }
        public string DEVICE_LAYERS { get; set; }
        public object FINGERPRINT { get; set; }
        public object TIMEZONE { get; set; }
        public string LOCALTIME { get; set; }
        public object REGION { get; set; }
        public object COUNTRY { get; set; }
        public object PROXY { get; set; }
        public object JAVASCRIPT { get; set; }
        public object FLASH { get; set; }
        public object COOKIES { get; set; }
        public object HTTP_COUNTRY { get; set; }
        public object LANGUAGE { get; set; }
        public object MOBILE_DEVICE { get; set; }
        public object MOBILE_TYPE { get; set; }
        public object MOBILE_FORWARDER { get; set; }
        public object VOICE_DEVICE { get; set; }
        public object PC_REMOTE { get; set; }
        public int RULES_TRIGGERED { get; set; }
        public List<string> RULE_DESCRIPTION { get; set; }
        public int COUNTERS_TRIGGERED { get; set; }
        public object REASON_CODE { get; set; }
        public object DDFS { get; set; }
        public object DSR { get; set; }
        public object UAS { get; set; }
        public object BROWSER { get; set; }
        public object OS { get; set; }
        public object PIP_IPAD { get; set; }
        public object PIP_LAT { get; set; }
        public object PIP_LON { get; set; }
        public object PIP_COUNTRY { get; set; }
        public object PIP_REGION { get; set; }
        public object PIP_CITY { get; set; }
        public object PIP_ORG { get; set; }
        public object IP_IPAD { get; set; }
        public object IP_LAT { get; set; }
        public object IP_LON { get; set; }
        public object IP_COUNTRY { get; set; }
        public object IP_REGION { get; set; }
        public object IP_CITY { get; set; }
        public object IP_ORG { get; set; }
        public double OMNISCORE { get; set; }
        public string PREVIOUSLY_WHITELISTED { get; set; }
        public object THREE_DS_MERCHANT_RESPONSE { get; set; }
        public int WARNING_COUNT { get; set; }

        public int ERROR_COUNT { get; set; }
        public string ERROR_0 { get; set; }

        public string ApprovalStatus
        {
            get
            {
                if (AUTO == "A" || AUTO == "R")
                {
                    return TransactionStatus.Approve.ToString();
                }
                else if (AUTO == "D" || AUTO == "E" || MODE == "E")
                {
                    return TransactionStatus.Decline.ToString();
                }
                else
                {
                    return TransactionStatus.Decline.ToString();
                }
            }
        }

        [JsonExtensionData]
        private Dictionary<string, JToken> _additionalProperties;

        [JsonIgnore] // Ignore the private field when serializing
        [JsonExtensionData]
        public Dictionary<string, JToken> AdditionalProperties
        {
            get
            {
                if (_additionalProperties == null)
                {
                    _additionalProperties = new Dictionary<string, JToken>();
                }
                return _additionalProperties;
            }
            set
            {
                _additionalProperties = value;
            }
        }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class WarningData
    {
        public string Warning { get; set; }
    }
}
