﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:2.0.50727.8766
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Xml.Serialization;

// 
// This source code was auto-generated by xsd, Version=2.0.50727.3038.
// 


/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
[System.Xml.Serialization.XmlRootAttribute("hub-response", Namespace = "", IsNullable = false)]
public partial class hubresponse
{

    private hubresponseTransaction[] itemsField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Transaction", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransaction[] Items
    {
        get
        {
            return this.itemsField;
        }
        set
        {
            this.itemsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransaction
{

    private hubresponseTransactionCard[] cardField;

    private hubresponseTransactionEMI[] eMIField;

    private hubresponseTransactionState[] stateField;

    private hubresponseTransactionChargeslipData[] chargeslipDataField;

    private string idField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Card", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionCard[] Card
    {
        get
        {
            return this.cardField;
        }
        set
        {
            this.cardField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("EMI", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionEMI[] EMI
    {
        get
        {
            return this.eMIField;
        }
        set
        {
            this.eMIField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("State", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionState[] State
    {
        get
        {
            return this.stateField;
        }
        set
        {
            this.stateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ChargeslipData", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionChargeslipData[] ChargeslipData
    {
        get
        {
            return this.chargeslipDataField;
        }
        set
        {
            this.chargeslipDataField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string ID
    {
        get
        {
            return this.idField;
        }
        set
        {
            this.idField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionCard
{

    private string isManualEntryField;

    private string cardNumberField;

    private string issuerNameField;

    private string schemeTypeField;

    private string cardHolderNameField;

    private hubresponseTransactionCardExpirationDate[] expirationDateField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string IsManualEntry
    {
        get
        {
            return this.isManualEntryField;
        }
        set
        {
            this.isManualEntryField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string CardNumber
    {
        get
        {
            return this.cardNumberField;
        }
        set
        {
            this.cardNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string IssuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string SchemeType
    {
        get
        {
            return this.schemeTypeField;
        }
        set
        {
            this.schemeTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string CardHolderName
    {
        get
        {
            return this.cardHolderNameField;
        }
        set
        {
            this.cardHolderNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ExpirationDate", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionCardExpirationDate[] ExpirationDate
    {
        get
        {
            return this.expirationDateField;
        }
        set
        {
            this.expirationDateField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionCardExpirationDate
{

    private string mmField;

    private string yyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string MM
    {
        get
        {
            return this.mmField;
        }
        set
        {
            this.mmField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string YY
    {
        get
        {
            return this.yyField;
        }
        set
        {
            this.yyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionEMI
{

    private string issuerNameField;

    private string acquirerNameField;

    private string tenureField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string IssuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string AcquirerName
    {
        get
        {
            return this.acquirerNameField;
        }
        set
        {
            this.acquirerNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Tenure
    {
        get
        {
            return this.tenureField;
        }
        set
        {
            this.tenureField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionState
{

    private string terminalIDField;

    private string invoiceNumberField;

    private string batchNumberField;

    private string acquirerNameField;

    private string unipayTerminalIdField;

    private string conncetivityField;

    private string primeIDField;

    private string dCCExchangeRateField;

    private string dCCMarginFeeField;

    private string dCCTxnCurrencyField;

    private string dCCCurrencyNameField;

    private string dCCConvertedAmountField;

    private string amountField;

    private string transactionTimeField;

    private string pREBField;

    private string pOSBField;

    private string transactionTypeField;

    private string statusCodeField;

    private string statusMessageField;

    private string trackingNumberField;

    private string feeField;

    private string taxField;

    private string txnAmountField;

    private string totalAmountField;

    private hubresponseTransactionStateSelectedAcquirer[] selectedAcquirerField;

    private hubresponseTransactionStateMerchant[] merchantField;

    private hubresponseTransactionStateHostResponse[] hostResponseField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string TerminalID
    {
        get
        {
            return this.terminalIDField;
        }
        set
        {
            this.terminalIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string InvoiceNumber
    {
        get
        {
            return this.invoiceNumberField;
        }
        set
        {
            this.invoiceNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string BatchNumber
    {
        get
        {
            return this.batchNumberField;
        }
        set
        {
            this.batchNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string AcquirerName
    {
        get
        {
            return this.acquirerNameField;
        }
        set
        {
            this.acquirerNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string unipayTerminalId
    {
        get
        {
            return this.unipayTerminalIdField;
        }
        set
        {
            this.unipayTerminalIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string conncetivity
    {
        get
        {
            return this.conncetivityField;
        }
        set
        {
            this.conncetivityField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string PrimeID
    {
        get
        {
            return this.primeIDField;
        }
        set
        {
            this.primeIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string DCCExchangeRate
    {
        get
        {
            return this.dCCExchangeRateField;
        }
        set
        {
            this.dCCExchangeRateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string DCCMarginFee
    {
        get
        {
            return this.dCCMarginFeeField;
        }
        set
        {
            this.dCCMarginFeeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string DCCTxnCurrency
    {
        get
        {
            return this.dCCTxnCurrencyField;
        }
        set
        {
            this.dCCTxnCurrencyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string DCCCurrencyName
    {
        get
        {
            return this.dCCCurrencyNameField;
        }
        set
        {
            this.dCCCurrencyNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string DCCConvertedAmount
    {
        get
        {
            return this.dCCConvertedAmountField;
        }
        set
        {
            this.dCCConvertedAmountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Amount
    {
        get
        {
            return this.amountField;
        }
        set
        {
            this.amountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string TransactionTime
    {
        get
        {
            return this.transactionTimeField;
        }
        set
        {
            this.transactionTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string PREB
    {
        get
        {
            return this.pREBField;
        }
        set
        {
            this.pREBField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string POSB
    {
        get
        {
            return this.pOSBField;
        }
        set
        {
            this.pOSBField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string TransactionType
    {
        get
        {
            return this.transactionTypeField;
        }
        set
        {
            this.transactionTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string StatusCode
    {
        get
        {
            return this.statusCodeField;
        }
        set
        {
            this.statusCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string StatusMessage
    {
        get
        {
            return this.statusMessageField;
        }
        set
        {
            this.statusMessageField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string TrackingNumber
    {
        get
        {
            return this.trackingNumberField;
        }
        set
        {
            this.trackingNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Fee
    {
        get
        {
            return this.feeField;
        }
        set
        {
            this.feeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Tax
    {
        get
        {
            return this.taxField;
        }
        set
        {
            this.taxField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string TxnAmount
    {
        get
        {
            return this.txnAmountField;
        }
        set
        {
            this.txnAmountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string TotalAmount
    {
        get
        {
            return this.totalAmountField;
        }
        set
        {
            this.totalAmountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("SelectedAcquirer", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionStateSelectedAcquirer[] SelectedAcquirer
    {
        get
        {
            return this.selectedAcquirerField;
        }
        set
        {
            this.selectedAcquirerField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Merchant", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionStateMerchant[] Merchant
    {
        get
        {
            return this.merchantField;
        }
        set
        {
            this.merchantField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("HostResponse", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionStateHostResponse[] HostResponse
    {
        get
        {
            return this.hostResponseField;
        }
        set
        {
            this.hostResponseField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionStateSelectedAcquirer
{

    private string idField;

    private string nameField;

    private string discountRateField;

    private string discounstRateField;

    private string statusField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string ID
    {
        get
        {
            return this.idField;
        }
        set
        {
            this.idField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string DiscountRate
    {
        get
        {
            return this.discountRateField;
        }
        set
        {
            this.discountRateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string DiscounstRate
    {
        get
        {
            return this.discounstRateField;
        }
        set
        {
            this.discounstRateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Status
    {
        get
        {
            return this.statusField;
        }
        set
        {
            this.statusField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionStateMerchant
{

    private string idField;

    private string nameField;

    private string addressField;

    private string cityField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string ID
    {
        get
        {
            return this.idField;
        }
        set
        {
            this.idField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string Address
    {
        get
        {
            return this.addressField;
        }
        set
        {
            this.addressField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string City
    {
        get
        {
            return this.cityField;
        }
        set
        {
            this.cityField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionStateHostResponse
{

    private string responseCodeField;

    private string responseMessageField;

    private string approvalCodeField;

    private string retrievalRefrenceNumberField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string ResponseCode
    {
        get
        {
            return this.responseCodeField;
        }
        set
        {
            this.responseCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string ResponseMessage
    {
        get
        {
            return this.responseMessageField;
        }
        set
        {
            this.responseMessageField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string ApprovalCode
    {
        get
        {
            return this.approvalCodeField;
        }
        set
        {
            this.approvalCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string RetrievalRefrenceNumber
    {
        get
        {
            return this.retrievalRefrenceNumberField;
        }
        set
        {
            this.retrievalRefrenceNumberField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionChargeslipData
{

    private hubresponseTransactionChargeslipDataReceipt[] receiptField;

    private string printerWidthField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Receipt", Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public hubresponseTransactionChargeslipDataReceipt[] Receipt
    {
        get
        {
            return this.receiptField;
        }
        set
        {
            this.receiptField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string printerWidth
    {
        get
        {
            return this.printerWidthField;
        }
        set
        {
            this.printerWidthField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionChargeslipDataReceipt
{

    private hubresponseTransactionChargeslipDataReceiptPrintline[] printlineField;

    private string isCustomerCopyField;

    private string lineCountField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Printline", Form = System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable = true)]
    public hubresponseTransactionChargeslipDataReceiptPrintline[] Printline
    {
        get
        {
            return this.printlineField;
        }
        set
        {
            this.printlineField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string isCustomerCopy
    {
        get
        {
            return this.isCustomerCopyField;
        }
        set
        {
            this.isCustomerCopyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string lineCount
    {
        get
        {
            return this.lineCountField;
        }
        set
        {
            this.lineCountField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class hubresponseTransactionChargeslipDataReceiptPrintline
{

    private string isBoldField;

    private string isCenteredField;

    private string lineNumberField;

    private string valueField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string isBold
    {
        get
        {
            return this.isBoldField;
        }
        set
        {
            this.isBoldField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string isCentered
    {
        get
        {
            return this.isCenteredField;
        }
        set
        {
            this.isCenteredField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string lineNumber
    {
        get
        {
            return this.lineNumberField;
        }
        set
        {
            this.lineNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    public string Value
    {
        get
        {
            return this.valueField;
        }
        set
        {
            this.valueField = value;
        }
    }
}
