﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class PaymentConfiguration
    {
        protected Dictionary<string, string> configurations = new Dictionary<string, string>();

        public void SetConfiguration(string key, string value)
        {
            if (configurations.ContainsKey(key))
            {
                configurations[key] = value;
            }
            else
            {
                configurations.Add(key, value);
            }
        }

        public string GetConfiguration(string key)
        {
            if (configurations.ContainsKey(key) == false)
            {
                throw new Exception("Key: " + key + " not found");
                
            }
            return configurations[key];
        }


    }
}
