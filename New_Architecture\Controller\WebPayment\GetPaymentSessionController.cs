﻿using Semnox.CommonAPI.Helpers;
using Semnox.Core.GenericUtilities;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.Site;
using Semnox.Parafait.ViewContainer;
using Semnox.Parafait.WebPayments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

namespace Semnox.CommonAPI.Controllers.WebPayment
{
    public class GetPaymentSessionController : ApiController
    {
        private Semnox.Parafait.logging.Logger log;

        [HttpGet]
        [Route("api/Transaction/WebPayment/Session")]
        [Authorize]
        public async Task<HttpResponseMessage> Get(string requestGuid)
        {
            ExecutionContext executionContext = null;
            try
            {
                executionContext = ExecutionContextBuilder.GetExecutionContext(Request);
                log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                log.LogMethodEntry(requestGuid);

                if (string.IsNullOrWhiteSpace(requestGuid))
                {
                    log.Error("requestGuid is not present. requestGuid:" + requestGuid);
                    throw new PaymentIdentifierNotFoundException(MessageViewContainerList.GetMessage(executionContext, 6088, requestGuid)); // Parameter requestGuid is null.
                }
                PaymentRequestDTO paymentRequestDTO = new PaymentRequestDTO(-1, requestGuid, -1, new List<PaymentResponseDTO>(), false, SiteContainerList.CurrentDateTime(executionContext), ServerDateTime.Now, string.Empty,-1);
                paymentRequestDTO.IpAddress = GetClientIP();

                IWebPaymentsUseCases webPaymentUseCases = WebPaymentsUseCaseFactory.GetWebPaymentsUseCases(executionContext, RequestIdentifierHelper.GetRequestIdentifier(Request));
                PaymentSessionDTO result = await webPaymentUseCases.GetPaymentSession(paymentRequestDTO);
                log.LogMethodExit(result);
                return Request.CreateResponse(HttpStatusCode.OK, new { data = result });
            }
            catch (ParafaitApplicationException ex)
            {
                string customException = GenericExceptionMessage.GetValidCustomExeptionMessage(ex, executionContext);
                log.Error(customException);
                return Request.CreateResponse(HttpStatusCode.BadRequest, new { data = customException, exception = ExceptionSerializer.Serialize(ex) });
            }
            catch (Exception ex)
            {
                string customException = GenericExceptionMessage.GetValidCustomExeptionMessage(ex, executionContext);
                log.Error(customException);
                return Request.CreateResponse(HttpStatusCode.InternalServerError, new { data = customException, exception = ExceptionSerializer.Serialize(ex) });
            }
        }

        private string GetClientIP()
        {
            log.LogMethodEntry();
            string clientIp = "";

            try
            {
                if (HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"] != null)
                {
                    clientIp = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"].Split(',')[0];
                    log.Info("Got client Ip address from HTTP_X_FORWARDED_FOR " + clientIp);
                }
                else if (HttpContext.Current.Request.Headers["X-Forwarded-For"] != null)
                {
                    clientIp = HttpContext.Current.Request.Headers["X-Forwarded-For"].Split(',')[0];
                    log.Info("Got client Ip address from X-Forwarded-For " + clientIp);
                }
                else if (!string.IsNullOrWhiteSpace(HttpContext.Current.Request.UserHostAddress))
                {
                    clientIp = HttpContext.Current.Request.UserHostAddress;
                    log.Info("Got client Ip address from UserHostAddress" + clientIp);
                }

                log.Debug("ClientIp address: " + clientIp);
            }
            catch (Exception ex)
            {
                log.Error("Error retrieving client ip address " + ex.Message);
            }

            log.LogMethodExit(clientIp);
            return clientIp;
        }
    }
}
