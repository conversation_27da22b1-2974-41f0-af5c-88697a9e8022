﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.SSLCommerz
{
    public class Gw
    {
        public string visa { get; set; }
        public string master { get; set; }
        public string amex { get; set; }
        public string othercards { get; set; }
        public string internetbanking { get; set; }
        public string mobilebanking { get; set; }
    }

    public class Desc
    {
        public string name { get; set; }
        public string type { get; set; }
        public string logo { get; set; }
        public string gw { get; set; }
        public string r_flag { get; set; }
        public string redirectGatewayURL { get; set; }
    }

    public class SSLCommerzResponseDTO
    {
        public string status { get; set; }
        public string failedreason { get; set; }
        public string sessionkey { get; set; }
        public Gw gw { get; set; }
        public string redirectGatewayURL { get; set; }
        public string directPaymentURLBank { get; set; }
        public string directPaymentURLCard { get; set; }
        public string directPaymentURL { get; set; }
        public string redirectGatewayURLFailed { get; set; }
        public string GatewayPageURL { get; set; }
        public string storeBanner { get; set; }
        public string storeLogo { get; set; }
        public string store_name { get; set; }
        public List<Desc> desc { get; set; }
        public string is_direct_pay_enable { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class SSLCommerzCallbackResponse
    {
        public string tran_id { get; set; }
        public string val_id { get; set; }
        public string amount { get; set; }
        public string card_type { get; set; }
        public string store_amount { get; set; }
        public string card_no { get; set; }
        public string bank_tran_id { get; set; }
        public string status { get; set; }
        public string tran_date { get; set; }
        public string error { get; set; }
        public string currency { get; set; }
        public string card_issuer { get; set; }
        public string card_brand { get; set; }
        public string card_sub_brand { get; set; }
        public string card_issuer_country { get; set; }
        public string card_issuer_country_code { get; set; }
        public string store_id { get; set; }
        public string verify_sign { get; set; }
        public string verify_key { get; set; }
        public string verify_sign_sha2 { get; set; }
        public string currency_type { get; set; }
        public string currency_amount { get; set; }
        public string currency_rate { get; set; }
        public string base_fair { get; set; }
        public string value_a { get; set; }
        public string value_b { get; set; }
        public string value_c { get; set; }
        public string value_d { get; set; }
        public string subscription_id { get; set; }
        public string risk_level { get; set; }
        public string risk_title { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class SSLCommerzTrxStatusElementDTO
    {
        public string val_id { get; set; }
        public string status { get; set; }
        public string validated_on { get; set; }
        public string currency_type { get; set; }
        public string currency_amount { get; set; }
        public string currency_rate { get; set; }
        public string base_fair { get; set; }
        public string value_a { get; set; }
        public string value_b { get; set; }
        public string value_c { get; set; }
        public string value_d { get; set; }
        public string discount_percentage { get; set; }
        public string discount_remarks { get; set; }
        public double discount_amount { get; set; }
        public string tran_date { get; set; }
        public string tran_id { get; set; }
        public string amount { get; set; }
        public string store_amount { get; set; }
        public string bank_tran_id { get; set; }
        public string card_type { get; set; }
        public string risk_title { get; set; }
        public string risk_level { get; set; }
        public string currency { get; set; }
        public string bank_gw { get; set; }
        public string card_no { get; set; }
        public string card_issuer { get; set; }
        public string card_brand { get; set; }
        public string card_issuer_country { get; set; }
        public string card_issuer_country_code { get; set; }
        public string gw_version { get; set; }
        public string emi_instalment { get; set; }
        public string emi_amount { get; set; }
        public string emi_description { get; set; }
        public string emi_issuer { get; set; }
        public string error { get; set; }
    }

    public class SSLCommerzTrxStatusResponseDTO
    {
        public string APIConnect { get; set; }
        public int no_of_trans_found { get; set; }
        public List<SSLCommerzTrxStatusElementDTO> element { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class SSLCommerzRefundResponseDTO
    {
        public string APIConnect { get; set; }
        public string bank_tran_id { get; set; }
        public string trans_id { get; set; }
        public string refund_ref_id { get; set; }
        public string status { get; set; }
        public string errorReason { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
