﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Midtrans
{
   public class MidtransHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string _serverKey, _gatewayPostUrl;

        public MidtransHostedCommandHandler(string serverkey,string gatewayPostUrl)
        {
            log.LogMethodEntry();
            //this._endPoint = endpoint;
            this._serverKey = serverkey;
            this._gatewayPostUrl = gatewayPostUrl;
            log.LogMethodExit();
        }


        public string MakeRequest(MidtransGetTokenRequestDTO.GetTokenRequest getTokenRequest,string endPoint)
        {
            // Serialize the request object to JSON
            string postData = JsonConvert.SerializeObject(getTokenRequest, Formatting.Indented);
            // Log the request data
            log.Debug("Request data for get token:" + postData);

            // Create the WebRequestClient
            WebRequestClient webRequestClient = new WebRequestClient(endPoint, HttpVerb.POST, postData);
            webRequestClient.Username = _serverKey;
            webRequestClient.IsBasicAuthentication = true;

            // Send the request and get the response
            string response = webRequestClient.MakeRequest();

            // Return the response
            return response;
        }

        public MidTransVoidResponseDTO MakeVoid(string endPoint)
        {
            WebRequestClient webRequestClient = new WebRequestClient(endPoint, HttpVerb.POST);
            webRequestClient.Username = _serverKey;
            webRequestClient.IsBasicAuthentication = true;

            string response = webRequestClient.MakeRequest();
            MidTransVoidResponseDTO midTransVoidResponseDTO = Newtonsoft.Json.JsonConvert.DeserializeObject<MidTransVoidResponseDTO>(response);

            return midTransVoidResponseDTO;
        }

        public MidTransRefundResponseDTO MakeRefund(MidtransRefundRequestDTO.MidtransRefundRequest midtransRefundRequest, string endPoint)
        {
            string postData = JsonConvert.SerializeObject(midtransRefundRequest, Formatting.Indented);
            log.Debug("Refund request data: " + postData);

            WebRequestClient webRequestClient = new WebRequestClient(endPoint, HttpVerb.POST, postData);
            webRequestClient.Username = _serverKey;
            webRequestClient.IsBasicAuthentication = true;
            string response = webRequestClient.MakeRequest();
            log.Debug("Refund Response: " + response);
            MidTransRefundResponseDTO midTransRefundResponseDTO = JsonConvert.DeserializeObject<MidTransRefundResponseDTO>(response);

            return midTransRefundResponseDTO;
        }

        public MidTransTxSearchResponseDTO GetOrderStatus(string trxId)
        {
            log.LogMethodEntry(trxId);

            MidTransTxSearchResponseDTO midTransTxSearchResponseDTO = null;
            try
            {
                if (string.IsNullOrEmpty(trxId))
                {
                    log.Error("No TransactionId passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                string endPoint = _gatewayPostUrl + "/v2/" + trxId + "/status";
                WebRequestClient webRequestClient = new WebRequestClient(endPoint, HttpVerb.GET);
                webRequestClient.Username = _serverKey;
                webRequestClient.IsBasicAuthentication = true;
                string response = webRequestClient.GetResponse();
                log.Debug("Response for Get transaction status: " + response);
                midTransTxSearchResponseDTO = JsonConvert.DeserializeObject<MidTransTxSearchResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(midTransTxSearchResponseDTO);
            return midTransTxSearchResponseDTO;
        }
    }
}
