﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PeachWalletPayments
{
    class PeachWalletHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string ENTITY_ID;
        private string SECRET_TOKEN;
        private string CLIENT_ID;
        private string CLIENT_SECRET;
        private string MERCHANT_ID;
        private string REFERER_URL;
        private string POST_URL;
        private string BASE_URL;
        private string CURRENCY;
        private string DEBIT_PAYMENT_TYPE;
        private string REFUND_PAYMENT_TYPE;


        PeachWalletHostedCommandHandler peachWalletCommandHandler;


        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
{
    { "000.000.000", PaymentStatusType.SUCCESS }, // Transaction succeeded
    { "000.000.100", PaymentStatusType.SUCCESS }, // successful request
    { "000.100.105", PaymentStatusType.SUCCESS }, // Chargeback Representment is successful
    { "000.100.106", PaymentStatusType.SUCCESS }, // Chargeback Representment cancellation is successful
    { "000.100.110", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Integrator Test Mode'
    { "000.100.111", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Validator Test Mode'
    { "000.100.112", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Connector Test Mode'
    { "000.300.000", PaymentStatusType.SUCCESS }, // Two-step transaction succeeded
    { "000.300.100", PaymentStatusType.SUCCESS }, // Risk check successful
    { "000.300.101", PaymentStatusType.SUCCESS }, // Risk bank account check successful
    { "000.300.102", PaymentStatusType.SUCCESS }, // Risk report successful
    { "000.300.103", PaymentStatusType.SUCCESS }, // Exemption check successful
    { "000.310.100", PaymentStatusType.SUCCESS }, // Account updated
    { "000.310.101", PaymentStatusType.SUCCESS }, // Account updated (Credit card expired)
    { "000.310.110", PaymentStatusType.SUCCESS }, // No updates found, but account is valid
    { "000.400.110", PaymentStatusType.SUCCESS }, // Authentication successful (frictionless flow)
    { "000.400.120", PaymentStatusType.SUCCESS }, // Authentication successful (data only flow)
    { "000.600.000", PaymentStatusType.SUCCESS }, // transaction succeeded due to external update

    { "000.400.000", PaymentStatusType.PENDING }, // Transaction succeeded (review for fraud suspicion)
    { "000.400.010", PaymentStatusType.PENDING }, // Transaction succeeded (review for AVS return code)
    { "000.400.020", PaymentStatusType.PENDING }, // Transaction succeeded (review for CVV return code)
    { "000.400.040", PaymentStatusType.PENDING }, // Transaction succeeded (review for amount mismatch)
    { "000.400.050", PaymentStatusType.PENDING }, // Transaction succeeded (pending)
    { "000.400.060", PaymentStatusType.PENDING }, // Transaction succeeded (merchant’s risk)
    { "000.400.070", PaymentStatusType.PENDING }, // Transaction succeeded (external risk review)
    { "000.400.080", PaymentStatusType.PENDING }, // Transaction succeeded (service unavailable)
    { "000.400.081", PaymentStatusType.PENDING }, // Transaction succeeded (network timeout)
    { "000.400.082", PaymentStatusType.PENDING }, // Transaction succeeded (processing timeout)
    { "000.400.090", PaymentStatusType.PENDING }, // Transaction succeeded (external risk check)
    { "000.400.100", PaymentStatusType.PENDING }, // Transaction succeeded, risk after payment rejected

    { "000.200.000", PaymentStatusType.PENDING }, // Transaction pending
    { "000.200.001", PaymentStatusType.PENDING }, // Transaction pending for acquirer
    { "000.200.100", PaymentStatusType.PENDING }, // successfully created checkout
    { "000.200.101", PaymentStatusType.PENDING }, // successfully updated checkout
    { "000.200.102", PaymentStatusType.PENDING }, // successfully deleted checkout
    { "000.200.103", PaymentStatusType.PENDING }, // checkout is pending
    { "000.200.200", PaymentStatusType.PENDING }, // Transaction initialized
    { "000.200.201", PaymentStatusType.PENDING }, // QR Scanned/Link Clicked
    { "100.400.500", PaymentStatusType.PENDING }, // waiting for external risk
    { "800.400.500", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant payment
    { "800.400.501", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant debit
    { "800.400.502", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant refund

    { "000.400.101", PaymentStatusType.FAILED }, // card not participating/authentication unavailable
    { "000.400.102", PaymentStatusType.FAILED }, // user not enrolled
    { "000.400.103", PaymentStatusType.FAILED }, // Technical Error in 3D system
    { "000.400.104", PaymentStatusType.FAILED }, // Missing or malformed 3DSecure Configuration for Channel
    { "000.400.105", PaymentStatusType.FAILED }, // Unsupported User Device
    { "000.400.106", PaymentStatusType.FAILED }, // invalid payer authentication response
    { "000.400.107", PaymentStatusType.FAILED }, // Communication Error to Scheme Directory Server
    { "000.400.108", PaymentStatusType.FAILED }, // Cardholder Not Found
    { "000.400.109", PaymentStatusType.FAILED }, // Card not enrolled for 3DS version 2
    { "000.400.111", PaymentStatusType.FAILED }, // Data Only request failed
    { "000.400.112", PaymentStatusType.FAILED }, // 3RI transaction not permitted
    { "000.400.200", PaymentStatusType.FAILED }, // risk management check communication error
};

        public PeachWalletHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12 | SecurityProtocolType.Ssl3;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            Initialize();
            log.LogMethodExit(null);
        }
        public override void Initialize()
        {
            log.LogMethodEntry();

            MERCHANT_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            ENTITY_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            SECRET_TOKEN = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            //CLIENT_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            //CLIENT_SECRET = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            POST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            REFERER_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            CURRENCY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");

            DEBIT_PAYMENT_TYPE = "DB";
            REFUND_PAYMENT_TYPE = "RF";

            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }
            if (POST_URL.EndsWith("/"))
            {
                POST_URL = POST_URL.Remove(POST_URL.Length - 1);
            }
            if (REFERER_URL.EndsWith("/"))
            {
                REFERER_URL = REFERER_URL.Remove(REFERER_URL.Length - 1);
            }
            peachWalletCommandHandler = new PeachWalletHostedCommandHandler(ENTITY_ID, SECRET_TOKEN, BASE_URL, POST_URL, REFERER_URL);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;



            if (string.IsNullOrWhiteSpace(SECRET_TOKEN))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(MERCHANT_ID))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(REFERER_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            }
            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));

            List<LookupValuesDTO> lookupValuesDTOlist = lookupValuesList.GetAllLookupValues(searchParameters);

            hostedGatewayDTO.SuccessURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "SUCCESS_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletHostedPayment.ToString());

            //FAILED_URL
            hostedGatewayDTO.FailureURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "FAILED_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletHostedPayment.ToString());

            //CALLBACK_URL
            hostedGatewayDTO.CallBackURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CALLBACK_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletHostedPayment.ToString());

            //CANCEL_URL
            hostedGatewayDTO.CancelURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CANCEL_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletHostedPayment.ToString());

            if (string.IsNullOrWhiteSpace(hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.FailureURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.CallBackURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/CALLBACK_URL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/."));
            }


            log.LogMethodExit();
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            try
            {
                if (transactionPaymentsDTO.Amount <= 0)
                {
                    log.Error($"Order amount must be greater than zero. Order Amount was {transactionPaymentsDTO.Amount}");
                    throw new Exception("Order amount must be greater than zero");
                }
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;

                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());
                PeachPaymentsHostedResponseDTO checkoutResponse;
                //hostedGatewayDTO.SuccessURL = "https://largegoldboard63.conveyor.cloud/account/PeachWalletHostedPayment/checkoutSuccess";
                //hostedGatewayDTO.CallBackURL = "https://largegoldboard63.conveyor.cloud/account/PeachWalletHostedPayment/callbackSuccess";
                //hostedGatewayDTO.CancelURL = "https://longgreenrock93.conveyor.cloud/account/callbackSuccess";
                double amount = transactionPaymentsDTO.Amount;
                string formattedAmount = amount.ToString("F2", CultureInfo.InvariantCulture);

                PeachPaymentsHostedRequestDTO peachPaymentsRequestDto = new PeachPaymentsHostedRequestDTO
                {

                    AuthenticationEntityId = ENTITY_ID,
                    MerchantTransactionId = "OrderNo" + transactionPaymentsDTO.TransactionId.ToString(),
                    Amount = formattedAmount,
                    Nonce = transactionPaymentsDTO.TransactionId.ToString(),
                    ShopperResultUrl = hostedGatewayDTO.SuccessURL,
                    Currency = CURRENCY,
                    PaymentType = DEBIT_PAYMENT_TYPE,
                    NotificationUrl = hostedGatewayDTO.CallBackURL,
                    CancelUrl = hostedGatewayDTO.CancelURL
                };

                checkoutResponse = peachWalletCommandHandler.CreateCheckout(peachPaymentsRequestDto);

                if (checkoutResponse == null)
                {
                    log.Error("CreateGatewayPaymentRequest(): Checkout Transaction Response was empty");
                    throw new Exception("Error: could not create payment session");
                }

                if (string.IsNullOrWhiteSpace(checkoutResponse.RedirectUrl))
                {
                    log.Error("redirectUrl was null");
                    throw new Exception("Error creating the payment request");
                }
                string checkoutUrl = checkoutResponse.RedirectUrl;
                log.Debug($"CreateGatewayPaymentRequest(): Payment ResponseDto: {checkoutResponse}");
                log.Debug($"CreateGatewayPaymentRequest(): Payment request is created, redirecting to Checkout URL: {checkoutUrl}");

                hostedGatewayDTO.RequestURL = checkoutUrl;

                hostedGatewayDTO.GatewayRequestString = GetSubmitFormKeyValueList(checkoutUrl, "fromPeachForm", "GET");
                log.Info("request url:" + hostedGatewayDTO.RequestURL);

                log.Debug("Gateway Request string: " + hostedGatewayDTO.GatewayRequestString);
                hostedGatewayDTO.FailureURL = "/account/checkouterror";
                hostedGatewayDTO.SuccessURL = "/account/receipt";
                hostedGatewayDTO.CancelURL = "/account/checkoutstatus";
                LookupsList lookupList = new LookupsList(utilities.ExecutionContext);
                List<KeyValuePair<LookupsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<LookupsDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
                List<LookupsDTO> lookups = lookupList.GetAllLookups(searchParameters, true);
                if (lookups != null && lookups.Any())
                {
                    List<LookupValuesDTO> lookupValuesDTOList = lookups[0].LookupValuesDTOList;
                    LookupValuesDTO temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_FAILURE_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.FailureURL = temp.Description;
                    }

                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_SUCCESS_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.SuccessURL = temp.Description;
                    }

                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_CANCEL_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.CancelURL = temp.Description;
                    }
                    // pending url
                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_PENDING_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.PendingURL = temp.Description;
                    }
                }

            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }


        private string GetSubmitFormKeyValueList(string redirectUrl, string formName, string submitMethod = "POST")
        {
            log.LogMethodEntry(redirectUrl, formName);
            // Parse the URL to get query parameters
            Uri uri = new Uri(redirectUrl);
            System.Collections.Specialized.NameValueCollection queryParams = System.Web.HttpUtility.ParseQueryString(uri.Query);

            // Extract "plugin" and "checkoutId" from the URL
            string plugin = queryParams["plugin"];
            string checkoutId = queryParams["checkoutId"];

            // Build the form
            string method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();

            builder.Append("<html>");
            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", formName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", formName, method, uri.GetLeftPart(UriPartial.Path))); // Use the base URL without query params

            // Add plugin and checkoutId as hidden form fields
            if (!string.IsNullOrEmpty(plugin))
            {
                builder.Append(string.Format("<input name=\"plugin\" type=\"hidden\" value=\"{0}\" />", plugin));
            }
            if (!string.IsNullOrEmpty(checkoutId))
            {
                builder.Append(string.Format("<input name=\"checkoutId\" type=\"hidden\" value=\"{0}\" />", checkoutId));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");
            log.LogMethodExit();
            return builder.ToString();
        }


        /// <summary>
        /// Processes the response received from the payment gateway and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the updated payment details and status.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment or updating the payment status.</exception>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            PeachPaymentsTrxSeachResponseDTO peachCardResponse = null;
            bool isStatusUpdated;
            try
            {
                peachCardResponse = JsonConvert.DeserializeObject<PeachPaymentsTrxSeachResponseDTO>(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + peachCardResponse.ToString());


                if (peachCardResponse.MerchantTransactionId != null)
                {
                    log.Debug("Transaction id: " + peachCardResponse.MerchantTransactionId);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(peachCardResponse.MerchantTransactionId.Replace("OrderNo", ""));
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                PeachPaymentsTrxSeachResponseDTO trxSearchResponse = peachWalletCommandHandler.VerifyPayment(peachCardResponse.MerchantTransactionId);

                if (trxSearchResponse.ResultCode == "")
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }

                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(trxSearchResponse.Amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = CURRENCY;

                hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.MerchantTransactionId;

                hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PROCESSING;
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);

                if (!isStatusUpdated)
                {
                    log.Error("ProcessGatewayResponse():Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }

                //check if ccTransactionPGW updated
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;
                if (!String.IsNullOrEmpty(cCRequestsPGWDTO.ReferenceNo))
                {
                    string[] resvalues = cCRequestsPGWDTO.ReferenceNo.ToString().Split('|');
                    foreach (string word in resvalues)
                    {
                        if (word.Contains("PaymentModeId") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(word.Split(':')[1]);
                        }
                        else if (word.Contains("CurrencyCode") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = word.Split(':')[1];
                        }
                    }
                }

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Error("No reference id/Transaction present in PaymentAsia receipt response");
                    cCTransactionsPGWDTOList = null;
                }


                if (cCTransactionsPGWDTOList == null)
                {
                    PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.ResultCode, PaymentGatewayTransactionType.SALE);
                    log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());



                    if (salePaymentStatus == PaymentStatusType.SUCCESS)
                    {
                        log.Debug("Payment status is success");
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;

                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                        hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.Id.ToString();
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = trxSearchResponse.PaymentType; //auth_codec
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = trxSearchResponse.CardLast4Digits;
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardExpiry = trxSearchResponse.CardExpiryMonth + "/" + trxSearchResponse.CardExpiryYear;
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = trxSearchResponse.PaymentBrand;
                        hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = trxSearchResponse.CardHolder;
                    }
                    else if (salePaymentStatus == PaymentStatusType.PENDING)
                    {
                        log.Debug("Payment status is pending");
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.PENDING;

                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                    }
                    else if (salePaymentStatus == PaymentStatusType.FAILED)
                    {
                        log.Debug("Payment status is failed");
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;

                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    }
                    else
                    {
                        log.Error("Payment status is unknown. Considering status as failed Status: " + salePaymentStatus.ToString());
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    }

                    if (cCTransactionsPGWDTOList == null)
                    {  // update the CCTransactionsPGWDTO
                        log.Debug("No CC Transactions found");
                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                        cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                        cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                        cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                        cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                        cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        cCTransactionsPGWDTO.TextResponse = trxSearchResponse.ResultCode;
                        cCTransactionsPGWDTO.DSIXReturnCode = trxSearchResponse.ResultDescription;
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                        cCTransactionsPGWDTO.CardType = !string.IsNullOrEmpty(trxSearchResponse.PaymentBrand) ? trxSearchResponse.PaymentBrand : null;
                        cCTransactionsPGWDTO.AcctNo = !string.IsNullOrEmpty(trxSearchResponse.CardLast4Digits) ? "XXXXXXXXXXXX" + trxSearchResponse.CardLast4Digits : null;
                        cCTransactionsPGWDTO.AuthCode = trxSearchResponse.PaymentType;
                        cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                        cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse.Timestamp);

                        hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                    }
                    else
                    {
                        //if YES
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.ERROR;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                        isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                    }

                    isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                }

                if (!isStatusUpdated)
                {
                    log.Error("Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        private DateTime GetPaymentDate(string peachTime)
        {
            log.LogMethodEntry(peachTime);
            DateTime paymentDate = new DateTime();

            if (!string.IsNullOrEmpty(peachTime))
            {
                if (DateTime.TryParseExact(peachTime, "yyyyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }

        /// <summary>
        /// Initiates a refund process for a transaction based on the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details for initiating the refund.</param>
        /// <returns>
        /// Returns the updated TransactionPaymentsDTO after processing the refund.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefund = false;
            refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    //to get original TrxId(in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }

                log.Debug("Refund processing started");
                PeachPaymentsRefundResponseDTO refundResponseDTO = null;
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                PeachPaymentsRefundRequestDTO requestDto = new PeachPaymentsRefundRequestDTO
                {
                    AuthenticationEntityId = ENTITY_ID,
                    Id = transactionPaymentsDTO.Reference,
                    Amount = transactionPaymentsDTO.Amount.ToString(),
                    Currency = CURRENCY,
                    PaymentType = REFUND_PAYMENT_TYPE,
                };

                log.Debug("Peach Refund Request has been created, RequestDTO: " + requestDto.ToString());

                refundResponseDTO = peachWalletCommandHandler.CreateRefund(requestDto);
                log.Debug("Peach Refund Response refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }
                if (string.IsNullOrWhiteSpace(refundResponseDTO.Result.Code))
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }
                PaymentStatusType refundStatus = MapPaymentStatus(refundResponseDTO.Result.Code, PaymentGatewayTransactionType.REFUND);
                log.Debug("Value of refundPaymentStatus: " + refundStatus.ToString());

                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                ccTransactionsPGWDTO.DSIXReturnCode = refundResponseDTO.Result.Description;
                ccTransactionsPGWDTO.RefNo = refundResponseDTO.Id.ToString(); //paystack paymentId

                if (refundStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Refund Success for trxId: " + refundTrxId);
                    isRefund = true;
                    ccTransactionsPGWDTO.TextResponse = refundResponseDTO.Result.Code;
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.Result.Code;
                    ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", refundResponseDTO.Amount.ToString());
                    ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", refundResponseDTO.Amount.ToString());
                    ccTransactionsPGWDTO.PaymentStatus = refundStatus.ToString();
                }
                else
                {
                    //refund failed
                    isRefund = false;
                    string errorMessage = refundResponseDTO.Result.Description;
                    log.Error($"Refund Failed. Error Message received: {errorMessage}");
                    ccTransactionsPGWDTO.TextResponse = "FAILED";
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.Result.Code;
                    ccTransactionsPGWDTO.PaymentStatus = PaymentStatusType.FAILED.ToString();

                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }


            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;
            string referenceId;
            try
            {
                if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.INVOICE_NUMBER, cCRequestsPGWDTO.RequestID.ToString()));

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);
                hostedGatewayDTO.CCTransactionsPGWDTO = null;
                CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;

                //get transaction type of sale CCRequest record
                ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                //to get original TrxId(in case of POS refund)
                referenceId = ccOrigTransactionsPGWDTO.RefNo;
                log.Debug("Original TrxId for gettrxsearch: " + referenceId);
                PeachPaymentsTrxSeachResponseDTO trxSearchResponse = peachWalletCommandHandler.VerifyPayment(referenceId);
                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (string.IsNullOrWhiteSpace(trxSearchResponse.ResultCode))
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }


                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                cCTransactionsPGWDTO.TextResponse = trxSearchResponse.ResultCode;
                cCTransactionsPGWDTO.DSIXReturnCode = trxSearchResponse.ResultDescription;
                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                cCTransactionsPGWDTO.CardType = trxSearchResponse.PaymentBrand;
                cCTransactionsPGWDTO.AcctNo = !string.IsNullOrEmpty(trxSearchResponse.CardLast4Digits) ? "XXXXXXXXXXXX" + trxSearchResponse.CardLast4Digits : null;
                cCTransactionsPGWDTO.AuthCode = trxSearchResponse.PaymentType;
                cCTransactionsPGWDTO.AcqRefData = trxSearchResponse.Id.ToString();
                cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse.Timestamp);


                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                if (SaleStatusMappingDict.ContainsKey(trxSearchResponse.ResultCode) &&
                    SaleStatusMappingDict[trxSearchResponse.ResultCode] == PaymentStatusType.SUCCESS)
                {
                    dict.Add("status", "1");
                    dict.Add("message", "success");
                    dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                    dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                    dict.Add("orderId", trxId);
                    dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                }
                else if (SaleStatusMappingDict.ContainsKey(trxSearchResponse.ResultCode) &&
                    SaleStatusMappingDict[trxSearchResponse.ResultCode] == PaymentStatusType.PENDING)
                {
                    log.Error("GetTransactionStatus(): Error updating the payment status");

                    // Cancel the transaction in Parafait DB
                    dict.Add("status", "0");
                    dict.Add("message", trxSearchResponse.ResultCode.ToString());
                    dict.Add("orderId", trxId);
                }
                else
                {
                    log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");

                    // Cancel the transaction in Parafait DB
                    dict.Add("status", "0");
                    dict.Add("message", trxSearchResponse.ResultCode.ToString());
                    dict.Add("orderId", trxId);
                }


                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                    case PaymentGatewayTransactionType.REFUND:

                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PeachPaymentsTrxSeachResponseDTO orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId.ToString());

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = peachWalletCommandHandler.VerifyPayment("OrderNo" + trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (orderStatusResult.ResultCode == "")
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }

                log.Debug($"TxSearch Response for TrxId: {orderStatusResult.MerchantTransactionId}: " + orderStatusResult);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(orderStatusResult.ResultCode, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());


                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO != null ? cCRequestsPGWDTO.RequestID.ToString() : trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (orderStatusResult.Amount.ToString()));
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (orderStatusResult.Amount.ToString()));
                cCTransactionsPGWDTO.RefNo = orderStatusResult.Id;
                cCTransactionsPGWDTO.RecordNo = orderStatusResult.MerchantTransactionId;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.ResultCode;
                cCTransactionsPGWDTO.DSIXReturnCode = orderStatusResult.ResultDescription + " | Payment: " + salePaymentStatus.ToString();
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.CardType = !string.IsNullOrEmpty(orderStatusResult.PaymentBrand) ? orderStatusResult.PaymentBrand : null;
                cCTransactionsPGWDTO.AcctNo = !string.IsNullOrEmpty(orderStatusResult.CardLast4Digits) ? "XXXXXXXXXXXX" + orderStatusResult.CardLast4Digits : null;
                cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();
                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();

            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }
    }

}
