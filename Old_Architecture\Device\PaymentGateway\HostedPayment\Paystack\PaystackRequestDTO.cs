﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;


namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Paystack
{
    public class PayStackTransactionRequestDTO
    {
        public double amount { get; set; }

        public string email { get; set; }

        public string reference { get; set; }

        public string currency { get; set; }

        public string callback_url { get; set; }

        public string plan { get; set; }

        public int invoice_limit { get; set; }

        public string metadata { get; set; }

        public string[] channels { get; set; }

        public string split_code { get; set; }

        public string subaccount { get; set; }

        public int transaction_charge { get; set; }

        public string bearer { get; set; }




        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class PayStackVerifyTransactionsRequestDTO
    {
        public string reference { get; set; }

    }
    public class PayStackQueryTransactionsRequestDTO
    {
        public int id { get; set; }


    }

    public class PayStackRefundRequestDTO
    {
        public string transaction { get; set; }
        public double amount { get; set; }
        public string currency { get; set; }
        public string customer_note { get; set; }
        public string merchant_note { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }
 

}
