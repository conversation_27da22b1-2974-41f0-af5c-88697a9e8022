﻿/********************************************************************************************
 * Project Name - Hosted Payment
 * Description  - DTO Classes created as part of payment request
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    public class HostedPaymentRequestDTO
    {
        public int TransactionId { get; set; }
        public int TransactionPaymentId { get; set; }
        public string TransactionPaymentGuid { get; set; }
        public string TransactionGuid { get; set; }
        public int SiteId { get; set; }
        public int PaymentModeId { get; set; }
        public string PaymentMode { get; set; }
        public string POSMachine { get; set; }
        public string CurrencyCode { get; set; }
        public decimal Amount { get; set; }
        public string SubscriptionAuthorizationMode { get; set; }
        public List<PaymentGatewayProduct> Products { get; set; }
        public PaymentGatewayCustomer PaymentGatewayCustomer { get; set; }
        public string PGTransactionIdentifier { get; set; }
        public string SuccessURL { get; set; }
        public string FailedURL { get; set; }
        public string CancelURL { get; set; }
        public string CallbackURL { get; set; }
        public string PaymentSucceededURL { get; set; }
        public string PaymentFailedURL { get; set; }
        public string PaymentCancelledURL { get; set; }
        public string WebsitePaymentPageURL { get; set; }
        public string WebsitePaymentPageLink { get; set; }
        public string GatewayAPIURL { get; set; }
        public string GatewayRequestHTML { get; set; }
        public string MerchantId { get; set; }
        public bool IsCaptchEnabled { get; set; }
        public string CaptchaIdentifier { get; set; }
        public string CaptchaURL { get; set; }
        public bool IsPinCodeValidationRequired { get; set; }
    }
}
