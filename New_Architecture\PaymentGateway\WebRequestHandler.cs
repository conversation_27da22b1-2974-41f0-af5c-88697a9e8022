
/******************************************************************************************************************************
 * Project Name - PaymentGatewayInterface
 * Description  - WebRequestHandler
 * 
 **************
 **Version Log
 **************
 *Version     Date          Modified By             Remarks          
 *******************************************************************************************************************************
 *2.190.0     30-Jan-2025   Amrutha                   Added : As part of Payment Gateway Redesign
 ******************************************************************************************************************************/
 using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    public class WebRequestHandler
    {
        private static readonly  Semnox.Parafait.logging.Logger  log = new  Semnox.Parafait.logging.Logger (System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private static readonly HttpClient client;

        static WebRequestHandler()
        {
            client = new HttpClient()
            {
                Timeout = TimeSpan.FromMilliseconds(60000 * 5)
            };
        }
        private int timeOut;
        public int ReadWriteTimeout { get { return timeOut; } set { timeOut = value; log.LogVariableState("ReadWriteTimeout", timeOut); } }
        public WebRequestHandler()
        {
            log.LogMethodEntry();
            timeOut = 100;
            log.LogMethodExit();
        }

        /// <summary>
        /// Creates the web request object by using the passed parameters
        /// </summary>
        /// <param name="url">Is the Endpoint url</param>
        /// <param name="data">Data which need to be transafered with request</param>
        /// <param name="contentType"> content type like application/json or text/xml etc</param>
        /// <param name="method"> method like POST,PUT, GET etc</param>
        /// <param name="authorization">this value will be added to the header key value 'Authorization'</param>
        /// <returns> returns the request which is created using passed parameter</returns>
        public HttpRequestMessage CreateRequest(string url, HttpMethod method, string authorization)
        {
            log.LogMethodEntry(url, authorization);
            log.LogVariableState("url", url);
            //log.LogVariableState("contentType", contentType);
            log.LogVariableState("method", method);
            log.LogVariableState("authorization",authorization);
            HttpRequestMessage request = new HttpRequestMessage(method, url);
            //request.ContentType = contentType;                      
            //request.ReadWriteTimeout = ReadWriteTimeout;
             
            //request.Headers.Authorization = new AuthenticationHeaderValue(AuthenticationSchemes.None.ToString(), authorization);
            request.Headers.TryAddWithoutValidation("Authorization", authorization);//["Authorization"] = authorization;
            log.LogMethodExit(request);
            return request;
        }
        /// <summary>
        /// Creates the web request object by using the passed parameters. 
        /// The username and password will be converted in to base64 format and prefixed with the word 'Basic' and added to the header key value 'Authorization'
        /// </summary>
        /// <param name="url">Is the Endpoint url</param>
        /// <param name="contentType">content type like application/json or text/xml etc</param>
        /// <param name="method"> method like POST,PUT, GET etc</param>
        /// <param name="userName">user name</param>
        /// <param name="password"> password</param>
        /// <returns></returns>
        public HttpRequestMessage CreateRequest(string url, HttpMethod method, string userName, string password)
        {
            log.LogMethodEntry();
            log.LogVariableState("url", url);
            log.LogVariableState("method", method);
            log.LogVariableState("userName", userName);
            log.LogVariableState("password", password);
            var plainTextBytes = System.Text.Encoding.ASCII.GetBytes(userName+":"+ password);
            string credentials = Convert.ToBase64String(plainTextBytes);
            log.LogMethodExit();
            return CreateRequest(url, method, new AuthenticationHeaderValue("Basic", credentials).ToString());            
        }

        /// <summary>
        /// Creates the web request object by using the passed parameters. 
        /// The username and password will be converted in to base64 format and prefixed with the word 'Basic' and added to the header key value 'Authorization'
        /// </summary>
        /// <param name="url">Is the Endpoint url</param>
        /// <param name="contentType">content type like application/json or text/xml etc</param>
        /// <param name="method"> method like POST,PUT, GET etc</param>
        /// <param name="credential">user will send the credential and that will be set to the request.Credentials value</param>
        /// <returns></returns>
        public HttpRequestMessage CreateRequest(string url, HttpMethod method, NetworkCredential credential)
        {
            log.LogMethodEntry(url, method, "credential");
            HttpRequestMessage request = new HttpRequestMessage(method, url);
            request.Method = method;
            //request.ContentType = contentType;
            //request.ReadWriteTimeout = ReadWriteTimeout;
            //request.Credentials = credential;
            log.LogMethodExit(request);
            return request;
        }
        /// <summary>
        /// Will add the passed key and value to the header
        /// </summary>
        /// <param name="request">HttpWebRequest type object</param>
        /// <param name="key">Key name the of value should be added</param>
        /// <param name="value">Value to add to the header </param>
        public void AddToHeader(HttpRequestMessage request, string key, string value)
        {
            log.LogMethodEntry();            
            request.Headers.Add(key, value);
            log.LogMethodExit();
        }

        /// <summary>
        /// Will add the passed key and value to the header
        /// </summary>
        /// <param name="response">HttpWebResponse type object</param>
        /// <param name="key">Key name the of value should be added</param>
        /// <returns>value of passed key</returns>
        public string GetHeaderKeyValue(HttpResponseMessage response, string key)
        {
            log.LogMethodEntry(response, key);
            string result = null;
            if (response.Headers.Contains(key))
            {
                result = response.Headers.GetValues(key).First();
            }
            log.LogMethodExit(result);
            return result;
        }

        /// <summary>
        /// sends the web request for passed parameter
        /// </summary>
        /// <param name="request">HttpWebRequest type object</param>
        /// <param name="data">Data which need to be transafered with request</param>
        /// <returns>HttpWebResponse object will be returned</returns>
        public async Task<HttpResponseMessage> SendRequest(HttpRequestMessage request,
                                                     string data, 
                                                     string contentType,
                                                     CancellationToken cancellationToken)
        {
            log.LogMethodEntry();
            log.LogVariableState("data", data);
            if (string.IsNullOrWhiteSpace(data) == false)
            {
                StringContent stringContent = new StringContent(data, Encoding.UTF8, contentType);
                request.Content = stringContent;
                //byte[] byteArray = Encoding.UTF8.GetBytes(data);
                ////request.ContentLength = byteArray.Length;
                //Stream dataStream = request.GetRequestStream();
                //// Write the data to the request stream. 
                //dataStream.Write(byteArray, 0, byteArray.Length);
                ////string newestversion = dataStream.ToString();
            }
            HttpResponseMessage response = await client.SendAsync(request, cancellationToken);
            log.LogMethodExit(response);
            return response;
        }

        /// <summary>
        /// sends the web request for passed parameter
        /// </summary>
        /// <param name="request">HttpWebRequest type object</param>
        /// <param name="data">Data which need to be transafered with request</param>
        /// <returns>HttpWebResponse object will be returned</returns>
        public async Task<HttpResponseMessage> SendRequest(HttpRequestMessage request,
                                                     string data,
                                                     string contentType)
        {
            log.LogMethodEntry();
            log.LogVariableState("data", data);
            if (string.IsNullOrWhiteSpace(data) == false)
            {
                StringContent stringContent = new StringContent(data, Encoding.UTF8, contentType);
                request.Content = stringContent;
                //byte[] byteArray = Encoding.UTF8.GetBytes(data);
                ////request.ContentLength = byteArray.Length;
                //Stream dataStream = request.GetRequestStream();
                //// Write the data to the request stream. 
                //dataStream.Write(byteArray, 0, byteArray.Length);
                ////string newestversion = dataStream.ToString();
            }
            HttpResponseMessage response = await client.SendAsync(request);
            log.LogMethodExit(response);
            return response;
        }

        /// <summary>
        /// Accepts the web response and if the receiving data in json format
        /// </summary>
        /// <param name="response">HttpWebResponse type object</param>
        /// <returns></returns>
        public async Task<string> GetJsonData(HttpResponseMessage response)
        {
            log.LogMethodEntry();
            log.LogVariableState("response", response);
            string data = await response.Content.ReadAsStringAsync();
            log.LogMethodExit(data);
            return data;
        }
    }
}
