﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PeachCardPayments
{
    public class PeachPaymentsHostedResponseDTO
    {
        public string RedirectUrl { get; set; }
    }
    public class PeachPaymentsHostedCallbackResponseDTO
    {
        public string Id { get; set; }
        public string ReferencedId { get; set; }
        public string PaymentType { get; set; }
        public string PaymentBrand { get; set; }
        public string Amount { get; set; }
        public string MerchantTransactionId { get; set; }
        public string MerchantInvoiceId { get; set; }
        public string MerchantAccountId { get; set; }
        public string Descriptor { get; set; }
        public string Currency { get; set; }
        public string PresentationAmount { get; set; }
        public string PresentationCurrency { get; set; }
        public ResultDTO Result { get; set; }
        public ResultDetailsDTO ResultDetails { get; set; }
        public string ConnectorTxID1 { get; set; }
        public AuthenticationDTO Authentication { get; set; }
        public CardDTO Card { get; set; }
        public DateTime Timestamp { get; set; }
        public CustomerDTO Customer { get; set; }
        public AddressDTO Shipping { get; set; }
        public AddressDTO Billing { get; set; }
        public ShopifyDTO Shopify { get; set; }
        public BankAccountDTO BankAccount { get; set; }
        public ReconDTO Recon { get; set; }
        public CustomParametersDTO CustomParameters { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class ResultDTO
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class ResultDetailsDTO
    {
        public string ClearingInstituteName { get; set; }
        public string ExtendedDescription { get; set; }
        public int AcquirerResponse { get; set; }
    }

    public class AuthenticationDTO
    {
        public string EntityId { get; set; }
    }

    public class CardDTO
    {
        public string Bin { get; set; }
        public string Last4Digits { get; set; }
        public string Holder { get; set; }
        public string Type { get; set; }
        public string ExpiryMonth { get; set; }
        public string ExpiryYear { get; set; }
    }

    public class CustomerDTO
    {
        public string GivenName { get; set; }
        public string Surname { get; set; }
        public string MerchantCustomerId { get; set; }
        public string Sex { get; set; }
        public string Mobile { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public string Phone { get; set; }
    }

    public class AddressDTO
    {
        public string Street1 { get; set; }
        public string Street2 { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string State { get; set; }
        public string Postcode { get; set; }
        public string Company { get; set; }
    }

    public class ShopifyDTO
    {
        public string OrderId { get; set; }
        public string AccountId { get; set; }
        public string Signature { get; set; }
        public bool? TestMode { get; set; }
    }

    public class BankAccountDTO
    {
        public string Holder { get; set; }
        public string BankName { get; set; }
        public string BankCode { get; set; }
    }

    public class ReconDTO
    {
        public string AuthCode { get; set; }
        public string CiMerchantNumber { get; set; }
        public string ResultCode { get; set; }
        public string Rrn { get; set; }
        public string Stan { get; set; }
    }

    public class CustomParametersDTO
    {
        public string PeachMerchantId { get; set; }
    }

    //public class PeachPaymentsTrxSeachResponseDTO
    //{
    //    public string Id { get; set; }
    //    public string CheckoutId { get; set; }
    //    public string MerchantName { get; set; }
    //    public string MerchantTransactionId { get; set; }
    //    public string PaymentBrand { get; set; }
    //    public string PaymentType { get; set; }
    //    public string Amount { get; set; }
    //    public string Currency { get; set; }
    //    public CardDetailsDTO CardDetails { get; set; }
    //    public ResultDTO Result { get; set; }
    //    public string Timestamp { get; set; }

    //    public override string ToString()

    //    {
    //        return JsonConvert.SerializeObject(this);
    //    }
    //}

    public class PeachPaymentsTrxSeachResponseDTO
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("checkoutId")]
        public string CheckoutId { get; set; }

        [JsonProperty("merchant.name")]
        public string MerchantName { get; set; }

        [JsonProperty("merchantTransactionId")]
        public string MerchantTransactionId { get; set; }

        [JsonProperty("merchantInvoiceId")]
        public string MerchantInvoiceId { get; set; }

        [JsonProperty("paymentBrand")]
        public string PaymentBrand { get; set; }

        [JsonProperty("paymentType")]
        public string PaymentType { get; set; }

        [JsonProperty("amount")]
        public string Amount { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("card.bin")]
        public string CardBin { get; set; }

        [JsonProperty("card.expiryMonth")]
        public string CardExpiryMonth { get; set; }

        [JsonProperty("card.expiryYear")]
        public string CardExpiryYear { get; set; }

        [JsonProperty("card.holder")]
        public string CardHolder { get; set; }

        [JsonProperty("card.last4Digits")]
        public string CardLast4Digits { get; set; }

        [JsonProperty("result.code")]
        public string ResultCode { get; set; }

        [JsonProperty("result.description")]
        public string ResultDescription { get; set; }

        [JsonProperty("timestamp")]
        public string Timestamp { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
    public class CardDetailsDTO
    {
        public string Bin { get; set; }
        public string ExpiryMonth { get; set; }
        public string ExpiryYear { get; set; }
        public string Holder { get; set; }
        public string Last4Digits { get; set; }
    }

    public class PeachPaymentsRefundResponseDTO
    {
        public string Id { get; set; }
        public string ReferencedId { get; set; }
        public string PaymentType { get; set; }
        public string Amount { get; set; }
        public string Currency { get; set; }
        public string Descriptor { get; set; }
        public string MerchantTransactionId { get; set; }
        public ResultDto Result { get; set; }
        public ResultDetailsDto ResultDetails { get; set; }
        public string BuildNumber { get; set; }
        public string Timestamp { get; set; }
        public string Ndc { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class ResultDto
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class ResultDetailsDto
    {
        public string ExtendedDescription { get; set; }
        public string ConnectorTxID1 { get; set; }
        public string ConnectorTxID2 { get; set; }
        public string ConnectorTxID3 { get; set; }
        public string AcquirerResponse { get; set; }
    }

    public class PeachPaymentsAuthResponseDTO
    {
        public string access_token { get; set; }
        public string token_type { get; set; }
        public string expires_in { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class PeachPaymentsCheckoutDTO
    {
        public string checkoutId { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
