/********************************************************************************************
 * Project Name - Payment Gateway Factory                                                                     
 * Description  - Payment Gateway Factory
 *
 **************
 **Version Log
  *Version     Date          Modified By          Remarks          
 *********************************************************************************************
 *2.60         08-May-2019   Nitin Pai           Added Stripe Gateway for Guest app
 *2.70.2       20-Sep-2019   Archana             Added Freedompay Gateway 
 *2.70.2       05-Jan-2020   Jeevan              Added CCAvenue Gateway for Website/Guest app
 *2.70.3       10-Feb-2020   Jeevan              Added CorvusPay/Adyen Gateway for Website
 *2.90.0       14-Jul-2020   Jeevan              Added Payeezy/Payfort/Worldpay/Bambora Hosted Payments for Website
 *2.90.0       14-Jul-2020   Flavia              Added CardConnectHosted Payments for Website
 *2.80.0       09-Jun-2020   Jinto Thomas        Added Alipay Gateway
 *2.10.0       18-Aug-2020   Jinto Thomas        Added Ipay Gateway
 *2.10.0       22-Aug-2020   Jeevan              Added Ecom Payment Gateway
 *2.10.0       23-Sep-2020   Dakshakh            Added Mada Payment Gateway
 *2.140.0      08-Oct-2021   Jinto Thomas        Added VisaNets Hosted Payment Gateway
 *2.160.0      16-Jun-2023   Nitin               Changes dones for new POS UI project
 *********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using utility = Semnox.Core.Utilities;
using System.Reflection;
using System.Threading;
using Semnox.Parafait.PaymentMode;
using Semnox.Core.Utilities;
using Semnox.Parafait.ViewContainer;
using ExecutionContext = Semnox.Core.Utilities.ExecutionContext;
using Semnox.Parafait.PaymentGatewayInterface;

namespace Semnox.Parafait.PaymentGateway
{
    /// <summary>
    /// This class is used to instantiate payment gateway class.
    /// </summary>
    public class PaymentGatewayFactory
    {
        private Semnox.Parafait.logging.Logger log;
        private static readonly Semnox.Parafait.logging.Logger factoryLog = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private Dictionary<PaymentGateways, PaymentGateway> paymentGateways = new Dictionary<PaymentGateways, PaymentGateway>();
        private static PaymentGatewayFactory paymentGatewayFactory;

        /// <summary>
        /// Indicates whether the Payment gateway factory is initialized.
        /// </summary>
        protected bool initialized = false;

        /// <summary>
        /// Whether the payment process is supervised by an attendant.
        /// </summary>
        protected bool isUnattended = false;

        /// <summary>
        /// Parafait utilities
        /// </summary>
        //protected utility.ExecutionContext executionContext = null;

        ///// <summary>
        ///// Delegate instance to display message.
        ///// </summary>
        //protected ShowMessageDelegate showMessageDelegate;

        /// <summary>
        /// Progress instance.
        /// </summary>
        protected IProgress<PaymentProgressReport> progress;
        
        /// <summary>
        /// Progress instance.
        /// </summary>
        protected System.Threading.CancellationToken cancellationToken;

        /// <summary>
        /// Delegate instance for writing the Log to File
        /// </summary>
        protected WriteToLogDelegate writeToLogDelegate;

        private PaymentGatewayFactory()
        {
            factoryLog.LogMethodEntry();
            factoryLog.LogMethodExit(null);
        }

        /// <summary>
        /// Returns a singleton instance of payment gateway factory.
        /// </summary>
        /// <returns></returns>
        public static PaymentGatewayFactory GetInstance()
        {
            factoryLog.LogMethodEntry();
            if (paymentGatewayFactory == null)
            {
                paymentGatewayFactory = new PaymentGatewayFactory();
            }
            factoryLog.LogMethodExit(paymentGatewayFactory);
            return paymentGatewayFactory;
        }

        /// <summary>
        /// Initializes the Payment Gateway Factory. 
        /// </summary>
        /// <param name="utilities">Parafait utilities</param>
        /// <param name="isUnattended">Whether the payment process is supervised by an attendant.</param>
        /// <param name="showMessageDelegate"> Delegate instance to display message.</param>
        /// <param name="writeToLogDelegate">Delegate instance for writing into log</param>
       // public virtual void Initialize(Core.Utilities.ExecutionContext executionContext, bool isUnattended, ShowMessageDelegate showMessageDelegate = null, WriteToLogDelegate writeToLogDelegate = null)
        public virtual void Initialize(bool isUnattended, 
                                       System.Threading.CancellationToken cancellationToken)
        {
            factoryLog.LogMethodEntry(isUnattended, cancellationToken);
            this.isUnattended = isUnattended;
            initialized = true;
            this.cancellationToken = cancellationToken;
            factoryLog.LogMethodExit(null);
        }

        /// <summary>
        /// Instatiates the payment gateway based on the gateway type.
        /// </summary>
        /// <param name="gateway">Payment gateway type</param>
        /// <returns></returns>
        private PaymentGateway GetPaymentGateway(PaymentGateways gateway, ExecutionContext paymentExecutionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            log.LogMethodEntry(gateway, paymentExecutionContext);
            PaymentGateway paymentGateway = null;
            if (initialized)
            {

                if (paymentExecutionContext == null)
                {
                    log.LogMethodExit(null, "Throwing ArgumentNullException - utilities is null.");
                    throw new ArgumentNullException("utilities");
                }
                if (CanCreateMultipleInstances(gateway))
                {
                    paymentGateway = CreatePaymentGateway(gateway, paymentExecutionContext, isUnattended, paymentModeContainerDTO);
                }
                else
                {
                    if (paymentGateways == null)
                    {
                        paymentGateways = new Dictionary<PaymentGateways, PaymentGateway>();
                    }
                    if (paymentGateways.ContainsKey(gateway) == false)
                    {
                        paymentGateway = CreatePaymentGateway(gateway, paymentExecutionContext, isUnattended, paymentModeContainerDTO);
                        paymentGateways.Add(gateway, paymentGateway);
                    }
                    else
                    {
                        paymentGateway = paymentGateways[gateway];
                    }
                }
            }
            else
            {
                log.LogMethodExit(null, "Throwing PaymentGatewayConfigurationException - Payment Gateway Factory not initialized. Please initialize before creating payment gateway.");
                throw new PaymentGatewayConfigurationException("Payment Gateway Factory not initialized. Please initialize before creating payment gateway.");
            }
            log.LogMethodExit(paymentGateway);
            return paymentGateway;
        }

        /// <summary>
        /// Instatiates the payment gateway based on the gateway type string.
        /// </summary>
        /// <param name="gatewayString"></param>
        /// <returns></returns>
        public PaymentGateway GetPaymentGateway(PaymentModeContainerDTO paymentModeContainerDTO, ExecutionContext paymentExecutionContext)
        {
            log = LogManager.GetLogger(paymentExecutionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(paymentModeContainerDTO);
            PaymentGateway paymentGateway = null;
            PaymentGateways gateWay = PaymentGateways.None;
            
            if (paymentModeContainerDTO.Gateway > -1)
            {
                LookupValuesContainerDTO lookupValuesContainerDTO = LookupsViewContainerList.GetLookupValuesContainerDTO(paymentExecutionContext.SiteId, paymentModeContainerDTO.Gateway);
                Enum.TryParse(lookupValuesContainerDTO.LookupValue, out gateWay);
            }
            else if (paymentModeContainerDTO.IsCash)
            {
                gateWay = PaymentGateways.Cash;
            }
            else if (paymentModeContainerDTO.IsCoupon)
            {
                gateWay = PaymentGateways.Coupon;
            }
            else if (paymentModeContainerDTO.IsDebitCard)
            {
                gateWay = PaymentGateways.DebitCard;
            }
            paymentGateway = GetPaymentGateway(gateWay, paymentExecutionContext, paymentModeContainerDTO);
            log.LogMethodExit(paymentGateway);
            return paymentGateway;
        }

        private PaymentGateway CreatePaymentGateway(PaymentGateways gateway, Core.Utilities.ExecutionContext paymentExecutionContext, bool isUnattended, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            log.LogMethodEntry(gateway, paymentExecutionContext, isUnattended);
            PaymentGateway paymentGateway = null;
            switch (gateway)
            {
                case PaymentGateways.None:
                case PaymentGateways.Cash:
                    {
                        paymentGateway = new PaymentGateway(paymentExecutionContext, isUnattended, cancellationToken);
                        break;
                    }
                case PaymentGateways.Coupon:
                    {
                        paymentGateway = new CouponPaymentGateway(paymentExecutionContext, isUnattended, cancellationToken);
                        break;
                    }
                case PaymentGateways.DebitCard:
                    {
                        paymentGateway = new DebitCardPaymentGateway(paymentExecutionContext, isUnattended, cancellationToken);
                        break;
                    }
                case PaymentGateways.Geidea:
                case PaymentGateways.AdyenPayment:
                case PaymentGateways.PaytmDQRPayment:
                case PaymentGateways.CardConnect:
                case PaymentGateways.PineLabsCardPayment:
                case PaymentGateways.PineLabsQRPayment:
                case PaymentGateways.Mashreq:
                    {
                        IPaymentGateway paymentGatewayInterfaceObject = ParafaitPaymentGatewayFactory.GetPaymentGateway(gateway, paymentExecutionContext, paymentModeContainerDTO,isUnattended);
                        paymentGateway = new ParafaitPaymentGateway(paymentExecutionContext, isUnattended, cancellationToken, paymentGatewayInterfaceObject, paymentModeContainerDTO);
                        break;
                    }
                
                case PaymentGateways.CardConnectHostedPayment:
                case PaymentGateways.CardConnectCallbackHostedPayment:
                case PaymentGateways.CCAvenueHostedPayment:
                case PaymentGateways.CCAvenueCallbackHostedPayment:
                case PaymentGateways.AdyenHostedPayment:
                case PaymentGateways.AdyenCallbackHostedPayment:
                case PaymentGateways.WPCyberSourceHostedPayment:
                case PaymentGateways.WPCyberSourceCallbackHostedPayment:
                    {
                        IPaymentGateway paymentGatewayInterfaceObject = ParafaitPaymentGatewayFactory.GetPaymentGateway(gateway, paymentExecutionContext, paymentModeContainerDTO,isUnattended);
                        paymentGateway = new ParafaitWebPaymentGateway(paymentExecutionContext, isUnattended, cancellationToken, paymentGatewayInterfaceObject, paymentModeContainerDTO);
                        break;
                    }
                default:
                    {
                        paymentGateway = new PaymentGateway(paymentExecutionContext, isUnattended, cancellationToken);
                        break;
                    }
            }
            
            log.LogMethodExit(paymentGateway);
            return paymentGateway;
        }
        private bool CanCreateMultipleInstances(PaymentGateways gateway)
        {
            log.LogMethodEntry(gateway);
            bool returnValue = true;
            switch (gateway)
            {

                case PaymentGateways.Mashreq:
                    {
                        returnValue = false;
                        break;
                    }
                case PaymentGateways.CardConnect:
                    {
                        returnValue = false;
                        break;
                    }
                case PaymentGateways.PineLabsQRPayment:
                    {
                        returnValue = false;
                        break;
                    }
                case PaymentGateways.PineLabsCardPayment:
                    {
                        returnValue = false;
                        break;
                    }
                case PaymentGateways.AdyenPayment:
                    {
                        returnValue = false;
                        break;
                    }
                case PaymentGateways.Geidea:
                    {
                        returnValue = false;
                        break;
                    }
                case PaymentGateways.PaytmDQRPayment:
                    {
                        returnValue = false;
                        break;
                    }
            }
            log.LogMethodExit(returnValue);
            return returnValue;
        }


    }
}
