﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PayMaya
{
    class PayMayaHostedUtility : HostedPaymentGatewayUtility
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Constructor for SSLCommerzUtility.
        /// </summary>
        public PayMayaHostedUtility() : base()
        {
            log.LogMethodEntry();
            Initialize();
            log.LogMethodExit();
        }

        // Initializes payment credentials list with default values
        private void Initialize()
        {
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_BASE_URL", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_API_URL", "");

        }

        /// <summary>
        /// Initializes and returns an SSLCommerzHostedCommandHandler instance.
        /// </summary>
        /// <param name="paymentCredentialsList">The payment credentials list.</param>
        /// <returns>An instance of SSLCommerzHostedCommandHandler.</returns>
        public PayMayaHostedCommandHandler InitializeCommandHandler(Dictionary<string, string> paymentCredentialsList)
        {
            if (paymentCredentialsList == null)
            {
                throw new ArgumentNullException("paymentCredentialsList", "The payment credentials list cannot be null.");
            }

            string secretKey;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", out secretKey) || string.IsNullOrEmpty(secretKey))
            {
                throw new ArgumentException("Please enter a valid ''%HOSTED_PAYMENT_GATEWAY_SECRET_KEY'.");
            }

            string publicKey;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", out publicKey) || string.IsNullOrEmpty(publicKey))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY'.");
            }

            string baseUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_BASE_URL", out baseUrl) || string.IsNullOrEmpty(baseUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_BASE_URL'.");
            }

            string apiUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_API_URL", out apiUrl) || string.IsNullOrEmpty(apiUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_API_URL'.");
            }

            if (baseUrl.EndsWith("/"))
            {
                baseUrl = baseUrl.Remove(baseUrl.Length - 1);
            }

            return new PayMayaHostedCommandHandler(publicKey, secretKey, baseUrl, apiUrl);
        }

        /// <summary>
        /// Retrieves the payment status search result for a transaction ID.
        /// </summary>
        /// <param name="trxId">The transaction ID to search for.</param>
        /// <returns>The payment status search result.</returns>
        public override TrxSearchUtilityDTO GetPaymentStatusSearch(string trxId)
        {
            log.LogMethodEntry(trxId);
            TrxSearchUtilityDTO trxSearchResult = new TrxSearchUtilityDTO
            {
                TransactionId = trxId
            };

            if (string.IsNullOrWhiteSpace(trxId))
            {
                log.Error("Invalid transaction ID.");
                trxSearchResult.ErrorMessage = "Invalid transaction ID.";
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.PaymentStatus = "Failed";
                trxSearchResult.TransactionId = "null";
                return trxSearchResult;
            }

            try
            {
                PayMayaHostedCommandHandler payMayaCommandHandler = InitializeCommandHandler(PaymentCredentailsList);
                if (payMayaCommandHandler == null)
                {
                    log.Error("CommandHandler instance is null");
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    return trxSearchResult;
                }

                PayMayaHostedPaymentResponseDto response = payMayaCommandHandler.CreateTrxSearch(trxId);
                if (response == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    return trxSearchResult;
                }
                log.Debug("response status: " + response.status);
                // Map payment status
                PaymentStatusType salePaymentStatus = MapPaymentStatus(response.status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                // Format response
                string formattedJson = JsonConvert.SerializeObject(response, Formatting.Indented);
                trxSearchResult.FormattedResponse = formattedJson;
                trxSearchResult.PaymentStatus = salePaymentStatus.ToString();
            }
            catch (Exception ex)
            {
                log.Error("Error searching transaction details for trxId: " + trxId);
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.ErrorMessage = ex.Message; // todo: some cases msg is: String reference not set to an instance of a String.
                trxSearchResult.PaymentStatus = "Failed"; // Set payment status to "Failed"
            }

            log.LogMethodExit(trxSearchResult);
            return trxSearchResult;
        }

        /// <summary>
        /// Maps the payment status from raw response to Semnox payment status.
        /// </summary>
        /// <param name="rawPaymentGatewayStatus">The raw payment gateway status.</param>
        /// <param name="pgwTrxType">The type of payment gateway transaction.</param>
        /// <returns>The mapped payment status type.</returns>
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {

                            { "PAYMENT_SUCCESS", PaymentStatusType.SUCCESS },
                            { "PAYMENT_PROCESSING", PaymentStatusType.PENDING },
                            { "PENDING_TOKEN", PaymentStatusType.PENDING },
                            { "PENDING_PAYMENT", PaymentStatusType.PENDING },
                            { "FOR_AUTHENTICATION", PaymentStatusType.PENDING },
                            { "AUTHENTICATING", PaymentStatusType.PENDING },
                            { "AUTH_SUCCESS", PaymentStatusType.PENDING },
                            { "AUTH_FAILED", PaymentStatusType.FAILED },
                            { "PAYMENT_EXPIRED", PaymentStatusType.FAILED },
                            { "PAYMENT_FAILED", PaymentStatusType.FAILED },
                            { "PAYMENT_CANCELLED", PaymentStatusType.FAILED },
                            { "VOIDED", PaymentStatusType.FAILED },
                            { "REFUNDED", PaymentStatusType.FAILED }

                        };
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "Success", PaymentStatusType.SUCCESS },
                            { "Failed", PaymentStatusType.FAILED },
                            { "Processing", PaymentStatusType.FAILED }

                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.FAILED;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.FAILED;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

    }
}
