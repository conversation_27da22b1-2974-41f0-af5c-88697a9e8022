﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="Semnox.Parafait.PaymentGateway.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="ExpressSoap">
                    <security mode="Transport" />
                </binding>
                <binding name="ExpressSoap1" />
                <binding name="srsServiceBinding">
                    <security mode="Transport" />
                </binding>
                <binding name="srsServiceBinding1" />
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="https://certtransaction.elementexpress.com/express.asmx"
                binding="basicHttpBinding" bindingConfiguration="ExpressSoap"
                contract="ElementExpress.ExpressSoap" name="ExpressSoap" />
            <endpoint address="https://stagingsupport.datawire.net/rc/srssoap/"
                binding="basicHttpBinding" bindingConfiguration="srsServiceBinding"
                contract="FirstdataReg.srsPortType" name="srsServicePort" />
        </client>
    </system.serviceModel>
    <applicationSettings>
        <Semnox.Parafait.PaymentGateway.Properties.Settings>
            <setting name="Device_FirstDataTrxn_rcService" serializeAs="String">
                <value>https://stg.dw.us.fdcnet.biz/rc</value>
            </setting>
            <setting name="Device_FirstdataReg_srsService" serializeAs="String">
                <value>https://stagingsupport.datawire.net/rc/srssoap/</value>
            </setting>
        </Semnox.Parafait.PaymentGateway.Properties.Settings>
    </applicationSettings>
</configuration>