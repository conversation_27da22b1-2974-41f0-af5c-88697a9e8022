﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Kount
{
    public class KountRisRequestDTO
    {
        public string Merc { get; set; } = string.Empty;
        public string Vers { get; set; } = string.Empty;
        public string SiteId { get; set; } = string.Empty;
        public string Mack { get; set; } = string.Empty;
        public string Formt { get; set; } = string.Empty;
        public string Mode { get; set; } = string.Empty; 
        public string Sess { get; set; } = string.Empty;
        public string Ipad { get; set; } = string.Empty;
        public List<ProductDTO> Products { get; set; } = new List<ProductDTO>(); // Initialize with an empty list
        public string Auth { get; set; } = string.Empty;
        public string Curr { get; set; } = string.Empty;
        public string Ptyp { get; set; } = string.Empty;
        public string Ptok { get; set; } = string.Empty;
        public string Penc { get; set; } = string.Empty;
        public string Totl { get; set; } = string.Empty;
        public string Emal { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Tran { get; set; } = string.Empty;
        public string Ordr { get; set; } = string.Empty;

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class ProductDTO
    {
        public string Price { get; set; } = string.Empty;
        public string Quantity { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Item { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }

    public class KountConfigDTO
    {
        public string Mode { get; set; } = string.Empty;
        public string Auth { get; set; } = string.Empty;
        public string Ptyp { get; set; } = string.Empty;
    }

    public enum MaskingPattern
    {
        AllMaskedButLastFour,
        First2Last6,
        First4Last4
    }

    public enum TransactionStatus
    {
        Decline,
        Approve,
        Review,
        Escalate
    }

}
