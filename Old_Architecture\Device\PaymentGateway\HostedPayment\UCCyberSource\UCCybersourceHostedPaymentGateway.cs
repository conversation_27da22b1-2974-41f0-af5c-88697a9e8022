﻿/********************************************************************************************
 * Project Name - Unified checkout CyberSource Hosted Payment Gateway                                                                     
 * Description  -  Class to handle the payment of CyberSource Hosted Payment Gateway - Callback for Angular
 ********************************************************************************************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 ********************************************************************************************
 *2.152.0     13-March-2024    Yashodhara C H             Created for Website 
 ********************************************************************************************/

using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.UCCyberSource
{
    class UCCyberSourceHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        //HOSTED CHECKOUT Config params
        private string access_key;
        //TODO: Constants
        private string profile_id;
        private string transaction_uuid;
        private string post_url;
        private string action_url;
        private string signature;
        string objectGuid;
        string webformurl;
        private string SECRET_KEY;

        //API config params
        private string HOST_URL;
        private string PAYMENT_REQUEST_URL;
        private string REST_SECRET_KEY;
        private string PUBLIC_KEY;
        private string MERCHANT_ID;
        private string COUNTRY_CODE;
        private string CURRENCY_CODE;
        private bool requestEmail;
        private bool requestPhone;
        //Todo: confoirm

        const bool REQUESTSHIPPING = false;
        const bool SHOWACCEPTEDNETWORKICONS = true;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";
        const string BILLINGTYPE = "FULL";
        const string LOCALE = "en_US";
        const string UNSIGNED_FIELD_NAMES = "transient_token";
        const string CHECKOUTAPIINITIALIZATION_LOCALE = "en-US";
        const string TRANSACTION_TYPE = "sale";
        const string CLIENTVERSION = "0.19"; // old version - 0.10

        List<LookupValuesContainerDTO> lookupValuesContainerDTOList = null;
        private List<string> allowedPaymentTypes = new List<string>();

        private Dictionary<string, string> configParameters = new Dictionary<string, string>();
        private HostedGatewayDTO hostedGatewayDTO;
        UCCyberSourceCommandHandler ucCyberSourceCommandHandler = null;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"100", PaymentStatusType.SUCCESS},
            {"101", PaymentStatusType.FAILED},
            {"102", PaymentStatusType.FAILED},
            {"104", PaymentStatusType.FAILED}, //Duplicate payment
            {"110", PaymentStatusType.FAILED}, //Partial Payment
            {"150", PaymentStatusType.FAILED},
            {"151", PaymentStatusType.FAILED},
            {"152", PaymentStatusType.FAILED},
            {"154", PaymentStatusType.FAILED},
            {"200", PaymentStatusType.FAILED},
            {"201", PaymentStatusType.FAILED},
            {"202", PaymentStatusType.FAILED},
            {"203", PaymentStatusType.FAILED},
            {"204", PaymentStatusType.FAILED},
            {"205", PaymentStatusType.FAILED},
            {"207", PaymentStatusType.FAILED},
            {"208", PaymentStatusType.FAILED},
            {"209", PaymentStatusType.FAILED},
            {"210", PaymentStatusType.FAILED},
            {"211", PaymentStatusType.FAILED},
            {"220", PaymentStatusType.FAILED},
            {"221", PaymentStatusType.FAILED},
            {"222", PaymentStatusType.FAILED},
            {"230", PaymentStatusType.FAILED},
            {"231", PaymentStatusType.FAILED},
            {"232", PaymentStatusType.FAILED},
            {"233", PaymentStatusType.FAILED},
            {"234", PaymentStatusType.FAILED},
            {"235", PaymentStatusType.FAILED},
            {"236", PaymentStatusType.FAILED},
            {"237", PaymentStatusType.FAILED},
            {"238", PaymentStatusType.FAILED},
            {"239", PaymentStatusType.FAILED},
            {"240", PaymentStatusType.FAILED},
            {"241", PaymentStatusType.FAILED},
            {"242", PaymentStatusType.FAILED},
            {"243", PaymentStatusType.FAILED},
            {"246", PaymentStatusType.FAILED},
            {"247", PaymentStatusType.FAILED},
            {"248", PaymentStatusType.FAILED},
            {"250", PaymentStatusType.FAILED},
            {"251", PaymentStatusType.FAILED},
            {"254", PaymentStatusType.FAILED},
            {"268", PaymentStatusType.FAILED},
            {"400", PaymentStatusType.FAILED},
            {"450", PaymentStatusType.FAILED},
            {"451", PaymentStatusType.FAILED},
            {"452", PaymentStatusType.FAILED},
            {"453", PaymentStatusType.FAILED},
            {"454", PaymentStatusType.FAILED},
            {"455", PaymentStatusType.FAILED},
            {"456", PaymentStatusType.FAILED},
            {"457", PaymentStatusType.FAILED},
            {"458", PaymentStatusType.FAILED},
            {"459", PaymentStatusType.FAILED},
            {"460", PaymentStatusType.FAILED},
            {"461", PaymentStatusType.FAILED},
            {"474", PaymentStatusType.FAILED},
            {"475", PaymentStatusType.FAILED},
            {"476", PaymentStatusType.FAILED},
            {"478", PaymentStatusType.FAILED},
            {"480", PaymentStatusType.FAILED},
            {"481", PaymentStatusType.FAILED},
            {"490", PaymentStatusType.FAILED},
            {"491", PaymentStatusType.FAILED},
            {"520", PaymentStatusType.FAILED},
            {"700", PaymentStatusType.FAILED},
            {"701", PaymentStatusType.FAILED},
            {"702", PaymentStatusType.FAILED},
            {"703", PaymentStatusType.FAILED},
            {"-1", PaymentStatusType.NONE},
            {"-2", PaymentStatusType.NONE},
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"PENDING", PaymentStatusType.SUCCESS}, //Ref link: https://developer.cybersource.com/api-reference-assets/index.html#payments_refund_refund-a-payment_responsefielddescription_201_status
        };
        private static readonly Dictionary<string, PaymentStatusType> VoidStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"VOIDED", PaymentStatusType.SUCCESS},
            {"CANCELLED", PaymentStatusType.FAILED}, //Ref link: https://developer.cybersource.com/api-reference-assets/index.html#payments_void_void-a-payment_responsefielddescription_201_status
            {"FAILED", PaymentStatusType.FAILED},
        };

        public UCCyberSourceHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            this.hostedGatewayDTO = new HostedGatewayDTO();
            this.Initialize();
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();
            this.requestEmail = true;
            this.requestPhone = true;
            //By default AllowedPayment types
            
            HOST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            PAYMENT_REQUEST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            REST_SECRET_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            PUBLIC_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            MERCHANT_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");


            this.profile_id = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            this.access_key = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            SECRET_KEY = SystemOptionContainerList.GetSystemOption(utilities.ParafaitEnv.SiteId, "Hosted Payment keys", "UCCyberSource secret key");

            this.COUNTRY_CODE = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "COUNTRY_CODE");
            this.CURRENCY_CODE = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");

            post_url = "/account/UCCyberSource";

            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";

            if (string.IsNullOrWhiteSpace(HOST_URL))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            else if (string.IsNullOrWhiteSpace(PAYMENT_REQUEST_URL))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            }
            else if (string.IsNullOrWhiteSpace(REST_SECRET_KEY))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            else if (string.IsNullOrWhiteSpace(PUBLIC_KEY))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            }
            else if (string.IsNullOrWhiteSpace(MERCHANT_ID))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            }
            else if (string.IsNullOrWhiteSpace(this.profile_id))
            {
                errMsg = String.Format(errMsgFormat, "UCCYBERSOURCE_HOSTED_PAYMENT_PROFILE_ID");
            }
            else if (string.IsNullOrWhiteSpace(this.access_key))
            {
                errMsg = String.Format(errMsgFormat, "UCCYBERSOURCE_HOSTED_PAYMENT_ACCESS_KEY");
            }
            else if (string.IsNullOrWhiteSpace(this.SECRET_KEY))
            {
                errMsg = String.Format(errMsgFormat, "UCCyberSource secret key");
            }
            else if (string.IsNullOrWhiteSpace(this.CURRENCY_CODE))
            {
                errMsg = String.Format(errMsgFormat, "CURRENCY_CODE");
            }
            else if (string.IsNullOrWhiteSpace(this.COUNTRY_CODE))
            {
                errMsg = String.Format(errMsgFormat, "COUNTRY_CODE");
            }

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }

  
            //Change container
            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));

            List<LookupValuesDTO> lookupValuesDTOlist = lookupValuesList.GetAllLookupValues(searchParameters);

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_URL").Count() == 1)
            {
                this.hostedGatewayDTO.SuccessURL = lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.UCCyberSourceHostedPayment.ToString());
                Uri NewUri;
                if (Uri.TryCreate(this.hostedGatewayDTO.SuccessURL, UriKind.Absolute, out NewUri))
                {
                    webformurl = NewUri.GetLeftPart(UriPartial.Authority);
                    post_url =  webformurl + post_url;
                }
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILED_URL").Count() == 1)
            {
                this.hostedGatewayDTO.FailureURL = lookupValuesDTOlist.Where(x => x.LookupValue == "FAILED_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.UCCyberSourceHostedPayment.ToString());
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_URL").Count() == 1)
            {
                this.hostedGatewayDTO.CallBackURL = lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.UCCyberSourceHostedPayment.ToString());
            }

            if (string.IsNullOrWhiteSpace(this.hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(this.hostedGatewayDTO.FailureURL) || string.IsNullOrWhiteSpace(this.hostedGatewayDTO.CallBackURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            //Send Ditionary
            ucCyberSourceCommandHandler = new UCCyberSourceCommandHandler( REST_SECRET_KEY, PUBLIC_KEY,  MERCHANT_ID, HOST_URL, PAYMENT_REQUEST_URL);
            
            log.LogMethodExit();
        }


        /// <summary>
        /// Creates a initial gateway request.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            try
            {
                log.LogMethodEntry();
                log.LogMethodEntry("CCRequestSite:" + utilities.ExecutionContext.GetSiteId());
                objectGuid = transactionPaymentsDTO.Reference;
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;
                CCRequestPGWDTO cCRequestPGWDTO = this.CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());
                CaptureContextRequestDTO captureContextRequestDTO = new CaptureContextRequestDTO
                {
                    targetOrigins = new List<string>
                    {
                        webformurl
                    },
                    clientVersion = CLIENTVERSION,
                    allowedPaymentTypes = new List<string>
                    {
                        "SRC",
                        "PANENTRY"
                    },//Pan and SRC allowedPaymentTypes,
                    country = this.COUNTRY_CODE,
                    locale = LOCALE,
                    captureMandate = new Capturemandate
                    {
                        billingType = BILLINGTYPE,
                        requestEmail = this.requestEmail,
                        requestPhone = this.requestPhone,
                        requestShipping = REQUESTSHIPPING,
                        shipToCountries = new List<string>
                        {
                            "US",
                            "GB"
                        },
                        showAcceptedNetworkIcons = SHOWACCEPTEDNETWORKICONS
                    },
                    orderInformation = new Orderinformation
                    {
                        amountDetails = new Amountdetails
                        {
                            totalAmount = Convert.ToString(transactionPaymentsDTO.Amount),
                            currency = this.CURRENCY_CODE
                        }
                    },
                    checkoutApiInitialization = new Checkoutapiinitialization
                    {
                        profile_id = this.profile_id,
                        access_key = this.access_key,
                        reference_number = Convert.ToString(transactionPaymentsDTO.TransactionId),
                        transaction_uuid = cCRequestPGWDTO.Guid,
                        transaction_type = TRANSACTION_TYPE,
                        currency = this.CURRENCY_CODE,
                        amount = Convert.ToString(transactionPaymentsDTO.Amount),
                        locale = CHECKOUTAPIINITIALIZATION_LOCALE,
                        unsigned_field_names = UNSIGNED_FIELD_NAMES
                    }
                };

                log.Info("request DTO:" + JsonConvert.SerializeObject(captureContextRequestDTO));

                UCCyberSourceRequestDTO uCCyberSourceRequestDTO = ucCyberSourceCommandHandler.CreateCheckout(captureContextRequestDTO);
                this.hostedGatewayDTO.GatewayRequestString = GetSubmitFormKeyValueList(SetPostParameters(uCCyberSourceRequestDTO), post_url, "paymentForm");

                log.Info("request url:" + this.hostedGatewayDTO.RequestURL);
                log.Info("request string:" + this.hostedGatewayDTO.GatewayRequestString);
                log.Info("App Post url: " + webformurl);
                log.Info(this.hostedGatewayDTO.GatewayRequestFormString);

                LookupsList lookupList = new LookupsList(utilities.ExecutionContext);
                List<KeyValuePair<LookupsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<LookupsDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
                List<LookupsDTO> lookups = lookupList.GetAllLookups(searchParameters, true);
                if (lookups != null && lookups.Any())
                {
                    List<LookupValuesDTO> lookupValuesDTOList = lookups[0].LookupValuesDTOList;
                    LookupValuesDTO temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_FAILURE_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        this.hostedGatewayDTO.FailureURL = temp.Description;

                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_SUCCESS_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        this.hostedGatewayDTO.SuccessURL = temp.Description;

                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_CANCEL_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        this.hostedGatewayDTO.CancelURL = temp.Description;

                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_PENDING_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        this.hostedGatewayDTO.PendingURL = temp.Description;
                }

                log.LogMethodEntry("gateway dto:" + this.hostedGatewayDTO.ToString());

            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + this.hostedGatewayDTO.ToString());

            return this.hostedGatewayDTO;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            log.LogMethodEntry(postparamslist,URL,FormName,submitMethod);
            string Method = submitMethod;
            StringBuilder builder = new StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append(string.Format("</head><body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" id=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");

            log.LogMethodExit(builder.ToString());
            return builder.ToString();
        }

        /// <summary>
        /// Creates A IDictionary
        /// </summary>
        /// <returns></returns>
        private IDictionary<string, string> SetPostParameters(UCCyberSourceRequestDTO uCCyberSourceRequestDTO)
        {
            log.LogMethodEntry(uCCyberSourceRequestDTO);

            IDictionary<string, string> postparamslist = new Dictionary<string, string>();

            postparamslist.Clear();
            postparamslist.Add("capturecontext", uCCyberSourceRequestDTO.captureContext);
            postparamslist.Add("JSLibraryURL", uCCyberSourceRequestDTO.clientLibrary);
            postparamslist.Add("PostURL", uCCyberSourceRequestDTO.postURL);
            return postparamslist;
        }

        /// <summary>
        /// Process the payment response
        /// </summary>
        /// <param name="gatewayResponse"></param>
        /// <returns></returns>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            this.hostedGatewayDTO.CCTransactionsPGWDTO = null;
            string paymentStatus = "";
            bool isStatusUpdated = false;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            try
            {
                if (string.IsNullOrWhiteSpace(gatewayResponse))
                {
                    log.Error("Response for Sale Transaction was empty.");
                    throw new Exception("Error processing your payment");
                }

                dynamic response = JsonConvert.DeserializeObject(gatewayResponse);


                string currencyCode = response["currency"] == null ? "" : response["currency"];
                string orderReference = response["req_reference_number"] == null ? "" : response["req_reference_number"];
                string responseDecision = response["decision"] == null ? "" : response["decision"];

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, orderReference.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                this.TransactionSiteId = cCRequestsPGWDTO.SiteId;

                hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(orderReference);
                this.hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PROCESSING;

                log.Debug("Trying to update the CC request to payment processing status");
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);

                if (!isStatusUpdated)
                {
                    throw new Exception("redirect checkoutmessage");
                }


                log.Info("Begin of mappedPaymentStatus");
                string rawPaymentStatus = response.reason_code;
                log.Debug("Raw payment status: " + rawPaymentStatus);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(rawPaymentStatus, PaymentGatewayTransactionType.SALE);
                log.Debug("Value of mappedPaymentStatus: " + salePaymentStatus.ToString());

                log.Info("End of mappedPaymentStatus");

                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                    paymentStatus = "APPROVED_";
                }
                else
                {
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    paymentStatus = "FAILED_";
                }
                paymentStatus += (string.IsNullOrWhiteSpace(response["message"]?.ToString()) ? "" : response["message"]);
                //hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(orderReference);
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = orderReference;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = response["transaction_id"] == null ? "" : response["transaction_id"];
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = response["req_card_number"] == null ? "" : response["req_card_number"];
                hostedGatewayDTO.TransactionPaymentsDTO.Amount = response["auth_amount"] == null ? 0.0 : response["auth_amount"];
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = response["score_card_scheme"] == null ? "" : response["score_card_scheme"];
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = currencyCode;
                hostedGatewayDTO.PaymentStatusMessage = string.IsNullOrWhiteSpace(Convert.ToString(response["message"])) ? "" : response["message"];

                log.Debug("Got the DTO " + hostedGatewayDTO.ToString());

                log.Debug("Trying to update the CC request payment processing status " + hostedGatewayDTO.PaymentStatus + ":" + hostedGatewayDTO.PaymentStatusMessage);
                
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);

                if (!isStatusUpdated)
                {
                    throw new Exception("redirect checkoutmessage");
                }

                if (!String.IsNullOrEmpty(cCRequestsPGWDTO.ReferenceNo))
                {
                    string[] resvalues = cCRequestsPGWDTO.ReferenceNo.ToString().Split('|');
                    foreach (string word in resvalues)
                    {
                        if (word.Contains("PaymentModeId") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(word.Split(':')[1]);
                        }
                        else if (word.Contains("CurrencyCode") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = word.Split(':')[1];
                        }
                    }
                }

                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = null;

                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }

                if (cCTransactionsPGWDTOList == null)
                {
                    log.Debug("No CC Transactions found");

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString(); 
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.Authorize = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.Purchase = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.AcctNo = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber;
                    cCTransactionsPGWDTO.CardType = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName;
                    cCTransactionsPGWDTO.DSIXReturnCode = GetErrorMessageFromResponseCode(rawPaymentStatus);
                    cCTransactionsPGWDTO.TextResponse = paymentStatus;
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();
                    this.hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }

                log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
                log.LogMethodExit(hostedGatewayDTO);
            }
            catch (Exception ex)
            {
                log.Error("Payment Processing failed", ex);
                throw new Exception(ex.Message);
            }
            return hostedGatewayDTO;
        }

        /// <summary>
        /// Refund the transaction amount.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = null;

            if (transactionPaymentsDTO != null)
            {
                try
                {
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;

                    if (transactionPaymentsDTO.CCResponseId > -1)
                    {
                        CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                        List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                        searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                        List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                        // get transaction type of sale CCRequest record
                        ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                        log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO);

                        // to get original TrxId  (in case of POS refund)
                        refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                        log.Debug("Original TrxId for refund: " + refundTrxId);
                    }
                    else
                    {
                        refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                        log.Debug("Refund TrxId for refund: " + refundTrxId);
                    }

                    DateTime originalPaymentDate = new DateTime();
                    CCRequestPGWDTO ccOrigRequestPGWDTO = null;
                    CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                    List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                    searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, refundTrxId));
                    List<CCRequestPGWDTO> cCRequestPGWDTOList = cCRequestPGWListBL.GetCCRequestPGWDTOList(searchParametersPGW);

                    if (cCRequestPGWDTOList != null)
                    {
                        ccOrigRequestPGWDTO = cCRequestPGWDTOList[0]; // to get SALE Tx Type
                    }
                    else
                    {
                        log.Error("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                        throw new Exception("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                    }

                    if (ccOrigRequestPGWDTO != null)
                    {
                        originalPaymentDate = ccOrigRequestPGWDTO.RequestDatetime;
                    }

                    // Define Business Start and End Time
                    DateTime bussStartTime = utilities.getServerTime().Date.AddHours(Convert.ToInt32(ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "BUSINESS_DAY_START_TIME")));
                    DateTime bussEndTime = bussStartTime.AddDays(1);
                    if (utilities.getServerTime() < bussStartTime)
                    {
                        bussStartTime = bussStartTime.AddDays(-1);
                        bussEndTime = bussStartTime.AddDays(1);
                    }

                    // Decide Void vs Refund basis the Date
                    if ((originalPaymentDate >= bussStartTime) && (originalPaymentDate <= bussEndTime))
                    {
                        // same day: VOID
                        log.Info("Same day: Void");
                        bool isVoidSuccess = false;
                        UCCyberSourceRefundRequestDTO uCCyberSourceRefundRequestDTO = null;
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);
                        uCCyberSourceRefundRequestDTO = ucCyberSourceCommandHandler.getRequestDTO(transactionPaymentsDTO.Reference);
                        log.Debug("getRequestDTO- cyberSourceRequestDTO: " + uCCyberSourceRefundRequestDTO);
                        VoidRequestDTO voidRequestDTO = null;
                        voidRequestDTO = new VoidRequestDTO
                        {
                            clientReferenceInformation = new Clientreferenceinformation
                            {
                                code = refundTrxId, // ccRequestId
                            },
                        };
                        VoidResponseDTO voidResponseDTO;
                        voidResponseDTO = ucCyberSourceCommandHandler.CreateVoid(uCCyberSourceRefundRequestDTO, voidRequestDTO);
                        if(voidResponseDTO == null)
                        {
                            //throw error
                            string errorMessage = "Refund failed";
                            throw new Exception(errorMessage);
                        }
                        log.Debug("voidResponseDTO: " + voidResponseDTO);

                        log.Debug("Map Status Type start");
                        String rawVoidStatus = voidResponseDTO.status;
                        log.Debug("Void status from response " + rawVoidStatus);
                        PaymentStatusType voidPaymentStatusType = MapPaymentStatus(rawVoidStatus.ToLower(), PaymentGatewayTransactionType.VOID);
                        log.Debug("Map Status Type end");

                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.VOID.ToString();
                        ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                        ccTransactionsPGWDTO.Purchase = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.PaymentStatus = voidPaymentStatusType.ToString();

                        if (voidPaymentStatusType == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Void Success");
                            ccTransactionsPGWDTO.Authorize = voidResponseDTO.voidAmountDetails.voidAmount;
                            ccTransactionsPGWDTO.RecordNo = voidResponseDTO.clientReferenceInformation.code; //parafait TrxId
                            ccTransactionsPGWDTO.RefNo = voidResponseDTO.id; //paymentId
                            ccTransactionsPGWDTO.TextResponse = voidResponseDTO.status;
                            isVoidSuccess = true;
                        }
                        else
                        {
                            ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                            ccTransactionsPGWDTO.DSIXReturnCode = PaymentStatusType.FAILED.ToString();
                            ccTransactionsPGWDTO.TextResponse = "Void Failed";
                            isVoidSuccess = false;
                        }
                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        if (!isVoidSuccess)
                        {
                            throw new Exception("Void falied");
                        }
                    }
                    else
                    {
                        // Next Day: Refund
                        log.Info("Next Day: Refund");
                        bool isRefundSuccess = false;
                        UCCyberSourceRefundRequestDTO uCCyberSourceRefundRequestDTO = null;
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);
                        uCCyberSourceRefundRequestDTO = ucCyberSourceCommandHandler.getRequestDTO(transactionPaymentsDTO.Reference);
                        log.Debug("getRequestDTO- cyberSourceRequestDTO: " + uCCyberSourceRefundRequestDTO);
                        RefundResponseDTO refundResponseDTO = null;
                        RefundRequestDTO refundRequestDTO = null;
                        refundRequestDTO = new RefundRequestDTO
                        {
                            clientReferenceInformation = new Clientreferenceinformation
                            {
                                code = refundTrxId, // ccRequestId
                            },
                            orderInformation = new Orderinformation
                            {
                                amountDetails = new Amountdetails
                                {
                                    totalAmount = Convert.ToString(transactionPaymentsDTO.Amount),
                                    //currency = CURRENCY_CODE,
                                }
                            },
                        };
                        refundResponseDTO = ucCyberSourceCommandHandler.CreateRefund(uCCyberSourceRefundRequestDTO, refundRequestDTO);
                        log.Debug("refundResponseDTO: " + refundResponseDTO);
                        if(refundResponseDTO == null)
                        {
                            //throw error
                            string errorMessage = "Refund failed";
                            throw new Exception(errorMessage);
                        }

                        log.Debug("Map Payment Status type start");
                        String rawRefundStatus = refundResponseDTO.status;
                        log.Debug("Refund status from response" + rawRefundStatus);
                        PaymentStatusType refundPaymentStatusType = MapPaymentStatus(rawRefundStatus.ToLower(), PaymentGatewayTransactionType.REFUND);
                        log.Debug("Map Payment Status type End");

                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                        ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatusType.ToString();

                        if (refundPaymentStatusType == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Refund Success");

                            ccTransactionsPGWDTO.Authorize = refundResponseDTO.refundAmountDetails.refundAmount;
                            ccTransactionsPGWDTO.RecordNo = refundResponseDTO.clientReferenceInformation.code; //parafait TrxId
                            ccTransactionsPGWDTO.RefNo = refundResponseDTO.id; //paymentId
                            ccTransactionsPGWDTO.TextResponse = refundResponseDTO.status;
                            isRefundSuccess = true;
                        }
                        else
                        {
                            ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                            ccTransactionsPGWDTO.DSIXReturnCode = PaymentStatusType.FAILED.ToString();
                            ccTransactionsPGWDTO.TextResponse = "Refund Failed";
                            isRefundSuccess = false;
                        }

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                        if (!isRefundSuccess)
                        {
                            throw new Exception("Refund failed");
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw;
                }
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentGatewayTransactionType);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PaymentStatusType mappedPaymentStatus = PaymentStatusType.NONE;
            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();

            try
            {
                string trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.TRANSACTION_TYPE, TransactionType.SALE.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("cCRequestsPGWDTO is null. Failed to perform Make payment status");
                    throw new Exception("Failed to perform Make payment status");
                }

                string orderId = cCRequestsPGWDTO.Guid;
                if (string.IsNullOrWhiteSpace(orderId))
                {
                    log.Error("OrderId is null. Failed to perform Make payment status");
                    throw new Exception("Failed to perform Make payment status");
                }

                TxSearchRequestDTO searchRequestDTO = ucCyberSourceCommandHandler.GetTxSearchRequestDTO(trxIdString);
                log.Debug("GetTxSearchRequestDTO- searchRequestDTO: " + searchRequestDTO);
                TxSearchResponseDTO txSearchResponseDTO = ucCyberSourceCommandHandler.CreateTxSearch(searchRequestDTO);
                log.Debug("CreateTxSearch- txSearchResponseDTO: " + txSearchResponseDTO);

                if (txSearchResponseDTO != null && txSearchResponseDTO.totalCount > 0)
                {
                    log.Info("Total count of txSearchResponse: " + txSearchResponseDTO.totalCount.ToString());

                    TxStatusDTO txStatus = ucCyberSourceCommandHandler.GetTxStatusFromSearchResponse(txSearchResponseDTO);

                    log.Debug("GetTxStatusFromSearchResponse- txStatus: " + txStatus);
                    
                    mappedPaymentStatus = MapPaymentStatus(txStatus.reasonCode.ToString(), PaymentGatewayTransactionType.STATUSCHECK);
                    log.Debug("Value of mappedPaymentStatus: " + mappedPaymentStatus.ToString());

                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = trxIdString; //parafait TrxId
                    cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.PaymentStatus = mappedPaymentStatus.ToString();
                    cCTransactionsPGWDTO.TextResponse = txStatus?.TextResponse;
                    cCTransactionsPGWDTO.DSIXReturnCode = GetErrorMessageFromResponseCode(txStatus?.reasonCode.ToString());
                    cCTransactionsPGWDTO.RefNo = txStatus?.RecordNo; //paymentId
                    cCTransactionsPGWDTO.Authorize = txStatus?.Authorize;
                    cCTransactionsPGWDTO.Purchase = txStatus?.Purchase;
                    cCTransactionsPGWDTO.AuthCode = txStatus?.AuthCode;
                    cCTransactionsPGWDTO.AcctNo = txStatus?.AcctNo;
                    
                    CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    cCTransactionsPGWBL.Save();
                }
                else
                {
                    throw new Exception("Response count is 0");
                }
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
                cCTransactionsPGWDTO = new CCTransactionsPGWDTO
                {
                    Authorize = transactionPaymentsDTO.Amount.ToString(),
                    Purchase = transactionPaymentsDTO.Amount.ToString(),
                    RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                    TextResponse = "No payment found!",
                    PaymentStatus = PaymentStatusType.NONE.ToString()
                };
            }

            paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }

        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);

            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;
            try
            {
                if (string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("No Transaction id passed");
                }

                // build Tx Search requestDTO
                TxSearchRequestDTO searchRequestDTO = ucCyberSourceCommandHandler.GetTxSearchRequestDTO(trxId);
                log.Debug("GetTxSearchRequestDTO- searchRequestDTO: " + searchRequestDTO);
                TxSearchResponseDTO txSearchResponseDTO = ucCyberSourceCommandHandler.CreateTxSearch(searchRequestDTO);
                log.Debug("CreateTxSearch- txSearchResponseDTO: " + txSearchResponseDTO);

                if (txSearchResponseDTO != null && txSearchResponseDTO.totalCount > 0)
                {
                    log.Info("Total count of txSearchResponse: " + txSearchResponseDTO.totalCount.ToString());

                    TxStatusDTO txStatus = ucCyberSourceCommandHandler.GetTxStatusFromSearchResponse(txSearchResponseDTO);

                    log.Debug("GetTxStatusFromSearchResponse- txStatus: " + txStatus);
                    if (txStatus.reasonCode != -2 && txStatus.reasonCode != -1)
                    {
                        //Tx found
                        // Tx is either Sale/VOID/REFUND
                        log.Info("Tx Status reasonCode: " + txStatus.reasonCode.ToString());

                        // check if sale/void/refund Tx present
                        // if yes then proceed

                        if (txStatus.TxType == "SALE")
                        {
                            CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                            List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                            searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                            CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                            if (txStatus.reasonCode == 100)
                            {
                                log.Info("CC Transactions found with reasonCode 100");
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.AuthCode = txStatus.AuthCode;
                                cCTransactionsPGWDTO.Authorize = txStatus.Authorize;
                                cCTransactionsPGWDTO.Purchase = txStatus.Purchase;
                                cCTransactionsPGWDTO.TransactionDatetime = txStatus.TransactionDatetime;
                                cCTransactionsPGWDTO.RefNo = txStatus.RecordNo; //paymentId
                                cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                                cCTransactionsPGWDTO.AcctNo = txStatus.AcctNo;

                                cCTransactionsPGWDTO.TextResponse = txStatus.TextResponse;
                                cCTransactionsPGWDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
                                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                                ccTransactionsPGWBL.Save();

                                dict.Add("status", "1");
                                dict.Add("message", "success");
                                dict.Add("retref", txStatus.paymentId);
                                dict.Add("amount", txStatus.Authorize);

                                dict.Add("orderId", trxId);
                                dict.Add("acctNo", txStatus.AcctNo);
                            }
                            else
                            {
                                log.Info("CC Transactions found with reasonCode other than 100");
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.Authorize = !String.IsNullOrEmpty(txStatus.Authorize) ? txStatus.Authorize : String.Empty;
                                cCTransactionsPGWDTO.Purchase = txStatus.Purchase;
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.RefNo = txStatus.RecordNo; //paymentId
                                cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                                cCTransactionsPGWDTO.AcctNo = !String.IsNullOrEmpty(txStatus.AcctNo) ? txStatus.AcctNo : String.Empty;
                                cCTransactionsPGWDTO.TextResponse = txStatus.TextResponse;
                                cCTransactionsPGWDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
                                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                                ccTransactionsPGWBL.Save();

                                dict.Add("status", "0");
                                dict.Add("message", "Transaction found with reasonCode other than 100");
                                dict.Add("retref", txStatus.paymentId);
                                dict.Add("amount", txStatus.Authorize);
                                dict.Add("orderId", trxId);
                                dict.Add("acctNo", txStatus.AcctNo);
                            }

                            resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                            log.LogMethodExit(resData);
                            return resData;
                        }
                    }
                }
                // cancel the Tx in Parafait DB
                dict.Add("status", "0");
                dict.Add("message", "no transaction found");
                dict.Add("orderId", trxId);
                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType defaultStatus = PaymentStatusType.FAILED; //default status
            PaymentStatusType paymentStatusType = defaultStatus;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.VOID:
                        pgwStatusMappingDict = VoidStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");
                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());
                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in salePaymentStatusMappingDict. Defaulting payment status to pending.");
                    paymentStatusType = defaultStatus;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = defaultStatus;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        private string GetErrorMessageFromResponseCode(string reasonCode)
        {
            string errorStatus = "";
            try
            {
                Dictionary<string, string> responseTextCollection = new Dictionary<string, string>
                {
                    {"100", "Successful transaction."},
                    {"101", "Declined - The request is missing one or more fields"},
                    {"102", "Declined - One or more fields in the request contains invalid data."},
                    {"104", "Declined - The merchantReferenceCode sent with this authorization request matches the merchantReferenceCode of another authorization request that you sent in the last 15 minutes."},
                    {"110", "Partial amount was approved"},
                    {"150", "Error - General system failure."},
                    {"151", "Error - The request was received but there was a server timeout. This error does not include timeouts between the client and the server."},
                    {"152", "Error: The request was received, but a service did not finish running in time."},
                    {"154", "Bad MAC key"},
                    {"200", "Soft Decline - The authorization request was approved by the issuing bank but flagged by Cybersource because it did not pass the Address Verification Service (AVS) check."},
                    {"201", "Decline - The issuing bank has questions about the request. You do not receive an authorization code programmatically, but you might receive one verbally by calling the processor."},
                    {"202", "Decline - Expired card. You might also receive this if the expiration date you provided does not match the date the issuing bank has on file."},
                    {"203", "Decline - General decline of the card. No other information provided by the issuing bank."},
                    {"204", "Decline - Insufficient funds in the account."},
                    {"205", "Decline - Stolen or lost card."},
                    {"207", "Decline - Issuing bank unavailable."},
                    {"208", "Decline - Inactive card or card not authorized for card-not-present transactions."},
                    {"209", "Decline - card verification number (CVN) did not match."},
                    {"210", "Decline - The card has reached the credit limit."},
                    {"211", "Decline - Invalid Card Verification Number (CVN)."},
                    {"220", "Decline - Generic Decline."},
                    {"221", "Decline - The customer matched an entry on the processor's negative file."},
                    {"222", "Decline - customer's account is frozen"},
                    {"230", "Soft Decline - The authorization request was approved by the issuing bank but flagged by Cybersource because it did not pass the Card Verification Number (CVN) check."},
                    {"231", "Decline - Invalid account number"},
                    {"232", "Decline - The card type is not accepted by the payment processor."},
                    {"233", "Decline - General decline by the processor."},
                    {"234", "Decline - There is a problem with your Cybersource merchant configuration."},
                    {"235", "Decline - The requested amount exceeds the originally authorized amount."},
                    {"236", "Decline - Processor failure."},
                    {"237", "Decline - The authorization has already been reversed."},
                    {"238", "Decline - The transaction has already been settled."},
                    {"239", "Decline - The requested transaction amount must match the previous transaction amount."},
                    {"240", "Decline - The card type sent is invalid or does not correlate with the credit card number."},
                    {"241", "Decline - The referenced request id is invalid for all follow-on transactions."},
                    {"242", "Decline - The request ID is invalid."},
                    {"243", "Decline - The transaction has already been settled or reversed."},
                    {"246", "Decline - The capture or credit is not voidable because the capture or credit information has already been submitted to your processor. Or, you requested a void for a type of transaction that cannot be voided."},
                    {"247", "Decline - You requested a credit for a capture that was previously voided."},
                    {"248", "Decline - The boleto request was declined by your processor."},
                    {"250", "Error - The request was received, but there was a timeout at the payment processor."},
                    {"251", "Decline - The Pinless Debit card's use frequency or maximum amount per use has been exceeded."},
                    {"254", "Decline - Account is prohibited from processing stand-alone refunds."},
                    {"268", "Transaction Error: Unable to confirm, please contact Barclaycard help desk. Do not re-process"},
                    {"400", "Soft Decline - Fraud score exceeds threshold."},
                    {"450", "Apartment number missing or not found."},
                    {"451", "Insufficient address information."},
                    {"452", "House/Box number not found on street."},
                    {"453", "Multiple address matches were found."},
                    {"454", "P.O. Box identifier not found or out of range."},
                    {"455", "Route service identifier not found or out of range."},
                    {"456", "Street name not found in Postal code."},
                    {"457", "Postal code not found in database."},
                    {"458", "Unable to verify or correct address."},
                    {"459", "Multiple address matches were found (international)"},
                    {"460", "Address match not found (no reason given)"},
                    {"461", "Unsupported character set"},
                    {"474", "PIN Data Required"},
                    {"475", "The cardholder is enrolled in Payer Authentication. Please authenticate the cardholder before continuing with the transaction."},
                    {"476", "Encountered a Payer Authentication problem. Payer could not be authenticated."},
                    {"478", "Strong customer authentication (SCA) is required for this transaction."},
                    {"480", "The order is marked for review by Decision Manager"},
                    {"481", "The order has been rejected by Decision Manager"},
                    {"490", "Your aggregator or acquirer is not accepting transactions from you at this time."},
                    {"491", "Your aggregator or acquirer is not accepting this transaction."},
                    {"520", "Soft Decline - The authorization request was approved by the issuing bank but declined by Cybersource based on your Smart Authorization settings."},
                    {"700", "The customer matched the Denied Parties List"},
                    {"701", "Decline - Export bill_country/ship_country match"},
                    {"702", "Decline - Export email_country match"},
                    {"703", "Decline - Export hostname_country/ip_country match"}
                };

                if (!responseTextCollection.TryGetValue(reasonCode, out errorStatus))
                {
                    errorStatus = "Error occured during transaction.";
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            return errorStatus;
        }
    }
}
