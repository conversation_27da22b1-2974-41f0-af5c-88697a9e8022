﻿/********************************************************************************************
 * Project Name -  Bambora Hosted Payment Gateway                                                                     
 * Description  - Class to handle the payment of Bambora Payment Gateway
 *
 **************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 *********************************************************************************************
 *2.110        11-Jan-2021    Jinto                        Created for Website 
 *2.110        30-Jul-2011    Jinto                        Added ipay payment option 
 *2.130.12     18-Nov-2022    Muaaz Musthafa               Added support for payment status and refund
 *2.152.1      10-Jun-2024    Prajwal <PERSON>kanth Hegde      Payment Standardization - Added MapPaymentStatus() and GetPaymentStatusSearch()
 ********************************************************************************************/
using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using Semnox.Core.Utilities;
using Semnox.Parafait.Languages;
using Semnox.Parafait.Site;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.IPay88
{
    class Ipay88HostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        string merchantCode;
        string merchantKey;
        string serverUrl;
        string requeryUrl;
        string voidUrl;
        string currencyCode;

        string paymentId;
        string refNo;
        string amount;
        string prodDesc;
        string userName;
        string userEmail;
        string userContact;
        string Xfield1;
        string transId;
        string authCode;
        string lang;
        string signatureType;
        string signature;
        string responseUrl;
        string backendUrl;
        string baseApiUrl;
        string objectGuid;

        private string post_url;
        private string txSearchApiUrl;
        string voidApiUrl;
        string refundApiUrl;
        private HostedGatewayDTO hostedGatewayDTO;
        private static Dictionary<string, string> voidTransactionErrCode = new Dictionary<string, string>();

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"1", PaymentStatusType.SUCCESS},
            {"0", PaymentStatusType.FAILED},
        };
        private static readonly Dictionary<string, PaymentStatusType> StatusCheckStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"1", PaymentStatusType.SUCCESS},
            {"0", PaymentStatusType.FAILED},
            {"6", PaymentStatusType.PENDING},
            {"20", PaymentStatusType.PENDING},
        };
        private static readonly Dictionary<string, PaymentStatusType> VoidStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"0", PaymentStatusType.SUCCESS},
            {"1", PaymentStatusType.FAILED},
            {"3", PaymentStatusType.FAILED},
            {"4", PaymentStatusType.FAILED},
            {"5", PaymentStatusType.FAILED},
            {"6", PaymentStatusType.FAILED},
            {"7", PaymentStatusType.FAILED},
            {"12", PaymentStatusType.FAILED},
            {"13", PaymentStatusType.FAILED},
            {"14", PaymentStatusType.FAILED},
            {"15", PaymentStatusType.FAILED},
            {"19", PaymentStatusType.FAILED},
            {"20", PaymentStatusType.FAILED},
            {"21", PaymentStatusType.FAILED},
            {"22", PaymentStatusType.FAILED},
            {"30", PaymentStatusType.FAILED},
            {"33", PaymentStatusType.FAILED},
            {"34", PaymentStatusType.FAILED},
            {"36", PaymentStatusType.FAILED},
            {"41", PaymentStatusType.FAILED},
            {"43", PaymentStatusType.FAILED},
            {"51", PaymentStatusType.FAILED},
            {"54", PaymentStatusType.FAILED},
            {"59", PaymentStatusType.FAILED},
            {"61", PaymentStatusType.FAILED},
            {"62", PaymentStatusType.FAILED},
            {"63", PaymentStatusType.FAILED},
            {"65", PaymentStatusType.FAILED},
            {"91", PaymentStatusType.FAILED},
            {"96", PaymentStatusType.FAILED},
            {"1001", PaymentStatusType.FAILED},
            {"1002", PaymentStatusType.FAILED},
            {"1003", PaymentStatusType.FAILED},
            {"1004", PaymentStatusType.FAILED},
            {"1005", PaymentStatusType.FAILED},
            {"1006", PaymentStatusType.FAILED},
            {"1007", PaymentStatusType.FAILED},
            {"1008", PaymentStatusType.FAILED},
            {"1009", PaymentStatusType.FAILED},
            {"1010", PaymentStatusType.FAILED},
            {"1011", PaymentStatusType.FAILED},
            {"1012", PaymentStatusType.FAILED},
            {"9999", PaymentStatusType.FAILED}
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"5", PaymentStatusType.SUCCESS},
            {"0", PaymentStatusType.FAILED},
            {"1", PaymentStatusType.FAILED},
            {"2", PaymentStatusType.FAILED},
            {"3", PaymentStatusType.FAILED},
            {"4", PaymentStatusType.FAILED},
        };

        public Ipay88HostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            this.hostedGatewayDTO = new HostedGatewayDTO();
            this.Initialize();
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();
            this.hostedGatewayDTO.GatewayRequestStringContentType = RequestContentType.FORM.ToString();
            merchantCode = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            merchantKey = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            serverUrl = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            requeryUrl = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            baseApiUrl = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            currencyCode = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");
            post_url = "/account/Ipay88";

            voidTransactionErrCode.Clear();
            InitializeVoidErrorCode();

            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", merchantCode);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", merchantKey);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_API_URL", serverUrl);
            log.LogVariableState("HOSTED_PAYMENT_ATEWAY_REQUERY_URL", requeryUrl);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_BASE_URL", baseApiUrl);
            //log.LogVariableState("WORLDPAY_HOSTED_PAYMENT_SESSION_URL", gatewayPostUrl);

            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";

            if (string.IsNullOrWhiteSpace(merchantCode))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            }
            if (string.IsNullOrWhiteSpace(merchantKey))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            }
            if (string.IsNullOrWhiteSpace(serverUrl))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(requeryUrl))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_ATEWAY_REQUERY_URL");
            }
            if (string.IsNullOrWhiteSpace(baseApiUrl))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }


            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }

            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));

            List<LookupValuesDTO> lookupValuesDTOlist = lookupValuesList.GetAllLookupValues(searchParameters);

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_URL").Count() == 1)
            {
                this.hostedGatewayDTO.SuccessURL = lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.ipay88HostedPayment.ToString());
                this.responseUrl = this.hostedGatewayDTO.SuccessURL;
                Uri NewUri;
                if (Uri.TryCreate(this.hostedGatewayDTO.SuccessURL, UriKind.Absolute, out NewUri))
                {
                    this.post_url = NewUri.GetLeftPart(UriPartial.Authority) + post_url;
                }
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILED_URL").Count() == 1)
            {
                this.hostedGatewayDTO.FailureURL = lookupValuesDTOlist.Where(x => x.LookupValue == "FAILED_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.ipay88HostedPayment.ToString());
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_URL").Count() == 1)
            {
                this.hostedGatewayDTO.CallBackURL = lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.ipay88HostedPayment.ToString());
                this.backendUrl = this.hostedGatewayDTO.CallBackURL;
            }

            if (string.IsNullOrWhiteSpace(this.hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(this.hostedGatewayDTO.FailureURL) || string.IsNullOrWhiteSpace(this.hostedGatewayDTO.CallBackURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }
            log.LogMethodExit();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns></returns>
        private IDictionary<string, string> SetPostParameters(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCRequestPGWDTO);
            SecurityTokenBL securityTokenBL = new SecurityTokenBL(utilities.ExecutionContext);
            string guid = string.IsNullOrEmpty(objectGuid) ? Guid.NewGuid().ToString() : objectGuid;
            securityTokenBL.GenerateNewJWTToken("External POS", guid, utilities.ExecutionContext.GetSiteId().ToString(), "-1", "-1", "Customer");
            IDictionary<string, string> postparamslist = new Dictionary<string, string>();

            signatureType = "HMACSHA512";
            postparamslist.Clear();
            postparamslist.Add("MerchantCode", this.merchantCode);
            postparamslist.Add("PaymentId", "");
            postparamslist.Add("RefNo", transactionPaymentsDTO.TransactionId.ToString());
            postparamslist.Add("Amount", transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_FORMAT));
            postparamslist.Add("Currency", this.currencyCode);
            postparamslist.Add("ProdDesc", TransactionType.SALE.ToString());
            postparamslist.Add("UserName", transactionPaymentsDTO.CreditCardName);
            postparamslist.Add("UserEmail", transactionPaymentsDTO.NameOnCreditCard);
            postparamslist.Add("UserContact", transactionPaymentsDTO.CardEntitlementType);

            SiteList siteList = new SiteList(utilities.ExecutionContext);
            List<KeyValuePair<SiteDTO.SearchBySiteParameters, string>> searchParameters = new List<KeyValuePair<SiteDTO.SearchBySiteParameters, string>>();
            searchParameters.Add(new KeyValuePair<SiteDTO.SearchBySiteParameters, string>(SiteDTO.SearchBySiteParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
            List<SiteDTO> siteDTOList = siteList.GetAllSites(searchParameters);
            string siteName = (siteDTOList != null && siteDTOList.Any()) ? siteDTOList[0].SiteShortName : "";
            log.Debug("siteName: " + siteName);
            Xfield1 = utilities.ExecutionContext.GetSiteId().ToString() + "|" + transactionPaymentsDTO.PaymentModeId + "|" + siteName; //Xfield1 assigned for signature calculation
            postparamslist.Add("Xfield1", Xfield1);
            string signatureString = GetPaymentSignatureString(transactionPaymentsDTO, cCRequestPGWDTO);
            //postparamslist.Add("Lang", this.lang);
            postparamslist.Add("SignatureType", this.signatureType);
            this.signature = GetPaymentSignature(signatureString);

            postparamslist.Add("Signature", this.signature);
            postparamslist.Add("ResponseURL", this.responseUrl);
            postparamslist.Add("BackendURL", this.backendUrl);
            postparamslist.Add("PostURL", serverUrl);
            postparamslist.Add("customerToken", securityTokenBL.GetSecurityTokenDTO.Token);
            postparamslist.Add("usedId", transactionPaymentsDTO.CustomerCardProfileId);
            postparamslist.Add("email", transactionPaymentsDTO.NameOnCreditCard);


            log.LogMethodExit(postparamslist);
            return postparamslist;
        }

        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);

            this.hostedGatewayDTO.RequestURL = this.serverUrl;
            log.LogMethodEntry("CCRequestSite:" + utilities.ExecutionContext.GetSiteId());
            CCRequestPGWDTO cCRequestPGWDTO = this.CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());
            objectGuid = transactionPaymentsDTO.Reference;
            this.hostedGatewayDTO.GatewayRequestString = SubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO), this.post_url, "ePayment");

            log.Debug("request utl:" + this.hostedGatewayDTO.RequestURL);
            log.Debug("request string:" + this.hostedGatewayDTO.GatewayRequestString);

            this.hostedGatewayDTO.FailureURL = "/account/checkouterror";
            this.hostedGatewayDTO.SuccessURL = "/account/receipt";
            this.hostedGatewayDTO.CancelURL = "/account/checkoutstatus";
            LookupsList lookupList = new LookupsList(utilities.ExecutionContext);
            List<KeyValuePair<LookupsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<LookupsDTO.SearchByParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
            searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            List<LookupsDTO> lookups = lookupList.GetAllLookups(searchParameters, true);
            if (lookups != null && lookups.Any())
            {
                List<LookupValuesDTO> lookupValuesDTOList = lookups[0].LookupValuesDTOList;
                LookupValuesDTO temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_FAILURE_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    this.hostedGatewayDTO.FailureURL = temp.Description;

                temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_SUCCESS_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    this.hostedGatewayDTO.SuccessURL = temp.Description;

                temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_CANCEL_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    this.hostedGatewayDTO.CancelURL = temp.Description;

                temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_PENDING_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    this.hostedGatewayDTO.PendingURL = temp.Description;
            }

            log.LogMethodExit(this.hostedGatewayDTO);

            return this.hostedGatewayDTO;
        }

        private string SubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            log.LogMethodEntry(postparamslist, URL, FormName, submitMethod);
            string Method = submitMethod;
            StringBuilder builder = new StringBuilder();
            builder.Clear();
            builder.Append("<html>");

            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");
            log.LogMethodExit(builder.ToString());
            return builder.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns></returns>
        private string GetPaymentSignatureString(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string signatureString = string.Empty;
            signatureString += merchantKey + merchantCode + transactionPaymentsDTO.TransactionId;
            string amount = Regex.Replace(transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_FORMAT), @"[^0-9]+", "");
            signatureString += amount + currencyCode + Xfield1;

            log.LogMethodExit(signatureString);
            return signatureString;
        }

        private string GetResponseSignatureString(dynamic response)
        {
            log.LogMethodEntry(response);
            string signatureString = string.Empty;
            signatureString += merchantKey + merchantCode + response["PaymentId"] + response["RefNo"];
            string amount = Regex.Replace(response["Amount"].ToString(), @"[^0-9]+", "");
            signatureString += amount + currencyCode + response["Status"];

            log.LogMethodExit(signatureString);
            return signatureString;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="rawData"></param>
        /// <returns></returns>
        private string GetPaymentSignature(string rawData)
        {
            log.LogMethodEntry(rawData);
            byte[] keyBytes = Encoding.UTF8.GetBytes(merchantKey);
            // Create a HMACSHA512   
            using (HMACSHA512 hMACSHA512 = new HMACSHA512(keyBytes))
            {
                // ComputeHash - returns byte array  
                byte[] bytes = hMACSHA512.ComputeHash(Encoding.UTF8.GetBytes(rawData));

                // Convert byte array to a string   
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                log.LogMethodExit(builder.ToString());
                return builder.ToString();
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="gatewayResponse"></param>
        /// <returns></returns>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            bool isStatusUpdated = false;
            this.hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            string encResponse = gatewayResponse;
            string[] result = new string[] { };
            try
            {


                log.Info(encResponse);

                dynamic response = JsonConvert.DeserializeObject(gatewayResponse);
                if (!string.IsNullOrEmpty(Convert.ToString(response["RefNo"])))
                {
                    refNo = Convert.ToString(response["RefNo"]);
                }
                else
                {
                    log.Error("Transaction Id is null: " + response);
                    throw new Exception("Transsaction Id is null");
                }

                hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(refNo);
                this.hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PROCESSING;
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);

                if (!isStatusUpdated)
                {
                    log.Error("Double payment processing detected! Reject payment processing");
                    throw new Exception("redirect checkoutmessage");
                }

                amount = string.IsNullOrEmpty(Convert.ToString(response["Amount"])) ? "" : Convert.ToString(response["Amount"]);
                Xfield1 = string.IsNullOrEmpty(Convert.ToString(response["Xfield1"])) ? "" : Convert.ToString(response["Xfield1"]);
                transId = string.IsNullOrEmpty(Convert.ToString(response["TransId"])) ? "" : Convert.ToString(response["TransId"]);
                authCode = string.IsNullOrEmpty(Convert.ToString(response["AuthCode"])) ? "" : Convert.ToString(response["AuthCode"]);
                paymentId = string.IsNullOrEmpty(Convert.ToString(response["PaymentId"])) ? "" : Convert.ToString(response["PaymentId"]);
                if (!string.IsNullOrEmpty(Xfield1))
                {
                    result = Xfield1.Split('|');
                }
                string creditCardNumber = string.IsNullOrEmpty(Convert.ToString(response["CCNo"])) ? "" : Convert.ToString(response["CCNo"]);
                if (creditCardNumber.Length > 4)
                    creditCardNumber = creditCardNumber.Substring(creditCardNumber.Length - 4).PadLeft(creditCardNumber.Length, 'X');

                string nameOnCreditCard = string.IsNullOrEmpty(Convert.ToString(response["CCName"])) ? "" : Convert.ToString(response["CCName"]);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(Convert.ToString(response["Status"]), PaymentGatewayTransactionType.SALE);

                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("success");
                    string responseSignature = GetResponseSignatureString(response);
                    this.signature = GetPaymentSignature(responseSignature);
                    if (this.signature.Equals(Convert.ToString(response["Signature"])))
                    {
                        hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = nameOnCreditCard;
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = creditCardNumber;
                        hostedGatewayDTO.TransactionPaymentsDTO.Reference = transId;
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = transId;
                        hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(amount);
                        hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = result.Length >= 2 ? Convert.ToInt32(result[1]) : -1;
                        hostedGatewayDTO.PaymentStatus = salePaymentStatus;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                    }
                    else
                    {
                        hostedGatewayDTO.PaymentStatusMessage = "Signature Miss Match";
                        log.Error(hostedGatewayDTO.PaymentStatusMessage);
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.ERROR;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    }
                }
                //else if(response["Status"] == "0" && response["ErrDesc"].ToString().Equals("Customer Cancel Transaction"))
                //{
                //    hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(result[1]);
                //    hostedGatewayDTO.TransactionPaymentsDTO.Reference = refNo;
                //    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = transId;
                //    hostedGatewayDTO.PaymentStatus = PaymentStatusType.CANCELLED;
                //}
                else
                {
                    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = transId;
                    hostedGatewayDTO.TransactionPaymentsDTO.Reference = transId;
                    hostedGatewayDTO.PaymentStatusMessage = string.IsNullOrEmpty(Convert.ToString(response["ErrDesc"])) ? "" : Convert.ToString(response["ErrDesc"]);
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }

                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                if (!isStatusUpdated)
                {
                    throw new Exception("redirect checkoutmessage");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                this.TransactionSiteId = cCRequestsPGWDTO.SiteId;

                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                if (cCTransactionsPGWDTOList == null)
                {
                    log.Debug("No CC Transactions found");

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.TokenID = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.Authorize = amount.ToString();
                    cCTransactionsPGWDTO.Purchase = amount.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.TextResponse = hostedGatewayDTO.PaymentStatus.ToString();
                    cCTransactionsPGWDTO.AuthCode = authCode;
                    cCTransactionsPGWDTO.DSIXReturnCode = hostedGatewayDTO.PaymentStatusMessage;
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                    if (!string.IsNullOrEmpty(paymentId))
                    {
                        List<LookupValuesDTO> lookupValuesDTOList;
                        LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
                        List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> lookUpValueSearchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
                        lookUpValueSearchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "IPAY_PAYMENT_OPTIONS"));
                        lookupValuesDTOList = lookupValuesList.GetAllLookupValues(lookUpValueSearchParameters);

                        if (lookupValuesDTOList != null && lookupValuesDTOList.Any())
                        {
                            var lookupValuesDTO = lookupValuesDTOList.Where(x => x.LookupValue == paymentId).FirstOrDefault();
                            if (lookupValuesDTO != null)
                            {
                                cCTransactionsPGWDTO.CardType = lookupValuesDTO.Description;
                            }
                            else
                            {
                                cCTransactionsPGWDTO.CardType = string.Empty;
                            }

                        }
                        else
                        {
                            cCTransactionsPGWDTO.CardType = string.Empty;
                        }
                    }
                    this.hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment Processing failed", ex);
                throw new Exception(ex.Message);
            }
            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            if (hostedGatewayDTO.PaymentStatus != PaymentStatusType.SUCCESS)
            {
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = -1;
                hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = -1;
            }

            return hostedGatewayDTO;
        }

        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = null;

            voidApiUrl = baseApiUrl + "/ePayment/WebService//VoidAPI/VoidFunction.asmx";
            log.Debug("Void API URL: " + voidApiUrl);
            refundApiUrl = baseApiUrl + "/epayment/Webservice/RefundAPI_V2/Refund/RefundRequest";
            log.Debug("Refund API URL: " + refundApiUrl);

            if (transactionPaymentsDTO != null)
            {
                try
                {
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;

                    if (transactionPaymentsDTO.CCResponseId > -1)
                    {
                        CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                        List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                        searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                        List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                        // get transaction type of sale CCRequest record
                        ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                        log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO);

                        // to get original TrxId  (in case of POS refund)
                        refundTrxId = ccOrigTransactionsPGWDTO.InvoiceNo;
                        log.Debug("Original TrxId for refund: " + refundTrxId);
                    }
                    else
                    {
                        refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                        log.Debug("Refund TrxId for refund: " + refundTrxId);
                    }

                    DateTime originalPaymentDate = new DateTime();
                    CCRequestPGWDTO ccOrigRequestPGWDTO = null;
                    CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                    List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                    searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, refundTrxId));
                    List<CCRequestPGWDTO> cCRequestPGWDTOList = cCRequestPGWListBL.GetCCRequestPGWDTOList(searchParametersPGW);

                    if (cCRequestPGWDTOList != null)
                    {
                        ccOrigRequestPGWDTO = cCRequestPGWDTOList[0]; // to get SALE Tx Type
                    }
                    else
                    {
                        log.Error("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                        throw new Exception("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                    }

                    if (ccOrigRequestPGWDTO != null)
                    {
                        originalPaymentDate = ccOrigRequestPGWDTO.RequestDatetime;
                    }


                    DateTime bussStartTime = utilities.getServerTime().Date.AddHours(Convert.ToInt32(ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "BUSINESS_DAY_START_TIME")));
                    DateTime bussEndTime = bussStartTime.AddDays(1);
                    if (utilities.getServerTime() < bussStartTime)
                    {
                        bussStartTime = bussStartTime.AddDays(-1);
                        bussEndTime = bussStartTime.AddDays(1);
                    }

                    Ipay88HostedCommandHandler ipayCmdHandler = new Ipay88HostedCommandHandler(merchantKey, merchantCode, currencyCode);
                    if ((originalPaymentDate >= bussStartTime) && (originalPaymentDate <= bussEndTime))
                    {
                        //SAME DAY: VOID
                        log.Debug("SAME DAY: VOID");
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);

                        try
                        {
                            string errorMessage = string.Empty;
                            bool isVoidSuccess = false;
                            string voidStatus = ipayCmdHandler.VoidTransaction(transactionPaymentsDTO.Reference, transactionPaymentsDTO.Amount,
                                voidApiUrl);

                            PaymentStatusType voidPaymentStatus = MapPaymentStatus(voidStatus, PaymentGatewayTransactionType.VOID);

                            CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                            ccTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString();//"A";
                            ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.VOID.ToString();
                            ccTransactionsPGWDTO.CardType = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.CardType : "";
                            ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : "";
                            ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            ccTransactionsPGWDTO.PaymentStatus = voidPaymentStatus.ToString();

                            //if (!string.IsNullOrEmpty(voidStatus) && voidStatus == "0") // VOID SUCCESS
                            if(voidPaymentStatus == PaymentStatusType.SUCCESS)
                            {
                                log.Debug("Void Successfully");
                                isVoidSuccess = true;
                                ccTransactionsPGWDTO.TextResponse = "Approved";
                            }
                            else
                            {
                                isVoidSuccess = false;
                                if (voidTransactionErrCode.ContainsKey(voidStatus))
                                {
                                    errorMessage = voidTransactionErrCode[voidStatus];
                                    log.Error(errorMessage);
                                }
                                else
                                {
                                    errorMessage = "Unknown Error";
                                    log.Error(errorMessage);
                                }
                                ccTransactionsPGWDTO.DSIXReturnCode = voidStatus;
                                ccTransactionsPGWDTO.TextResponse = errorMessage;
                            }

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                            ccTransactionsPGWBL.Save();

                            if (!isVoidSuccess)
                            {
                                throw new Exception(errorMessage);
                            }
                        }
                        catch (Exception ex)
                        {
                            log.Error("Error occured while voiding Transaction", ex);
                            log.LogMethodExit(null, "Throwing Payment Gateway Exception-" + ex.Message);
                            throw new PaymentGatewayException(ex.Message);
                        }
                    }
                    else
                    {
                        //NEXT DAY: REFUND
                        log.Debug("NEXT DAY: REFUND");
                        bool isRefundSuccess = false;

                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                        //Perform Refund
                        IPay88RefundResponseDTO iPay88RefundResponseDTO = ipayCmdHandler.RefundTransaction(transactionPaymentsDTO.Reference, transactionPaymentsDTO.Amount, refundApiUrl);
                        if (iPay88RefundResponseDTO == null)
                        {
                            log.Error("Refund Response is null: " + iPay88RefundResponseDTO);
                            throw new Exception("Refund Response is null.");
                        }
                        PaymentStatusType refundPaymentStatus = MapPaymentStatus(iPay88RefundResponseDTO.Status, PaymentGatewayTransactionType.REFUND);

                        // save refund details
                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString(); //trxId
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                        ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                        ccTransactionsPGWDTO.Authorize = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.Purchase = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();

                        //if (!string.IsNullOrEmpty(iPay88RefundResponseDTO.Status) && iPay88RefundResponseDTO.Status == "5") //"5 = SUCCESS"
                        if(refundPaymentStatus == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Refund successful");
                            isRefundSuccess = true;
                            ccTransactionsPGWDTO.TextResponse = "SUCCESS";
                            ccTransactionsPGWDTO.DSIXReturnCode = iPay88RefundResponseDTO.Status + "|" + iPay88RefundResponseDTO.ErrDesc;
                            ccTransactionsPGWDTO.RefNo = iPay88RefundResponseDTO.TransId; //iPay88 TrxId
                        }
                        else
                        {
                            log.Error("Refund failed, response status: " + iPay88RefundResponseDTO.ErrDesc);
                            isRefundSuccess = false;
                            ccTransactionsPGWDTO.TextResponse = "FAILED";
                            ccTransactionsPGWDTO.DSIXReturnCode = iPay88RefundResponseDTO.Status + "|" + iPay88RefundResponseDTO.ErrDesc;
                            ccTransactionsPGWDTO.RefNo = iPay88RefundResponseDTO.TransId; //iPay88 TrxId

                        }

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();

                        if (!isRefundSuccess)
                        {
                            log.Error("Refund failed");
                            throw new Exception("Refund failed");
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw new Exception(ex.Message);
                }

            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType defaultStatus = PaymentStatusType.FAILED; //default status
            PaymentStatusType paymentStatusType = defaultStatus;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = StatusCheckStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.VOID:
                        pgwStatusMappingDict = VoidStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to failed.");
                    paymentStatusType = defaultStatus;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentStatusType = defaultStatus;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentGatewayTransactionType);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PaymentStatusType mappedPaymentStatus = PaymentStatusType.NONE;
            string trxIdString = string.Empty;
            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();

            txSearchApiUrl = baseApiUrl + "/ePayment/Webservice/TxInquiryCardDetails/TxDetailsInquiry.asmx";
            log.Debug("TxSearch API URL: " + txSearchApiUrl);

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    throw new Exception("Insufficient Params passed to the request");
                }
                //get amount from CCREQPGW 
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("No CCRequestPGW found for trxid:" + trxIdString);
                    throw new Exception("No CCRequestPGW found for trxid:" + trxIdString);
                }

                //TxSearch
                Ipay88HostedCommandHandler ipayCmdHandler = new Ipay88HostedCommandHandler(merchantKey, merchantCode, currencyCode);
                TxDetailsInquiryCardInfoResponse txDetailsInquiryCardInfoResponse = ipayCmdHandler.RequeryPayment(trxIdString, cCRequestsPGWDTO.POSAmount, txSearchApiUrl);
                TxDetailsInquiryCardInfoResponseTxDetailsInquiryCardInfoResult transactionInquiry = txDetailsInquiryCardInfoResponse.TxDetailsInquiryCardInfoResult;

                if (transactionInquiry != null)
                {
                    log.Debug("RawStatus from gateway: " + transactionInquiry.Status);

                    mappedPaymentStatus = MapPaymentStatus(transactionInquiry.Status, PaymentGatewayTransactionType.STATUSCHECK);
                    log.Debug("Value of mappedPaymentStatus: " + mappedPaymentStatus.ToString());

                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = transactionInquiry.RefNo ?? trxIdString; //trxId
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                    cCTransactionsPGWDTO.Purchase = cCRequestsPGWDTO.POSAmount;
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.PaymentStatus = mappedPaymentStatus.ToString();

                    //if (!string.IsNullOrEmpty(transactionInquiry.Status) && transactionInquiry.Status == "1")
                    if (mappedPaymentStatus == PaymentStatusType.SUCCESS)
                    {
                        log.Debug("Transaction found");

                        cCTransactionsPGWDTO.AuthCode = transactionInquiry.AuthCode;
                        cCTransactionsPGWDTO.Authorize = transactionInquiry.Amount;
                        cCTransactionsPGWDTO.RefNo = transactionInquiry.TransId; //iPay88 TrxId
                        cCTransactionsPGWDTO.TextResponse = transactionInquiry.Status;
                    }
                    else
                    {
                        log.Debug("No transaction found");
                        cCTransactionsPGWDTO.DSIXReturnCode = transactionInquiry.Errdesc;
                        cCTransactionsPGWDTO.TextResponse = transactionInquiry.Status;
                    }

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();
                }
                else
                {
                    throw new Exception("Response is null: "+ transactionInquiry);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                cCTransactionsPGWDTO = new CCTransactionsPGWDTO
                {
                    Authorize = transactionPaymentsDTO.Amount.ToString(),
                    Purchase = transactionPaymentsDTO.Amount.ToString(),
                    RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                    TextResponse = "No payment found!",
                    PaymentStatus = PaymentStatusType.NONE.ToString()
                };

            }

            paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;


            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }

        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);

            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;

            txSearchApiUrl = baseApiUrl + "/ePayment/Webservice/TxInquiryCardDetails/TxDetailsInquiry.asmx";
            log.Debug("TxSearch API URL: " + txSearchApiUrl);

            try
            {
                //get amount from CCREQPGW 
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("No CCRequestPGW found for trxid:" + trxId);
                    throw new Exception("No CCRequestPGW found for trxid:" + trxId);
                }

                //TxSearch
                Ipay88HostedCommandHandler ipayCmdHandler = new Ipay88HostedCommandHandler(merchantKey, merchantCode, currencyCode);
                TxDetailsInquiryCardInfoResponse txDetailsInquiryCardInfoResponse = ipayCmdHandler.RequeryPayment(trxId, cCRequestsPGWDTO.POSAmount, txSearchApiUrl);
                TxDetailsInquiryCardInfoResponseTxDetailsInquiryCardInfoResult transactionInquiry = txDetailsInquiryCardInfoResponse.TxDetailsInquiryCardInfoResult;

                if (!string.IsNullOrEmpty(transactionInquiry.Status) && transactionInquiry.Status == "1")
                {
                    log.Debug("Transaction found");

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.AuthCode = transactionInquiry.AuthCode;
                    cCTransactionsPGWDTO.Authorize = transactionInquiry.Amount;
                    cCTransactionsPGWDTO.Purchase = cCRequestsPGWDTO.POSAmount;
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.RefNo = transactionInquiry.TransId; //iPay88 TrxId
                    cCTransactionsPGWDTO.RecordNo = transactionInquiry.RefNo; //trxId
                    cCTransactionsPGWDTO.TextResponse = transactionInquiry.Status;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    dict.Add("status", "1");
                    dict.Add("message", "success");
                    dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                    dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                    dict.Add("orderId", trxId);
                    //dict.Add("acctNo", resData["sourceOfFunds"]["provided"]["card"]["number"]);

                    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                }
                else
                {
                    log.Debug("No transaction found");

                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", trxId);
                    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                }

            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }


            log.LogMethodExit(resData);
            return resData;
        }

        private void InitializeVoidErrorCode()
        {
            voidTransactionErrCode.Add("1", "Refer to card issuer ");
            voidTransactionErrCode.Add("3", "Invalid Merchant  ");
            voidTransactionErrCode.Add("4", "Retain Card ");
            voidTransactionErrCode.Add("5", "Do not honor ");
            voidTransactionErrCode.Add("6", "System error ");
            voidTransactionErrCode.Add("7", "Pick up card (special) ");
            voidTransactionErrCode.Add("12", "Invalid transaction ");
            voidTransactionErrCode.Add("13", "Invalid Amount ");
            voidTransactionErrCode.Add("14", "Invalid card number ");
            voidTransactionErrCode.Add("15", "Invalid issuer ");
            voidTransactionErrCode.Add("19", "System timeout ");
            voidTransactionErrCode.Add("20", "Invalid response ");
            voidTransactionErrCode.Add("21", "No action taken ");
            voidTransactionErrCode.Add("22", "Suspected malfunction ");
            voidTransactionErrCode.Add("30", "Format error ");
            voidTransactionErrCode.Add("33", "Expired card ");
            voidTransactionErrCode.Add("34", "Suspected fraud ");
            voidTransactionErrCode.Add("36", "Restricted card ");
            voidTransactionErrCode.Add("41", "Pick up card (lost) ");
            voidTransactionErrCode.Add("43", "Pick up card (stolen) ");
            voidTransactionErrCode.Add("51", "Not sufficient funds ");
            voidTransactionErrCode.Add("54", "Expired card");
            voidTransactionErrCode.Add("59", "Suspected fraud ");
            voidTransactionErrCode.Add("61", "Exceeds withdrawal limit ");
            voidTransactionErrCode.Add("62", "Restricted card ");
            voidTransactionErrCode.Add("63", "Security violation ");
            voidTransactionErrCode.Add("65", "Activity count exceeded ");
            voidTransactionErrCode.Add("91", "Issuer or switch inoperative ");
            voidTransactionErrCode.Add("96", "System malfunction");
            voidTransactionErrCode.Add("1001", "Merchant Code is empty ");
            voidTransactionErrCode.Add("1002", "Transaction ID is empty ");
            voidTransactionErrCode.Add("1003", "Amount is empty ");
            voidTransactionErrCode.Add("1004", "Currency is empty ");
            voidTransactionErrCode.Add("1005", "Signature is empty ");
            voidTransactionErrCode.Add("1006", "Signature not match ");
            voidTransactionErrCode.Add("1007", "Invalid Amount ");
            voidTransactionErrCode.Add("1008", "Invalid Currency ");
            voidTransactionErrCode.Add("1009", "Invalid Merchant Code ");
            voidTransactionErrCode.Add("1010", "This transaction is not eligible for voiding ");
            voidTransactionErrCode.Add("1011", "Transaction not found ");
            voidTransactionErrCode.Add("1012", "Connection error ");
            voidTransactionErrCode.Add("9999", "Transaction already voided ");
        }
    }
}
