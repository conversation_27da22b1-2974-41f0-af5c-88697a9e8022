﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="Device.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="ExpressSoap">
                    <security mode="Transport" />
                </binding>
                <binding name="ExpressSoap1" />
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="https://certtransaction.elementexpress.com/express.asmx"
                binding="basicHttpBinding" bindingConfiguration="ExpressSoap"
                contract="ElementExpress.ExpressSoap" name="ExpressSoap" />
        </client>
    </system.serviceModel>
    <applicationSettings>
        <Device.Properties.Settings>
            <setting name="Device_FirstDataTrxn_rcService" serializeAs="String">
                <value>https://stg.dw.us.fdcnet.biz/rc</value>
            </setting>
            <setting name="Device_FirstdataReg_srsService" serializeAs="String">
                <value>https://stagingsupport.datawire.net/rc/srssoap/</value>
            </setting>
        </Device.Properties.Settings>
    </applicationSettings>
</configuration>