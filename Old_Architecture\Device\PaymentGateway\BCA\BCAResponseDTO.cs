﻿using BCAPayments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.BCA
{
    class BCAResponseDTO:Response
    {

        //public string Version { get; set; }
        public string TransactionType { get; }
        public string TransactionAmount { get; }
        public string OtherAmount { get; }
        public string PAN { get; }
        public string ResponseCode { get; }
        public string RRN { get; }
        public string ApprovalCode { get; }
        public string Date { get; }
        public string Time { get; }
        public string MerchantId { get; }
        public string InvoiceNumber { get; }

        public string IssuerId { get; }
        public string TerminalId { get; }

        public BCAResponseDTO(byte[] responseBytes) : base(responseBytes)
        {
            TransactionType = GetField(4, 2);
            //TransactionAmount = GetField(6, 12);
            TransactionAmount = GetField(6, 10);
            OtherAmount = GetField(18, 12);
            PAN = GetField(30, 19);
            ResponseCode = GetField(53, 2);
            RRN = GetField(55, 12);
            ApprovalCode = GetField(67, 6);
            Date = GetField(73, 8);
            Time = GetField(81, 6);
            MerchantId = GetField(87, 15); ;
            TerminalId = GetField(102, 8);
            InvoiceNumber = GetField(153, 6);
            IssuerId = GetField(165,2);

        }


        public bool IsValidApprovalCode()
        {
            // Check if the code is null
            if (ApprovalCode == null)
                return false;

            // Check if the code is exactly 6 spaces
            if (ApprovalCode.Equals("      "))
                return false;

            // Check if the code is exactly 6 digits
            if (ApprovalCode.Length == 6 && ApprovalCode.All(char.IsDigit))
                return true;

            // If neither condition is met, return false
            return false;
        }
    }

}

