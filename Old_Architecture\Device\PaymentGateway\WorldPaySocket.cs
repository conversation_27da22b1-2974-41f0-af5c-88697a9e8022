﻿using System;
using System.Net;
using System.Net.Sockets;
using System.Runtime.ExceptionServices;
using System.Threading;

namespace Semnox.Parafait.Device.PaymentGateway
{
    public class WorldPaySocket
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private Socket socket;
        private int retryCounter;
        private readonly int MAX_RETIRES = 3;
        private readonly int WAIT_PERIOD_IN_SECONDS = 1;
        private readonly int? sendTimeOut;
        private readonly int? receiveTimeOut;
        private readonly EndPoint endPoint;
        private readonly AddressFamily addressFamily;
        private readonly Action<String> displayStatus;
        public WorldPaySocket(string deviceUrl, int port, int? receiveTimeOut = null, int? sendTimeOut = null, Action<String> displayStatus = null)
        {
            log.LogMethodEntry(deviceUrl, port, receiveTimeOut, sendTimeOut, displayStatus);
            MAX_RETIRES = GetMaxRetries();
            WAIT_PERIOD_IN_SECONDS = GetWaitPeriod();
            this.sendTimeOut = sendTimeOut;
            this.receiveTimeOut = receiveTimeOut;
            IPHostEntry host = Dns.GetHostEntry(deviceUrl);
            IPAddress ipAddress = host.AddressList[0];
            endPoint = new IPEndPoint(ipAddress, port);
            addressFamily = ipAddress.AddressFamily;
            retryCounter = 0;
            this.displayStatus = displayStatus;
            CreateSocket();
            log.LogMethodExit();
        }

        private int GetWaitPeriod()
        {
            log.LogMethodEntry();
            int result;
            if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["WorldPayServiceRetryWaitPeriod"],
                out result) == false)
            {
                result = 5;
            }

            if (result < 1)
            {
                result = 1;
            }
            log.LogMethodExit(result);
            return result;
        }

        private int GetMaxRetries()
        {
            log.LogMethodEntry();
            int result;
            if (int.TryParse(System.Configuration.ConfigurationManager.AppSettings["WorldPayServiceRetryCount"],
                    out result) == false)
            {
                result = 3;
            }

            if (result < 1)
            {
                result = 1;
            }
            log.LogMethodExit(result);
            return result;
        }

        public int Send(byte[] buffer)
        {
            log.LogMethodEntry("buffer");
            retryCounter = 0;
            Exception originalException = null;
            while (retryCounter <= MAX_RETIRES)
            {
                try
                {
                    int result = socket.Send(buffer);
                    retryCounter = 0;
                    log.LogMethodExit(result);
                    return result;
                }
                catch (Exception e)
                {
                    if (originalException == null)
                    {
                        originalException = e;
                    }
                    HandleException(originalException, e);
                }
            }

            string errorMessage = "Unable to send.";
            log.LogMethodExit("Throwing Exception " + errorMessage);
            throw new Exception(errorMessage);
        }

        private void HandleException(Exception originalException, Exception e)
        {
            log.LogMethodEntry(originalException, e);
            if(e is SocketException)
            {
                SocketException se = e as SocketException;
                if(se.SocketErrorCode == SocketError.TimedOut)
                {
                    log.Debug("Time out error occured. Not retrying.");
                    log.LogMethodExit();
                    ExceptionDispatchInfo.Capture(originalException).Throw();
                }
            }
            log.Error(e);
            CloseSocket();
            Wait();
            CreateSocket();
            if (retryCounter >= MAX_RETIRES)
            {
                ExceptionDispatchInfo.Capture(originalException).Throw();
            }
            log.Debug($@"Retrying. retryCounter: {retryCounter}");
            retryCounter++;
            log.LogMethodExit();
        }

        private void Wait()
        {
            log.LogMethodEntry();
            if (displayStatus != null)
            {
                displayStatus("Connection Lost. Trying to connect. Attempts: " + retryCounter);
            }
            Thread.Sleep(WAIT_PERIOD_IN_SECONDS * 1000);
            log.LogMethodExit();
        }

        private void CloseSocket()
        {
            log.LogMethodEntry();
            try
            {
                socket.Shutdown(SocketShutdown.Both);
                socket.Close();
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }

            log.LogMethodExit();
        }

        private void CreateSocket()
        {
            log.LogMethodEntry();
            try
            {
                socket = new Socket(addressFamily, SocketType.Stream, ProtocolType.Tcp);
                socket.Connect(endPoint);
                if (sendTimeOut.HasValue)
                {
                    socket.SendTimeout = sendTimeOut.Value;
                }

                if (receiveTimeOut.HasValue)
                {
                    socket.ReceiveTimeout = receiveTimeOut.Value;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error occured while creating the socket", ex);
            }
            log.LogMethodExit();
        }

        public int Receive(byte[] buffer)
        {
            log.LogMethodEntry("buffer");
            retryCounter = 0;
            Exception originalException = null;
            while (retryCounter < MAX_RETIRES)
            {
                try
                {
                    int result = socket.Receive(buffer);
                    retryCounter = 0;
                    log.LogMethodExit(result);
                    return result;
                }
                catch (Exception e)
                {
                    if (originalException == null)
                    {
                        originalException = e;
                    }
                    HandleException(originalException, e);
                }
            }

            string errorMessage = "Unable to receive.";
            log.LogMethodExit("Throwing Exception " + errorMessage);
            throw new Exception(errorMessage);
        }

        internal void Refresh()
        {
            CloseSocket();
            CreateSocket();
        }
    }
}