﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Semnox.Parafait.Discounts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Globalization;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.SSLCommerz
{
    class SSLCommerzHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string STORE_ID;
        private string STORE_PASSWD;

        private string BASE_URL;
        private string CURRENCY;

        private string trxInitializeUrl;
        private string verifyTrxApiUrl;
        private string refundApiUrl;

        const string VALID = "valid";
        const string FAILED = "failed";
        const string VALIDATED = "validated";
        const string UNATTEMPTED = "Unattempted";
        const string PENDING = "pending";
        const string PROCESSING = "processing";
        const string EXPIRED = "Expired";

        SSLCommerzHostedCommandHandler sslCommerzCommandHandler;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Valid", PaymentStatusType.SUCCESS },
            { "Validated", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
            { "Unattempted", PaymentStatusType.FAILED },
            { "Expired", PaymentStatusType.FAILED },
            { "Pending", PaymentStatusType.PENDING },
            { "Processing", PaymentStatusType.PENDING }
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Success", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
            { "Processing", PaymentStatusType.FAILED }
        };

        public SSLCommerzHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            Initialize();
            log.LogMethodExit(null);
        }
        public override void Initialize()
        {
            log.LogMethodEntry();

            STORE_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            STORE_PASSWD = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");

            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            CURRENCY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");
            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }
            trxInitializeUrl = BASE_URL + $"/gwprocess/v4/api.php";

            verifyTrxApiUrl = BASE_URL + $"/validator/api/merchantTransIDvalidationAPI.php";
            refundApiUrl = BASE_URL + $"/validator/api/merchantTransIDvalidationAPI.php";

            sslCommerzCommandHandler = new SSLCommerzHostedCommandHandler(STORE_ID, STORE_PASSWD, BASE_URL, trxInitializeUrl, verifyTrxApiUrl, refundApiUrl);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;


            if (string.IsNullOrWhiteSpace(STORE_ID))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            }
            if (string.IsNullOrWhiteSpace(STORE_PASSWD))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }

            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));

            List<LookupValuesDTO> lookupValuesDTOlist = lookupValuesList.GetAllLookupValues(searchParameters);

            //SUCCESS_URL
            hostedGatewayDTO.SuccessURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "SUCCESS_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzHostedPayment.ToString());

            //FAILED_URL
            hostedGatewayDTO.FailureURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "FAILED_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzHostedPayment.ToString());

            //CALLBACK_URL
            hostedGatewayDTO.CallBackURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CALLBACK_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzHostedPayment.ToString());

            //CANCEL_URL
            hostedGatewayDTO.CancelURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CANCEL_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzHostedPayment.ToString());


            if (string.IsNullOrWhiteSpace(hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.FailureURL) /*|| string.IsNullOrWhiteSpace(hostedGatewayDTO.CancelURL)*/ || string.IsNullOrWhiteSpace(hostedGatewayDTO.CallBackURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/CALLBACK_URL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/."));
            }


            log.LogMethodExit();
        }
        /// <summary>
        /// Creates a payment request through the PayStack gateway and prepares redirection information.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The details of the transaction to be processed.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the URL and request string for redirecting users to complete the payment.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error during payment request creation or processing.</exception>

        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            SSLCommerzResponseDTO sslCommerzCreatePaymentResponse = null;
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);

                if (transactionPaymentsDTO == null)
                {
                    log.Error("TransactionPaymentsDTO is null");
                    throw new ArgumentNullException(nameof(transactionPaymentsDTO));
                }
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                string checkoutUrl = string.Empty;
                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardName) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute5) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute4) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.NameOnCreditCard) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute3) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.CardEntitlementType))
                {
                    Dictionary<string, string> errorParams = new Dictionary<string, string>
                                {
                                    { "PaymentFailure", "1" },
                                    { "ErrorMessage", "Payment has been declined! Please enter all the mandatory customer details" },
                                    { "TrxId", transactionPaymentsDTO.TransactionId.ToString() },
                                    {"Date", utilities.getServerTime().ToString() }
                                };
                    hostedGatewayDTO.GatewayRequestString = sslCommerzCommandHandler.ErrorForm(errorParams);
                    CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                    List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                    searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, transactionPaymentsDTO.TransactionId.ToString()));
                    CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                    TransactionSiteId = cCRequestsPGWDTO.SiteId;

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;

                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.INVOICE_NUMBER, cCRequestsPGWDTO.RequestID.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                    if (cCTransactionsPGWDTOList == null)
                    {
                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO
                        {
                            TranCode = PaymentGatewayTransactionType.SALE.ToString(),
                            InvoiceNo = cCRequestsPGWDTO.RequestID.ToString(),
                            TransactionDatetime = utilities.getServerTime(),
                            DSIXReturnCode = "Payment has been declined! Please enter all the mandatory customer details",
                            TextResponse = "FAILED",
                            RecordNo = transactionPaymentsDTO.TransactionId.ToString()
                        };
                        hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();
                    }

                }
                else
                {

                    // error page for missing customer details - build error page when it fails - wpcybersource
                    SSLCommerzRequestDTO sslCommerzRequestDTO = new SSLCommerzRequestDTO
                    {
                        store_id = STORE_ID,
                        store_passwd = STORE_PASSWD,
                        tran_id = Convert.ToString(transactionPaymentsDTO.TransactionId) ?? "",
                        currency = transactionPaymentsDTO.CurrencyCode ?? "",
                        total_amount = transactionPaymentsDTO.Amount,
                        success_url = string.IsNullOrWhiteSpace(hostedGatewayDTO.SuccessURL) ? "" : hostedGatewayDTO.SuccessURL,
                        fail_url = string.IsNullOrWhiteSpace(hostedGatewayDTO.FailureURL) ? "" : hostedGatewayDTO.FailureURL,
                        cancel_url = string.IsNullOrWhiteSpace(hostedGatewayDTO.FailureURL) ? "" : hostedGatewayDTO.FailureURL,
                        cus_name = transactionPaymentsDTO.CreditCardName,
                        cus_add1 = transactionPaymentsDTO.Attribute5,
                        cus_city = transactionPaymentsDTO.Attribute4,
                        cus_email = transactionPaymentsDTO.NameOnCreditCard,
                        cus_country = transactionPaymentsDTO.Attribute3,
                        cus_phone = transactionPaymentsDTO.CardEntitlementType,
                        shipping_method = "NO",
                        value_a = Convert.ToString(transactionPaymentsDTO.PaymentModeId) ?? ""
                    };

                    foreach (DiscountCouponsDTO dcDTO in transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList)
                    {
                        sslCommerzRequestDTO.product_name = dcDTO.FromNumber ?? "";
                        sslCommerzRequestDTO.product_category = dcDTO.FromNumber ?? "";
                        sslCommerzRequestDTO.product_profile = dcDTO.FromNumber ?? "";

                    }

                    sslCommerzCreatePaymentResponse = sslCommerzCommandHandler.CreateCheckout(sslCommerzRequestDTO);

                    if (sslCommerzCreatePaymentResponse == null)
                    {
                        log.Error("CreateGatewayPaymentRequest(): Checkout Transaction Response was empty");
                        throw new Exception("Error: could not create payment session");
                    }

                    if (string.IsNullOrWhiteSpace(sslCommerzCreatePaymentResponse.GatewayPageURL))
                    {
                        log.Error("GatewayPageURL was null");
                        throw new Exception("Error creating the payment request");
                    }
                    checkoutUrl = sslCommerzCreatePaymentResponse.GatewayPageURL;
                    log.Debug($"CreateGatewayPaymentRequest(): Payment ResponseDto: {sslCommerzCreatePaymentResponse}");
                    log.Debug($"CreateGatewayPaymentRequest(): Payment request is created, redirecting to Checkout URL: {checkoutUrl}");

                    hostedGatewayDTO.RequestURL = checkoutUrl;
                    hostedGatewayDTO.GatewayRequestString = sslCommerzCommandHandler.PrepareGatewayRequestString(checkoutUrl, "fromSSLCommerzForm");

                    log.Info("request url:" + hostedGatewayDTO.RequestURL);
                    log.Info("request string:" + hostedGatewayDTO.GatewayRequestString);

                    hostedGatewayDTO.FailureURL = "/account/checkouterror";
                    hostedGatewayDTO.SuccessURL = "/account/receipt";
                    hostedGatewayDTO.CancelURL = "/account/checkoutstatus";
                    LookupsList lookupList = new LookupsList(utilities.ExecutionContext);
                    List<KeyValuePair<LookupsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<LookupsDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                    searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
                    List<LookupsDTO> lookups = lookupList.GetAllLookups(searchParameters, true);
                    if (lookups != null && lookups.Any())
                    {
                        List<LookupValuesDTO> lookupValuesDTOList = lookups[0].LookupValuesDTOList;
                        LookupValuesDTO temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_FAILURE_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.FailureURL = temp.Description;
                        }

                        temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_SUCCESS_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.SuccessURL = temp.Description;
                        }

                        temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_CANCEL_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.CancelURL = temp.Description;
                        }
                        // pending url
                        temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_PENDING_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.PendingURL = temp.Description;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }

        /// <summary>
        /// Processes the response received from the payment gateway and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the updated payment details and status.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment or updating the payment status.</exception>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            SSLCommerzCallbackResponse sslCommerzResponse = null;
            bool isStatusUpdated;
            try
            {
                sslCommerzResponse = JsonConvert.DeserializeObject<SSLCommerzCallbackResponse>(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + sslCommerzResponse.ToString());


                if (sslCommerzResponse.tran_id != null)
                {
                    log.Debug("Transaction id: " + sslCommerzResponse.tran_id);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(sslCommerzResponse.tran_id);
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                SSLCommerzTrxStatusElementDTO trxSearchResponse = sslCommerzCommandHandler.CreateTxSearch(sslCommerzResponse.tran_id);

                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("Error processing your payment");
                }

                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(trxSearchResponse.amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = trxSearchResponse.currency;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.bank_tran_id.ToString();//sslcommerz transaction id
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(trxSearchResponse.value_a);//paymentmode id
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = trxSearchResponse.card_no;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = trxSearchResponse.card_type;
                hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = trxSearchResponse.card_issuer;


                hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PROCESSING;
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                log.Debug("isStatusUpdated: " + isStatusUpdated.ToString());
                if (!isStatusUpdated)
                {
                    log.Error("ProcessGatewayResponse():Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }

                //check if ccTransactionPGW updated
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Error("No reference id/Transaction present in PaymentAsia receipt response");
                    cCTransactionsPGWDTOList = null;
                }
                PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.status, PaymentGatewayTransactionType.SALE);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());
                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Payment status is success");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                }
                else if (salePaymentStatus == PaymentStatusType.PENDING)
                {
                    log.Debug("Payment status is pending");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                }
                else if (salePaymentStatus == PaymentStatusType.FAILED)
                {
                    log.Debug("Payment status is failed");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }
                else
                {
                    log.Error("Payment status is unknown. Considering status as pending! Status: " + salePaymentStatus.ToString());
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }

                hostedGatewayDTO.PaymentStatus = salePaymentStatus;
                log.Debug("Final hostedGatewayDTO.PaymentStatus: " + hostedGatewayDTO.PaymentStatus);



                if (cCTransactionsPGWDTOList == null)
                {  // update the CCTransactionsPGWDTO
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.TextResponse = trxSearchResponse.status;
                    cCTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetStatusMessage(trxSearchResponse.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.CardType = trxSearchResponse.card_type;
                    cCTransactionsPGWDTO.CaptureStatus = "Risk level: " + trxSearchResponse.risk_level;
                    cCTransactionsPGWDTO.ResponseOrigin = "Risk title: " + trxSearchResponse.risk_title;
                    cCTransactionsPGWDTO.AcctNo = sslCommerzCommandHandler.GetMaskedCardNumber(trxSearchResponse.card_no);
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                }

                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                log.Debug("isStatusUpdated : " + isStatusUpdated);
                if (!isStatusUpdated)
                {
                    log.Error("Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        private DateTime GetPaymentDate(SSLCommerzTrxStatusElementDTO response)
        {
            log.LogMethodEntry(response);
            DateTime paymentDate = new DateTime();

            if (response != null)
            {
                log.Debug("Payment Date from response: " + response.tran_date);
                if (DateTime.TryParseExact(response.tran_date, "yyyyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }
        /// <summary>
        /// Retrieves the status of a transaction based on the provided transaction ID.
        /// </summary>
        /// <param name="trxId">The ID of the transaction to retrieve status for.</param>
        /// <returns>
        /// Returns a JSON string containing the status information of the transaction, including success or failure status, transaction amount, reference number, account number, and any relevant messages.
        /// </returns>
        /// <exception cref="Exception">Thrown when there are insufficient parameters passed to the request or when an error occurs during the processing of the transaction.</exception>
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;

            try
            {
                if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                SSLCommerzTrxStatusElementDTO trxSearchResponse = sslCommerzCommandHandler.CreateTxSearch(trxId);
                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                log.Debug($"TxSearch Response for TrxId: {trxId}: " + trxSearchResponse);

                if (trxSearchResponse != null)
                {
                    // 14 - Purchase Success
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.RefNo = trxSearchResponse.bank_tran_id;
                    cCTransactionsPGWDTO.RecordNo = trxSearchResponse.tran_id;
                    cCTransactionsPGWDTO.TextResponse = trxSearchResponse.status.ToUpper();
                    cCTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetStatusMessage(trxSearchResponse.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                    cCTransactionsPGWDTO.CardType = trxSearchResponse.card_type;
                    cCTransactionsPGWDTO.AcctNo = sslCommerzCommandHandler.GetMaskedCardNumber(trxSearchResponse.card_no);
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    string searchDataStatus = trxSearchResponse.status.ToLower();

                    if (searchDataStatus == VALID || searchDataStatus == VALIDATED)
                    {

                        //Update the CCTrxPGW
                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else if (searchDataStatus == PROCESSING || searchDataStatus == PENDING)
                    {

                        log.Error("GetTransactionStatus(): Error updating the payment status");

                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (trxSearchResponse.status));
                        dict.Add("orderId", trxId);
                        // throw new Exception("redirect checkoutmessage");
                    }
                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (trxSearchResponse.status));
                        dict.Add("orderId", trxId);
                    }
                }

                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// Initiates a refund process for a transaction based on the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details for initiating the refund.</param>
        /// <returns>
        /// Returns the updated TransactionPaymentsDTO after processing the refund.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefund = false;
            refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    // refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }
                log.Debug("Refund processing started");
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                if (string.IsNullOrEmpty(refundTrxId))
                {
                    log.Error("Error processing Refund");
                    throw new Exception("Error processing Refund");
                }


                SSLCommerzRefundResponseDTO refundResponseDTO = sslCommerzCommandHandler.CreateRefund(transactionPaymentsDTO.Reference, transactionPaymentsDTO.Amount);
                log.Debug("SSLCommerz Refund Response refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }
                PaymentStatusType refundPaymentStatus = MapPaymentStatus(refundResponseDTO.status, PaymentGatewayTransactionType.REFUND);
                log.Debug("Value of txSearchPaymentStatus: " + refundPaymentStatus.ToString());

                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                ccTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString(); //parafait TrxId
                ccTransactionsPGWDTO.TextResponse = refundResponseDTO.status.ToUpper();
                ccTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetRefundStatusMessage(refundResponseDTO.status);
                ccTransactionsPGWDTO.RefNo = refundResponseDTO.bank_tran_id; //sslcommerz paymentId

                ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();


                if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Refund Success for trxId: " + refundTrxId);
                    isRefund = true;
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.status;
                }
                else
                {
                    //refund failed
                    isRefund = false;
                    string errorMessage = refundResponseDTO.status;
                    log.Error($"Refund Failed. Error Message received: {errorMessage}");
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.status;
                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }


            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }


        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            SSLCommerzTrxStatusElementDTO orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    //throw new Exception("Insufficient Params passed to the request");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = sslCommerzCommandHandler.CreateTxSearch(trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error($"Order status for trxId: {trxIdString} failed.");
                    throw new Exception($"Transaction search failed for trxId: {trxIdString}!");
                }

                PaymentStatusType txSearchPaymentStatus = MapPaymentStatus(orderStatusResult.status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of txSearchPaymentStatus: " + txSearchPaymentStatus.ToString());

                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO != null ? cCRequestsPGWDTO.RequestID.ToString() : trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.RefNo = orderStatusResult.bank_tran_id;
                cCTransactionsPGWDTO.RecordNo = orderStatusResult.tran_id;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.status.ToUpper();
                cCTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetStatusMessage(orderStatusResult.status);
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.CardType = orderStatusResult.card_type;
                cCTransactionsPGWDTO.PaymentStatus = txSearchPaymentStatus.ToString();
                cCTransactionsPGWDTO.AcctNo = sslCommerzCommandHandler.GetMaskedCardNumber(orderStatusResult.card_no);
                cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(orderStatusResult);

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }



    }


}

