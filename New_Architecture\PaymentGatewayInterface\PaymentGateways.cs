﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public enum PaymentGateways
    {
        None,
        Cash,
        Coupon,
        DebitCard,

        PineLabsCardPayment,
        Ipay,
        ClubSpeedGiftCard,
        CardConnect,
        AdyenPayment,
        Geidea,
        PaytmDQRPayment,
        PineLabsQRPayment,
        Mashreq,


        CardConnectHostedPayment,
        CardConnectCallbackHostedPayment,
        CCAvenueHostedPayment,
        CCAvenueCallbackHostedPayment,
        WPCyberSourceHostedPayment,
        WPCyberSourceCallbackHostedPayment,
        AdyenHostedPayment,
        AdyenCallbackHostedPayment
    }
}
