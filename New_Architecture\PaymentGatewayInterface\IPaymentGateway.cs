﻿/********************************************************************************************
 * Project Name - Payment GatewayInterface
 * Description  - IPayment gateway class
 * 
 **************
 **Version Log
 **************
 *Version     Date              Modified By                    Remarks          
 *********************************************************************************************
 * 2.190.0    24-Sep-2024       Amrutha                        Created
 *******************************************************************************************/

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public interface IPaymentGateway
    {
        bool CanCreateMultipleInstances { get;}
        bool IsSaleSupported { get; }
        bool IsPaymentHistoryListRequired { get; }

        bool SupportsFullStatusCheck { get; }
        bool IsTipAdjustmentAllowed { get; }
        Task<PaymentResponseDTO> Sale(PaymentRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken);
        bool IsRefundSupported { get; }
        Task<PaymentResponseDTO> Refund(RefundRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken);
        bool IsStatusCheckSupported { get;  }
        Task<PaymentResponseDTO> StatusCheck(StatusCheckRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken,string errorMsg);
        bool IsAuthSupported { get; }
     
        Task<PaymentResponseDTO> Auth(PaymentRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken);
        Task<PaymentResponseDTO> PerformSettlement(SettlementRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken);
        Task<PaymentResponseDTO> PreAuth(PaymentRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken);
        string GetMerchantCopy(PaymentPrintAttribute paymentPrintAttributes, PaymentResponseDTO paymentResponse);
        string GetCustomerCopy(PaymentPrintAttribute paymentPrintAttributes,PaymentResponseDTO paymentResponse);
        Task<PaymentResponseDTO> SaveDonationResponse(PaymentResponseDTO paymentResponseDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken);
        bool IsTipEnabled(PaymentResponseDTO paymentResponseDTO,List<PaymentResponseDTO>paymentResponseDTOList);
        bool IsDonationSupported(PaymentResponseDTO paymentResponseDTO,List<PaymentResponseDTO>paymentResponseDTOList);
        void ValidateConfiguration();
        bool IsCustomerInfoRequired { get; }
        bool IsProductsInfoRequired { get; }
        Task<PaymentSessionDTO> CreatePaymentSessionDTO(PaymentRequestDTO paymentRequestDTO);
        Task<string> GetPaymentIdentifier(string paymentResponse);
        Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO, List<PaymentResponseDTO> paymentHistoryList = null);
        bool RedirectResponseToWebsite { get; }
        string CallbackResponseMessage { get; }
    }
}
