/********************************************************************************************
 * Project Name - Transaction
 * Description  - Business logic class of transaction discount criteria line
 **************
 **Version Log
 **************
 *Version     Date             Modified By         Remarks          
 *********************************************************************************************
 *2.140.0     16-Feb-2021      Lakshminarayana     Created 
 ********************************************************************************************/
using Semnox.Core.Utilities;
using Semnox.Parafait.Languages;
using Semnox.Parafait.Site;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Semnox.Parafait.Transaction.V2
{
    /// <summary>
    /// Business logic class of transaction discount criteria line
    /// </summary>
    public class PaymentTransactionBL
    {
        #region Fields
        private Semnox.Parafait.logging.Logger log;
        private readonly ExecutionContext executionContext;
        private readonly UnitOfWork unitOfWork;
        private PaymentTransactionDTO paymentTransactionDTO;
        #endregion

        #region Constructor
        /// <summary>
        /// Parameterized constructor
        /// </summary>
        private PaymentTransactionBL(ExecutionContext executionContext, UnitOfWork unitOfWork)
        {

            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, unitOfWork);
            this.executionContext = executionContext;
            this.unitOfWork = unitOfWork;
            log.LogMethodExit();
        }

        /// <summary>
        /// Parameterized constructor
        /// </summary>
        public PaymentTransactionBL(ExecutionContext executionContext, PaymentTransactionDTO paymentTransactionDTO, UnitOfWork unitOfWork)
        {

            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, unitOfWork);
            this.executionContext = executionContext;
            this.unitOfWork = unitOfWork;
            this.paymentTransactionDTO = paymentTransactionDTO;
            log.LogMethodExit();
        }


        /// <summary>
        /// Parameterized constructor
        /// </summary>
        public PaymentTransactionBL(ExecutionContext executionContext,
                                    int transactionId,
                                    string transactionPaymentGuid,
                                    PaymentTransactionDTO parameterPaymentTransactionDTO,
                                    UnitOfWork unitOfWork)
        {

            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, transactionId,
                               transactionPaymentGuid, parameterPaymentTransactionDTO,
                               unitOfWork);
            this.executionContext = executionContext;
            this.unitOfWork = unitOfWork;
            this.paymentTransactionDTO = new PaymentTransactionDTO(responseID: -1, transactionId: transactionId, 
                                                                   invoiceNo: parameterPaymentTransactionDTO.InvoiceNo, 
                                                                   tokenID: parameterPaymentTransactionDTO.TokenID, 
                                                                   recordNo: parameterPaymentTransactionDTO.RecordNo, 
                                                                   dSIXReturnCode: parameterPaymentTransactionDTO.DSIXReturnCode,
                                                                   statusID: parameterPaymentTransactionDTO.StatusID,
                                                                   textResponse: parameterPaymentTransactionDTO.TextResponse, 
                                                                   acctNo: parameterPaymentTransactionDTO.AcctNo, 
                                                                   cardType: parameterPaymentTransactionDTO.CardType, 
                                                                   tranCode: parameterPaymentTransactionDTO.TranCode, 
                                                                   refNo: parameterPaymentTransactionDTO.RefNo, 
                                                                   purchase: parameterPaymentTransactionDTO.Purchase,
                                                                   authorize: parameterPaymentTransactionDTO.Authorize, 
                                                                   transactionDatetime: parameterPaymentTransactionDTO.TransactionDatetime,
                                                                   authCode: parameterPaymentTransactionDTO.AuthCode, 
                                                                   processData: parameterPaymentTransactionDTO.ProcessData, 
                                                                   responseOrigin: parameterPaymentTransactionDTO.ResponseOrigin,
                                                                   userTraceData: parameterPaymentTransactionDTO.UserTraceData, 
                                                                   captureStatus: parameterPaymentTransactionDTO.CaptureStatus, 
                                                                   acqRefData: parameterPaymentTransactionDTO.AcqRefData, 
                                                                   tipAmount: parameterPaymentTransactionDTO.TipAmount, 
                                                                   customerCopy: parameterPaymentTransactionDTO.CustomerCopy,
                                                                   merchantCopy: parameterPaymentTransactionDTO.MerchantCopy, 
                                                                   customerCardProfileId: parameterPaymentTransactionDTO.CustomerCardProfileId, 
                                                                   trxPaymentGuid: transactionPaymentGuid, isActive: true, status: parameterPaymentTransactionDTO.Status,
                                                                   creditCardName: parameterPaymentTransactionDTO.CreditCardName, nameOnCreditCard: parameterPaymentTransactionDTO.NameOnCreditCard, creditCardExpiry:parameterPaymentTransactionDTO.CreditCardExpiry, amount:parameterPaymentTransactionDTO.Amount, parentResponseId: parameterPaymentTransactionDTO.ParentResponseId);
            log.LogMethodExit();
        }

        #endregion

        #region Properties
        /// <summary>
        /// Get/Set method of the ResponseID field
        /// </summary>
        public int ResponseID
        {
            get
            {
                return paymentTransactionDTO.ResponseID;
            }
        }

        /// <summary>
        /// Get/Set method of the transactionId field
        /// </summary>
        public int TransactionId
        {
            get
            {
                return paymentTransactionDTO.TransactionId;
            }
        }

        /// <summary>
        /// Get/Set method of the InvoiceNo field
        /// </summary>
        public string InvoiceNo
        {
            get
            {
                return paymentTransactionDTO.InvoiceNo;
            }
        }

        /// <summary>
        /// Get/Set method of the TokenID field
        /// </summary>
        public string TokenID
        {
            get
            {
                return paymentTransactionDTO.TokenID;
            }
        }

        /// <summary>
        /// Get/Set method of the RecordNo field
        /// </summary>
        public string RecordNo
        {
            get
            {
                return paymentTransactionDTO.RecordNo;
            }
        }

        /// <summary>
        /// Get/Set method of the DSIXReturnCode field
        /// </summary>
        public string DSIXReturnCode
        {
            get
            {
                return paymentTransactionDTO.DSIXReturnCode;
            }
        }

        /// <summary>
        /// Get/Set method of the StatusID field
        /// </summary>
        public int StatusID
        {
            get
            {
                return paymentTransactionDTO.StatusID;
            }
        }

        /// <summary>
        /// Get/Set method of the TextResponse field
        /// </summary>
        public string TextResponse
        {
            get
            {
                return paymentTransactionDTO.TextResponse;
            }
        }

        /// <summary>
        /// Get/Set method of the AcctNo field
        /// </summary>
        public string AcctNo
        {
            get
            {
                return paymentTransactionDTO.AcctNo;
            }
        }

        /// <summary>
        /// Get/Set method of the CardType field
        /// </summary>
        public string CardType
        {
            get
            {
                return paymentTransactionDTO.CardType;
            }
        }

        /// <summary>
        /// Get/Set method of the TranCode field
        /// </summary>
        public string TranCode
        {
            get
            {
                return paymentTransactionDTO.TranCode;
            }
        }

        /// <summary>
        /// Get/Set method of the RefNo field
        /// </summary>
        public string RefNo
        {
            get
            {
                return paymentTransactionDTO.RefNo;
            }
        }

        /// <summary>
        /// Get/Set method of the Purchase field
        /// </summary>
        public string Purchase
        {
            get
            {
                return paymentTransactionDTO.Purchase;
            }
        }

        /// <summary>
        /// Get/Set method of the Authorize field
        /// </summary>
        public string Authorize
        {
            get
            {
                return paymentTransactionDTO.Authorize;
            }
        }

        /// <summary>
        /// Get/Set method of the TransactionDatetime field
        /// </summary>
        public DateTime TransactionDatetime
        {
            get
            {
                return paymentTransactionDTO.TransactionDatetime;
            }
        }

        /// <summary>
        /// Get/Set method of the AuthCode field
        /// </summary>
        public string AuthCode
        {
            get
            {
                return paymentTransactionDTO.AuthCode;
            }
        }

        /// <summary>
        /// Get/Set method of the ProcessData field
        /// </summary>
        public string ProcessData
        {
            get
            {
                return paymentTransactionDTO.ProcessData;
            }
        }

        /// <summary>
        /// Get/Set method of the ResponseOrigin field
        /// </summary>
        public string ResponseOrigin
        {
            get
            {
                return paymentTransactionDTO.ResponseOrigin;
            }
        }

        /// <summary>
        /// Get/Set method of the UserTraceData field
        /// </summary>
        public string UserTraceData
        {
            get
            {
                return paymentTransactionDTO.UserTraceData;
            }
        }

        /// <summary>
        /// Get/Set method of the CaptureStatus field
        /// </summary>
        public string CaptureStatus
        {
            get
            {
                return paymentTransactionDTO.CaptureStatus;
            }
        }

        /// <summary>
        /// Get/Set method of the AcqRefData field
        /// </summary>
        public string AcqRefData
        {
            get
            {
                return paymentTransactionDTO.AcqRefData;
            }
        }
        /// <summary>
        ///  Get/Set method of the TrxPaymentGuid field
        /// </summary>
        public string TransactionPaymentGuid
        {
            get
            {
                return paymentTransactionDTO.TransactionPaymentGuid;
            }
        }

        /// <summary>
        ///  Get/Set method of the TrxPaymentGuid field
        /// </summary>
        public bool IsActive
        {
            get
            {
                return paymentTransactionDTO.IsActive;
            }
        } 
        
        /// <summary>
        ///  Get/Set method of the Status field
        /// </summary>
        public string Status
        {
            get
            {
                return paymentTransactionDTO.Status;
            }
        }

        /// <summary>
        /// Get method of the TipAmount field
        /// </summary>
        public string TipAmount
        {
            get
            {
                return paymentTransactionDTO.TipAmount;
            }
        }

        /// <summary>
        ///  Get/Set method of the CustomerCopy field
        /// </summary>
        public string CustomerCopy
        {
            get
            {
                return paymentTransactionDTO.CustomerCopy;
            }
        }
        /// <summary>
        ///  Get/Set method of the MerchantCopy field
        /// </summary>
        public string MerchantCopy
        {
            get
            {
                return paymentTransactionDTO.MerchantCopy;
            }
        }
        /// <summary>
        ///  Get/Set method of the CustomerCardProfileId field
        /// </summary>
        public string CustomerCardProfileId
        {
            get
            {
                return paymentTransactionDTO.CustomerCardProfileId;
            }
        }
        /// <summary>
        /// Get Method of paymentTransactionDTO
        /// </summary>
        public PaymentTransactionDTO PaymentTransactionDTO
        {
            get
            {
                return paymentTransactionDTO;
            }
        }

        #endregion

        #region Internal Methods
        internal virtual void Accept(TransactionBatchSaveIterator transactionBatchSaveIterator)
        {
            log.LogMethodEntry(transactionBatchSaveIterator);
            if (paymentTransactionDTO.IsChanged)
            {
                transactionBatchSaveIterator.Add(paymentTransactionDTO);
            }
            log.LogMethodExit();
        }

        internal void InActivate()
        {
            log.LogMethodEntry();
            if (paymentTransactionDTO.IsActive)
            {
                paymentTransactionDTO.IsActive = false;
            }
            log.LogMethodExit();
        }

        #endregion

        #region private methods

        private void SetFromSiteTimeOffset()
        {
            log.LogMethodEntry(paymentTransactionDTO);
            if (SiteContainerList.IsCorporate() == false)
            {
                log.LogMethodExit(paymentTransactionDTO, "local site");
                return;
            }
            if (paymentTransactionDTO == null)
            {
                log.LogMethodExit(paymentTransactionDTO, "paymentTransactionDTO is empty");
                return;
            }
            if (paymentTransactionDTO.TransactionDatetime != DateTime.MinValue)
            {
                paymentTransactionDTO.TransactionDatetime = SiteContainerList.FromSiteDateTime(paymentTransactionDTO.SiteId, paymentTransactionDTO.TransactionDatetime);
            }
            paymentTransactionDTO.AcceptChanges();
            log.LogMethodExit(paymentTransactionDTO);
        }

        
        #endregion
    }

    /// <summary>
    /// Manages the list of transaction line
    /// </summary>
    public class PaymentTransactionListBL
    {
        private Semnox.Parafait.logging.Logger log;
        private readonly ExecutionContext executionContext;
        private readonly UnitOfWork unitOfWork;

        /// <summary>
        /// Parameterized constructor
        /// </summary>
        public PaymentTransactionListBL(ExecutionContext executionContext, UnitOfWork unitOfWork)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, unitOfWork);
            this.executionContext = executionContext;
            this.unitOfWork = unitOfWork;
            log.LogMethodExit();
        }

        /// <summary>
        /// Returns the transaction account dto list of the specified transactions
        /// </summary>
        public List<PaymentTransactionDTO> GetPaymentTransactionDTOListOfTransactions(List<int> transactionIdList, bool activeRecords)
        {
            log.LogMethodEntry(transactionIdList);
            PaymentTransactionDataHandler transactionDataHandler = new PaymentTransactionDataHandler(executionContext, unitOfWork);
            List<PaymentTransactionDTO> result = transactionDataHandler.GetPaymentTransactionDTOListOfTransactions(transactionIdList, activeRecords);
            result = SiteContainerList.FromSiteDateTime(executionContext, result);
            log.LogMethodExit(result);
            return result;
        }
        public List<PaymentTransactionDTO> GetPaymentTransactionDTOListOfTransactionPayment(List<string> transactionIPaymentGuidList, bool activeRecords)
        {
            log.LogMethodEntry(transactionIPaymentGuidList);
            PaymentTransactionDataHandler transactionDataHandler = new PaymentTransactionDataHandler(executionContext, unitOfWork);
            List<PaymentTransactionDTO> result = transactionDataHandler.GetPaymentTransactionDTOListOfTransactionPayments(transactionIPaymentGuidList, activeRecords);
            result = SiteContainerList.FromSiteDateTime(executionContext, result);
            log.LogMethodExit(result);
            return result;
        }
        public void Save(List<PaymentTransactionDTO> PaymentTransactionDTOList)
        {
            log.LogMethodEntry(PaymentTransactionDTOList);
            PaymentTransactionDataHandler paymentTransactionDataHandler = new PaymentTransactionDataHandler(executionContext, unitOfWork);
            paymentTransactionDataHandler.Save(PaymentTransactionDTOList);
            log.LogMethodExit();
        }

        ///
        ///This method returns list of paymenttransaction
        ///
        public List<PaymentTransactionDTO> GetPaymentTransactionDTOList(List<KeyValuePair<PaymentTransactionDTO.SearchByParameters, string>> searchParameters)
        {
            log.LogMethodEntry(searchParameters);
            PaymentTransactionDataHandler paymentTransactionDataHandler = new PaymentTransactionDataHandler(executionContext, unitOfWork);
            List<PaymentTransactionDTO> result = paymentTransactionDataHandler.GetPaymentTransactionDTOList(searchParameters);      
            result = SiteContainerList.FromSiteDateTime(executionContext, result);
            log.LogMethodExit(result);
            return result;
        }
    }

}
