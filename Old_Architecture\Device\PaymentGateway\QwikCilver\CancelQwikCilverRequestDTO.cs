﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
    class CancelQwikCilverRequestDTO
    {
        public char InputType { get; set; }
        public int TransactionModeId { get; set; }
        public string IdempotencyKey { get; set; }
        public string InvoiceNumber { get; set; }
        public List<CardsRequestDTO> Cards { get; set; }
        public string Notes { get; set; }
        public List<OriginalTransactionRequestDTO> originalRequestDTO { get; set; }
    }
}
