﻿/********************************************************************************************
 * Project Name - Transaction
 * Description  - Payment Transaction statuses.  
 *  
 **************
 **Version Log
 **************
 *Version     Date              Modified By              Remarks          
 *********************************************************************************************
 2.160.0      1-Jun-2021        Dakshakh Raj             Created
 ********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public enum PaymentTransactionStatuses
    {
        /// <summary>
        /// This would be status when the transaction is successfull.
        /// </summary>
        SUCCESS,
        /// <summary>
        /// This would be status when the transaction fails
        /// </summary>
        ERROR,
        /// <summary>
        /// This would be status when the transaction is cancelled
        /// </summary>
        CANCELLED,
        /// <summary>
        /// This would be status when the transaction is failed
        /// </summary>
        FAILED,
        /// <summary>
        /// To indicate a respose received from payment gateway
        /// </summary>
        RESPONSE_RECEIVED,
        /// <summary>
        /// This would be status when the transaction is pending
        /// </summary>
        PENDING,
        /// <summary>
        /// This would be status when the transaction requires pre-authorization (e.g., 3DS authentication, redirects)
        /// Used for complex payment flows where additional actions are required before completing the payment
        /// </summary>
        PRE_AUTH,
    }
}
