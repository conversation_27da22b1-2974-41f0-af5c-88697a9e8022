﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Globalization;


namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PayMaya
{
    public class PayMayaHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private readonly string publicKey;
        private readonly string secretKey;
        private readonly string baseUrl;
        private readonly string apiUrl;
        private readonly string voidUrl = "/voids";
        private readonly string refundUrl = "/refunds";


        public PayMayaHostedCommandHandler(string publicKey, string secretKey, string baseUrl, string apiUrl)
        {
            this.publicKey = publicKey;
            this.secretKey = secretKey;
            this.baseUrl = baseUrl;
            this.apiUrl = apiUrl;
        }
        public PayMayaHostedCheckoutResponseDto CreateCheckout(PayMayaHostedPaymentRequestDto requestDto)
        {
            log.LogMethodEntry(requestDto.ToString());
            WebRequestClient webRequestClient = new WebRequestClient(apiUrl, HttpVerb.POST, requestDto);
            webRequestClient.ContentType = "application/json";
            webRequestClient.IsBasicAuthentication = true;
            webRequestClient.Username = publicKey;

            string responseFromServer = webRequestClient.MakeRequest();
            if (string.IsNullOrWhiteSpace(responseFromServer))
            {
                log.Error("Error occurred while creating your payment request");
                throw new Exception("Error occurred while creating your payment request");
            }
            PayMayaHostedCheckoutResponseDto checkoutResponseDto = JsonConvert.DeserializeObject<PayMayaHostedCheckoutResponseDto>(responseFromServer);
            log.LogMethodExit(checkoutResponseDto.ToString());
            return checkoutResponseDto;

        }

        public PayMayaHostedPaymentResponseDto CreateTrxSearch(string reference)
        {
            log.LogMethodEntry(reference);
            PayMayaHostedPaymentResponseDto checkoutResponseDto = null;
            try
            {
                if (reference == null)
                {
                    log.Error("Error occurred while processing your payment: TrxId is null");
                    throw new Exception("Error occurred while processing your payment");
                }

                string API_URL = baseUrl + "/" + reference;
                string responseFromServer;

                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.GET);
                webRequestClient.ContentType = "application/json";
                webRequestClient.IsBasicAuthentication = true;
                webRequestClient.Username = secretKey;

                responseFromServer = webRequestClient.GetResponse();
                List<PayMayaHostedPaymentResponseDto> checkoutResponseDtoList = JsonConvert.DeserializeObject<List<PayMayaHostedPaymentResponseDto>>(responseFromServer);
                // sort based on updatedAt - pick the latest record
                if (checkoutResponseDtoList != null)
                {
                    checkoutResponseDto = checkoutResponseDtoList
                                               .OrderByDescending(trx => DateTime.Parse(trx.updatedAt))
                                               .FirstOrDefault();
                }
                else
                {
                    log.Error("No transaction elements found in the response.");
                    log.LogMethodExit("Filtered Transaction Details: null");
                    return null;
                }

            }
            catch (Exception ex)
            {
                log.Error("An error occurred: " + ex.Message);
                throw;
            }
            log.LogMethodExit(checkoutResponseDto.ToString());
            return checkoutResponseDto;
        }

        public string ErrorForm(Dictionary<string, string> errorParams)
        {
            try
            {
                StringBuilder builder = new StringBuilder();
                builder.Append("<html><head>");
                builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
                builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
                builder.Append("</head><body onload=\"document.PaymentFailureForm.submit()\">");
                builder.Append("<form name=\"PaymentFailureForm\" method=\"GET\" action=\"/account/checkouterror\">");

                foreach (KeyValuePair<string, string> param in errorParams)
                {
                    builder.Append(string.Format("<input type=\"hidden\" name=\"{0}\" value=\"{1}\">", param.Key, param.Value));
                }

                builder.Append("</form>");
                builder.Append("</body></html>");

                log.LogMethodExit();
                return builder.ToString();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// Prepares a gateway request string as an HTML page that automatically redirects the user to the specified checkout URL.
        /// </summary>
        /// <param name="checkoutURL">The URL to redirect the user to for the checkout process.</param>
        /// <returns>
        /// Returns a string representing the HTML page with JavaScript to perform the redirection.
        /// </returns>
        public string PrepareGatewayRequestString(string checkoutURL)
        {
            log.LogMethodEntry("PrepareGatewayRequestString()" + checkoutURL);
            StringBuilder strBuilder = new StringBuilder();

            strBuilder.Append("<html>");
            strBuilder.Append("<body onload=\"loadPayment()\">");
            strBuilder.Append("<script>function loadPayment() { ");
            strBuilder.Append($"window.location.replace(\"{checkoutURL}\");");
            strBuilder.Append("}</script>");

            strBuilder.Append("</body></html>");
            log.LogMethodExit();
            return strBuilder.ToString();

        }

        public string GetPaymentStatusDescription(string status)
        {
            switch (status)
            {
                case "PENDING_TOKEN":
                    return "Initial status of the authorization for all payments awaiting customer action for authorization.";
                case "PENDING_PAYMENT":
                    return "Initial payment status of the checkout transaction.";
                case "FOR_AUTHENTICATION":
                    return "Payment option requires further authentication before processing the payment transaction.";
                case "AUTHENTICATING":
                    return "Authentication is ongoing.";
                case "AUTH_SUCCESS":
                    return "Authentication has been successfully executed.";
                case "AUTH_FAILED":
                    return "Authentication of payment has failed.";
                case "PAYMENT_EXPIRED":
                    return "Payment transaction not executed, and expiration time has been reached.";
                case "PAYMENT_PROCESSING":
                    return "Transaction has been sent to the payment processor but has not yet completed execution.";
                case "PAYMENT_SUCCESS":
                    return "Payment is successfully processed.";
                case "PAYMENT_FAILED":
                    return "Payment is not successfully processed.";
                case "PAYMENT_CANCELLED":
                    return "Payment is cancelled with no credit/debit card or e-wallet deduction made.";
                case "VOIDED":
                    return "Successfully processed payment has been reversed before settlement cut-off.";
                case "REFUNDED":
                    return "Successfully processed payment has been fully or partially reversed after settlement cut-off.";
                case "SUCCESS":
                    return "Successfully initiated Refund/Void request.";
                default:
                    return "Transaction failed";
            }
        }

        public PayMayaHostedRefundVoidResponseDto CreateVoid(string reference, PayMayaHostedVoidRequestDto requestDto)
        {
            log.LogMethodEntry(reference);
            PayMayaHostedRefundVoidResponseDto refundResponseDto = null;
            try
            {
                if (reference == null)
                {
                    log.Error("Error occurred while processing your payment: TrxId is null");
                    throw new Exception("Error occurred while processing your payment");
                }

                string API_URL = baseUrl + "/" + reference + voidUrl;
                string responseFromServer;

                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.POST, requestDto);
                webRequestClient.ContentType = "application/json";
                webRequestClient.IsBasicAuthentication = true;
                webRequestClient.Username = secretKey;

                responseFromServer = webRequestClient.MakeRequest();
                refundResponseDto = JsonConvert.DeserializeObject<PayMayaHostedRefundVoidResponseDto>(responseFromServer);

            }
            catch (Exception ex)
            {
                log.Error("An error occurred: " + ex.Message);
                throw;
            }
            log.LogMethodExit(refundResponseDto.ToString());
            return refundResponseDto;
        }

        public PayMayaHostedRefundVoidResponseDto CreateRefund(string reference, PayMayaHostedRefundRequestDto requestDto)
        {
            log.LogMethodEntry(reference);
            PayMayaHostedRefundVoidResponseDto refundResponseDto = null;
            try
            {
                if (reference == null)
                {
                    log.Error("Error occurred while processing your payment: TrxId is null");
                    throw new Exception("Error occurred while processing your payment");
                }

                string API_URL = baseUrl + "/" + reference + refundUrl;
                string responseFromServer;
                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.POST, requestDto);
                webRequestClient.ContentType = "application/json";
                webRequestClient.IsBasicAuthentication = true;
                webRequestClient.Username = secretKey;

                responseFromServer = webRequestClient.MakeRequest();
                refundResponseDto = JsonConvert.DeserializeObject<PayMayaHostedRefundVoidResponseDto>(responseFromServer);
            }
            catch (Exception ex)
            {
                log.Error("An error occurred: " + ex.Message);
                throw;
            }
            log.LogMethodExit(refundResponseDto.ToString());
            return refundResponseDto;
        }
    }
}
