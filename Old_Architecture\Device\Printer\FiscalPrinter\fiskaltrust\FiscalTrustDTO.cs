﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.Printer.FiscalPrinter.fiskaltrust
{
    /// <summary>
    /// FiscalTrustDTO
    /// </summary>
    public class FiscalTrustDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private int requestId;
        private int posId;
        private string posName;
        private string computerName;
        private string status;
        private string data;
        private string remarks;

        /// <summary>
        /// Default Constructor
        /// </summary>
        public FiscalTrustDTO()
        {
            log.LogMethodEntry();
            this.requestId = -1;
            this.posId = -1; 
            log.LogMethodExit();
        }

        /// <summary>
        /// Parameterized Contructor
        /// </summary>
        public FiscalTrustDTO(int requestId, int posId, string posName, string computerName, string status, string data, string remarks)
        {
            log.LogMethodEntry(requestId, posId, posName, computerName, status, data, remarks);
            this.requestId = requestId;
            this.posId = posId;
            this.posName = posName;
            this.computerName = computerName;
            this.status = status;
            this.data = data;
            this.remarks = remarks;
            log.LogMethodExit();
        }
        /// <summary>
        /// Parameterized Contructor
        /// </summary>
        public FiscalTrustDTO(FiscalTrustDTO fiscalTrustDTO): this(fiscalTrustDTO.requestId, fiscalTrustDTO.posId, fiscalTrustDTO.posName, fiscalTrustDTO.computerName,
            fiscalTrustDTO.status, fiscalTrustDTO.data, fiscalTrustDTO.remarks)
        {
            log.LogMethodEntry(fiscalTrustDTO); 
            log.LogMethodExit();
        }
        /// <summary>
        /// Set and get properties for RequestId
        /// </summary>
        public int RequestId { get { return requestId; } set { requestId = value; } }

        /// <summary>
        /// Set and get properties for POSId
        /// </summary>
        public int POSId { get { return posId; } set { posId = value; } }

        /// <summary>
        /// Set and get properties for POSName
        /// </summary>
        public string POSName { get { return posName; } set { posName = value; } }

        /// <summary>
        /// Set and get properties for ComputerName
        /// </summary>
        public string ComputerName { get { return computerName; } set { computerName = value; } }

        /// <summary>
        /// Set and get properties for Status
        /// </summary>
        public string Status { get { return status; } set { status = value; } }

        /// <summary>
        /// Set and get properties for Data
        /// </summary>
        public string Data { get { return data; } set { data = value; } }
        /// <summary>
        /// Set and get properties for Remarks
        /// </summary>
        public string Remarks { get { return remarks; } set { remarks = value; } }
    }
}
