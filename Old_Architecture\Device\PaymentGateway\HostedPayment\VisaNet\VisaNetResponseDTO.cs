﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.VisaNet
{

    #region VisaNetResponseDTO
    /// <summary>
    /// VisaNet Response DTO
    /// </summary>
    public class VisaNetResponseDTO
    {
        public VisaNetHeader header { get; set; }
        public VisaNetFulfillment fulfillment { get; set; }
        public VisaNetOrder order { get; set; }
        public VisaNetDataMap dataMap { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    #region VisaNetDataMap
    public class VisaNetDataMap
    {
        public string TERMINAL { get; set; }
        public string TRACE_NUMBER { get; set; }
        public string ECI_DESCRIPTION { get; set; }
        public string MERCHANT { get; set; }
        public string CARD { get; set; }
        public string QUOTA_NI_DISCOUNT { get; set; }
        public string STATUS { get; set; }
        public string ACTION_DESCRIPTION { get; set; }
        public string ID_UNICO { get; set; }
        public string AMOUNT { get; set; }
        public string QUOTA_NUMBER { get; set; }
        public string QUOTA_NI_AMOUNT { get; set; }
        public string QUOTA_NI_PROGRAM { get; set; }
        public string AUTHORIZATION_CODE { get; set; }
        public string CURRENCY { get; set; }
        public string TRANSACTION_DATE { get; set; }
        public string ACTION_CODE { get; set; }
        public string BIN { get; set; }
        public string ECI { get; set; }
        public string BRAND { get; set; }
        public string QUOTA_NI_TYPE { get; set; }
        public string AUTHORIZED_AMOUNT { get; set; }
        public string QUOTA_AMOUNT { get; set; }
        public string ADQUIRENTE { get; set; }
        public string SETTLEMENT { get; set; }
        public string TRANSACTION_ID { get; set; }
        public string QUOTA_NI_MESSAGE { get; set; }
        public string QUOTA_DEFERRED { get; set; }
        public string ORIGINAL_DATETIME { get; set; }
        public string PROCESS_CODE { get; set; }
    }
    #endregion VisaNetDataMap

    #region VisaNetFulfillment
    public class VisaNetFulfillment
    {
        public string channel { get; set; }
        public string merchantId { get; set; }
        public string terminalId { get; set; }
        public string captureType { get; set; }
        public bool countable { get; set; }
        public bool fastPayment { get; set; }
        public string signature { get; set; }
    }
    #endregion VisaNetFulfillment

    #region VisaNetHeader
    public class VisaNetHeader
    {
        public string ecoreTransactionUUID { get; set; }
        public long ecoreTransactionDate { get; set; }
        public int millis { get; set; }
    }
    #endregion VisaNetHeader

    #region VisaNetOrder
    public class VisaNetOrder
    {
        public string purchaseNumber { get; set; }
        public double amount { get; set; }
        public string currency { get; set; }
        public string externalTransactionId { get; set; }
        public double authorizedAmount { get; set; }
        public string authorizationCode { get; set; }
        public string actionCode { get; set; }
        public string status { get; set; }
        public string traceNumber { get; set; }
        public string transactionDate { get; set; }
        public string transactionId { get; set; }
        public string originalTraceNumber { get; set; }
        public string originalDateTime { get; set; }
    }
    #endregion VisaNetOrder

    #endregion VisaNetResponseDTO
}
