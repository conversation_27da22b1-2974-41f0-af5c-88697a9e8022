﻿/********************************************************************************************
 * Project Name - Hosted Payment
 * Description  - Payment Handler for card connect
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Semnox.Core.Utilities;
using Semnox.Parafait.Languages;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.ViewContainer;

namespace Semnox.Parafait.PaymentGateway
{
    public class CardConnectCallbackHostedPaymentGateway : CardConnectHostedPaymentGateway
    {
        internal override string paymentGatewayName { get { return "CardConnectCallbackHostedPayment"; } }
        public CardConnectCallbackHostedPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken)
            : base(executionContext, isUnattended, cancellationToken)
        {
            log.LogMethodEntry(executionContext, writeToLogDelegate);
            log.Debug("Pagement Gateway " + paymentGatewayName);
            log.LogMethodExit(null);
        }
    }

    public class CardConnectHostedPaymentGateway : WebHostedPaymentGateway
    {
        internal readonly Semnox.Parafait.logging.Logger log;
        internal virtual string paymentGatewayName { get { return "CardConnectHostedPayment"; } }

        public CardConnectHostedPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken)
            : base(executionContext, isUnattended, cancellationToken)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, writeToLogDelegate);
            log.Debug("Pagement Gateway " + paymentGatewayName);
            this.InitConfigurations();
            log.LogMethodExit(null);
        }

        public override Boolean RedirectResponseToWebsite()
        {
            log.LogMethodEntry();
            log.LogMethodExit(false);
            return false;
        }

        private void InitConfigurations()
        {
            log.LogMethodEntry();
            log.Debug("Initializing config for " + paymentGatewayName);
            int tempNum = 15;
            this.WebPaymentGatewayConfiguration = new WebPaymentGatewayConfiguration();
            this.WebPaymentGatewayConfiguration.MerchantId = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID");
            this.WebPaymentGatewayConfiguration.GatewayUserNamePubKey = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CARD_CONNECT_HOSTED_PAYMENT_USER_NAME");
            this.WebPaymentGatewayConfiguration.GatewayPasswordSecretKey = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CARD_CONNECT_HOSTED_PAYMENT_PASSWORD");
            this.WebPaymentGatewayConfiguration.GatewayAPIURL = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CARD_CONNECT_HOSTED_PAYMENT_BASE_URL");
            this.WebPaymentGatewayConfiguration.IsPinCodeValidationRequired = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "ENABLE_ADDRESS_VALIDATION") == "Y" ? true : false;
            this.WebPaymentGatewayConfiguration.TransactionTimeLimit = (int.TryParse((ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_TRX_WINDOW_TIME")), out tempNum)) ? tempNum : 15;
            this.WebPaymentGatewayConfiguration.IsCaptchEnabled = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "ENABLE_GOOGLE_RECAPTCHA") == "Y" ? true : false;
            if (this.WebPaymentGatewayConfiguration.IsCaptchEnabled)
            {
                this.WebPaymentGatewayConfiguration.CaptchaIdentifier = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "GOOGLE_RECAPTCHA_CLIENT_ID");
                this.WebPaymentGatewayConfiguration.CaptchaURL = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "GOOGLE_RECAPTCHA_URL");
            }

            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.MerchantId))
            {
                log.Error("Please enter CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID value in configuration."));
            }
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayUserNamePubKey))
            {
                log.Error("Please enter CARD_CONNECT_HOSTED_PAYMENT_USER_NAME value in configuration.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter CARD_CONNECT_HOSTED_PAYMENT_USER_NAME value in configuration."));
            }
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayPasswordSecretKey))
            {
                log.Error("Please enter CARD_CONNECT_HOSTED_PAYMENT_PASSWORD value in configuration.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter CARD_CONNECT_HOSTED_PAYMENT_PASSWORD value in configuration."));
            }
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayAPIURL))
            {
                log.Error("Please enter CARD_CONNECT_HOSTED_PAYMENT_BASE_URL value in configuration.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter CARD_CONNECT_HOSTED_PAYMENT_BASE_URL value in configuration."));
            }

            log.Debug("Building lookups");
            LookupsContainerDTO lookupValuesList = LookupsViewContainerList.GetLookupsContainerDTO(ExecutionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            if (lookupValuesList == null)
            {
                log.Error("WEB_PAYMENT_CONFIGURATION lookup not found.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "WEB_PAYMENT_CONFIGURATION lookup not found."));
            }
            List<LookupValuesContainerDTO> lookupValuesDTOlist = lookupValuesList.LookupValuesContainerDTOList;
            if (lookupValuesDTOlist == null || !lookupValuesDTOlist.Any())
            {
                log.Error("WEB_PAYMENT_CONFIGURATION lookup not found.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "WEB_PAYMENT_CONFIGURATION lookup not found."));
            }

            log.Debug("Built lookups");
            String apiSite = "";
            String webSite = "";
            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").Count() > 0)
            {
                apiSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").First().Description;
            }
            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").Count() > 0)
            {
                webSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").First().Description;
            }

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEB_PAYMENT_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter WEB_PAYMENT_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").Count() > 0)
            {
                String linkPage = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").First().Description;
                linkPage = linkPage.Replace("@gateway", paymentGatewayName);
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL = webSite + linkPage;
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink = webSite + linkPage;
            }
            else
            {
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL = webSite + "/payment/cardconnecthostedpayment?payload=@payLoad&siteId=@siteId&posMachine=@posMachine";
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink = webSite + "/payment/cardconnecthostedpayment?payload=@payLoad&siteId=@siteId&posMachine=@posMachine";
            }

            log.Debug(this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL);
            log.Debug(this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink);

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.SuccessURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").First().Description.Replace("@gateway", this.paymentGatewayName);
                //log.Debug("successResponseAPIURL " + successResponseAPIURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.FailedURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").First().Description.Replace("@gateway", this.paymentGatewayName);
                //log.Debug("failureResponseAPIURL " + failureResponseAPIURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.CancelURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").First().Description.Replace("@gateway", this.paymentGatewayName);
                //log.Debug("cancelResponseAPIURL " + cancelResponseAPIURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.CallbackURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").First().Description.Replace("@gateway", this.paymentGatewayName);
                //log.Debug("callbackResponseAPIURL " + callbackResponseAPIURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").Count() == 1)
            {
                this.WebPaymentGatewayConfiguration.PaymentSucceededURL = lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").First().Description;
                //log.Debug("SUCCESS_REDIRECT_URL " + successRedirectURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").Count() == 1)
            {
                this.WebPaymentGatewayConfiguration.PaymentFailedURL = lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").First().Description;
                //log.Debug("FAILURE_REDIRECT_URL " + successRedirectURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").Count() == 1)
            {
                this.WebPaymentGatewayConfiguration.PaymentCancelledURL = lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").First().Description;
                //log.Debug("FAILURE_REDIRECT_URL " + successRedirectURL);
            }

            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.SuccessURL) ||
                string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.FailedURL) ||
                string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.CancelURL) ||
                string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.CallbackURL) ||
                string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.PaymentSucceededURL) ||
                string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.PaymentFailedURL))
            {
                log.Error("Please enter WEB_PAYMENT_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CancelURL/CallBackURL/PaymentSucceededURL/PaymentFailedURL.");
                log.Debug(this.WebPaymentGatewayConfiguration.SuccessURL);
                log.Debug(this.WebPaymentGatewayConfiguration.FailedURL);
                log.Debug(this.WebPaymentGatewayConfiguration.CancelURL);
                log.Debug(this.WebPaymentGatewayConfiguration.CallbackURL);
                log.Debug(this.WebPaymentGatewayConfiguration.PaymentSucceededURL);
                log.Debug(this.WebPaymentGatewayConfiguration.PaymentFailedURL);
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter WEB_PAYMENT_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CancelURL/CallBackURL/PaymentSucceededURL/PaymentFailedURL."));
            }

            log.LogMethodExit(this.WebPaymentGatewayConfiguration);
        }

        public override HostedPaymentRequestDTO CreateGatewayPaymentRequest(CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO)
        {
            log.LogMethodEntry(createHostedPaymentRequestDTO);
            HostedPaymentRequestDTO HostedPaymentRequestDTO = GetHostedPaymentRequestDTO(createHostedPaymentRequestDTO, this.paymentGatewayName);

            HostedPaymentRequestDTO.WebsitePaymentPageURL = this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL;
            String GatewayRequestJSON = JsonConvert.SerializeObject(SetPostParameters(HostedPaymentRequestDTO));
            HostedPaymentRequestDTO.WebsitePaymentPageLink = this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink
                                                                .Replace("@payLoad", Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(GatewayRequestJSON)))
                                                                .Replace("@siteId", HostedPaymentRequestDTO.SiteId.ToString())
                                                                .Replace("@posMachine", HostedPaymentRequestDTO.POSMachine.ToString());
            HostedPaymentRequestDTO.GatewayRequestHTML = GetSubmitFormKeyValueList(null,
                                                                HostedPaymentRequestDTO.WebsitePaymentPageLink,
                                                                "frmPaymentForm");
            log.LogMethodExit(HostedPaymentRequestDTO);
            return HostedPaymentRequestDTO;
        }

        public override HostedPaymentResponseDTO ProcessGatewayResponse(String paymentGatewayResponse)
        {
            log.LogMethodEntry(paymentGatewayResponse);
            HostedPaymentResponseDTO HostedPaymentResponseDTO = GetHostedPaymentResponseDTO();
            HostedPaymentResponseDTO.RedirectResponseToWebsite = RedirectResponseToWebsite();

            PaymentTransactionDTO PaymentTransactionDTO = new PaymentTransactionDTO();
            bool isPaymentSuccess = false;
            try
            {
                // replace the merchant id in the response string
                log.Debug("Initiate Response processing");
                paymentGatewayResponse = paymentGatewayResponse.Replace("@merchantId", this.WebPaymentGatewayConfiguration.MerchantId);
                dynamic response = JsonConvert.DeserializeObject(paymentGatewayResponse);
                String customerName = response.name;
                String currency_Code = response.currency;
                String tempAccount = response.account;
                String tempToken = response.token;
                // specific validation for card connect for security purpose
                if (tempAccount.Length < 16 || !tempAccount.StartsWith("9") || !tempAccount.Equals(tempToken))
                {
                    log.Error("The response contains invalid information " + paymentGatewayResponse);
                    throw new PaymentGatewayInvalidCardException("Payment has been rejected. Invalid card.");
                }

                string paymentIdentifier = "";
                string captchToken = "";
                Int64 ticks = 0;
                try
                {
                    string fields = response.userfields;
                    dynamic userfields = JsonConvert.DeserializeObject(fields);
                    captchToken = userfields.captchToken;
                    String trxIdString = userfields.transactionId;
                    trxIdString = Encryption.Decrypt(Encoding.UTF8.GetString(Convert.FromBase64String(trxIdString)));
                    if (!String.IsNullOrWhiteSpace(trxIdString))
                    {
                        paymentIdentifier = trxIdString.Substring(0, trxIdString.IndexOf(":"));
                        ticks = Convert.ToInt64(trxIdString.Substring(trxIdString.IndexOf(":") + 1));
                    }
                }
                catch (Exception ex)
                {
                    log.Error("Error while getting trxId and captchToken" + ex);
                    throw new PaymentGatewayProcessingException("Payment has been rejected. Invalid card.");
                }

                log.Debug("paymentIdentifier:" + paymentIdentifier);
                log.Debug("ticks:" + ticks);
                HostedPaymentResponseDTO.TransactionPaymentGuid = paymentIdentifier;
                

                Int64 TrxWindow = Convert.ToInt64(ServerDateTime.Now.Subtract(new TimeSpan(0, this.WebPaymentGatewayConfiguration.TransactionTimeLimit, 0)).Ticks);
                if (ticks < TrxWindow)
                {
                    log.Error("Ticks is older than trx windown. Reject payment.");
                    throw new PaymentGatewayTransactionTimeoutException("Payment has been rejected.");
                }

                if (this.WebPaymentGatewayConfiguration.IsCaptchEnabled)
                {
                    if (!ValidateReCaptchaToken(captchToken))
                    {
                        log.Error("Captch validation failed " + captchToken);
                        throw new PaymentGatewayCaptchaValidationException("Payment has been rejected.");
                    }
                }

                //Add OrderId key to auth request payload with paymentIdentifier value
                response.orderid = paymentIdentifier;
                paymentGatewayResponse = JsonConvert.SerializeObject(response);

                log.Debug("Calling card connect API");
                //api call for card connect authorization 
                var resultJson = ExecuteAPIRequest(this.WebPaymentGatewayConfiguration.GatewayAPIURL + "cardconnect/rest/auth", paymentGatewayResponse, "PUT");

                String cardConnectReference = "", accountNo = "", amount = "", profileid = "", ccnumber = "", ccexpiry = "", acctId = "", ccauth = "", respTest = "";
                if (resultJson["retref"] != null)
                {
                    cardConnectReference = resultJson["retref"];
                }
                if (resultJson["account"] != null)
                {
                    accountNo = resultJson["account"];
                    string maskedAccount = (new String('X', 12) + ((accountNo.Length > 4)
                    ? accountNo.Substring(accountNo.Length - 4)
                    : accountNo));
                    accountNo = maskedAccount;
                }
                if (resultJson["amount"] != null)
                {
                    amount = resultJson["amount"];
                }
                if (resultJson["profileid"] != null)
                {
                    profileid = resultJson["profileid"];
                }
                if (resultJson["token"] != null)
                {
                    ccnumber = resultJson["token"];
                }
                if (resultJson["expiry"] != null)
                {
                    ccexpiry = resultJson["expiry"];
                }
                if (resultJson["acctid"] != null)
                {
                    acctId = resultJson["acctid"];
                }
                if (resultJson["authcode"] != null)
                {
                    ccauth = resultJson["authcode"];
                }
                if (resultJson["resptext"] != null)
                {
                    respTest = resultJson["resptext"];
                }

                // Nitin - Why do we need this?
                // Issue is that payment is already completed here. If this call fails, payment is being treated as not handled
                {
                    //call to inquiry api of card connect to get transaction details
                    var orderInquiryResultJson = GetOrderDetail(cardConnectReference);
                    string userfield = (orderInquiryResultJson["userfields"]);
                    dynamic postdata = JsonConvert.DeserializeObject(userfield);
                    string orderInquiryPaymentIdentifier = "";
                    String trxIdString1 = postdata.transactionId;
                    trxIdString1 = Encryption.Decrypt(Encoding.UTF8.GetString(Convert.FromBase64String(trxIdString1)));
                    if (!String.IsNullOrWhiteSpace(trxIdString1))
                    {
                        orderInquiryPaymentIdentifier = trxIdString1.Substring(0, trxIdString1.IndexOf(":"));
                    }

                    if (!orderInquiryPaymentIdentifier.Equals(paymentIdentifier, StringComparison.InvariantCultureIgnoreCase))
                    {
                        log.Error("Payment validation failed. The reference belongs to a different transaction. " + orderInquiryPaymentIdentifier);
                        throw new PaymentGatewayProcessingException("Payment validation failed. The reference belongs to a different transaction. ");
                    }
                }

                PaymentTransactionDTO.RefNo = cardConnectReference;
                if (resultJson["respstat"] != null && resultJson["respstat"] == "A" && resultJson["resptext"] != null && resultJson["resptext"] == "Approval")
                {
                    isPaymentSuccess = true;
                }
                else
                {
                    // Nitin - Should a revalidation be done here?
                }


                if (isPaymentSuccess == false)
                {
                    PaymentTransactionDTO.RecordNo = "C";
                    PaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                    HostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.FAILED;
                }
                else
                {
                    PaymentTransactionDTO.RecordNo = "A";
                    PaymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                    HostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.SUCCESS;
                }

                PaymentTransactionDTO.RefNo = cardConnectReference;
                PaymentTransactionDTO.AcqRefData += "|Name:" + customerName + "|EXP:" + ccexpiry + "|ZIP:" /*+ pin*/;
                PaymentTransactionDTO.Purchase = String.Format("{0:0.00}", amount);
                PaymentTransactionDTO.Authorize = amount;
                Decimal approvedAmount = 0.0M;
                Decimal.TryParse(amount, out approvedAmount);
                PaymentTransactionDTO.Amount = approvedAmount;
                PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                PaymentTransactionDTO.ProcessData = profileid;
                PaymentTransactionDTO.AuthCode = ccauth;
                PaymentTransactionDTO.DSIXReturnCode = respTest;
                PaymentTransactionDTO.TextResponse = respTest;

                // CC Info
                PaymentTransactionDTO.NameOnCreditCard = customerName;
                PaymentTransactionDTO.AcctNo = ccnumber;
                PaymentTransactionDTO.CustomerCardProfileId = profileid;
                PaymentTransactionDTO.CreditCardExpiry = resultJson["expiry"];
                PaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
            }
            catch (Exception ex) when (ex is PaymentGatewayProcessingException
                                        || ex is PaymentGatewayTransactionTimeoutException
                                        || ex is PaymentGatewayCaptchaValidationException
                                        || ex is PaymentGatewayInvalidCardException)
            {
                log.Debug("Caught a processing error " + ex);
                PaymentTransactionDTO.RecordNo = "C";
                PaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                HostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                PaymentTransactionDTO.Purchase = "0";
                PaymentTransactionDTO.Authorize = "0";
                PaymentTransactionDTO.Amount = 0.0M;
                PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                PaymentTransactionDTO.DSIXReturnCode = ex.Message;
                PaymentTransactionDTO.TextResponse = ex.Message;
            }
            catch (Exception ex)
            {
                log.Error("Error occurred while processing payment: " + ex.Message);
                PaymentTransactionDTO.RecordNo = "C";
                PaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                HostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                PaymentTransactionDTO.Purchase = "0";
                PaymentTransactionDTO.Authorize = "0";
                PaymentTransactionDTO.Amount = 0.0M;
                PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                PaymentTransactionDTO.DSIXReturnCode = ex.Message;
                PaymentTransactionDTO.TextResponse = ex.Message;
            }

            HostedPaymentResponseDTO.PaymentTransactionDTO = PaymentTransactionDTO;
            log.LogMethodExit(HostedPaymentResponseDTO);
            return HostedPaymentResponseDTO;
        }

        public override string GetPaymentIdentifier(String paymentGatewayResponse)
        {
            log.LogMethodEntry(paymentGatewayResponse);
            String paymentGatewayIdentifier = string.Empty;

            dynamic response = JsonConvert.DeserializeObject(paymentGatewayResponse);
            try
            {
                string fields = response.userfields;
                dynamic userfields = JsonConvert.DeserializeObject(fields);
                String trxIdString = userfields.transactionId;
                trxIdString = Encryption.Decrypt(Encoding.UTF8.GetString(Convert.FromBase64String(trxIdString)));
                if (!String.IsNullOrWhiteSpace(trxIdString))
                {
                    paymentGatewayIdentifier = trxIdString.Substring(0, trxIdString.IndexOf(":"));
                }
            }
            catch (Exception ex)
            {
                log.Error("Error while getting trxId and captchToken" + ex);
            }

            log.LogMethodExit(paymentGatewayIdentifier);
            return paymentGatewayIdentifier;
        }

        public override HostedPaymentResponseDTO GetPaymentStatus(string transactionPaymentGuid)
        {
            log.LogMethodEntry();
            HostedPaymentResponseDTO statusDTO = new HostedPaymentResponseDTO();
            CardConnectHostedPaymentSearchResponseDTO paymentSearchResponseDTO = null;
            try
            {
                if (string.IsNullOrWhiteSpace(transactionPaymentGuid))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    throw new PaymentGatewayProcessingException("Insufficient Params passed to the request!");
                }

                statusDTO.RedirectResponseToWebsite = RedirectResponseToWebsite();
                statusDTO.TransactionPaymentGuid = transactionPaymentGuid;
                var resultJson = ExecuteAPIRequest(this.WebPaymentGatewayConfiguration.GatewayAPIURL + "cardconnect/rest/inquireByOrderid" + "/" + transactionPaymentGuid + "/" + this.WebPaymentGatewayConfiguration.MerchantId + "/1", "", "GET");

                string strResultJson = JsonConvert.SerializeObject(resultJson);
                log.Debug("Raw response of payment status search: " + strResultJson);

                bool isResponseArrayType = false;
                try
                {
                    log.Info("Identifying the response type...");
                    JToken resultJsonToken = JToken.Parse(strResultJson);
                    log.Debug("Response type: " + resultJsonToken.Type);
                    isResponseArrayType = resultJsonToken.Type == JTokenType.Array;
                }
                catch (Exception ex)
                {
                    log.Error("Defaulting response type to object type. Error: " + ex);
                }

                if (isResponseArrayType)
                {
                    List<CardConnectHostedPaymentSearchResponseDTO> paymentSearchResponseDTOList = JsonConvert.DeserializeObject<List<CardConnectHostedPaymentSearchResponseDTO>>(strResultJson);
                    log.Debug("Raw payment status search from cardconnect api: " + paymentSearchResponseDTOList.ToString());

                    paymentSearchResponseDTO = paymentSearchResponseDTOList.OrderByDescending(trx => trx.capturedate).First();
                    log.Debug("Latest payment status search from cardconnect api: " + paymentSearchResponseDTO.ToString());
                }
                else
                {
                    paymentSearchResponseDTO = JsonConvert.DeserializeObject<CardConnectHostedPaymentSearchResponseDTO>(strResultJson);
                    log.Debug("Payment status search from cardconnect api: " + paymentSearchResponseDTO.ToString());
                }

                log.Info("Begin of mappedPaymentStatus");
                string rawPaymentStatus = paymentSearchResponseDTO?.respstat ?? "";
                log.Debug("Raw payment status: " + rawPaymentStatus);

                PaymentTransactionStatuses mappedPaymentStatus = MapPaymentStatus(rawPaymentStatus, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of mapped PaymentStatus: " + mappedPaymentStatus.ToString());

                log.Info("Check if payment applied");
                if (paymentSearchResponseDTO.refundable == "Y")
                {
                    log.Info("Transaction can be refunded.");
                }
                else
                {
                    log.Debug("Transaction cannot be refunded");
                    mappedPaymentStatus = PaymentTransactionStatuses.FAILED;
                }
                log.Info("End of mappedPaymentStatus");


                statusDTO.PaymentTransactionStatus = mappedPaymentStatus;

                //Map payment status search response to HostedPaymentResponseDTO>paymentTransactionDTO
                PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO();
                paymentTransactionDTO.RefNo = paymentSearchResponseDTO?.retref ?? ""; // Set the pgw ref no value here in cCTransactionsPGWDTO.RefNo
                paymentTransactionDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                paymentTransactionDTO.InvoiceNo = transactionPaymentGuid;
                paymentTransactionDTO.Authorize = paymentTransactionDTO.Purchase = String.Format("{0:0.00}", double.Parse(paymentSearchResponseDTO?.amount));
                paymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                paymentTransactionDTO.ProcessData = paymentSearchResponseDTO?.profileid ?? "";
                paymentTransactionDTO.AuthCode = paymentSearchResponseDTO?.authcode ?? "";
                paymentTransactionDTO.DSIXReturnCode = paymentSearchResponseDTO?.resptext;
                paymentTransactionDTO.TextResponse = paymentSearchResponseDTO?.resptext;
                paymentTransactionDTO.TokenID = paymentSearchResponseDTO?.token ?? "";
                paymentTransactionDTO.AcctNo = paymentSearchResponseDTO?.account ?? "";
                paymentTransactionDTO.CustomerCardProfileId = paymentSearchResponseDTO?.profileid ?? "";
                paymentTransactionDTO.Status = mappedPaymentStatus.ToString(); //Based on payment status job decides whether payment os applied or not
                string ccTokenNum = Convert.ToString(paymentTransactionDTO.TokenID).Substring(1, 2); //Token 2nd and 3rd are original card 1st and 2nd digit
                string cardType = GetCardTypeHelper(ccTokenNum.Length > 0 ? ccTokenNum : "");
                log.Debug("Card type: " + cardType);
                paymentTransactionDTO.CardType = cardType;

                decimal amount = 0.0M;
                decimal.TryParse(paymentTransactionDTO.Purchase, out amount);
                paymentTransactionDTO.Amount = amount;

                log.Debug("Result paymentTransactionDTO: " + paymentTransactionDTO.ToString());

                //Assign paymentTransactionDTO
                statusDTO.PaymentTransactionDTO = paymentTransactionDTO;
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatus. Error message: " + ex);

                statusDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO
                {
                    InvoiceNo = transactionPaymentGuid,
                    Purchase = "0",
                    Authorize = "0",
                    Amount = 0.0M,
                    TransactionDatetime = ServerDateTime.Now,
                    TextResponse = "Error performing GetPaymentStatus!",
                    DSIXReturnCode = ex.Message,
                    Status = PaymentTransactionStatuses.ERROR.ToString()
                };
                statusDTO.PaymentTransactionDTO = paymentTransactionDTO;
            }
            log.LogMethodExit(statusDTO);
            return statusDTO;
        }

        private PaymentTransactionDTO BuildPaymentTransactionResponseDTO(TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO);
            PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO(-1, transactionPaymentDTO.TransactionId, transactionPaymentDTO.Guid, string.Empty,
                string.Empty, string.Empty,
                -1, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty,
                DateTime.Now, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, null, null, null, transactionPaymentDTO.Guid, true, PaymentTransactionStatuses.SUCCESS.ToString(), "", "", "", transactionPaymentDTO.Amount,-1);
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        /// <summary>
        /// Reverts the payment.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public override async Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO refundTransactionPaymentsDTO, PaymentTransactionDTO originalPaymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(refundTransactionPaymentsDTO, originalPaymentTransactionDTO);
            PaymentTransactionDTO refundPaymentTransactionDTO = new PaymentTransactionDTO();
            refundPaymentTransactionDTO.Purchase = refundTransactionPaymentsDTO.Amount.ToString();
            refundPaymentTransactionDTO.InvoiceNo = refundTransactionPaymentsDTO.Guid.ToString();

            try
            {
                string timestamp = ServerDateTime.Now.ToString("yyyyMMddHHmmss");
                Dictionary<string, Object> dict = new Dictionary<string, Object>();
                dict.Add("merchid", this.WebPaymentGatewayConfiguration.MerchantId);
                dict.Add("retref", refundTransactionPaymentsDTO.Reference);
                dict.Add("amount", refundTransactionPaymentsDTO.Amount);

                string postData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                //Refund API call for card connect authorization 
                dynamic resultJson = ExecuteAPIRequest(this.WebPaymentGatewayConfiguration.GatewayAPIURL + "cardconnect/rest/refund", postData, "PUT");

                if (resultJson["retref"] != null)
                {
                    refundPaymentTransactionDTO.RefNo = resultJson["retref"];
                }

                if (resultJson["amount"] != null)
                {
                    refundPaymentTransactionDTO.Authorize = resultJson["amount"];
                }
                if (resultJson["resptext"] != null)
                {
                    refundPaymentTransactionDTO.TextResponse = resultJson["resptext"];
                }
                if (resultJson["authcode"] != null)
                {
                    refundPaymentTransactionDTO.AuthCode = resultJson["authcode"];
                }
                if (resultJson["resptext"] != null)
                {
                    refundPaymentTransactionDTO.DSIXReturnCode = resultJson["resptext"];
                    refundPaymentTransactionDTO.TextResponse = resultJson["resptext"];
                }

                refundPaymentTransactionDTO.AcctNo = originalPaymentTransactionDTO != null ? originalPaymentTransactionDTO.AcctNo : "";
                refundPaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                refundPaymentTransactionDTO.CardType = originalPaymentTransactionDTO != null ? originalPaymentTransactionDTO.CardType : "";
                refundPaymentTransactionDTO.TransactionDatetime = DateTime.Now;
                refundPaymentTransactionDTO.RecordNo = originalPaymentTransactionDTO != null ? originalPaymentTransactionDTO.RecordNo : "";
                refundPaymentTransactionDTO.ResponseOrigin = originalPaymentTransactionDTO != null ? originalPaymentTransactionDTO.ResponseID.ToString() : "";
                refundPaymentTransactionDTO.TokenID = originalPaymentTransactionDTO != null ? originalPaymentTransactionDTO.AcctNo.ToString() : "";

                if (resultJson["resptext"] != null && resultJson["resptext"] == "Approval")
                {
                    refundPaymentTransactionDTO.CaptureStatus = "succeeded";
                    refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                }
                else if (resultJson["respstat"] != null && resultJson["respstat"] == "C")
                {
                    log.LogVariableState("Error mesaage", resultJson["resptext"]);
                    log.LogVariableState("Error Code", resultJson["respproc"]);
                    refundPaymentTransactionDTO.DSIXReturnCode = resultJson["respproc"];
                    refundPaymentTransactionDTO.TextResponse = resultJson["resptext"];
                    refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                }
                else
                {
                    refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(refundPaymentTransactionDTO);
            return refundPaymentTransactionDTO;
        }


        public override async Task<PaymentTransactionDTO> ReversePayment(TransactionPaymentDTO reversedTransactionPaymentDTO, IProgress<PaymentProgressReport> progress,
                                                           CancellationToken cancellationToken)
        {
            log.LogMethodEntry(reversedTransactionPaymentDTO);
            PaymentTransactionDTO originalPaymentTransactionDTO = BuildPaymentTransactionResponseDTO(reversedTransactionPaymentDTO);
            originalPaymentTransactionDTO.Status = PaymentTransactionStatuses.ERROR.ToString();
            originalPaymentTransactionDTO.TextResponse = MessageViewContainerList.GetMessage(ExecutionContext, "Payment Reverse is not supported.");
            log.LogMethodExit(originalPaymentTransactionDTO);
            return originalPaymentTransactionDTO;
        }

        private bool ValidateReCaptchaToken(string token)
        {
            String secretKey = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "GOOGLE_RECAPTCHA_SECRET_KEY");
            if (String.IsNullOrEmpty(secretKey) ||
                String.IsNullOrEmpty(this.WebPaymentGatewayConfiguration.CaptchaURL) ||
                string.IsNullOrEmpty(token))
            {
                log.Error("Google captch settings are not present ");
                throw new Exception("Payment Failed.");
            }

            try
            {
                using (WebClient webClient = new WebClient())
                {
                    ServicePointManager.Expect100Continue = true;
                    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12 | SecurityProtocolType.Ssl3;
                    webClient.Headers[HttpRequestHeader.ContentType] = "application/x-www-form-urlencoded";
                    string result = webClient.DownloadString(string.Format(this.WebPaymentGatewayConfiguration.CaptchaURL, secretKey, token));
                    log.Debug("captch result" + result);
                    dynamic response = JsonConvert.DeserializeObject(result);
                    bool captchResult = response.success;
                    log.LogMethodExit(captchResult);
                    return captchResult;
                }
            }
            catch (WebException webException)
            {
                log.Error(webException);
                if (webException.Response != null)
                {
                    using (HttpWebResponse errorResponse = (HttpWebResponse)webException.Response)
                    {
                        using (StreamReader streamReader = new StreamReader(errorResponse.GetResponseStream()))
                        {
                            string error = streamReader.ReadToEnd();
                            log.LogMethodExit(error);
                            log.Error("Google Captch failed " + error);
                            return false;
                        }
                    }
                }
                log.LogMethodExit();
                throw;
            }
        }

        /// <summary>
        /// GetSubmitFormKeyValueList
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            log.LogMethodEntry(postparamslist, URL, FormName, submitMethod);
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<style>.ProgressWaiting {position: fixed; background-color: #333; opacity: 0.8;overflow: hidden; font-size:20px;" +
                "text-align: center; top: 0; left: 0; height: 100%; width: 100%; padding-top: 20%; z-index: 2147483647 !important;" +
                "-webkit-transition: all 0.3s ease; -moz-transition: all 0.3s ease; -ms-transition: all 0.3s ease; -o-transition: all 0.3s ease;" +
                " transition: all 0.3s ease; color: ActiveBorder; }</style>");
            builder.Append("<meta http-equiv=\"refresh\" content=\"1; URL =");
            builder.Append(URL);
            builder.Append("\"/>");
            builder.Append("</head><body>");
            builder.Append(string.Format("<div id=\"pnlPaymentProcess\" style=\"padding: 50px; \"> <div class=\"ProgressWaiting\" style=\"padding-bottom: 50px;\">" +
                "<p>Redirecting to payment gateway. Do not click on back button or close the browser. </p>" +
                "<img src = \"/images/img/loading-indicator.svg\" alt=\"Please wait... \" /></div></div>"));
            builder.Append("</body></html>");
            log.LogMethodExit(builder.ToString());
            return builder.ToString();
        }
        /// <summary>
        /// Creates IDictionary 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns>IDictionary<string, string></returns>
        private IDictionary<string, string> SetPostParameters(HostedPaymentRequestDTO HostedPaymentRequestDTO)
        {
            log.LogMethodEntry();
            IDictionary<string, string> postparamslistOut = new Dictionary<string, string>();
            // do not send the sensitive information to the website
            postparamslistOut.Add("merchantId", "@merchantId");
            postparamslistOut.Add("currencyCode", HostedPaymentRequestDTO.CurrencyCode);
            postparamslistOut.Add("amount", String.Format("{0:0.00}", HostedPaymentRequestDTO.Amount));
            postparamslistOut.Add("transactionId", Convert.ToBase64String(
                                                        Encoding.UTF8.GetBytes(
                                                               Encryption.Encrypt(HostedPaymentRequestDTO.TransactionPaymentGuid.ToString() + ":" + ServerDateTime.Now.Ticks.ToString()))));
            postparamslistOut.Add("paymentModeId", HostedPaymentRequestDTO.PaymentModeId.ToString());
            postparamslistOut.Add("requestId", HostedPaymentRequestDTO.TransactionPaymentId.ToString());
            postparamslistOut.Add("api_post_url", HostedPaymentRequestDTO.GatewayAPIURL);

            if (HostedPaymentRequestDTO.SubscriptionAuthorizationMode == "N")
            {
                postparamslistOut.Add("capture", "Y");
                postparamslistOut.Add("ecomind", "Y");
                postparamslistOut.Add("cof", "C");
                postparamslistOut.Add("cofscheduled", "N");
            }
            else if (HostedPaymentRequestDTO.SubscriptionAuthorizationMode == "I")
            {
                postparamslistOut.Add("capture", "Y");
                postparamslistOut.Add("ecomind", "R");
                postparamslistOut.Add("profile", "Y");
                postparamslistOut.Add("cof", "M");
                if (HostedPaymentRequestDTO.Amount != 0)
                {
                    postparamslistOut.Add("cofscheduled", "Y");
                }
                else
                {
                    postparamslistOut.Add("cofscheduled", "N");
                }
            }
            else if (HostedPaymentRequestDTO.SubscriptionAuthorizationMode == "P")
            {
                postparamslistOut.Add("capture", "Y");
                postparamslistOut.Add("ecomind", "R");
                postparamslistOut.Add("profile", (HostedPaymentRequestDTO.PaymentGatewayCustomer != null ?
                                                    HostedPaymentRequestDTO.PaymentGatewayCustomer.CustomerIdentifier : ""));
                postparamslistOut.Add("cof", "M");
                if (HostedPaymentRequestDTO.Amount != 0)
                {
                    postparamslistOut.Add("cofscheduled", "Y");
                }
                else
                {
                    postparamslistOut.Add("cofscheduled", "N");
                }
            }
            postparamslistOut.Add("postalValidation", HostedPaymentRequestDTO.IsPinCodeValidationRequired ? "Y" : "N");
            postparamslistOut.Add("clientID", WebPaymentGatewayConfiguration.CaptchaIdentifier);
            postparamslistOut.Add("isRecaptchaEnabled", HostedPaymentRequestDTO.IsCaptchEnabled ? "Y" : "N");
            postparamslistOut.Add("requestGuid", HostedPaymentRequestDTO.TransactionPaymentGuid.ToString());
            postparamslistOut.Add("CallbackURL", HostedPaymentRequestDTO.CallbackURL.ToString());
            postparamslistOut.Add("ResponseURL", HostedPaymentRequestDTO.SuccessURL.ToString());
            log.LogMethodExit();
            return postparamslistOut;
        }

        /// <summary>
        /// Method for http post and get
        /// </summary>
        /// <param name="url"></param>
        /// <param name="postData"></param>
        /// <param name="method"></param>
        /// <returns></returns>
        private dynamic ExecuteAPIRequest(string url, string postData, string method)
        {
            log.LogMethodEntry(url, postData, method);
            try
            {
                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
                byte[] data = Encoding.ASCII.GetBytes(postData);
                req.Method = method; // Post method
                req.ContentType = "application/json";
                String apiKey = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(
                                this.WebPaymentGatewayConfiguration.GatewayUserNamePubKey + ":" + this.WebPaymentGatewayConfiguration.GatewayPasswordSecretKey));
                req.Headers.Add("Authorization", "Basic" + apiKey);
                if (!(method == "GET"))
                {
                    req.Accept = "application/json";
                    req.ContentLength = data.Length;
                    Stream requestStream = req.GetRequestStream();
                    requestStream.Write(data, 0, data.Length);
                    requestStream.Close();
                }
                WebResponse rsp = req.GetResponse();
                StreamReader responseStream = new StreamReader(rsp.GetResponseStream());
                string resultXml = responseStream.ReadToEnd();
                log.Debug(resultXml);
                var resultJson = JsonConvert.DeserializeObject(resultXml);
                log.LogMethodExit(resultJson);
                return resultJson;
            }
            catch (Exception ex)
            {
                log.Error("Got an error while calling the card connect api " + ex);
                throw;
            }
        }

        private dynamic GetOrderDetail(string cardConnectReference)
        {
            log.LogMethodEntry();
            var resultJson = ExecuteAPIRequest(this.WebPaymentGatewayConfiguration.GatewayAPIURL + "cardconnect/rest/inquire" + "/" + cardConnectReference + "/" + this.WebPaymentGatewayConfiguration.MerchantId, "", "GET");
            log.LogMethodExit(resultJson);
            return resultJson;
        }

        /// <summary>
        /// GetCreditCardExpiryMonth
        /// </summary>
        /// <param name="cardExpiryData"></param>
        private int GetCreditCardExpiryMonth(string cardExpiryData)
        {
            log.LogMethodEntry(cardExpiryData);
            int monthValue;
            if (string.IsNullOrWhiteSpace(cardExpiryData) || cardExpiryData.Length < 3
                || int.TryParse(cardExpiryData.Substring(0, 2), out monthValue) == false)
            {
                throw new ValidationException(MessageContainerList.GetMessage(ExecutionContext, 597));//Invalid date format in Expiry Date
            }
            log.LogMethodExit(monthValue);
            return monthValue;
        }
        /// <summary>
        /// GetCreditCardExpiryYear
        /// </summary>
        /// <param name="cardExpiryData"></param>
        private int GetCreditCardExpiryYear(string cardExpiryData)
        {
            log.LogMethodEntry(cardExpiryData);
            int yearValue;
            string yearData = DateTime.Now.Year.ToString().Substring(0, 2);
            log.Info("yearData: " + yearData);
            if (string.IsNullOrWhiteSpace(cardExpiryData) || cardExpiryData.Length < 4
              || int.TryParse(yearData + cardExpiryData.Substring(2, 2), out yearValue) == false)
            {
                throw new ValidationException(MessageContainerList.GetMessage(ExecutionContext, 597));//Invalid date format in Expiry Date
            }
            log.LogMethodExit(yearValue);
            return yearValue;
        }

        internal override PaymentTransactionStatuses MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentTransactionStatuses paymentTransactionStatus = PaymentTransactionStatuses.FAILED;
            try
            {
                Dictionary<string, PaymentTransactionStatuses> pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "Approved", PaymentTransactionStatuses.SUCCESS },
                            { "Retry", PaymentTransactionStatuses.FAILED },
                            { "Declined", PaymentTransactionStatuses.FAILED },
                            { "A", PaymentTransactionStatuses.SUCCESS },
                            { "B", PaymentTransactionStatuses.FAILED },
                            { "C", PaymentTransactionStatuses.FAILED }
                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");
                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentTransactionStatus);
                log.Debug("Value of transformed payment status: " + paymentTransactionStatus.ToString());
                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in salePaymentStatusMappingDict. Defaulting payment status to failed.");
                    paymentTransactionStatus = PaymentTransactionStatuses.FAILED;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentTransactionStatus = PaymentTransactionStatuses.FAILED;
            }

            log.LogMethodExit(paymentTransactionStatus);
            return paymentTransactionStatus;
        }

    }
}
