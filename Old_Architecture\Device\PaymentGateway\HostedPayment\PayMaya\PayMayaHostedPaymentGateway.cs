﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PayMaya
{
    public class PayMayaHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string PUBLIC_KEY;
        private string SECRET_KEY;
        private string POST_URL;
        private string BASE_URL;
        private string CURRENCY;


        PayMayaHostedCommandHandler payMayaHostedCommandHandler;

        public PayMayaHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            Initialize();
            log.LogMethodExit(null);
        }
        public override void Initialize()
        {
            log.LogMethodEntry();

            PUBLIC_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            SECRET_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");

            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            POST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            CURRENCY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");
            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }

            payMayaHostedCommandHandler = new PayMayaHostedCommandHandler(PUBLIC_KEY, SECRET_KEY, BASE_URL, POST_URL);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;


            if (string.IsNullOrWhiteSpace(PUBLIC_KEY))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            }
            if (string.IsNullOrWhiteSpace(SECRET_KEY))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));

            List<LookupValuesDTO> lookupValuesDTOlist = lookupValuesList.GetAllLookupValues(searchParameters);

            //SUCCESS_URL
            hostedGatewayDTO.SuccessURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "SUCCESS_URL").Description.Replace("@gateway", PaymentGateways.PayMayaHostedPayment.ToString());

            //FAILED_URL
            hostedGatewayDTO.FailureURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "FAILED_URL").Description.Replace("@gateway", PaymentGateways.PayMayaHostedPayment.ToString());

            //CALLBACK_URL
            hostedGatewayDTO.CallBackURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CALLBACK_URL").Description.Replace("@gateway", PaymentGateways.PayMayaHostedPayment.ToString());

            //CANCEL_URL
            hostedGatewayDTO.CancelURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CANCEL_URL").Description.Replace("@gateway", PaymentGateways.PayMayaHostedPayment.ToString());


            if (string.IsNullOrWhiteSpace(hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.FailureURL) /*|| string.IsNullOrWhiteSpace(hostedGatewayDTO.CancelURL)*/ || string.IsNullOrWhiteSpace(hostedGatewayDTO.CallBackURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/CALLBACK_URL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/."));
            }


            log.LogMethodExit();
        }

        /// <summary>
        /// Creates a payment request through the PayStack gateway and prepares redirection information.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The details of the transaction to be processed.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the URL and request string for redirecting users to complete the payment.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error during payment request creation or processing.</exception>

        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            PayMayaHostedCheckoutResponseDto payMayaCreatePaymentResponse = null;
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);

                if (transactionPaymentsDTO == null)
                {
                    log.Error("TransactionPaymentsDTO is null");
                    throw new ArgumentNullException(nameof(transactionPaymentsDTO));
                }
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());
                string trxId = Convert.ToString(transactionPaymentsDTO.TransactionId) ?? "";
                string checkoutUrl = string.Empty;


                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardName) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute5) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute4) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.NameOnCreditCard) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.CardEntitlementType)
    //|| string.IsNullOrWhiteSpace(transactionPaymentsDTO.Memo)
    )
                {
                    Dictionary<string, string> errorParams = new Dictionary<string, string>
                                {
                                    { "PaymentFailure", "1" },
                                    { "ErrorMessage", "Payment has been declined! Please enter all the mandatory customer details" },
                                    { "TrxId", transactionPaymentsDTO.TransactionId.ToString() },
                                    {"Date", utilities.getServerTime().ToString() }
                                };
                    hostedGatewayDTO.GatewayRequestString = payMayaHostedCommandHandler.ErrorForm(errorParams);

                }
                else
                {
                    PayMayaHostedPaymentRequestDto payMayaRequestDTO = new PayMayaHostedPaymentRequestDto
                    {
                        totalAmount = new Amount
                        {
                            value = transactionPaymentsDTO.Amount,
                            currency = CURRENCY,
                            details = new AmountDetails
                            {
                                discount = 0,
                                serviceCharge = 0,
                                shippingFee = 0,
                                tax = 0,
                                subtotal = transactionPaymentsDTO.Amount
                            }
                        },
                        buyer = new Buyer
                        {
                            firstName = transactionPaymentsDTO.CreditCardName,
                            //middleName = "Paul",
                            lastName = transactionPaymentsDTO.Memo,
                            contact = new Contact
                            {
                                phone = transactionPaymentsDTO.CardEntitlementType,
                                email = transactionPaymentsDTO.NameOnCreditCard
                            },
                            shippingAddress = new Address
                            {
                                firstName = transactionPaymentsDTO.CreditCardName,
                                //middleName = "Paul",
                                lastName = transactionPaymentsDTO.Memo,
                                phone = transactionPaymentsDTO.CardEntitlementType,
                                email = transactionPaymentsDTO.NameOnCreditCard,
                                line1 = transactionPaymentsDTO.Attribute5,
                                //line2 = "Reliance Street",
                                city = transactionPaymentsDTO.Attribute4,
                                state = transactionPaymentsDTO.CreditCardNumber,
                                zipCode = transactionPaymentsDTO.PaymentCardNumber,
                                countryCode = "PH", // since PayMaya is for Philippines only
                                                    //shippingType = "ST"
                            },
                            billingAddress = new Address
                            {
                                line1 = transactionPaymentsDTO.Attribute5,
                                //line2 = "Reliance Street",
                                city = transactionPaymentsDTO.Attribute4,
                                state = transactionPaymentsDTO.CreditCardNumber,
                                zipCode = transactionPaymentsDTO.PaymentCardNumber,
                                countryCode = "PH",
                            }
                        },
                        redirectUrl = new RedirectUrl
                        {
                            success = hostedGatewayDTO.SuccessURL + "?requestReferenceNumber=" + trxId,
                            failure = hostedGatewayDTO.FailureURL + "?requestReferenceNumber=" + trxId,
                            cancel = hostedGatewayDTO.CancelURL + "?requestReferenceNumber=" + trxId,
                        },
                        requestReferenceNumber = trxId,
                        metadata = new MetaData
                        {
                            paymentModeId = transactionPaymentsDTO.PaymentModeId.ToString(),
                        }
                    };

                    payMayaCreatePaymentResponse = payMayaHostedCommandHandler.CreateCheckout(payMayaRequestDTO);

                    if (payMayaCreatePaymentResponse == null)
                    {
                        log.Error("CreateGatewayPaymentRequest(): Checkout Transaction Response was empty");
                        throw new Exception("Error: could not create payment session");
                    }

                    if (string.IsNullOrWhiteSpace(payMayaCreatePaymentResponse.redirectUrl))
                    {
                        log.Error("redirectUrl was null");
                        throw new Exception("Error creating the payment request");
                    }
                    checkoutUrl = payMayaCreatePaymentResponse.redirectUrl;
                    log.Debug($"CreateGatewayPaymentRequest(): Payment ResponseDto: {payMayaCreatePaymentResponse}");
                    log.Debug($"CreateGatewayPaymentRequest(): Payment request is created, redirecting to Checkout URL: {checkoutUrl}");

                    //hostedGatewayDTO.RequestURL = checkoutUrl;
                    //refer thawani
                    //hostedGatewayDTO.GatewayRequestString = payMayaHostedCommandHandler.PrepareGatewayRequestString(checkoutUrl);
                    hostedGatewayDTO.RequestURL = checkoutUrl;

                    hostedGatewayDTO.GatewayRequestString = GetSubmitFormKeyValueList(SetPostParameters(payMayaCreatePaymentResponse.checkoutId), checkoutUrl, "fromPayMayaForm", "GET");
                    //hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(hostedGatewayDTO.GatewayRequestFormString, paymentPageLink);

                    log.Info("request url:" + hostedGatewayDTO.RequestURL);
                    log.Info("request string:" + hostedGatewayDTO.GatewayRequestString);

                    hostedGatewayDTO.FailureURL = "/account/checkouterror";
                    hostedGatewayDTO.SuccessURL = "/account/receipt";
                    hostedGatewayDTO.CancelURL = "/account/checkoutstatus";
                    LookupsList lookupList = new LookupsList(utilities.ExecutionContext);
                    List<KeyValuePair<LookupsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<LookupsDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                    searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
                    List<LookupsDTO> lookups = lookupList.GetAllLookups(searchParameters, true);
                    if (lookups != null && lookups.Any())
                    {
                        List<LookupValuesDTO> lookupValuesDTOList = lookups[0].LookupValuesDTOList;
                        LookupValuesDTO temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_FAILURE_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.FailureURL = temp.Description;
                        }

                        temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_SUCCESS_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.SuccessURL = temp.Description;
                        }

                        temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_CANCEL_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.CancelURL = temp.Description;
                        }
                        // pending url
                        temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_PENDING_URL"));
                        if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                        {
                            hostedGatewayDTO.PendingURL = temp.Description;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }
        private Dictionary<string, string> SetPostParameters(string id)
        {

            log.LogMethodEntry(id);

            try
            {
                Dictionary<string, string> postparamslist = new Dictionary<string, string>();

                postparamslist.Clear();
                postparamslist.Add("id", id);

                log.LogMethodExit(postparamslist);

                return postparamslist;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        private string GetSubmitFormKeyValueList(Dictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();

            builder.Append("<html>");

            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");
            return builder.ToString();
        }
        /// <summary>
        /// Processes the response received from the payment gateway and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the updated payment details and status.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment or updating the payment status.</exception>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            PayMayaHostedPaymentResponseDto payMayaResponse = null;
            bool isStatusUpdated;
            try
            {
                payMayaResponse = JsonConvert.DeserializeObject<PayMayaHostedPaymentResponseDto>(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + payMayaResponse.ToString());


                if (payMayaResponse.requestReferenceNumber != null)
                {
                    log.Debug("Transaction id: " + payMayaResponse.requestReferenceNumber);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(payMayaResponse.requestReferenceNumber);
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                PayMayaHostedPaymentResponseDto trxSearchResponse = payMayaHostedCommandHandler.CreateTrxSearch(payMayaResponse.requestReferenceNumber);

                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("Error processing your payment");
                }

                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(trxSearchResponse.amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = trxSearchResponse.currency;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.id.ToString();//paymaya transaction id
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(trxSearchResponse.metadata.paymentModeId);//paymentmode id
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.description; // cardnumber : **** **** **** 2346
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.details.scheme; //cardtype : master-card : Others
                hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.details.issuer; //issuername


                hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PROCESSING;
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                log.Debug("isStatusUpdated: " + isStatusUpdated.ToString());
                if (!isStatusUpdated)
                {
                    log.Error("ProcessGatewayResponse():Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }

                //check if ccTransactionPGW updated
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Error("No reference id/Transaction present in PaymentAsia receipt response");
                    cCTransactionsPGWDTOList = null;
                }
                PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.status, PaymentGatewayTransactionType.SALE);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());
                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Payment status is success");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                }
                else if (salePaymentStatus == PaymentStatusType.PENDING)
                {
                    log.Debug("Payment status is pending");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                }
                else if (salePaymentStatus == PaymentStatusType.FAILED)
                {
                    log.Debug("Payment status is failed");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }
                else
                {
                    log.Error("Payment status is unknown. Considering status as pending! Status: " + salePaymentStatus.ToString());
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }

                hostedGatewayDTO.PaymentStatus = salePaymentStatus;
                log.Debug("Final hostedGatewayDTO.PaymentStatus: " + hostedGatewayDTO.PaymentStatus);



                if (cCTransactionsPGWDTOList == null)
                {  // update the CCTransactionsPGWDTO
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.TextResponse = trxSearchResponse.status;
                    cCTransactionsPGWDTO.DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(trxSearchResponse.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.CardType = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName;
                    cCTransactionsPGWDTO.AcctNo = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber;
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();
                    cCTransactionsPGWDTO.AuthCode = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.details.last4;
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse.createdAt);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                }

                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                log.Debug("isStatusUpdated : " + isStatusUpdated);
                if (!isStatusUpdated)
                {
                    log.Error("Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }


        /// <summary>
        /// Initiates a refund process for a transaction based on the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details for initiating the refund.</param>
        /// <returns>
        /// Returns the updated TransactionPaymentsDTO after processing the refund.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefund = false;
            refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
            PaymentStatusType refundPaymentStatus;
            CCRequestPGWDTO cCRequestPGWDTO = null;
            PayMayaHostedRefundVoidResponseDto refundResponseDTO = null;

            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }
                PayMayaHostedPaymentResponseDto trxSearchResponse = payMayaHostedCommandHandler.CreateTrxSearch(refundTrxId);

                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("Error processing your payment");
                }

                log.Debug("Refund processing started");
                if (trxSearchResponse.canRefund)
                {
                    cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                    PayMayaHostedRefundRequestDto requestDto = new PayMayaHostedRefundRequestDto
                    {
                        totalAmount = new RefundAmount
                        {
                            amount = transactionPaymentsDTO.Amount,
                            currency = CURRENCY,
                        },
                        reason = "Customer request", //todo: hardcode?
                    };

                    log.Debug("PayMaya Refund Request has been created, RequestDTO: " + requestDto);

                    refundResponseDTO = payMayaHostedCommandHandler.CreateRefund(refundTrxId, requestDto);
                }
                else if (trxSearchResponse.canVoid)
                {
                    cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);

                    PayMayaHostedVoidRequestDto requestDto = new PayMayaHostedVoidRequestDto
                    {
                        reason = "Customer request",
                    };

                    log.Debug("Paystack Refund Request has been created, RequestDTO: " + requestDto);

                    refundResponseDTO = payMayaHostedCommandHandler.CreateVoid(refundTrxId, requestDto);
                }

                log.Debug("PayMaya Void Response refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }


                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                ccTransactionsPGWDTO.DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(refundResponseDTO.status);
                ccTransactionsPGWDTO.RefNo = refundResponseDTO.id.ToString(); //paymaya paymentId
                ccTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(refundResponseDTO.updatedAt);

                refundPaymentStatus = MapPaymentStatus(refundResponseDTO.status, PaymentGatewayTransactionType.REFUND);
                log.Debug("Refund Status: " + refundPaymentStatus.ToString());
                if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Refund Success for trxId: " + refundTrxId);
                    isRefund = true;
                    ccTransactionsPGWDTO.TextResponse = refundResponseDTO.status;
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.reason;
                    ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                    ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                    ccTransactionsPGWDTO.AcctNo = refundResponseDTO.reason;
                    ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();

                }
                else
                {
                    //refund failed
                    isRefund = false;
                    string errorMessage = refundResponseDTO.status;
                    log.Error($"Refund Failed. Error Message received: {errorMessage}");
                    ccTransactionsPGWDTO.TextResponse = "FAILED";
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.reason;
                    ccTransactionsPGWDTO.PaymentStatus = PaymentStatusType.FAILED.ToString();

                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }


            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }
        private DateTime GetPaymentDate(string updatedAt)
        {
            log.LogMethodEntry(updatedAt);
            DateTime paymentDate = new DateTime();

            if (updatedAt != null)
            {
                log.Debug("Payment Date from response: " + updatedAt);
                if (DateTime.TryParseExact(updatedAt, "yyyyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }

        /// <summary>
        /// Maps the raw payment gateway status to a standardized internal payment status type
        /// based on the transaction type. Handles errors and logs the process.
        /// Defaults to a PENDING status if the raw status is not recognized.
        /// </summary>
        /// <param name="rawPaymentGatewayStatus">The status string from the payment gateway.</param>
        /// <param name="pgwTrxType">The type of payment gateway transaction.</param>
        /// <returns>A PaymentStatusType enumeration value representing the mapped payment status.</returns>
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {

                            { "PAYMENT_SUCCESS", PaymentStatusType.SUCCESS },
                            { "PAYMENT_PROCESSING", PaymentStatusType.PENDING },
                            { "PENDING_TOKEN", PaymentStatusType.PENDING },
                            { "PENDING_PAYMENT", PaymentStatusType.PENDING },
                            { "FOR_AUTHENTICATION", PaymentStatusType.PENDING },
                            { "AUTHENTICATING", PaymentStatusType.PENDING },
                            { "AUTH_SUCCESS", PaymentStatusType.PENDING },
                            { "AUTH_FAILED", PaymentStatusType.FAILED },
                            { "PAYMENT_EXPIRED", PaymentStatusType.FAILED },
                            { "PAYMENT_FAILED", PaymentStatusType.FAILED },
                            { "PAYMENT_CANCELLED", PaymentStatusType.FAILED },
                            { "VOIDED", PaymentStatusType.FAILED },
                            { "REFUNDED", PaymentStatusType.FAILED }

                        };
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "Success", PaymentStatusType.SUCCESS },
                            { "Failed", PaymentStatusType.FAILED },
                            { "Processing", PaymentStatusType.FAILED }

                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to failed.");
                    paymentStatusType = PaymentStatusType.FAILED;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.FAILED;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        /// <summary>
        /// Retrieves the payment status of a transaction by its ID from PayMaya.
        /// Logs the process and handles errors. If the transaction ID is not provided,
        /// it returns a default response indicating no payment found.
        /// </summary>
        /// <param name="transactionPaymentsDTO">Data Transfer Object containing transaction details.</param>
        /// <param name="paymentGatewayTransactionType">Optional transaction type, default is STATUSCHECK.</param>
        /// <returns>A HostedGatewayDTO object containing the payment status details.</returns>
        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PayMayaHostedPaymentResponseDto orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    //throw new Exception("Insufficient Params passed to the request");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = payMayaHostedCommandHandler.CreateTrxSearch(trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error($"Order status for trxId: {trxIdString} failed.");
                    throw new Exception($"Transaction search failed for trxId: {trxIdString}!");
                }

                PaymentStatusType txSearchPaymentStatus = MapPaymentStatus(orderStatusResult.status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of txSearchPaymentStatus: " + txSearchPaymentStatus.ToString());

                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO != null ? cCRequestsPGWDTO.RequestID.ToString() : trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.RefNo = orderStatusResult.id;
                cCTransactionsPGWDTO.RecordNo = orderStatusResult.requestReferenceNumber;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.status.ToUpper();
                cCTransactionsPGWDTO.DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(orderStatusResult.status);
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.CardType = orderStatusResult.fundSource == null ? "" : orderStatusResult.fundSource.details.scheme;
                cCTransactionsPGWDTO.PaymentStatus = txSearchPaymentStatus.ToString();
                cCTransactionsPGWDTO.AcctNo = orderStatusResult.fundSource == null ? "" : orderStatusResult.fundSource.description;
                cCTransactionsPGWDTO.AuthCode = orderStatusResult.fundSource == null ? "" : orderStatusResult.fundSource.details.last4;
                cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(orderStatusResult.createdAt);

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }
    }
}
