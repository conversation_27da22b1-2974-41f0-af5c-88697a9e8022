﻿using Newtonsoft.Json;

namespace PaymentAsiaPOC.PaymentAsia
{
    public class PaymentAsiaResponseDTO
    {
        public string merchant_reference { get; set; }

        public string request_reference { get; set; }

        public string currency { get; set; }

        public string amount { get; set; }

        public string status { get; set; }

        public string sign { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class PaymentAsiaQueryTransactionResponseDTO
    {
        public string merchant_reference { get; set; }

        public string request_reference { get; set; }

        public string currency { get; set; }

        public string amount { get; set; }

        public string status { get; set; }

        public string created_time { get; set; }

        public string completed_time { get; set; }

        public string type { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class PaymentAsiaVoidResponseDTO
    {
        public RequestDTO request { get; set; }
        public ResponseDTO response { get; set; }
        public PayloadDTO payload { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class PaymentAsiaVoidRefundResponseDTO
    {
        public RequestDTO request { get; set; }
        public ResponseDTO response { get; set; }
        public RefundPayloadDTO payload { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }
    public class RequestDTO
    {
        public string id { get; set; }
        public string time { get; set; }
    }

    public class ResponseDTO
    {
        public string code { get; set; }
        public string message { get; set; }
        public string time { get; set; }
    }

    public class PayloadDTO
    {
        public string merchant_reference { get; set; }
        public string sign { get; set; }
        public string amount { get; set; }

    }

    public class RefundPayloadDTO
    {
        public string merchant_reference { get; set; }
        public string refund_reference { get; set; }
        public string refund_amount { get; set; }
        public string origin_amount { get; set; }
        public string provider_reference { get; set; }
        public string created_time { get; set; }
        public string currency { get; set; }
        public string status { get; set; }
        public string sign { get; set; }
        public string refunded_time { get; set; }



    }
    public class RefundDetailsPayloadDTO
    {
        public string merchant_reference { get; set; }
        public string refund_reference { get; set; }
        public string refund_amount { get; set; }
        public string refunded_time { get; set; }
        public string sign { get; set; }
        public string status { get; set; }

    }

    public class SettlementDetailsPayloadDTO
    {
        public string settlement_date { get; set; }
        public string netwwork { get; set; }
        public string sign { get; set; }

    }

    public class PaymentAsiaRefundDetailsResponseDTO
    {
        public RequestDTO request { get; set; }
        public ResponseDTO response { get; set; }
        public RefundDetailsPayloadDTO payload { get; set; }
    }

    public class PaymentAsiaSettlementDetailsResponseDto
    {
        public RequestDTO request { get; set; }
        public ResponseDTO response { get; set; }
        public SettlementDetailsPayloadDTO payload { get; set; }
    }
}