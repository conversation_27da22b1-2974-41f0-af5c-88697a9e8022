﻿/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Pinelabs Payment Configurations
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.200.0     20-March-2025         Amrutha      Created
 ********************************************************************************************************/

using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    class ParafaitPinelabsPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        string bankCode;
        public ParafaitPinelabsPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO, bool isUnattended)
        {
            SetConfiguration("MAXIMUM_WAIT_PERIOD_IN_MINIUTES", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "MAXIMUM_WAIT_PERIOD_IN_MINIUTES"));
            LookupsContainerDTO lookupContainerDTO = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "PINELABS_BANK_CODE");
            if (lookupContainerDTO != null && lookupContainerDTO.LookupValuesContainerDTOList != null && lookupContainerDTO.LookupValuesContainerDTOList.Any())
            {
                bankCode = lookupContainerDTO.LookupValuesContainerDTOList[0].LookupValue;
                log.Info("bankCode" + "=" + bankCode);
            }

            SetConfiguration("isQRCode", paymentModeContainerDTO.IsQRCode.ToString());
            SetConfiguration("PINELABS_BANK_CODE", bankCode);
            SetConfiguration("isUnattended", isUnattended ? "Y" : "N");
            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));

            int upiType=2;
            SetConfiguration("upiType", upiType.ToString());
            string upiTypeConfig = ConfigurationManager.AppSettings["upiType"];           
            if (!string.IsNullOrEmpty(upiTypeConfig) && int.TryParse(upiTypeConfig, out upiType))
            {
                if (upiType == 1)
                {
                    // Set to BharatQR
                    SetConfiguration("upiType", upiType.ToString());
                } 
            }
        }
            
    }
}
