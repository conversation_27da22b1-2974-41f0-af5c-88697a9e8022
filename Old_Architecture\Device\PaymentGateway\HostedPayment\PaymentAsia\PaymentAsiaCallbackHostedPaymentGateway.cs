﻿using Newtonsoft.Json;
using PaymentAsiaPOC.PaymentAsia;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PaymentAsia
{
    class PaymentAsiaCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string MERCHANT_ID;
        private string MERCHANT_TOKEN;
        private string SECRET_CODE;
        private string PAYMENTASIA_POST_URL;
        private string BASE_URL;

        private string searchTransactionUrl;
        private string voidApiUrl;
        private string refundApiUrl;
        private string refundDetailsUrl;
        private string paymentPageLink;

        private string defaultAddress;
        private string defaultState;
        private string defaultCountryIso;
        private string defaultPostalCode;
        private string defaultFirstName;
        private string defaultLastName;


        PaymentAsiaHostedCommandHandler paymentAsiaCommandHandler;

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "1", PaymentStatusType.SUCCESS },
            { "2", PaymentStatusType.FAILED },
            { "0", PaymentStatusType.PENDING },
            { "4", PaymentStatusType.PENDING },
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Success", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
            { "Pending", PaymentStatusType.SUCCESS }
        };

        public PaymentAsiaCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            Initialize();
            this.BuildTransactions = false;
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();


            MERCHANT_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            MERCHANT_TOKEN = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");

            SECRET_CODE = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            PAYMENTASIA_POST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");

            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }
            searchTransactionUrl = $"/v1.1/online/{MERCHANT_TOKEN}/transactions/query";
            voidApiUrl = $"/v1.1/online/{MERCHANT_TOKEN}/transactions/void";
            refundApiUrl = $"/v1.1/online/{MERCHANT_TOKEN}/transactions/refund";
            refundDetailsUrl = $"/v1.1/online/{MERCHANT_TOKEN}/transactions/refund-query";

            string searchTrxUrl = BASE_URL + searchTransactionUrl;

            defaultAddress = "G03, GF., Silversea Place, 18 Hoi Fai Road. Kowloon,Hong Kong";
            defaultState = "KLN";
            defaultCountryIso = "HK";
            defaultPostalCode = "000000";
            defaultFirstName = "Guest";
            defaultLastName = "Login";

            paymentAsiaCommandHandler = new PaymentAsiaHostedCommandHandler(MERCHANT_ID, MERCHANT_TOKEN, SECRET_CODE, searchTrxUrl);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;



            if (string.IsNullOrWhiteSpace(MERCHANT_ID))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            }
            if (string.IsNullOrWhiteSpace(MERCHANT_TOKEN))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            }
            if (string.IsNullOrWhiteSpace(SECRET_CODE))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(PAYMENTASIA_POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }

            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            string apiSite = "";
            string webSite = "";

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API") != null)
            {
                apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;

            }
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB") != null)
            {
                webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;

            }

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.PaymentAsiaCallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    var queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.PaymentAsiaCallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PaymentAsiaCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PaymentAsiaCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PaymentAsiaCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PaymentAsiaCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description;


            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description;



            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description;



            }
            hostedGatewayDTO.PGSuccessResponseMessage = "OK";
            hostedGatewayDTO.PGFailedResponseMessage = "OK";

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;

            log.LogMethodExit();
        }

        /// <summary>
        /// Sets the post parameters required for the PaymentAsia API request.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details to be used for parameter values.</param>
        /// <returns>
        /// Returns a dictionary containing the post parameters for the PaymentAsia API request.
        /// </returns>
        private IDictionary<string, string> SetPostParameters(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            try
            {
                const string userDefineNetwork = "UserDefine";

                string first_name = string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardName) ? defaultFirstName : transactionPaymentsDTO.CreditCardName;
                string last_name = string.IsNullOrWhiteSpace(transactionPaymentsDTO.Memo) ? defaultLastName : transactionPaymentsDTO.Memo;
                string countryName = string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute3) ? "Hong Kong" : transactionPaymentsDTO.Attribute3;
                string isoCode = paymentAsiaCommandHandler.GetIsoCode(countryName);
                string address = string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute5) ? defaultAddress : transactionPaymentsDTO.Attribute5;
                string state = string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardNumber) ? defaultState : transactionPaymentsDTO.CreditCardNumber;
                string postal_code = string.IsNullOrWhiteSpace(transactionPaymentsDTO.PaymentCardNumber) ? defaultPostalCode : transactionPaymentsDTO.PaymentCardNumber;
                string country = string.IsNullOrWhiteSpace(isoCode) ? defaultCountryIso : isoCode;
                log.Debug("isoCode: " + isoCode);

                Dictionary<string, string> postparamslist = new Dictionary<string, string>();
                postparamslist.Add("merchant_reference", transactionPaymentsDTO.TransactionId.ToString());
                postparamslist.Add("currency", transactionPaymentsDTO.CurrencyCode);
                postparamslist.Add("amount", transactionPaymentsDTO.Amount.ToString());
                postparamslist.Add("customer_phone", transactionPaymentsDTO.CardEntitlementType);
                postparamslist.Add("return_url", successResponseAPIURL);
                postparamslist.Add("subject", transactionPaymentsDTO.PaymentModeId.ToString());
                postparamslist.Add("customer_first_name", first_name);
                postparamslist.Add("customer_last_name", last_name);
                postparamslist.Add("customer_ip", transactionPaymentsDTO.ExternalSourceReference);
                postparamslist.Add("customer_email", transactionPaymentsDTO.NameOnCreditCard);
                postparamslist.Add("network", userDefineNetwork);
                postparamslist.Add("notify_url", callbackResponseAPIURL);
                postparamslist.Add("customer_address", address);
                postparamslist.Add("customer_state", state);
                postparamslist.Add("customer_postal_code", postal_code);
                postparamslist.Add("customer_country", country);

                // Compute the sign value using GenerateSignature
                postparamslist.Add("sign", paymentAsiaCommandHandler.GenerateSignature(postparamslist));
                log.LogMethodExit(postparamslist);
                log.Debug("-------------postparamslist:---------------  " + postparamslist.ToString());
                log.Debug("----------------------Customer address details----------------------" + " customer_address: " + address + " customer_state: " + state + " customer_postal_code: " + postal_code + " customer_country: " + country);
                return postparamslist;

            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// Generates an HTML form with hidden input fields based on the provided parameters.
        /// </summary>
        /// <param name="postparamslist">The dictionary containing post parameters (key-value pairs).</param>
        /// <param name="URL">The URL to which the form should submit.</param>
        /// <param name="FormName">The name of the HTML form.</param>
        /// <param name="submitMethod">The submit method for the form (default is "POST").</param>
        /// <returns>
        /// Returns the HTML representation of the form with hidden input fields.
        /// </returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            log.LogMethodEntry(postparamslist, URL, FormName, submitMethod);
            try
            {

                StringBuilder builder = new StringBuilder();
                builder.Append("<html>");
                builder.Append($"<body onload=\"document.{FormName}.submit()\">");
                builder.Append($"<form name=\"{FormName}\" method=\"{submitMethod}\" action=\"{URL}\">");

                foreach (KeyValuePair<string, string> param in postparamslist)
                {
                    builder.AppendFormat("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value);
                }

                builder.Append("</form>");
                builder.Append("</body></html>");
                log.LogMethodExit(builder.ToString());
                return builder.ToString();
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// Creates a initial gateway request.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="paymentToken"></param>
        /// <returns>HostedGatewayDTO</returns>
        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentToken);
            try
            {
                if (transactionPaymentsDTO.Amount <= 0)
                {
                    log.Error($"Order amount must be greater than zero. Order Amount was {transactionPaymentsDTO.Amount}");
                    throw new ValidationException("Order amount must be greater than zero");
                }
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
                requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
                requestParamsDict.Add("paymentToken", paymentToken);

                this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
                this.hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(this.hostedGatewayDTO.GatewayRequestString, paymentPageLink);
                this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(requestParamsDict, paymentPageLink, "authForm");

                log.Debug("Request string:" + this.hostedGatewayDTO.GatewayRequestString);
                log.Debug("Direct request link:" + this.hostedGatewayDTO.PaymentRequestLink);
                log.Debug("GatewayRequestFormString:" + this.hostedGatewayDTO.GatewayRequestFormString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }


        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);

                hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(SetPostParameters(transactionPaymentsDTO));
                hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO), PAYMENTASIA_POST_URL + MERCHANT_TOKEN, "paymentForm");

                log.Debug("Request string: " + hostedGatewayDTO.GatewayRequestString);
                log.Debug("Request Form string:" + this.hostedGatewayDTO.GatewayRequestFormString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }

        private PaymentAsiaResponseDTO GetResposeObj(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            
            PaymentAsiaResponseDTO responseObj = null;

            string jsonString = ConvertQueryStringToJson(gatewayResponse);
            log.Debug("Response as JSON: " + jsonString);

            responseObj = JsonConvert.DeserializeObject<PaymentAsiaResponseDTO>(jsonString.ToString());

            log.LogMethodExit(JsonConvert.SerializeObject(responseObj));
            return responseObj;
        }

        private string ConvertQueryStringToJson(string gatewayResponse)
        {
            log.LogMethodEntry();
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(gatewayResponse);

            Dictionary<string, string> responseDictionary = new Dictionary<string, string>();

            foreach (var key in responseCollection.AllKeys)
            {
                responseDictionary.Add(key, responseCollection[key]);
            }

            string responseJson = JsonConvert.SerializeObject(responseDictionary);

            log.LogMethodExit(responseJson);
            return responseJson;
        }

        /// <summary>
        /// Initiates the payment processing based on the provided gateway response.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the initialized payment details.
        /// </returns>
        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {

            log.LogMethodEntry(gatewayResponse);

            if (hostedGatewayDTO == null)
            {
                hostedGatewayDTO = new HostedGatewayDTO();
            }

            PaymentAsiaResponseDTO gatewayData = GetResposeObj(gatewayResponse);

            if (gatewayData.merchant_reference != null)
            {
                int trxId;
                if (int.TryParse(gatewayData.merchant_reference, out trxId))
                {
                    hostedGatewayDTO.TrxId = trxId;
                }
            }
            if (gatewayData.request_reference != null)
            {

                hostedGatewayDTO.GatewayReferenceNumber = gatewayData.request_reference;

            }


            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        /// <summary>
        /// Processes the gateway response and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing updated payment and transaction details.
        /// </returns>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            PaymentAsiaResponseDTO PaymentAsiaResponse = null;
            try
            {
                PaymentAsiaResponse = GetResposeObj(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + PaymentAsiaResponse.ToString());

                //check if gateway response is of type callback response or success response
                if (PaymentAsiaResponse.merchant_reference != null)
                {
                    log.Debug("Transaction id: " + PaymentAsiaResponse.merchant_reference);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(PaymentAsiaResponse.merchant_reference);
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                List<PaymentAsiaQueryTransactionResponseDTO> trxSearchResponseList = paymentAsiaCommandHandler.CreateTxSearchWithType(PaymentAsiaResponse.merchant_reference, "Sale");
                PaymentAsiaQueryTransactionResponseDTO trxSearchResponse = trxSearchResponseList.FirstOrDefault(t => t.status == "1") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "0") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "2") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "4");

                string amount = PaymentAsiaResponse.amount != null ? PaymentAsiaResponse.amount : trxSearchResponse.amount;


                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = string.IsNullOrWhiteSpace(PaymentAsiaResponse.currency) ? trxSearchResponse.currency : PaymentAsiaResponse.currency;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = string.IsNullOrWhiteSpace(PaymentAsiaResponse.request_reference) ? trxSearchResponse.request_reference : PaymentAsiaResponse.request_reference;//paymentasia transaction id


                //check if ccTransactionPGW updated
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;
                if (!String.IsNullOrEmpty(cCRequestsPGWDTO.ReferenceNo))
                {
                    string[] resvalues = cCRequestsPGWDTO.ReferenceNo.ToString().Split('|');
                    foreach (string word in resvalues)
                    {
                        if (word.Contains("PaymentModeId") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(word.Split(':')[1]);
                        }
                        else if (word.Contains("CurrencyCode") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = word.Split(':')[1];
                        }
                    }
                }

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Error("No reference id/Transaction present in PaymentAsia receipt response");
                    cCTransactionsPGWDTOList = null;
                }

                if (cCTransactionsPGWDTOList == null)
                {
                    PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.status, PaymentGatewayTransactionType.SALE);
                    log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());
                    if (salePaymentStatus == PaymentStatusType.SUCCESS)
                    {
                        log.Debug("Payment status is success");
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;

                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                    }
                    else if (salePaymentStatus == PaymentStatusType.PENDING)
                    {
                        log.Debug("Payment status is pending");
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.PENDING;

                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                    }
                    else if (salePaymentStatus == PaymentStatusType.FAILED)
                    {
                        log.Debug("Payment status is failed");
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;

                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    }
                    else
                    {
                        log.Error("Payment status is unknown. Considering status as failed Status: " + salePaymentStatus.ToString());
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    }

                    log.Debug("Trying to update the CC request to payment processing status");
                    CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);


                    int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(PaymentProcessStatusType.PAYMENT_PROCESSING.ToString(), hostedGatewayDTO.PaymentProcessStatus.ToString());

                    if (rowsUpdated == 0)
                    {
                        log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                    }
                    else
                    {
                        log.Debug("CC request updated to " + hostedGatewayDTO.PaymentProcessStatus.ToString());
                    }

                    // update the CCTransactionsPGWDTO
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                    cCTransactionsPGWDTO.CardType = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName;
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.TextResponse = GetTextResponseDescription(PaymentAsiaResponse.status);
                    cCTransactionsPGWDTO.DSIXReturnCode = GetPaymentStatusDescription(PaymentAsiaResponse.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }

            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        /// <summary>
        /// Gets the text response description based on the provided status code.
        /// </summary>
        /// <param name="statusCode">The status code for which the description is requested.</param>
        /// <returns>
        /// Returns a string describing the text response corresponding to the provided status code.
        /// </returns>
        public string GetTextResponseDescription(string statusCode)
        {
            log.LogMethodEntry(statusCode);
            Dictionary<string, string> paymentStatusDescriptions = new Dictionary<string, string>
            {
                { "0", "Pending" },
                { "1", "Success" },
                { "2", "Fail" },
                { "4", "Processing" },
            };

            if (paymentStatusDescriptions.ContainsKey(statusCode))
            {
                log.LogMethodExit(paymentStatusDescriptions[statusCode]);
                return paymentStatusDescriptions[statusCode];
            }

            else
            {
                string description;
                if (paymentStatusDescriptions.ContainsKey(statusCode))
                {
                    description = paymentStatusDescriptions[statusCode];
                    log.LogMethodExit(description);
                    return description;
                }

                description = "Fail";
                log.LogMethodExit(description);
                return description;
            }
        }

        /// <summary>
        /// Gets the payment status description based on the provided status code.
        /// </summary>
        /// <param name="statusCode">The status code for which the description is requested.</param>
        /// <returns>
        /// Returns a string describing the payment status corresponding to the provided status code.
        /// </returns>
        public string GetPaymentStatusDescription(string statusCode)
        {
            log.LogMethodEntry(statusCode);
            Dictionary<string, string> paymentStatusDescriptions = new Dictionary<string, string>
            {
            { "0", "Pending payment transaction." },
            { "1", "Complete payment transaction." },
            { "2", "Failed to make payment transaction." },
            { "4", "Processing the payment transaction." },

            };

            if (paymentStatusDescriptions.ContainsKey(statusCode))
            {
                log.LogMethodExit(paymentStatusDescriptions[statusCode]);
                return paymentStatusDescriptions[statusCode];
            }
            else
            {
                string description;
                if (paymentStatusDescriptions.ContainsKey(statusCode))
                {
                    description = paymentStatusDescriptions[statusCode];
                    log.LogMethodExit(description);
                    return description;
                }

                description = "Failed Payment";
                log.LogMethodExit(description);
                return description;
            }
        }

        /// <summary>
        /// Retrieves the transaction status for the specified transaction ID.
        /// </summary>
        /// <param name="trxId">The transaction ID to check for status.</param>
        /// <returns>
        /// Returns a JSON-formatted string containing the transaction status details.
        /// </returns>
        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;

            try
            {
                if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                List<PaymentAsiaQueryTransactionResponseDTO> trxSearchResponseList = paymentAsiaCommandHandler.CreateTxSearchWithType(trxId, "Sale");
                PaymentAsiaQueryTransactionResponseDTO txSearchResponseDTO = trxSearchResponseList.FirstOrDefault(t => t.status == "1") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "0") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "2") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "4");
                log.Debug($"TxSearch Response for TrxId: {trxId}: " + txSearchResponseDTO);

                if (txSearchResponseDTO != null)
                {
                    // 14 - Purchase Success
                    if (txSearchResponseDTO.status == "1")
                    {
                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.amount) / 100.00);
                        cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.amount) / 100.00);
                        cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        cCTransactionsPGWDTO.RefNo = txSearchResponseDTO.request_reference; //paymentId
                        cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                        cCTransactionsPGWDTO.TextResponse = GetTextResponseDescription(txSearchResponseDTO.status);
                        cCTransactionsPGWDTO.DSIXReturnCode = GetTextResponseDescription(txSearchResponseDTO.status);
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();

                        //Update the CCTrxPGW
                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", GetTextResponseDescription(txSearchResponseDTO.status));
                        dict.Add("orderId", trxId);
                    }
                }
                else
                {
                    log.Error($"Could not find Payment for trxId: {trxId}.");
                    //cancel the Tx in Parafait DB
                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", trxId);
                }

                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }



        /// <summary>
        /// Initiates a refund operation for the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details to be refunded.</param>
        /// <param name="isFunded">A flag indicating whether the refund operation is successful.</param>
        /// <param name="dSIXReturnCode">The DSIX return code associated with the refund operation.</param>
        /// <param name="responseObject">The response DTO from the refund operation.</param>
        private void PerformRefund(string refundTrxId, string amount, out bool isFunded, out string dSIXReturnCode, out PaymentAsiaVoidRefundResponseDTO responseObject)
        {
            log.LogMethodEntry(refundApiUrl, amount);

            responseObject = paymentAsiaCommandHandler.MakeRefund(refundTrxId, BASE_URL + refundApiUrl, amount);
            log.Debug("refundResponseDTO: " + responseObject);

            PaymentStatusType refundPaymentStatus = MapPaymentStatus(responseObject.payload.status, PaymentGatewayTransactionType.REFUND);
            log.Debug("Value of txSearchPaymentStatus: " + refundPaymentStatus.ToString());
            if (responseObject != null && refundPaymentStatus == PaymentStatusType.SUCCESS)
            {
                log.Debug("Refund Initiated");
                isFunded = true;
                dSIXReturnCode = "Refund Initiated";
            }
            else
            {
                log.Error("Refund failed");
                isFunded = false;
                dSIXReturnCode = "Refund failed";
            }
            log.LogMethodExit($"PerformRefund result: {responseObject}");

        }

        /// <summary>
        /// Handles the failure scenario when a void operation is unsuccessful and decides whether to perform a refund instead.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details to be processed.</param>
        /// <param name="responseObject">The response DTO from the unsuccessful void operation.</param>
        /// <param name="isFunded">A flag indicating whether the transaction is successfully funded.</param>
        /// <param name="dSIXReturnCode">The DSIX return code associated with the transaction.</param>
        /// <param name="tranCode">The transaction code indicating the type of operation (refund or void).</param>
        private void HandleVoidFailure(string refundTrxId, string amount, PaymentAsiaVoidRefundResponseDTO responseObject, out bool isFunded, out string dSIXReturnCode, string tranCode)
        {
            log.LogMethodEntry(refundApiUrl, amount);

            log.Error("Void failed");
            if (responseObject?.response?.message == "Transaction was successful, please use refund api")
            {
                PerformRefund(refundTrxId, amount, out isFunded, out dSIXReturnCode, out responseObject);
            }
            else
            {
                isFunded = false;
                dSIXReturnCode = "Void failed";
            }
            log.LogMethodExit(responseObject);
        }


        /// <summary>
        /// Initiates a refund or void operation based on the status of the original transaction and updates transaction details accordingly.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details to be processed.</param>
        /// <returns>
        /// Returns the updated transaction details after performing the refund or void operation.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isFunded = false;
            string tranCode = "", dSIXReturnCode = "";
            string refundTrxId = null;
            PaymentStatusType refundPaymentStatus = PaymentStatusType.NONE;

            PaymentAsiaVoidRefundResponseDTO responseObject = null;
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    // get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO);

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);


                }
                else
                {
                    refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }
                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }
                List<PaymentAsiaQueryTransactionResponseDTO> trxSearchResponseList = paymentAsiaCommandHandler.CreateTxSearchWithType(refundTrxId, "Sale");

                if (!trxSearchResponseList.Any())
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found for the specified conditions.");
                }

                PaymentAsiaQueryTransactionResponseDTO statusResponse = trxSearchResponseList.FirstOrDefault(t => t.status == "1") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "0");
                log.Debug($"Trx Search for TrxId: {refundTrxId} id {statusResponse}");

                if (statusResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found for the specified conditions.");
                }

                CCRequestPGWDTO cCRequestPGWDTO = null;
                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();

                if (statusResponse.status == "1")
                //perform refund
                {
                    log.Debug($"Initiating Refund for TrxId: {refundTrxId}");
                    tranCode = PaymentGatewayTransactionType.REFUND.ToString();
                    log.Debug($"Initiating Refund for TrxId: {refundTrxId}");
                    cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                    PerformRefund(refundTrxId, statusResponse.amount, out isFunded, out dSIXReturnCode, out responseObject);
                    log.Debug("refundResponseDTO: " + responseObject);

                }
                else if (statusResponse.status == "0")
                {
                    //perform void
                    log.Debug($"Initiating Void for TrxId: {refundTrxId}");
                    tranCode = PaymentGatewayTransactionType.VOID.ToString();
                    log.Debug($"Initiating Void for TrxId: {refundTrxId}");
                    cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);
                    responseObject = paymentAsiaCommandHandler.MakeVoid(refundTrxId, BASE_URL + voidApiUrl);
                    log.Debug("voidResponseDTO: " + responseObject);
                    refundPaymentStatus = MapPaymentStatus(responseObject.payload.status, PaymentGatewayTransactionType.REFUND);
                    log.Debug("Value of txSearchPaymentStatus: " + refundPaymentStatus.ToString());

                    if (responseObject != null && refundPaymentStatus == PaymentStatusType.SUCCESS)
                    {
                        log.Debug($"Void for TrxId: '{statusResponse.merchant_reference}' is Successful");
                        isFunded = true;
                        dSIXReturnCode = "Void success";

                    }
                    else
                    {
                        log.Debug($"Void for TrxId: '{statusResponse.merchant_reference}' is Failed!");
                        HandleVoidFailure(refundTrxId, statusResponse.amount, responseObject, out isFunded, out dSIXReturnCode, tranCode);

                    }
                }


                string paymentDateStr = responseObject.response?.time ?? statusResponse.completed_time;
                DateTime paymentDate;



                ccTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestPGWDTO.RequestID.ToString(), null,
                    responseObject.payload.merchant_reference ?? statusResponse.merchant_reference,
                     dSIXReturnCode, -1,
                    responseObject.response?.message, null, null,// card type
                    tranCode, responseObject.request.id ?? statusResponse.request_reference,
                    responseObject.payload?.origin_amount != null
                ? string.Format("{0:0.00}", Convert.ToDouble(responseObject.payload.origin_amount))
                : string.Format("{0:0.00}", Convert.ToDouble(statusResponse.amount)), responseObject.payload?.refund_amount != null
                ? string.Format("{0:0.00}", Convert.ToDouble(responseObject.payload.refund_amount))
                : string.Format("{0:0.00}", Convert.ToDouble(statusResponse.amount)),
                   DateTime.TryParseExact(paymentDateStr, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate)
                   ? paymentDate
                 : utilities.getServerTime(), null, null, ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null,
                    null, null, responseObject.response.code + ":" + GetRefundVoidInfo(responseObject.response.code), null, null, null, null);

                ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();
                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isFunded)
                {
                    string exceptionMessage = statusResponse.status == "1" ? "Refund failed" : "Void failed";
                    throw new Exception(exceptionMessage);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        /// <summary>
        /// Retrieves the description associated with a given refund or void status code.
        /// </summary>
        /// <param name="statusCode">The status code for a refund or void operation.</param>
        /// <returns>
        /// Returns a string describing the meaning of the provided status code.
        /// </returns>
        public string GetRefundVoidInfo(string statusCode)
        {
            log.LogMethodEntry(statusCode);

            Dictionary<string, string> statusCodesToDescription = new Dictionary<string, string>
    {
            {"20000", "Refund confirmed successfully"},
            {"20002", "Wait buyer action. It represents the customer needs further action for payment"},
            {"40000", "Mandatory field missing. Refer to the message to check what field is missing"},
            {"40001", "Invalid format. Refer to the message to check the format"},
            {"40002", "Access denied"},
            {"40003", "No available channel"},
            {"40005", "Provider response error. Refer to the message to check the error message from corresponding provider"},
            {"40007", "Merchant reference duplicated"},
            {"40008", "Invalid amount format"},
            {"40009", "Transaction not found"},
            {"40010", "Insufficient balance. The requested refund amount is greater than the original transaction amount or the left amount of the transaction"},
            {"40011", "Not processing transaction. The requested void transaction is not a processing transaction. If it is a completed transaction, please request with refund API"},
            {"40012", "Currency not supported"},
            {"40013", "Incorrect Signature. Please refer to Appendix for signature"},
            {"40014", "Invalid currency format"},
            {"50000", "Service busy"},
            {"50002", "Provider timeout. Request service to provider overtime"},
            {"200", "Refund initiated successfully"},

        };
            string description;
            if (statusCodesToDescription.ContainsKey(statusCode))
            {
                description = statusCodesToDescription[statusCode];
                log.LogMethodExit(description);
                return description;
            }

            description = "Declined";
            log.LogMethodExit(description);
            return description;

        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PaymentAsiaQueryTransactionResponseDTO orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                List<PaymentAsiaQueryTransactionResponseDTO> trxSearchResponseList = paymentAsiaCommandHandler.CreateTxSearchWithType(trxIdString, "Sale");
                orderStatusResult = trxSearchResponseList.FirstOrDefault(t => t.status == "1") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "0") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "2") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "4");
                log.Debug($"TxSearch Response for TrxId: {trxIdString}: " + orderStatusResult.ToString());


                if (orderStatusResult == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                log.Debug($"TxSearch Response for TrxId: {orderStatusResult.merchant_reference}: " + orderStatusResult);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(orderStatusResult.status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());


                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", Convert.ToDouble(orderStatusResult.amount));
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", Convert.ToDouble(orderStatusResult.amount));
                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                cCTransactionsPGWDTO.RefNo = orderStatusResult.request_reference; //paymentId
                cCTransactionsPGWDTO.RecordNo = trxIdString; //parafait TrxId
                cCTransactionsPGWDTO.TextResponse = GetTextResponseDescription(orderStatusResult.status);
                cCTransactionsPGWDTO.DSIXReturnCode = GetTextResponseDescription(orderStatusResult.status);
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }


    }
}
