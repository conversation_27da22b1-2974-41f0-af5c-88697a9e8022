﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E7B2F2E5-4EE0-4CDA-8E02-07738EE3450E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Semnox.Parafait.PaymentGateway</RootNamespace>
    <AssemblyName>PaymentGateway</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Adyen">
      <HintPath>..\..\..\..\OTS\Adyen.dll</HintPath>
    </Reference>
    <Reference Include="aliyun-net-sdk-dysmsapi">
      <HintPath>..\..\..\..\OTS\aliyun-net-sdk-dysmsapi.dll</HintPath>
    </Reference>
    <Reference Include="AopSdk">
      <HintPath>..\..\..\..\OTS\AopSdk.dll</HintPath>
    </Reference>
    <Reference Include="AxDPSEFTXLib">
      <HintPath>..\..\..\..\OTS\AxDPSEFTXLib.dll</HintPath>
    </Reference>
    <Reference Include="AxInterop.AcroPDFLib">
      <HintPath>..\..\..\..\OTS\AxInterop.AcroPDFLib.dll</HintPath>
    </Reference>
    <Reference Include="AxInterop.COECRCOMLib">
      <HintPath>..\..\..\..\OTS\AxInterop.COECRCOMLib.dll</HintPath>
    </Reference>
    <Reference Include="AxInterop.CSDEFTLib">
      <HintPath>..\..\..\..\OTS\AxInterop.CSDEFTLib.dll</HintPath>
    </Reference>
    <Reference Include="AxInterop.ctlUSBHID">
      <HintPath>..\..\..\..\OTS\AxInterop.ctlUSBHID.dll</HintPath>
    </Reference>
    <Reference Include="AxInterop.PosEftLib">
      <HintPath>..\..\..\..\OTS\AxInterop.PosEftLib.dll</HintPath>
    </Reference>
    <Reference Include="AxSB100PCLib">
      <HintPath>..\..\..\..\OTS\AxSB100PCLib.dll</HintPath>
    </Reference>
    <Reference Include="Creditcall.ChipDna.ClientLib">
      <HintPath>..\..\..\..\OTS\Creditcall.ChipDna.ClientLib.dll</HintPath>
    </Reference>
    <Reference Include="DitronDirverInterface">
      <HintPath>..\..\..\..\OTS\DitronDirverInterface.dll</HintPath>
    </Reference>
    <Reference Include="F2FPayDll">
      <HintPath>..\..\..\..\OTS\F2FPayDll.dll</HintPath>
    </Reference>
    <Reference Include="Interop.AcroPDFLib">
      <HintPath>..\..\..\..\OTS\Interop.AcroPDFLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Interop.AlohaFOHLib">
      <HintPath>..\..\..\..\OTS\Interop.AlohaFOHLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Interop.COECRCOMLib">
      <HintPath>..\..\..\..\OTS\Interop.COECRCOMLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Interop.CSDEFTLib">
      <HintPath>..\..\..\..\OTS\Interop.CSDEFTLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Interop.DSIEMVXLib">
      <HintPath>..\..\..\..\OTS\Interop.DSIEMVXLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Interop.PosEftLib">
      <HintPath>..\..\..\..\OTS\Interop.PosEftLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="IPADLib">
      <HintPath>..\..\..\..\OTS\IPADLib.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp">
      <HintPath>..\..\..\..\OTS\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.xmlworker">
      <HintPath>..\..\..\..\OTS\itextsharp.xmlworker.dll</HintPath>
    </Reference>
    <Reference Include="MCPG.CCA.Util">
      <HintPath>..\..\..\..\OTS\MCPG.CCA.Util.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens">
      <HintPath>..\..\..\..\OTS\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\OTS\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Nst">
      <HintPath>..\..\..\..\OTS\Nst.dll</HintPath>
    </Reference>
    <Reference Include="RBA_SDK_CS">
      <HintPath>..\..\..\..\OTS\RBA_SDK_CS.dll</HintPath>
    </Reference>
    <Reference Include="Stripe.net">
      <HintPath>..\..\..\..\OTS\Stripe.net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Net" />
    <Reference Include="System.Printing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="TfhkaNet">
      <HintPath>..\..\..\..\OTS\TfhkaNet.dll</HintPath>
    </Reference>
    <Reference Include="Transbank">
      <HintPath>..\..\..\..\OTS\Transbank.dll</HintPath>
    </Reference>
    <Reference Include="Tyro.Integ">
      <HintPath>..\..\..\..\OTS\Tyro.Integ.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CardConnect\ParafaitCardConnectPaymentConfigurations.cs" />
    <Compile Include="Geidea\ParafaitGeideaPaymentConfigurations.cs" />
    <Compile Include="Adyen\ParafaitAdyenPaymentConfigurations.cs" />
    <Compile Include="CCGatewayUtils.cs" />
    <Compile Include="CouponPaymentGateway.cs" />
    <Compile Include="DebitCardPaymentGateway.cs" />
    <Compile Include="EntryMode.cs" />
    <Compile Include="HostedPaymentGateway\Adyen\AdyenWebPaymentConfiguration.cs" />
    <Compile Include="HostedPaymentGateway\CardConnect\CardConnectWebPaymentConfiguration.cs" />
    <Compile Include="HostedPaymentGateway\CCAvenue\CCAvenueWebPaymentConfiguration.cs" />
    <Compile Include="HostedPaymentGateway\CreateHostedPaymentRequestDTO.cs" />
    <Compile Include="HostedPaymentGateway\HostedPaymentRequestDTO.cs" />
    <Compile Include="HostedPaymentGateway\HostedPaymentResponseDTO.cs" />
    <Compile Include="HostedPaymentGateway\ParafaitWebPaymentGateway.cs" />
    <Compile Include="HostedPaymentGateway\WebHostedPaymentGateway.cs" />
    <Compile Include="HostedPaymentGateway\WPCyberSource\WPCyberSourceWebPaymentConfiguration.cs" />
    <Compile Include="Mashreq\ParafaitMashreqPaymentConfiguration.cs" />
    <Compile Include="ParafaitPaymentGateway.cs" />
    <Compile Include="ParafaitPaymentGatewayFactory.cs" />
    <Compile Include="ParafaitPaymentMessages.cs" />
    <Compile Include="ParafaitPaymentPrintAttributes.cs" />
    <Compile Include="PaymentGateway.cs" />
    <Compile Include="PaymentGatewayExceptions.cs" />
    <Compile Include="PaymentGatewayFactory.cs" />
    <Compile Include="PayTMDQR\ParafaitPayTMDQRPaymentConfigurations.cs" />
    <Compile Include="PineLabs\ParafaitPinelabsPaymentConfiguration.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="WebRequestHandler.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Communication\Communication.csproj">
      <Project>{61bd1eb5-df16-4a31-83fc-8d6944f02a20}</Project>
      <Name>Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Customer\Customer.csproj">
      <Project>{09EC39CF-3DD3-4920-A155-CADE5B252B8A}</Project>
      <Name>Customer</Name>
    </ProjectReference>
    <ProjectReference Include="..\GenericUtilities\GenericUtilities.csproj">
      <Project>{5eec3fd1-9ea6-404e-9c7a-554268a3ef80}</Project>
      <Name>GenericUtilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Languages\Languages.csproj">
      <Project>{e7ceb68f-78bb-44f8-9b4e-ea3fb43dac9a}</Project>
      <Name>Languages</Name>
    </ProjectReference>
    <ProjectReference Include="..\Logging\Logging.csproj">
      <Project>{1a1bac8c-a16a-433c-8856-2ec4047d31fb}</Project>
      <Name>Logging</Name>
    </ProjectReference>
    <ProjectReference Include="..\PaymentGatewayInterface\PaymentGatewayInterface.csproj">
      <Project>{d3e584d8-124a-46a3-8a52-6f4f3748ce31}</Project>
      <Name>PaymentGatewayInterface</Name>
    </ProjectReference>
    <ProjectReference Include="..\PaymentMode\PaymentMode.csproj">
      <Project>{F2462931-8892-42C1-9D45-C8DA7F6C4A61}</Project>
      <Name>PaymentMode</Name>
    </ProjectReference>
    <ProjectReference Include="..\Site\Site.csproj">
      <Project>{b0419249-f9f6-425f-9b56-13d2e12f319f}</Project>
      <Name>Site</Name>
    </ProjectReference>
    <ProjectReference Include="..\Transaction\Transaction.csproj">
      <Project>{266543a2-b12c-46fe-bcf1-a67319b9cfb8}</Project>
      <Name>Transaction</Name>
    </ProjectReference>
    <ProjectReference Include="..\User\User.csproj">
      <Project>{e30d3295-e230-44d2-880d-22bf5a349119}</Project>
      <Name>User</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utilities\Utilities.csproj">
      <Project>{667eab12-6c89-48ed-9fcb-934697cc2779}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\ViewContainer\ViewContainer.csproj">
      <Project>{b73af819-c0b5-4aa6-8c09-e8643f1437d1}</Project>
      <Name>ViewContainer</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>