﻿
/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Adyen Payment Configurations
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.190.0     24-Sep-2024         Amrutha      Created
 ********************************************************************************************************/

using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Site;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace Semnox.Parafait.PaymentGateway
{

   public class ParafaitGeideaPaymentConfigurations : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public ParafaitGeideaPaymentConfigurations(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            var posId = executionContext.POSMachineName;
            var siteContainerDTO = SiteViewContainerList.GetCurrentSiteContainerDTO(executionContext);
            var posVersion = string.Empty;
            if (siteContainerDTO != null)
            {
                posVersion = siteContainerDTO.Version;
            }

            OperatingSystem os = Environment.OSVersion;
            string osVersion = Convert.ToString(os.Version.Major);
            string operationSystem = os.ToString();

            SetConfiguration("CREDIT_CARD_TERMINAL_PORT_NO", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_PORT_NO"));
            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
            SetConfiguration("ENABLE_AUTO_CREDIT_CARD_AUTHORIZATION", Convert.ToString(paymentModeContainerDTO.EnableAutoCreditcardAuthorization));
            SetConfiguration("ALLOW_CREDIT_CARD_AUTHORIZATION", Convert.ToString(paymentModeContainerDTO.AllowCreditCardAuthorization));
            SetConfiguration("CREDIT_CARD_TERMINAL_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_ID"));
            SetConfiguration("CC_PAYMENT_RECEIPT_PRINT", paymentModeContainerDTO.CcPaymentReceiptPrint);
        }

    }
}
