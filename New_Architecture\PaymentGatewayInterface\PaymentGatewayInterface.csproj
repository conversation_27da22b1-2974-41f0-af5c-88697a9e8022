﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D3E584D8-124A-46A3-8A52-6F4F3748CE31}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Semnox.Parafait.PaymentGatewayInterface</RootNamespace>
    <AssemblyName>PaymentGatewayInterface</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto">
      <HintPath>..\..\..\..\OTS\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Interop.PlutusExchangeLib">
      <HintPath>..\..\..\..\OTS\Interop.PlutusExchangeLib.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="MCPG.CCA.Util">
      <HintPath>..\..\..\..\OTS\MCPG.CCA.Util.dll</HintPath>
    </Reference>
    <Reference Include="netstandard, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51" />
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\OTS\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PaytmQrDisplay">
      <HintPath>..\..\..\..\OTS\PaytmQrDisplay.dll</HintPath>
    </Reference>
    <Reference Include="SevenZipSharp">
      <HintPath>..\..\..\..\OTS\SevenZipSharp.dll</HintPath>
    </Reference>
    <Reference Include="sgEftInterface">
      <HintPath>..\..\..\..\OTS\sgEftInterface.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Printing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="zxing">
      <HintPath>..\..\..\..\OTS\zxing.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CardConnect\CardConnectCommandHandler.cs" />
    <Compile Include="CardConnect\CardConnectDeviceCommandHandler.cs" />
    <Compile Include="CardConnect\CardConnectDeviceHandler.cs" />
    <Compile Include="CardConnect\CardConnectGatewayCommandHandler.cs" />
    <Compile Include="CardConnect\CardConnectGatewayHandler.cs" />
    <Compile Include="CardConnect\CardConnectPaymentGateway.cs" />
    <Compile Include="CCGatewayUtils.cs" />
    <Compile Include="GeideaPayment\GeideaCommandHandler.cs" />
    <Compile Include="GeideaPayment\GeideaPaymentGateway.cs" />
    <Compile Include="GeideaPayment\GeideaRequestDTO.cs" />
    <Compile Include="GeideaPayment\GeideaResponseDTO.cs" />
    <Compile Include="CheckConnection.cs" />
    <Compile Include="CreatePaymentSessionDTO.cs" />
    <Compile Include="Initialize.cs" />
    <Compile Include="Json.cs" />
    <Compile Include="Mashreq\MashreqCommandHandler.cs" />
    <Compile Include="Mashreq\MashreqCommonRequestHandler.cs" />
    <Compile Include="Mashreq\MashreqPaymentGateway.cs" />
    <Compile Include="Mashreq\MashreqRequestDTO.cs" />
    <Compile Include="Mashreq\MashreqResponseDTO.cs" />
    <Compile Include="PaymentGateway.cs" />
    <Compile Include="PaymentGatewayResponseDTO.cs" />
    <Compile Include="PaymentGateways.cs" />
    <Compile Include="PaymentMessages.cs" />
    <Compile Include="PaymentPrintAttribute.cs" />
    <Compile Include="PayTMDQRPayment\PayTMDQRCommandHandler.cs" />
    <Compile Include="PayTMDQRPayment\PayTMDQRPaymentGateway.cs" />
    <Compile Include="PayTMDQRPayment\PayTMDQRRequestDTO.cs" />
    <Compile Include="PayTMDQRPayment\PayTMDQRResponseCodes.cs" />
    <Compile Include="PayTMDQRPayment\PayTMDQRResponseDTO.cs" />
    <Compile Include="PineLabsPayment\PinelabsPaymentGateway.cs" />
    <Compile Include="PineLabsPayment\PineLabsPlutusA920CommandHandler.cs" />
    <Compile Include="PineLabsPayment\PineLabsPlutusA920Configurations.cs" />
    <Compile Include="PineLabsPayment\PineLabsPlutusA920RequestDTO.cs" />
    <Compile Include="PineLabsPayment\PineLabsPlutusA920ResponseDTO.cs" />
    <Compile Include="TransactionType.cs" />
    <Compile Include="SettlementRequestDTO.cs" />
    <Compile Include="StatusCheckRequestDTO.cs" />
    <Compile Include="PaymentConfiguration.cs" />
    <Compile Include="AdyenPayment\AdyenPaymentGateway.cs" />
    <Compile Include="AdyenPayment\AdyenPosCommandhandler.cs" />
    <Compile Include="AdyenPayment\AdyenPosRequestDto.cs" />
    <Compile Include="AdyenPayment\AdyenPosResponseDTO.cs" />
    <Compile Include="IPaymentGateway.cs" />
    <Compile Include="PaymentGatewayException.cs" />
    <Compile Include="PaymentProgressReport.cs" />
    <Compile Include="PaymentRequestDTO.cs" />
    <Compile Include="PaymentResponseDTO.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RefundRequestDTO.cs" />
    <Compile Include="WebPaymentGateway\Adyen\AdyenWebCommandHandler.cs" />
    <Compile Include="WebPaymentGateway\Adyen\AdyenWebPaymentGateway.cs" />
    <Compile Include="WebPaymentGateway\Adyen\AdyenWebRequestDTO.cs" />
    <Compile Include="WebPaymentGateway\Adyen\AdyenWebResponseDTO.cs" />
    <Compile Include="WebPaymentGateway\CardConnect\CardConnectWebPaymentGateway.cs" />
    <Compile Include="WebPaymentGateway\CardConnect\CardConnectStatusCheckResponseDTO.cs" />
    <Compile Include="WebPaymentGateway\CCAvenue\CCAvenueWebPaymentGateway.cs" />
    <Compile Include="WebPaymentGateway\CCAvenue\CCAvenueWebRequestDTO.cs" />
    <Compile Include="WebPaymentGateway\CCAvenue\CCAvenueWebResponseDTO.cs" />
    <Compile Include="WebPaymentGateway\PaymentGatewayCustomerDTO.cs" />
    <Compile Include="WebPaymentGateway\PaymentGatewayProductDTO.cs" />
    <Compile Include="WebPaymentGateway\PaymentSessionDTO.cs" />
    <Compile Include="WebPaymentGateway\WPCyberSource\WPCyberSourceWebCommandHandler.cs" />
    <Compile Include="WebPaymentGateway\WPCyberSource\WPCyberSourceWebPaymentGateway.cs" />
    <Compile Include="WebPaymentGateway\WPCyberSource\WPCyberSourceWebRequestDTO.cs" />
    <Compile Include="WebPaymentGateway\WPCyberSource\WPCyberSourceWebResponseDTO.cs" />
    <Compile Include="WebPaymentGateway\WPCyberSource\WPCyberSourceWebSecurity.cs" />
    <Compile Include="PaymentTransactionStatuses.cs" />
    <Compile Include="WebRequestHandler.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Logging\Logging.csproj">
      <Project>{1a1bac8c-a16a-433c-8856-2ec4047d31fb}</Project>
      <Name>Logging</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>