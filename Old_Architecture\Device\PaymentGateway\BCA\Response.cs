﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BCAPayments
{
    public abstract class Response
    {
        private readonly byte[] _responseBytes;

        protected Response(byte[] responseBytes)
        {
            if (responseBytes == null || responseBytes.Length == 0)
                throw new ArgumentException("Response bytes cannot be null or empty.");
            string hexString = BitConverter.ToString(responseBytes).Replace("-", "");
            byte[] bytes = Enumerable.Range(0, hexString.Length / 2)
                             .Select(i => Convert.ToByte(hexString.Substring(i * 2, 2), 16))
                             .ToArray();
            _responseBytes = bytes;
        }

        protected string GetField(int start, int length)
        {
            string responseString = Encoding.ASCII.GetString(_responseBytes);
            string responseSubString = responseString.Substring(start, length);
            return responseSubString;
           
        }
    }
}
