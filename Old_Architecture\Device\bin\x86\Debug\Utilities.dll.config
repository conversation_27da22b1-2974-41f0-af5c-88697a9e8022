﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
    </configSections>
    <connectionStrings>
        <add name="DBUtilities.Properties.Settings.ParafaitConnectionString" connectionString="Data Source=MLR-LT052\SQLEXPRESS;Initial Catalog=parafait;Persist Security Info=True;User ID=parafait;Password=*****;pezz4aLNZ5rhAMN04bUczw==*****;;Connect Timeout=30" providerName="System.Data.SqlClient" />
        <add name="Semnox.Core.Utilities.Properties.Settings.ParafaitConnectionString" connectionString="Data Source=MLR-LT052\SQLEXPRESS;Initial Catalog=parafait;Persist Security Info=True;User ID=parafait;Password=*****;pezz4aLNZ5rhAMN04bUczw==*****;;Connect Timeout=30" providerName="System.Data.SqlClient" />
      <add name="ParafaitUtils.Properties.Settings.ParafaitConnectionString" connectionString="Data Source=MLR-LT052\SQLEXPRESS;Initial Catalog=parafait;Persist Security Info=True;User ID=parafait;Password=*****;pezz4aLNZ5rhAMN04bUczw==*****;;Connect Timeout=30" />
      <add name="Parafait.Properties.Settings.ParafaitConnectionString" connectionString="Data Source=MLR-LT052\SQLEXPRESS;Initial Catalog=parafait;Persist Security Info=True;User ID=parafait;Password=*****;pezz4aLNZ5rhAMN04bUczw==*****;;Connect Timeout=30" providerName="System.Data.SqlClient" />
    </connectionStrings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>