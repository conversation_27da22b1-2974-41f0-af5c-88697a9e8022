﻿/********************************************************************************************
 * Project Name - Hosted Payment
 * Description  - Payment Handler for WP Cyber Source
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/
using Newtonsoft.Json;
using Semnox.Parafait.PaymentGatewayInterface.WPCyberSource;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Remoting.Channels;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class WPCyberSourceWebCommandHandler
    {
        private readonly Semnox.Parafait.logging.Logger log;

        private string _partner_solution_id;
        private string _access_key;
        private string _profile_id;
        private string _checkout_url;
        private string _secret_key;
        private string _host_url;
        private string _base_url;
        private string _rest_secret_key;
        private string _public_key;
        private string _merchant_id;
        private string currency_code;
        //private string ignore_avs;

        private const string SCHEME = "https://";
        private const string ALGORITHM = "HmacSHA256";
        private const string LOCALE = "en-GB";
        private const string TX_TYPE = "sale";
        private const string PA_TX_MODE = "S"; // S=>E-commerce, R=>Retail
        private const string PA_CHALLENGE_CODE = "04"; // PA: Payer Authentication

        private WPCybersourceWebSecurity _wpCybersourceSecurity;

        public WPCyberSourceWebCommandHandler(PaymentConfiguration paymentConfiguration, Semnox.Parafait.logging.Logger logger)
        {
            log = logger;
            log.LogMethodEntry();

            _wpCybersourceSecurity = new WPCybersourceWebSecurity(log);
            this._partner_solution_id = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PARTNER_ID");
            this._access_key = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            this._profile_id = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            this._checkout_url = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_BASE_URL");
            this._secret_key = paymentConfiguration.GetConfiguration("WPCyberSource secret key");
            this._host_url = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL");
            this._rest_secret_key = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            this._public_key = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            this._merchant_id = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            this.currency_code = paymentConfiguration.GetConfiguration("CURRENCY_CODE");
            //this.ignore_avs = paymentConfiguration.GetConfiguration("ENABLE_ADDRESS_VALIDATION") == "Y" ? "false" : "true";

            this._base_url = SCHEME + _host_url;

            log.LogMethodExit();
        }

        public String getSignature(string signed_field_names, string signed_date_time, string paymentGuid, decimal amount)
        {
            log.LogMethodEntry();
            try
            {
                Dictionary<string, string> signatureParams = new Dictionary<string, string>();
                signatureParams.Add("access_key", _access_key);
                signatureParams.Add("profile_id", _profile_id);
                signatureParams.Add("transaction_uuid", paymentGuid);
                signatureParams.Add("signed_field_names", signed_field_names);
                signatureParams.Add("unsigned_field_names", "");
                signatureParams.Add("signed_date_time", signed_date_time);
                signatureParams.Add("locale", LOCALE);
                //signatureParams.Add("ignore_avs", ignore_avs);
                signatureParams.Add("transaction_type", TX_TYPE);
                signatureParams.Add("reference_number", paymentGuid);
                signatureParams.Add("amount", amount.ToString("0.000"));
                signatureParams.Add("currency", currency_code);

                signatureParams.Add("payer_authentication_transaction_mode", PA_TX_MODE);
                signatureParams.Add("payer_authentication_challenge_code", PA_CHALLENGE_CODE);
                signatureParams.Add("partner_solution_id", _partner_solution_id);

                log.LogMethodExit();
                return _wpCybersourceSecurity.sign(signatureParams, _secret_key);
            }
            catch (Exception ex)
            {
                log.Error("Signed field names: " + signed_field_names);
                log.Error("Signed date time: " + signed_date_time);
                log.Error("Payment Guid: " + paymentGuid);
                log.Error("Amount: " + amount.ToString());
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }

        public String prepareForm(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry();
            try
            {
                string signed_date_time = paymentRequestDTO.RequestUTCDate.ToUniversalTime().ToString("yyyy-MM-dd'T'HH:mm:ss'Z'"); // getUTCDateTime();

                string signed_field_names = "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,payer_authentication_transaction_mode,payer_authentication_challenge_code,partner_solution_id";

                string signature = getSignature(signed_field_names, signed_date_time, paymentRequestDTO.RequestIdentifier, paymentRequestDTO.Amount);

                string form = "<html><head>";
                form += "<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />";
                form += "<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />";
                form += $"</head><body onload =\"document.WorldPayWebPaymentsForm.submit()\">";
                form += $"<form name=\"WorldPayWebPaymentsForm\" method=\"POST\" action=\"{_checkout_url}\">";

                form += $"<input type=\"hidden\" name=\"access_key\" value=\"{_access_key}\" />";
                form += $"<input type=\"hidden\" name=\"profile_id\" value=\"{_profile_id}\" />";
                form += $"<input type=\"hidden\" name=\"transaction_uuid\" value=\"{paymentRequestDTO.RequestIdentifier}\" />";
                form += $"<input type=\"hidden\" name=\"signed_field_names\" value=\"{signed_field_names}\" />";
                form += $"<input type=\"hidden\" name=\"unsigned_field_names\" value=\"\" />";
                form += $"<input type=\"hidden\" name=\"signed_date_time\" value=\"{signed_date_time}\" />";
                form += $"<input type=\"hidden\" name=\"locale\" value=\"{LOCALE}\" />";
                //form += $"<input type=\"hidden\" name=\"ignore_avs\" value=\"{ignore_avs}\" />";
                form += $"<input type=\"hidden\" name=\"transaction_type\" value=\"{TX_TYPE}\" />";
                form += $"<input type=\"hidden\" name=\"reference_number\" value=\"{paymentRequestDTO.RequestIdentifier}\" />";
                form += $"<input type=\"hidden\" name=\"amount\" value=\"{paymentRequestDTO.Amount.ToString("0.000")}\" />";
                form += $"<input type=\"hidden\" name=\"currency\" value=\"{currency_code}\" />";
                form += $"<input type=\"hidden\" name=\"partner_solution_id\" value=\"{_partner_solution_id}\" />";
                form += $"<input type=\"hidden\" name=\"payer_authentication_transaction_mode\" value=\"{PA_TX_MODE}\" />";
                form += $"<input type=\"hidden\" name=\"payer_authentication_challenge_code\" value=\"{PA_CHALLENGE_CODE}\" />";
                form += $"<input type=\"hidden\" name=\"signature\" value=\"{signature}\" />";

                form += $"</form>";
                form += "</body></html>";

                log.LogMethodExit();
                return form;
            }
            catch (Exception ex)
            {
                log.Error("payment request DTO: " + paymentRequestDTO.ToString());
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Create Tx Search when we dont receive timely response of SALE Tx
        /// </summary>
        /// <param name="searchRequestDTO"></param>
        /// <returns></returns>
        public TxSearchResponseDTO CreateTxSearch(TxSearchRequestDTO searchRequestDTO)
        {
            log.LogMethodEntry(searchRequestDTO);
            TxSearchResponseDTO response = null;
            //string SCHEME = SCHEME;
            //string HOST = host_url;
            //string BASE_URL = SCHEME + host_url;
            try
            {
                if (!string.IsNullOrEmpty(_base_url))
                {
                    string API_URL = _base_url + "/tss/v2/searches";
                    if (searchRequestDTO != null)
                    {
                        response = MakeSearchRequest(searchRequestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error("Search request DTO: " + searchRequestDTO.ToString());
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(response);
            return response;
        }

        private TxSearchResponseDTO MakeSearchRequest(TxSearchRequestDTO searchRequestDTO, string API_URL)
        {
            log.LogMethodEntry(searchRequestDTO, API_URL);
            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "post /tss/v2/searches";

                string host = _host_url;
                string MerchantId = _merchant_id;
                //string SecretKey = _rest_secret_key;
                string KeyId = _public_key;

                string JsonObj = JsonConvert.SerializeObject(searchRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });

                var Digest = GenerateDigest(JsonObj);

                var signatureParams = "host: " + host + "\nv-c-date: " + requestDate + "\nrequest-target: " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("signatureParams: " + signatureParams);
                var SignatureHash = GenerateSignatureFromParams(signatureParams);

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "POST";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + ALGORITHM + "\", headers=\"host v-c-date request-target digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("TxSearch httpWebRequest headers: " + httpWebRequest.Headers);

                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    responseFromServer = streamReader.ReadToEnd();
                }

                // deserialize the response received
                TxSearchResponseDTO response = deserializeTxSearchResponse(responseFromServer);
                log.Debug("TxSearch responseFromServer:" + responseFromServer);

                log.LogMethodExit(response);
                return response;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Tx Status Check using Cybersource Payment Id
        /// </summary>
        /// <param name="requestDTO"></param>
        /// <returns></returns>
        public TxStatusResponseDTO CreateTransactionCheck(WorldPayWebRequestDTO requestDTO)
        {
            log.LogMethodEntry(requestDTO);
            TxStatusResponseDTO response = null;
            //string SCHEME = SCHEME;
            //string HOST = host_url;
            //string BASE_URL = SCHEME + host_url;
            try
            {
                if (!string.IsNullOrEmpty(_base_url))
                {
                    string API_URL = _base_url + "/tss/v2/transactions/" + requestDTO.paymentId;
                    log.Info(API_URL);
                    if (requestDTO != null)
                    {
                        response = MakeTransactionStatusCheckRequest(requestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit(response);
            return response;
        }

        private TxStatusResponseDTO MakeTransactionStatusCheckRequest(WorldPayWebRequestDTO requestDTO, string API_URL)
        {
            log.LogMethodEntry(requestDTO, API_URL);
            string responseFromServer;
            string statusFromServer;
            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "get /tss/v2/transactions/" + requestDTO.paymentId;
                log.Debug("requestTarget: " + requestTarget);

                string host = _host_url;
                string MerchantId = _merchant_id;
                //string SecretKey = _rest_secret_key;
                string KeyId = _public_key;

                var SignatureParm = "host: " + host + "\n(request-target): " + requestTarget + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("SignatureParm: " + SignatureParm);
                var SignatureHash = GenerateSignatureFromParams(SignatureParm);
                log.Debug("SignatureHash: " + SignatureHash);

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "GET";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + ALGORITHM + "\", headers=\"host (request-target) v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("TxSearch httpWebRequest headers: " + httpWebRequest.Headers);

                HttpWebResponse webResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                statusFromServer = ((HttpWebResponse)webResponse).StatusDescription;
                Stream receiveStream = webResponse.GetResponseStream();
                StreamReader readStream = new StreamReader(receiveStream, Encoding.UTF8);
                responseFromServer = readStream.ReadToEnd();

                webResponse.Close();
                readStream.Close();

                log.Info(responseFromServer);

                // deserialize the response received
                TxStatusResponseDTO response = deserializeTxStatusResponse(responseFromServer);

                log.LogMethodExit(response);
                return response;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// VOID : requires Cybersource Payment Id and ccRequestId
        /// </summary>
        /// <param name="requestDTO"></param>
        /// <param name="voidRequestDTO"></param>
        /// <returns></returns>
        public VoidResponseDTO CreateVoid(WorldPayWebRequestDTO requestDTO, VoidRequestDTO voidRequestDTO)
        {
            log.LogMethodEntry(requestDTO, voidRequestDTO);
            VoidResponseDTO response = null;
            //string SCHEME = SCHEME;
            //string HOST = host_url;
            //string BASE_URL = SCHEME + host_url;
            try
            {
                if (!string.IsNullOrEmpty(_base_url))
                {
                    string API_URL = _base_url + "/pts/v2/payments/" + requestDTO.paymentId + "/voids";
                    if (requestDTO != null)
                    {
                        response = MakeVoidRequest(requestDTO, voidRequestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(response);
            return response;
        }

        private VoidResponseDTO MakeVoidRequest(WorldPayWebRequestDTO requestDTO, VoidRequestDTO voidRequestDTO, string API_URL)
        {
            log.LogMethodEntry(requestDTO, voidRequestDTO, API_URL);
            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "post /pts/v2/payments/" + requestDTO.paymentId + "/voids";

                string host = _host_url;
                string MerchantId = _merchant_id;
                //string SecretKey = _rest_secret_key;
                string KeyId = _public_key;

                string JsonObj = JsonConvert.SerializeObject(voidRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });

                var Digest = GenerateDigest(JsonObj);

                var SignatureParm = "host: " + host + "\nv-c-date: " + requestDate + "\nrequest-target: " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("SignatureParm: " + SignatureParm);
                var SignatureHash = GenerateSignatureFromParams(SignatureParm);

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "POST";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + ALGORITHM + "\", headers=\"host v-c-date request-target digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("Void httpWebRequest headers: " + httpWebRequest.Headers);

                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    responseFromServer = streamReader.ReadToEnd();
                }

                // deserialize the response received
                VoidResponseDTO response = deserializeVoidResponse(responseFromServer);
                log.Info(responseFromServer);

                log.LogMethodEntry(response);
                return response;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// REFUND: requires Cybersource Payment Id, ccRequestId, totalAmount to be refunded and currency
        /// </summary>
        /// <param name="requestDTO"></param>
        /// <param name="refundRequestDTO"></param>
        /// <returns></returns>
        public RefundResponseDTO CreateRefund(WorldPayWebRequestDTO requestDTO, WPRefundRequestDTO refundRequestDTO)
        {
            log.LogMethodEntry(requestDTO, refundRequestDTO);
            RefundResponseDTO response = null;
            //string SCHEME = SCHEME;
            //string HOST = host_url;
            //string BASE_URL = SCHEME + host_url;
            try
            {
                if (!string.IsNullOrEmpty(_base_url))
                {
                    string API_URL = _base_url + "/pts/v2/payments/" + requestDTO.paymentId + "/refunds";
                    log.Info(API_URL);
                    if (requestDTO != null)
                    {
                        response = MakeRefundRequest(requestDTO, refundRequestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(response);
            return response;
        }

        private RefundResponseDTO MakeRefundRequest(WorldPayWebRequestDTO requestDTO, WPRefundRequestDTO refundRequestDTO, string API_URL)
        {
            log.LogMethodEntry(requestDTO, refundRequestDTO, API_URL);
            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "post /pts/v2/payments/" + requestDTO.paymentId + "/refunds";

                string host = _host_url;
                string MerchantId = _merchant_id;
                //string SecretKey = _rest_secret_key;
                string KeyId = _public_key;
                string algorithm = ALGORITHM;

                string JsonObj = JsonConvert.SerializeObject(refundRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });

                var Digest = GenerateDigest(JsonObj);

                var SignatureParm = "host: " + host + "\nv-c-date: " + requestDate + "\nrequest-target: " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("SignatureParm: " + SignatureParm);
                var SignatureHash = GenerateSignatureFromParams(SignatureParm);

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "POST";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + algorithm + "\", headers=\"host v-c-date request-target digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("Refund httpWebRequest headers: " + httpWebRequest.Headers);

                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    responseFromServer = streamReader.ReadToEnd();
                }

                // deserialize the response received
                RefundResponseDTO response = deserializeRefundResponse(responseFromServer);
                log.LogMethodExit(response);
                return response;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        private string MakeAPIRequest(string requestDate, string requestTarget, string JsonObj, string Digest, string API_URL, string Method = "POST")
        {
            log.LogMethodEntry();
            try
            {
                string host = _host_url;
                string MerchantId = _merchant_id;
                string KeyId = _public_key;
                string Algorithm = ALGORITHM;

                var signatureParams = "host: " + host + "\nv-c-date: " + requestDate + "\nrequest-target: " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("signatureParams: " + signatureParams);
                var SignatureHash = GenerateSignatureFromParams(signatureParams);

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = Method;

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + Algorithm + "\", headers=\"host v-c-date request-target digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("TxSearch httpWebRequest headers: " + httpWebRequest.Headers);

                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    responseFromServer = streamReader.ReadToEnd();
                }

                log.LogMethodExit();
                return responseFromServer;
            }
            catch (Exception ex)
            {
                log.Error("An error occured while making an HTTP API request");
                log.Error("host: " +  _host_url + " , Method: " + Method);
                log.Error("requestDate: " + requestDate + "requestTarget: " + requestTarget);
                throw new HttpException("An error occured while making an HTTP API request", ex);
            }
        }

        // Methods for Deserialization of the response
        private VoidResponseDTO deserializeVoidResponse(string response)
        {
            log.LogMethodEntry(response);
            try
            {
                log.LogMethodExit(response);
                return JsonConvert.DeserializeObject<VoidResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }
        private TxStatusResponseDTO deserializeTxStatusResponse(string response)
        {
            try
            {
                return JsonConvert.DeserializeObject<TxStatusResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }
        private RefundResponseDTO deserializeRefundResponse(string response)
        {
            try
            {
                return JsonConvert.DeserializeObject<RefundResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }
        private TxSearchResponseDTO deserializeTxSearchResponse(string response)
        {
            try
            {
                return JsonConvert.DeserializeObject<TxSearchResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }
        // End Deserialization

        public TxSearchRequestDTO GetTxSearchRequestDTO(string paymentGuid)
        {
            try
            {
                TxSearchRequestDTO searchRequestDTO = new TxSearchRequestDTO
                {
                    query = "clientReferenceInformation.code:" + paymentGuid,
                    sort = "id:desc",
                };
                return searchRequestDTO;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public string GenerateDigest(string jsonRestBody)
        {
            log.LogMethodEntry();
            var digest = "";
            var bodyText = jsonRestBody;
            try
            {
                using (var sha256hash = SHA256.Create())
                {
                    byte[] payloadBytes = sha256hash
                        .ComputeHash(Encoding.UTF8.GetBytes(bodyText));
                    digest = Convert.ToBase64String(payloadBytes);
                    digest = "SHA-256=" + digest;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(digest);
            return digest;
        }

        private string GenerateSignatureFromParams(string signatureParams)
        {
            log.LogMethodEntry();
            try
            {
                byte[] sigBytes = Encoding.UTF8.GetBytes(signatureParams);
                byte[] decodedSecret = Convert.FromBase64String(_rest_secret_key);
                HMACSHA256 hmacSha256 = new HMACSHA256(decodedSecret);
                byte[] messageHash = hmacSha256.ComputeHash(sigBytes);
                log.LogMethodExit(Convert.ToBase64String(messageHash));
                return Convert.ToBase64String(messageHash);
            }
            catch (Exception ex)
            {
                log.Error("Error generating signature from params: " + signatureParams);
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        //private string GetConfigParams(Dictionary<string, string> configParameters, string key)
        //{
        //    string configvalue = "";
        //    try
        //    {
        //        if (configParameters.ContainsKey(key))
        //        {
        //            configParameters.TryGetValue(key, out configvalue);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Error(ex.Message);
        //        throw new Exception(ex.Message);
        //    }
        //    return configvalue;
        //}

        private string getFormattedRequestDate()
        {
            log.LogMethodEntry();
            log.LogMethodExit(DateTime.Now.ToUniversalTime().ToString("r"));
            return DateTime.Now.ToUniversalTime().ToString("r");
        }


        public WorldPayWebRequestDTO getRequestDTO(string paymentId)
        {
            try
            {
                WorldPayWebRequestDTO requestDTO = null;
                requestDTO = new WorldPayWebRequestDTO
                {
                    paymentId = paymentId,
                };

                return requestDTO;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }


        //public void WriteToFile(string text)
        //{
        //    try
        //    {
        //        var dir = HttpContext.Current.Server.MapPath("~/responses/");
        //        string fileName = DateTime.Now.ToString("d") + ".txt";
        //        var file = Path.Combine(dir, fileName);

        //        Directory.CreateDirectory(dir);
        //        //File.WriteAllText(file, text);
        //        File.AppendAllText(file, text);
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Error(ex.Message);
        //        throw new Exception(ex.ToString());
        //    }

        //}

        public void WriteWebhookResponseToFile(string text)
        {
            try
            {
                var dir = HttpContext.Current.Server.MapPath("~/responses/");
                string fileName = "WR_" + DateTime.Now.ToString("d") + ".txt";
                var file = Path.Combine(dir, fileName);

                Directory.CreateDirectory(dir);
                //File.WriteAllText(file, text);
                File.AppendAllText(file, text);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.ToString());
            }

        }

        public String getUUID()
        {
            return Guid.NewGuid().ToString();
        }

        //public String getUTCDateTime()
        //{
        //    DateTime time = ServerDateTime.Now.ToUniversalTime();
        //    return time.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
        //}

        //public Dictionary<string, string> getUserBillingDetails(string userId)
        //{
        //    try
        //    {
        //        // fetch user billing details from the DB
        //        // Assumed the details have been fetched; now proceed
        //        Dictionary<string, string> userBillingDetails = new Dictionary<string, string>();
        //        userBillingDetails.Add("bill_to_forename", "Prasad");
        //        userBillingDetails.Add("bill_to_surname", "Diwan");
        //        userBillingDetails.Add("bill_to_email", "<EMAIL>");
        //        userBillingDetails.Add("bill_to_address_line1", "74 Shore Street");
        //        userBillingDetails.Add("bill_to_address_line2", "");
        //        userBillingDetails.Add("bill_to_address_city", "Stoke Sub Hambon");
        //        userBillingDetails.Add("bill_to_address_state", "");
        //        userBillingDetails.Add("bill_to_address_country", "GB"); // Use the two character ISO country code.
        //        userBillingDetails.Add("bill_to_address_postal_code", "TA14 3GD");
        //        userBillingDetails.Add("locale", "en");

        //        return userBillingDetails;
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Error(ex.Message);
        //        throw new Exception(ex.Message);
        //    }

        //}

        public TxStatusDTO GetTxStatusFromSearchResponse(TxSearchResponseDTO response)
        {
            log.LogMethodEntry(response);
            TxStatusDTO txStatusDTO = null;
            if (response != null)
            {
                try
                {
                    if (response.totalCount > 0)
                    {
                        List<Transactionsummary> transactionSummaries = response._embedded.transactionSummaries;
                        var transactions = from transaction in transactionSummaries
                                           from app in transaction.applicationInformation.applications
                                           where app.name == "ics_bill" && app.reasonCode == "100" // second condition added on 09 Nov 2022
                                           select transaction;
                        if (transactions.Count() > 0)
                        {
                            // check for void/refund
                            var voidTx = from transaction_void in transactionSummaries
                                         from app_void in transaction_void.applicationInformation.applications
                                         where app_void.name == "ics_void"
                                         select transaction_void;
                            var refundTx = from transaction_refund in transactionSummaries
                                           from app_refund in transaction_refund.applicationInformation.applications
                                           where app_refund.name == "ics_credit"
                                           select transaction_refund;
                            if (voidTx.Count() > 0)
                            {
                                //void found
                                log.Debug("Void already done");
                                txStatusDTO = new TxStatusDTO
                                {
                                    reasonCode = -2,
                                    status = "TX NOT FOUND",
                                    TxType = "NA"
                                };
                            }
                            else if (refundTx.Count() > 0)
                            {
                                //refund found
                                log.Debug("Refund already done");
                                txStatusDTO = new TxStatusDTO
                                {
                                    reasonCode = -2,
                                    status = "TX NOT FOUND",
                                    TxType = "NA"
                                };
                            }
                            else
                            {
                                // proceed with sale
                                foreach (var tx in transactions)
                                {
                                    if (tx.applicationInformation.reasonCode == 100)
                                    {
                                        txStatusDTO = new TxStatusDTO();
                                        txStatusDTO.reasonCode = Convert.ToInt32(tx.applicationInformation.reasonCode);
                                        txStatusDTO.RefNo = tx.id;
                                        txStatusDTO.InvoiceNo = tx.clientReferenceInformation.code.ToString();
                                        txStatusDTO.AuthCode = tx.processorInformation.approvalCode.ToString();
                                        txStatusDTO.Authorize = tx.orderInformation.amountDetails.totalAmount.ToString();
                                        txStatusDTO.Purchase = txStatusDTO.Authorize;
                                        txStatusDTO.TransactionDatetime = tx.submitTimeUtc;
                                        txStatusDTO.AcctNo = string.Concat("**** **** **** ", tx.paymentInformation.card.suffix.ToString());
                                        //txStatusDTO.RecordNo = tx.id;
                                        var app = from application in tx.applicationInformation.applications
                                                  where application.name == "ics_bill"
                                                  select application;
                                        foreach (var application_obj in app)
                                        {
                                            if (application_obj.rMessage != null)
                                            {
                                                txStatusDTO.TextResponse = application_obj.rMessage.ToString();
                                            }
                                        }

                                        txStatusDTO.TxType = "SALE";
                                    }
                                    else
                                    {
                                        // reasoncode other than 100
                                        txStatusDTO = new TxStatusDTO();
                                        txStatusDTO.reasonCode = Convert.ToInt32(tx.applicationInformation.reasonCode);
                                        txStatusDTO.InvoiceNo = tx.clientReferenceInformation.code.ToString();
                                        txStatusDTO.TxType = "SALE";
                                        var app = from application in tx.applicationInformation.applications
                                                  where application.name == "ics_bill"
                                                  select application;
                                        if (app.Count() > 0)
                                        {
                                            foreach (var application_obj in app)
                                            {
                                                if (application_obj.rMessage != null)
                                                {
                                                    txStatusDTO.TextResponse = application_obj.rMessage.ToString();
                                                }
                                            }
                                        }
                                        else
                                        {
                                            txStatusDTO.TextResponse = String.Empty;

                                        }

                                    }
                                }
                            }
                        }
                        else
                        {
                            txStatusDTO = new TxStatusDTO
                            {
                                reasonCode = -2,
                                status = "TX NOT FOUND",
                                TxType = "NA"
                            };
                        }
                    }
                    else
                    {
                        txStatusDTO = new TxStatusDTO
                        {
                            reasonCode = -2,
                            status = "TX NOT FOUND",
                            TxType = "NA"
                        };
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw new Exception(ex.Message);
                }

            }
            else
            {
                txStatusDTO = new TxStatusDTO
                {
                    reasonCode = -2,
                    status = "TX NOT FOUND",
                    TxType = "NA"
                };
            }
            log.LogMethodExit(txStatusDTO);
            return txStatusDTO;
        }

        public bool verifySignature(string response)
        {
            log.LogMethodEntry(response);
            bool result = false;
            
            WorldPayWebResponseDTO responseObj = JsonConvert.DeserializeObject<WorldPayWebResponseDTO>(response);
            if (responseObj != null)
            {
                Dictionary<string, string> responseParams = new Dictionary<string, string>();
                Dictionary<string, string> signatureParams = new Dictionary<string, string>();
                responseParams = JsonConvert.DeserializeObject<Dictionary<string, string>>(response);
                foreach (KeyValuePair<string, string> pair in responseParams)
                {
                    try
                    {
                        //log.Debug(pair.Key + ":" + pair.Value);
                        signatureParams.Add(pair.Key, pair.Value);
                    }
                    catch (Exception ex)
                    {
                        log.Error(ex);
                    }
                }
                log.Debug("Initiating sign process ");
                string sign = _wpCybersourceSecurity.sign(signatureParams, _secret_key);
                log.Debug("Generated Signature " + sign);
                log.Debug("Incoming Signature " + responseObj.signature);
                if (sign.Equals(responseObj.signature))
                {
                    result = true;
                }
            }
            log.LogMethodExit(result);
            return result;
        }

        //public bool verifySignature(WorldPayWebResponseDTO responseObj, string secret_key)
        //{
        //    log.LogMethodEntry(responseObj, secret_key);
        //    bool result = false;
        //    try
        //    {
        //        if (responseObj != null)
        //        {
        //            Dictionary<string, string> responseParams = new Dictionary<string, string>();
        //            Dictionary<string, string> signatureParams = new Dictionary<string, string>();
        //            responseParams = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonConvert.SerializeObject(responseObj));
        //            foreach (KeyValuePair<string, string> pair in responseParams)
        //            {
        //                try
        //                {
        //                    //log.Debug(pair.Key + ":" + pair.Value);
        //                    signatureParams.Add(pair.Key, pair.Value);
        //                }
        //                catch (Exception ex)
        //                {
        //                    log.Error(ex);
        //                }
        //            }
        //            log.Debug("Initiating sign process ");
        //            string sign = _wpCybersourceSecurity.sign(signatureParams, secret_key);
        //            log.Debug("Generated Signature " + sign);
        //            log.Debug("Incoming Signature " + responseObj.signature);
        //            if (sign.Equals(responseObj.signature))
        //            {
        //                result = true;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Error("Gor error in verify signature " + ex);
        //    }
        //    log.LogMethodExit(result);
        //    return result;
        //}
    }

}
