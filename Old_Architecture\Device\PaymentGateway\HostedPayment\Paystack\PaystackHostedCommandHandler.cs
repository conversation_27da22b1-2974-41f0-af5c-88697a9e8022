﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using System;
using System.Text;


namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Paystack
{

    public class PaystackHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private readonly string SECRET_KEY;
        private readonly string BASE_URL;
        private readonly string CHECKOUT_URL;
        private readonly string VERIFY_URL;
        private readonly string REFUND_URL;
        public PaystackHostedCommandHandler(string SECRET_KEY, string BASE_URL, string CHECKOUT_URL, string VERIFY_URL, string REFUND_URL)
        {

            this.SECRET_KEY = SECRET_KEY;
            this.BASE_URL = BASE_URL;
            this.CHECKOUT_URL = CHECKOUT_URL;
            this.VERIFY_URL = VERIFY_URL;
            this.REFUND_URL = REFUND_URL;
        }
        /// <summary>
        /// Sends an API request to the specified URL using the POST method with an optional request body.
        /// </summary>
        /// <param name="apiUrl">The URL of the API endpoint to send the request to.</param>
        /// <param name="requestBody">The optional request body to include in the request.</param>
        /// <returns>
        /// Returns the response received from the API as a string.
        /// </returns>
        public string SendApiRequest(string apiUrl, string requestBody = null)
        {

            WebRequestClient webRequestClient = new WebRequestClient(apiUrl, HttpVerb.POST, requestBody);
            webRequestClient.ContentType = "application/json";
            webRequestClient.IsBasicAuthentication = false;
            webRequestClient.Password = "Bearer " + SECRET_KEY;

            string txSearchResponse = webRequestClient.MakeRequest();
            return txSearchResponse;
        }
        /// <summary>
        /// Creates a checkout session by sending a request to the PayStack API with the provided transaction details.
        /// </summary>
        /// <param name="checkoutRequestDTO">The PayStackTransactionRequestDTO containing details of the checkout request.</param>
        /// <returns>
        /// Returns a PayStackResponseDTO representing the response received from the PayStack API.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error creating the payment request or processing the response.</exception>
        public PayStackResponseDTO CreateCheckout(PayStackTransactionRequestDTO checkoutRequestDTO)
        {
            log.LogMethodEntry("CreateChechout() request: " + checkoutRequestDTO);
            PayStackResponseDTO checkoutResponseDto = null;
            try
            {
                if (checkoutRequestDTO == null)
                {
                    log.Error($"CreateCheckout(): Request was empty.");
                    throw new Exception("Error creating payment request");
                }
                string responseFromServer;
                responseFromServer = SendApiRequest(CHECKOUT_URL, checkoutRequestDTO.ToString());
                checkoutResponseDto = JsonConvert.DeserializeObject<PayStackResponseDTO>(responseFromServer);
            }
            catch (Exception ex)
            {
                log.Error($"Exception in CreateCheckout(): {ex}");
                throw;
            }
            log.LogMethodExit("CreateChechout() response: " + checkoutResponseDto);
            return checkoutResponseDto;

        }
        /// <summary>
        /// Prepares a gateway request string as an HTML page that automatically redirects the user to the specified checkout URL.
        /// </summary>
        /// <param name="checkoutURL">The URL to redirect the user to for the checkout process.</param>
        /// <returns>
        /// Returns a string representing the HTML page with JavaScript to perform the redirection.
        /// </returns>
        public string PrepareGatewayRequestString(string checkoutURL)
        {
            log.LogMethodEntry("PrepareGatewayRequestString()" + checkoutURL);
            StringBuilder strBuilder = new StringBuilder();

            strBuilder.Append("<html>");
            strBuilder.Append("<body onload=\"loadPayment()\">");
            strBuilder.Append("<script>function loadPayment() { ");
            strBuilder.Append($"window.location.replace(\"{checkoutURL}\");");
            strBuilder.Append("}</script>");

            strBuilder.Append("</body></html>");
            log.LogMethodExit();
            return strBuilder.ToString();

        }
        /// <summary>
        /// Verifies a payment transaction with the specified reference using the PayStack API.
        /// </summary>
        /// <param name="reference">The reference ID of the transaction to be verified.</param>
        /// <returns>
        /// Returns a PayStackVerifyTransactionResponseDTO representing the response received from the API.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment verification request or handling the response.</exception>
        public PayStackVerifyTransactionResponseDTO VerifyPayment(string reference)
        {
            log.LogMethodEntry("VerifyPayment() reference: " + reference);
            PayStackVerifyTransactionResponseDTO checkoutResponseDto = null;
            try
            {
                log.Debug($"GetPaymentList() RequestDto: {reference}");
                if (reference == null)
                {
                    log.Error("GetPaymentSession() Request was empty.");
                    throw new Exception("Error occurred while processing your payment");
                }

                string API_URL = VERIFY_URL + reference;
                string responseFromServer;

                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.GET);
                webRequestClient.ContentType = "application/json";
                webRequestClient.IsBasicAuthentication = false;
                webRequestClient.Password = "Bearer " + SECRET_KEY;

                responseFromServer = webRequestClient.GetResponse();
                checkoutResponseDto = JsonConvert.DeserializeObject<PayStackVerifyTransactionResponseDTO>(responseFromServer);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit("VerifyPayment() payment response: " + checkoutResponseDto);
            return checkoutResponseDto;
        }

        /// <summary>
        /// Initiates a refund request for a transaction using the PayStack API.
        /// </summary>
        /// <param name="refundRequestDto">The PayStackRefundRequestDTO containing details of the refund request.</param>
        /// <returns>
        /// Returns a PayStackRefundDetailsDTO representing the response received from the PayStack API.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error creating the refund request or processing the response.</exception>
        public PayStackRefundDetailsDTO CreateRefund(PayStackRefundRequestDTO refundRequestDto)
        {
            log.LogMethodEntry("CreateRefund() refund request: " + refundRequestDto);
            PayStackRefundDetailsDTO refundResponseDto = null;
            try
            {
                if (refundRequestDto == null)
                {
                    log.Error($"CreateRefund(): Request was empty.");
                    throw new Exception("Request was empty.");
                }

                string responseFromServer;

                //string requestString = JsonConvert.SerializeObject(refundRequestDto);
                responseFromServer = SendApiRequest(REFUND_URL, refundRequestDto.ToString());
                log.Debug("responseFromServer: " + responseFromServer);
                refundResponseDto = JsonConvert.DeserializeObject<PayStackRefundDetailsDTO>(responseFromServer);
            }
            catch (Exception ex)
            {
                log.Error($"Exception in CreateRefund(): {ex}");
                throw;
            }
            log.LogMethodExit("CreateRefund() response: " + refundResponseDto.ToString());
            return refundResponseDto;
        }

    }

}

