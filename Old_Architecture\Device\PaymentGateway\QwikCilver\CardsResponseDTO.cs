﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
   public class CardsResponseDTO
    {
        public string CardNumber { get; set; }
        public string CardPin { get; set; }
        public string CardStatus { get; set; }
        public int CardStatusId { get; set; }
        public string CardType { get; set; }
        public int CardBehaviourTypeId { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string TransferCardNumber { get; set; }
        public DateTime TransferCardExpiry { get; set; }
        public string CorporateName { get; set; }
        public double Balance { get; set; }
        public double TransferCardBalance { get; set; }
        public double AdjustmentAmount { get; set; }
        public double ActivationAmount { get; set; }
        public double PreXactionCardBalance { get; set; }
        public double TransactionAmount { get; set; }
        public object ReloadableAmount { get; set; }
        public double TotalPreAuthAmount { get; set; }
        public double TotalReloadedAmount { get; set; }
        public double TotalRedeemedAmount { get; set; }
        public string CardCurrencySymbol { get; set; }
        public string CurrencyCode { get; set; }
        public float CurrencyConversionRate { get; set; }
        public double NativeCardBalance { get; set; }
        public string NativeCurrencyCode { get; set; }
        public string CardNativeBalanceWithoutSymbol { get; set; }
        public double CurrencyConvertedXactionAmount { get; set; }
        public double PreXactionCardBalanceInNativeCurrency { get; set; }
        public string PreXactionCardBalanceInNativeCurrencyWithSymbol { get; set; }
        public string CardCreationType { get; set; }
        public DateTime ActivationDate { get; set; }
        public object Holder { get; set; }
        public string InvoiceNumber { get; set; }
        public string CardProgramGroupType { get; set; }
        public string IssuerName { get; set; }
        public string EmployeeId { get; set; }
        public DateTime? TransactionDateTime { get; set; }
        public string ThemeId { get; set; }
        public string Trackdata { get; set; }
        public string ActivationCode { get; set; }
        public string CardIssuingMode { get; set; }
        public string ActivationURL { get; set; }
        public string BarCode { get; set; }
        public string ApprovalCode { get; set; }
        public int ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorDescription { get; set; }
        public DateTime RedeemStartDate { get; set; }
        public string PreAuthCode { get; set; }
        public object RecentTransactions { get; set; }
        public bool Transferable { get; set; }
        public bool Reusable { get; set; }
        public object Beneficiaries { get; set; }
        public string Notes { get; set; }
        public string Reason { get; set; }
        public List<object> CardFormats { get; set; }
        public string SequenceNumber { get; set; }
        public object Buckets { get; set; }
        public object ExtendedParameters { get; set; }
    }
}
