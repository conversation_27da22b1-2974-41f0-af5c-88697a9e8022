﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{877075FA-9234-4F68-BE7E-E4D9D08B02D6}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Semnox.Parafait.WebPayments</RootNamespace>
    <AssemblyName>WebPayments</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=5.2.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\OTS\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\OTS\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CompleteWebPaymentDTO.cs" />
    <Compile Include="IWebPaymentsUseCases.cs" />
    <Compile Include="LocalWebPaymentsUseCases.cs" />
    <Compile Include="PaymentGatewayHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RemoteWebPaymentsUseCases.cs" />
    <Compile Include="ReverseWebPaymentDTO.cs" />
    <Compile Include="WebPaymentDTO.cs" />
    <Compile Include="WebPaymentException.cs" />
    <Compile Include="WebPaymentsUseCaseFactory.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Customer\Customer.csproj">
      <Project>{09EC39CF-3DD3-4920-A155-CADE5B252B8A}</Project>
      <Name>Customer</Name>
    </ProjectReference>
    <ProjectReference Include="..\GenericUtilities\GenericUtilities.csproj">
      <Project>{5EEC3FD1-9EA6-404E-9C7A-554268A3EF80}</Project>
      <Name>GenericUtilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Languages\Languages.csproj">
      <Project>{E7CEB68F-78BB-44F8-9B4E-EA3FB43DAC9A}</Project>
      <Name>Languages</Name>
    </ProjectReference>
    <ProjectReference Include="..\logger\logger.csproj">
      <Project>{36b671d0-ef70-42db-940b-6a3622023f58}</Project>
      <Name>logger</Name>
    </ProjectReference>
    <ProjectReference Include="..\Logging\Logging.csproj">
      <Project>{1a1bac8c-a16a-433c-8856-2ec4047d31fb}</Project>
      <Name>Logging</Name>
    </ProjectReference>
    <ProjectReference Include="..\PaymentGatewayInterface\PaymentGatewayInterface.csproj">
      <Project>{d3e584d8-124a-46a3-8a52-6f4f3748ce31}</Project>
      <Name>PaymentGatewayInterface</Name>
    </ProjectReference>
    <ProjectReference Include="..\PaymentGateway\PaymentGateway.csproj">
      <Project>{e7b2f2e5-4ee0-4cda-8e02-07738ee3450e}</Project>
      <Name>PaymentGateway</Name>
    </ProjectReference>
    <ProjectReference Include="..\PaymentMode\PaymentMode.csproj">
      <Project>{f2462931-8892-42c1-9d45-c8da7f6c4a61}</Project>
      <Name>PaymentMode</Name>
    </ProjectReference>
    <ProjectReference Include="..\POS\POS.csproj">
      <Project>{EDD835A5-1862-4AED-B3F0-1C134B40FDB4}</Project>
      <Name>POS</Name>
    </ProjectReference>
    <ProjectReference Include="..\Site\Site.csproj">
      <Project>{B0419249-F9F6-425F-9B56-13D2E12F319F}</Project>
      <Name>Site</Name>
    </ProjectReference>
    <ProjectReference Include="..\Transaction\Transaction.csproj">
      <Project>{266543A2-B12C-46FE-BCF1-A67319B9CFB8}</Project>
      <Name>Transaction</Name>
    </ProjectReference>
    <ProjectReference Include="..\User\User.csproj">
      <Project>{E30D3295-E230-44D2-880D-22BF5A349119}</Project>
      <Name>User</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utilities\Utilities.csproj">
      <Project>{667EAB12-6C89-48ED-9FCB-934697CC2779}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\ViewContainer\ViewContainer.csproj">
      <Project>{b73af819-c0b5-4aa6-8c09-e8643f1437d1}</Project>
      <Name>ViewContainer</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>