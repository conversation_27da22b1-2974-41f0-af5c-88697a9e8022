﻿/********************************************************************************************
 * Project Name - Payment GatewayInterface
 * Description  - Adyen POS ResponseDTO
 * 
 **************
 **Version Log
 **************
 *Version     Date              Modified By                    Remarks          
 *********************************************************************************************
 *  2.200.0    24-Sep-2024       Amrutha                        Adyen POS Redesign
 *******************************************************************************************/

using Newtonsoft.Json;
using System;
using System.Collections.Generic;


namespace Semnox.Parafait.PaymentGatewayInterface
{
    internal class AdyenPosResponseDto
    {
    }

    #region [PAYMENT]
    public class PaymentResponseDto
    {
        public Saletopoiresponse SaleToPOIResponse { get; set; }
        public Saletopoirequest SaleToPOIRequest { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class Saletopoiresponse
    {
        public Messageheader MessageHeader { get; set; }
        public Paymentresponse PaymentResponse { get; set; }
        public Reversalresponse ReversalResponse { get; set; }
        public Transactionstatusresponse TransactionStatusResponse { get; set; }
    }

    public class EventNotification
    {
        public string RejectedMessage { get; set; }
        public string EventToNotify { get; set; }
        public string TimeStamp { get; set; }
        public string EventDetails { get; set; }
    }

    public class Paymentresponse
    {
        public Poidata POIData { get; set; }
        public AdyenResponse Response { get; set; }
        public Saledata SaleData { get; set; }
        public Paymentreceipt[] PaymentReceipt { get; set; }
        public Paymentresult PaymentResult { get; set; }

        public double ReversedAmount { get; set; }
    }

    public class Poidata
    {
        public Poitransactionid POITransactionID { get; set; }
        public string POIReconciliationID { get; set; }
    }

    public class Poitransactionid
    {
        public DateTime TimeStamp { get; set; }
        public string TransactionID { get; set; }
    }

    public class AdyenResponse
    {
        public string AdditionalResponse { get; set; }
        public string ErrorCondition { get; set; }
        public string Result { get; set; }
    }
    public class AmountsResp
    {
        public double AuthorizedAmount { get; set; }
        public string Currency { get; set; }
        public double TipAmount { get; set; }
    }
    #endregion

    #region [GetTransactionStatus]
    public class GetTransactionStatusResponseDto
    {
        public Saletopoiresponse SaleToPOIResponse { get; set; }
        public Saletopoirequest SaleToPOIRequest { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class Paymentresult
    {
        public string[] AuthenticationMethod { get; set; }
        public bool OnlineFlag { get; set; }
        public Paymentacquirerdata PaymentAcquirerData { get; set; }
        public Paymentinstrumentdata PaymentInstrumentData { get; set; }
        public AmountsResp AmountsResp { get; set; }
        public string PaymentType { get; set; }
    }

    public class Paymentacquirerdata
    {
        public string AcquirerPOIID { get; set; }
        public Acquirertransactionid AcquirerTransactionID { get; set; }
        public string MerchantID { get; set; }
        public string ApprovalCode { get; set; }
    }

    public class Acquirertransactionid
    {
        public DateTime TimeStamp { get; set; }
        public string TransactionID { get; set; }
    }

    public class Paymentinstrumentdata
    {
        public Carddata CardData { get; set; }
        public string PaymentInstrumentType { get; set; }
    }

    public class Carddata
    {
        public string CardCountryCode { get; set; }
        public string[] EntryMode { get; set; }
        public string MaskedPan { get; set; }
        public string PaymentBrand { get; set; }
        public Sensitivecarddata SensitiveCardData { get; set; }
    }

    public class Sensitivecarddata
    {
        public string CardSeqNumb { get; set; }
        public string ExpiryDate { get; set; }
    }

    public class Paymentreceipt
    {
        public string DocumentQualifier { get; set; }
        public Outputcontent OutputContent { get; set; }
        public bool RequiredSignatureFlag { get; set; }
    }

    public class Outputcontent
    {
        public string OutputFormat { get; set; }
        public Outputtext[] OutputText { get; set; }
    }

    public class Outputtext
    {
        public string CharacterStyle { get; set; }
        public bool EndOfLineFlag { get; set; }
        public string Text { get; set; }
    }
    public class Transactionstatusresponse
    {
        public Messagereference MessageReference { get; set; }
        public Repeatedmessageresponse RepeatedMessageResponse { get; set; }
        public AdyenResponse Response { get; set; }
    }

    public class Repeatedmessageresponse
    {
        public Messageheader MessageHeader { get; set; }
        public Repeatedresponsemessagebody RepeatedResponseMessageBody { get; set; }
    }

    public class Repeatedresponsemessagebody
    {
        public Paymentresponse PaymentResponse { get; set; }
        public Paymentresponse ReversalResponse { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class Amountsresp
    {
        public int AuthorizedAmount { get; set; }
        public string Currency { get; set; }
        public int TipAmount { get; set; }
    }
    #endregion

    #region [REFUND]
    public class RefundResponseDto
    {
        public Saletopoiresponse SaleToPOIResponse { get; set; }
        public Saletopoirequest SaleToPOIRequest { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class Reversalresponse
    {
        public Poidata POIData { get; set; }
        public object[] PaymentReceipt { get; set; }
        public AdyenResponse Response { get; set; }
        public double ReversedAmount { get; set; }
    }

    #endregion

    #region [CAPTURE]
    public class CaptureResponse
    {
        public string merchantAccount { get; set; }
        public string paymentPspReference { get; set; }
        public string reference { get; set; }
        public string pspReference { get; set; }
        public string status { get; set; }
        public Amount amount { get; set; }
        public Platformchargebacklogic platformChargebackLogic { get; set; }
    }

    public class Platformchargebacklogic
    {
        public string behavior { get; set; }
        public string targetAccount { get; set; }
        public string costAllocationAccount { get; set; }
    }
    #endregion

    #region [RECURRING_PAYMENT]

    public class SubscriptionPaymentResponse
    {
        public Additionaldata additionalData { get; set; }
        public Fraudresult fraudResult { get; set; }
        public string pspReference { get; set; }
        public string resultCode { get; set; }
        public Amount amount { get; set; }
        public string merchantReference { get; set; }
        public Paymentmethod paymentMethod { get; set; }
    }

    public class Additionaldata
    {
        public string refusalReasonRaw { get; set; }
        public string eci { get; set; }
        public string acquirerAccountCode { get; set; }
        public string xid { get; set; }
        public string recurringcontractTypes { get; set; }
        public string recurringrecurringDetailReference { get; set; }
        public string recurringProcessingModel { get; set; }
        public string threeDAuthenticated { get; set; }
        public string paymentMethodVariant { get; set; }
        public string issuerBin { get; set; }
        public string fraudManualReview { get; set; }
        public string threeDOffered { get; set; }
        public string threeDOfferedResponse { get; set; }
        public string authorisationMid { get; set; }
        public string recurringfirstPspReference { get; set; }
        public object bankAccountiban { get; set; }
        public string cavv { get; set; }
        public object bankAccountownerName { get; set; }
        public string authorisedAmountCurrency { get; set; }
        public string threeDAuthenticatedResponse { get; set; }
        public string avsResultRaw { get; set; }
        public string retryattempt1rawResponse { get; set; }
        public string paymentMethod { get; set; }
        public string recurringshopperReference { get; set; }
        public string fundingSource { get; set; }
        public string avsResult { get; set; }
        public string cardSummary { get; set; }
        public string retryattempt1avsResultRaw { get; set; }
        public string networkTxReference { get; set; }
        public string expiryDate { get; set; }
        public string cavvAlgorithm { get; set; }
        public string cardBin { get; set; }
        public string alias { get; set; }
        public string cvcResultRaw { get; set; }
        public string merchantReference { get; set; }
        public string acquirerReference { get; set; }
        public string cardIssuingCountry { get; set; }
        public string liabilityShift { get; set; }
        public string fraudResultType { get; set; }
        public string authCode { get; set; }
        public string cardHolderName { get; set; }
        public string isCardCommercial { get; set; }
        public string PaymentAccountReference { get; set; }
        public string retryattempt1acquirerAccount { get; set; }
        public string retryattempt1acquirer { get; set; }
        public string authorisedAmountValue { get; set; }
        public string issuerCountry { get; set; }
        public string cvcResult { get; set; }
        public string retryattempt1responseCode { get; set; }
        public string aliasType { get; set; }
        public string retryattempt1shopperInteraction { get; set; }
        public string cardPaymentMethod { get; set; }
        public string acquirerCode { get; set; }
        public string adjustAuthorisationData { get; set; }

        // ESD Data fields
        [JsonProperty(PropertyName = "enhancedSchemeData.customerReference")]
        public string enhancedSchemeData_customerReference { get; set; }

        [JsonProperty(PropertyName = "enhancedSchemeData.destinationPostalCode")]
        public string enhancedSchemeData_destinationPostalCode { get; set; }

        [JsonProperty(PropertyName = "enhancedSchemeData.orderDate")]
        public string enhancedSchemeData_orderDate { get; set; }

    }

    public class Fraudresult
    {
        public int accountScore { get; set; }
        public List<Result> results { get; set; }
    }

    public class Result
    {
        public int accountScore { get; set; }
        public int checkId { get; set; }
        public string name { get; set; }
    }

    #endregion

    #region [ADJUST AUTHORISATION]

    public class AdjustAuthResponseDto
    {
        public Additionaldata additionalData { get; set; }
        public string pspReference { get; set; }
        public string response { get; set; }
    }

    #endregion

    #region [WEB REFUND]

    public class WebRefundResponseDto
    {
        public string merchantAccount { get; set; }
        public string paymentPspReference { get; set; }
        public string reference { get; set; }
        public string pspReference { get; set; }
        public string status { get; set; }

    }

    #endregion
}
