﻿/********************************************************************************************
 * Project Name - Hosted Payment
 * Description  - DTO Classes to create the payment requests
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/
using System.Collections.Generic;

namespace Semnox.Parafait.PaymentGateway
{
    public class PaymentGatewayCustomer
    {
        public string CustomerName { get; set; }
        public string CustomerEmail { get; set; }
        public string CustomerPhone { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string AddressLine3 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string PinCode { get; set; }
        public string PGIdentifier { get; set; }
        public string CustomerIdentifier { get; set; }
    }
    public class PaymentGatewayProduct
    {
        public string ProductName { get; set; }
        public int ProductQuantity { get; set; }
        public decimal ProductAmount { get; set; }
    }
    public class CreateHostedPaymentRequestDTO
    {
        public int TransactionId { get; set; }
        public int TransactionPaymentId { get; set; }
        public string TransactionPaymentGuid { get; set; }
        public string TransactionGuid { get; set; }
        public int SiteId { get; set; }
        public string POSMachine { get; set; }
        public int PaymentModeId { get; set; }
        public string PaymentMode { get; set; }
        public string CurrencyCode { get; set; }
        public decimal Amount { get; set; }
        public string SubscriptionAuthorizationMode { get; set; }
        public List<PaymentGatewayProduct> Products { get; set; }
        public PaymentGatewayCustomer PaymentGatewayCustomer { get; set; }
    }
}
