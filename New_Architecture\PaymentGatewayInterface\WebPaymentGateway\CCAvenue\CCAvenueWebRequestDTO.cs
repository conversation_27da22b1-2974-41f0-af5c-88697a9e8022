﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    class CCAvenueWebRequestDTO
    {
    }

    #region [CCAvenueRefundRequestDTO]

    public class CCAvenueRefundRequestDTO
    {
        public string reference_no { get; set; }
        public string refund_amount { get; set; }
        public string refund_ref_no { get; set; }

        public override string ToString()
        {
            StringBuilder returnValue = new StringBuilder("\n----------------------RefundRequestDTO-----------------------------\n");
            returnValue.Append(" Reference_no : " + reference_no.ToString());
            returnValue.Append(" Refund_amount : " + refund_amount.ToString());
            returnValue.Append(" Refund_ref_no : " + refund_ref_no.ToString());
            returnValue.Append("\n-------------------------------------------------------------\n");
            return returnValue.ToString();
        }
    }
    #endregion
}
