﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PayMaya
{
    public class AmountDetails
    {
        public double discount { get; set; }
        public double serviceCharge { get; set; }
        public double shippingFee { get; set; }
        public double tax { get; set; }
        public double subtotal { get; set; }
    }

    public class Amount
    {
        public double value { get; set; }
        public string currency { get; set; }
        public AmountDetails details { get; set; }
    }

    public class Contact
    {
        public string phone { get; set; }
        public string email { get; set; }
    }

    public class Address
    {
        public string firstName { get; set; }
        public string middleName { get; set; }
        public string lastName { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string line1 { get; set; }
        public string line2 { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string zipCode { get; set; }
        public string countryCode { get; set; }
        public string shippingType { get; set; } // ST - for standard, SD - for same day
    }

    public class Buyer
    {
        public string firstName { get; set; }
        public string middleName { get; set; }
        public string lastName { get; set; }
        public string birthday { get; set; }
        public string customerSince { get; set; }
        public string sex { get; set; }
        public Contact contact { get; set; }
        public Address shippingAddress { get; set; }
        public Address billingAddress { get; set; }
    }

    public class ItemAmountDetails
    {
        public decimal discount { get; set; }
        public decimal serviceCharge { get; set; }
        public decimal shippingFee { get; set; }
        public decimal tax { get; set; }
        public decimal subtotal { get; set; }
    }

    public class ItemAmount
    {
        public decimal value { get; set; }
        public ItemAmountDetails details { get; set; }
    }

    public class MetaData
    {
        public string paymentModeId { get; set; }
    }
    public class ItemTotalAmount
    {
        public decimal value { get; set; }
        public ItemAmountDetails details { get; set; }
    }

    public class Item
    {
        public string name { get; set; }
        public int quantity { get; set; }
        public string code { get; set; }
        public string description { get; set; }
        public ItemAmount amount { get; set; }
        public ItemTotalAmount totalAmount { get; set; }
    }

    public class RedirectUrl
    {
        public string success { get; set; }
        public string failure { get; set; }
        public string cancel { get; set; }
    }

    public class PayMayaHostedPaymentRequestDto
    {
        public Amount totalAmount { get; set; }
        public Buyer buyer { get; set; }
        public List<Item> items { get; set; }
        public RedirectUrl redirectUrl { get; set; }
        public string requestReferenceNumber { get; set; }
        public MetaData metadata { get; set; }
    }

    public class PayMayaHostedRefundRequestDto
    {
        public RefundAmount totalAmount { get; set; }
        public string reason { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class RefundAmount
    {
        public double amount { get; set; }
        public string currency { get; set; }
    }

    public class PayMayaHostedVoidRequestDto
    {

        public string reason { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }

    }
}
