﻿/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Payment Gateway
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.190.0     24-Sep-2024         Amrutha      Created
 ********************************************************************************************************/

using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Semnox.Parafait.Languages;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Site;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
//using System.Windows.Forms;

namespace Semnox.Parafait.PaymentGateway
{
    public class ParafaitPaymentGateway : PaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        IPaymentGateway paymentGateway = null;
        PaymentModeContainerDTO paymentModeContainerDTO = null;
        bool flag;


        public ParafaitPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken, IPaymentGateway paymentGateway, PaymentModeContainerDTO paymentModeContainerDTO)
            : base(executionContext, isUnattended, cancellationToken)
        {

            log.LogMethodEntry(executionContext, isUnattended, writeToLogDelegate, paymentGateway);
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel   
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12; // comparable to modern browsers

            this.paymentGateway = paymentGateway;
            this.ExecutionContext = executionContext;
            this.paymentModeContainerDTO = paymentModeContainerDTO;
        }

        public override void Initialize(int paymentModeId)
        {
            log.LogMethodEntry(paymentModeId);
            //paymentGateway.Initialize();
            log.LogMethodExit();
        }

        /// <summary>
        /// This method to validate the configuration of payment gateway
        /// </summary>
        public override void ValidateConfiguration()
        {
            log.LogMethodEntry();
            paymentGateway.ValidateConfiguration();
            log.LogMethodExit();
        }

        /// <summary>
        /// Whether the card used for the current transaction is a credit card
        /// </summary>
        public override bool IsPaymentHistoryListRequired
        {
            get
            {
                return paymentGateway.IsPaymentHistoryListRequired;
            }
        }

        /// <summary>
        /// This method to allow entering the tip for the transaction based on condition
        /// </summary>
        /// <param name="transactionPaymentDTO"></param>
        /// <param name="PaymentTransactionDTOList"></param>
        /// <returns></returns>
        public override bool IsTipEnabled(TransactionPaymentDTO transactionPaymentDTO, List<PaymentTransactionDTO> PaymentTransactionDTOList)
        {
            log.LogMethodEntry(transactionPaymentDTO, PaymentTransactionDTOList);
            PaymentResponseDTO paymentResponseDTO = ConvertToPaymentResponseDTO(transactionPaymentDTO.PaymentTransactionDTOList[0]);
            List<PaymentResponseDTO> paymentResponseDTOList = ConvertToPaymentResponseDTO(PaymentTransactionDTOList);
            log.LogMethodExit(paymentResponseDTOList);
            return paymentGateway.IsTipEnabled(paymentResponseDTO, paymentResponseDTOList);
        }

        /// <summary>
        ///checks if the gateway supports tip adjust.
        /// </summary>
        public override bool IsTipAdjustmentAllowed
        {
            get
            {
                return paymentGateway.IsTipAdjustmentAllowed;
            }

        }


        ///// <summary>
        ///// This method will return the transaction with Trancode "TATokenRequest"        
        ///// /// </summary>
        ///// <param name="paymentTransactionDTOList"></param>
        static PaymentTransactionDTO FindTATokenRequestTransaction(List<PaymentTransactionDTO> paymentTransactionDTOList)
        {
            return paymentTransactionDTOList.FirstOrDefault(transaction => transaction.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString());
        }

        public async override Task<PaymentTransactionDTO> MakePayment(TransactionPaymentDTO transactionPaymentDTO,
                                                                      List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                                      IProgress<PaymentProgressReport> progress,
                                                                      CancellationToken cancellationToken,
                                                                      TransactionDTO transactionDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO, paymentTransactionDTOList, progress, cancellationToken);
            this.cancellationToken = cancellationToken;
            List<PaymentResponseDTO> paymentResponseList = null;
            PaymentTransactionDTO preAuthPaymentTransactionDTO = null;
            progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, 1839, transactionPaymentDTO.Amount.ToString(ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "AMOUNT_WITH_CURRENCY_SYMBOL"))), false));

            PaymentGatewayTransactionType trxType = GetTransactionType(transactionPaymentDTO);
            log.Info($"trxType = {trxType}");
            bool isManual = false;

            if (transactionPaymentDTO.IsKeyedMode == null ? false : (bool)transactionPaymentDTO.IsKeyedMode)
            {
                isManual = true;
            }
            if(paymentTransactionDTOList != null)
            {
                 paymentResponseList = ConvertToPaymentResponseDTO(paymentTransactionDTOList); 
                // log.Info($"paymentResponseList{paymentResponseList}");

                log.Info("Finding preAuthPaymentTransactionDTO");
                preAuthPaymentTransactionDTO = FindTATokenRequestTransaction(paymentTransactionDTOList);
                log.Info($"preAuthPaymentTransactionDTO{JsonConvert.SerializeObject(preAuthPaymentTransactionDTO)}");

            }
            progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, 1008), true));//Processing please wait
            int parentResponseId = preAuthPaymentTransactionDTO != null ? preAuthPaymentTransactionDTO.ResponseID : -1;
            log.Info($"parentResponseId{parentResponseId}");

            decimal transactionNetAmount = transactionDTO.TransactionNetAmount;

            PaymentRequestDTO paymentRequestDTO = new PaymentRequestDTO
                (transactionPaymentDTO.Amount, transactionPaymentDTO.Guid, 
                transactionPaymentDTO.PaymentId, paymentResponseList, 
                isManual, SiteDateTime.GetSiteDateTime(ExecutionContext), ServerDateTime.Now, transactionPaymentDTO.SubscriptionAuthorizationMode.ToString(),transactionNetAmount);

            PaymentResponseDTO paymentResponseDTO = null;
            try
            {
                paymentResponseDTO = await MakePaymentImpl(progress, trxType, paymentRequestDTO, cancellationToken);
            }

            catch (NonRetryableException ex)
            {
                log.Error("Error Type = "  + " " + ex.GetType().Name); 
                paymentResponseDTO = BuildPaymentTransactionResponseDTO(paymentRequestDTO.RequestIdentifier,paymentRequestDTO.Amount,ex.Message);
            }
            catch (Exception ex)
            {
                log.Error("Error in the MakePayment method . Initiating last transaction status check");
                log.Error($"paymentRequestDTO{JsonConvert.SerializeObject(paymentRequestDTO)}");
                log.Error($"paymentResponseDTO{JsonConvert.SerializeObject(paymentResponseDTO)}");

                string message = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
                log.Error(ex);


                if (cancellationToken != null && cancellationToken.IsCancellationRequested)
                {
                    log.Info("Cancellation Requested");
                    paymentResponseDTO = BuildPaymentTransactionResponseDTO(paymentRequestDTO.RequestIdentifier, paymentRequestDTO.Amount, message);
                    paymentResponseDTO.Status = PaymentTransactionStatuses.CANCELLED.ToString();
                }
                else
                {
                    //IsStatusCheckRequiredOnErrorState this checks if the payment gateway StatusCheck method returns the exact fileds as returned by the actual API request.
                    try
                    {
                        paymentResponseDTO = await RecoverPaymentResponseUsingStatusCheck(paymentRequestDTO, progress, paymentResponseList, cancellationToken, transactionPaymentDTO.PaymentStatus, message, transactionPaymentDTO.TransactionId);
                    }
                    catch (Exception)
                    {
                        paymentResponseDTO = BuildPaymentTransactionResponseDTO(paymentRequestDTO.RequestIdentifier, paymentRequestDTO.Amount, message);
                    }
                }
            }

            //Donation Check 
            //   PaymentResponseDTO donationResponse = null;
            await MakeDonation(transactionPaymentDTO, progress, paymentResponseList, paymentResponseDTO, cancellationToken);

            PrintReceipt(paymentResponseDTO, transactionDTO);

            PaymentTransactionDTO paymentTransactionResponseDTO = ConvertToPaymentTransactionResponseDTO(transactionPaymentDTO, parentResponseId, paymentResponseDTO);

            log.LogMethodExit(paymentTransactionResponseDTO);
            return paymentTransactionResponseDTO;

        }



        private void PrintReceipt(PaymentResponseDTO paymentResponseDTO, TransactionDTO transactionDTO)
        {
            log.LogMethodEntry(paymentResponseDTO);
            string printReceipt = paymentModeContainerDTO.CcPaymentReceiptPrint;
            log.Info("printReceipt = " + printReceipt);
            log.Info("paymentResponseDTO = " + JsonConvert.SerializeObject(paymentResponseDTO));
            if (printReceipt != "Y" && printReceipt != "A")
            {
                log.LogMethodExit("printReceipt is not equal to either Y or A");
                return;
            }
            bool printCustomerReceipt = paymentModeContainerDTO.PrintCustomerReceipt;
            bool printMerchantReceipt = paymentModeContainerDTO.PrintMerchantReceipt;
            log.Info("printCustomerReceipt = " + printCustomerReceipt);
            log.Info("printMerchantReceipt = " + printMerchantReceipt);
            ParafaitPaymentPrintAttributes parafaitPaymentPrintAttributes = new ParafaitPaymentPrintAttributes(ExecutionContext,transactionDTO);
            try
            {
                if (printCustomerReceipt)
                {
                    log.Info("Printing Customer receipt.");
                    string receipt = paymentGateway.GetCustomerCopy(parafaitPaymentPrintAttributes, paymentResponseDTO);
                    paymentResponseDTO.CustomerCopy = receipt;
                    log.Info($"customer receipt= {receipt}");
                }
                if (printMerchantReceipt)
                {
                    log.Info("Printing Merchant receipt.");
                    string receipt = paymentGateway.GetMerchantCopy(parafaitPaymentPrintAttributes, paymentResponseDTO);
                    paymentResponseDTO.MerchantCopy = receipt;
                    log.Info($"merchant receipt= {receipt}");
                }
            }
            catch (Exception e)
            {
                log.Error("Error while printing the payment receipt." + e);
                log.Error($"parafaitPaymentPrintAttributes = {parafaitPaymentPrintAttributes}");
                log.Error($"paymentResponseDTO= {JsonConvert.SerializeObject(paymentResponseDTO)}");
            }
            log.LogMethodExit();
        }

        /// <summary>
        /// To check if the payment gateway supports donation. If yes and if there is a donation details,  then donation details will be saved in the 
        /// ccTransactionsPGW table .
        /// </summary>
        private async Task MakeDonation(TransactionPaymentDTO transactionPaymentDTO,
                                        IProgress<PaymentProgressReport> progress,
                                        List<PaymentResponseDTO> paymentResponseList,
                                        PaymentResponseDTO paymentResponseDTO,
                                        CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentDTO, progress, paymentResponseList,
                               paymentResponseDTO, cancellationToken);

            if (paymentResponseDTO.Status != "SUCCESS")
            {
                log.Info("Payment Failed not making donation");
                log.LogMethodExit("Payment Failed");
                return;
            }

            if (!paymentGateway.IsDonationSupported(paymentResponseDTO, paymentResponseList))
            {
                log.LogMethodExit("Payment gateway doesn't support donation");
                return;
            }

            try
            {
                log.Info("IsDonationSupported method initiated");
                log.Info("SaveDonationResponsen method called.");
                PaymentResponseDTO donationResponse = await paymentGateway.SaveDonationResponse(paymentResponseDTO, progress, cancellationToken);
                log.Info($"donationResponse{JsonConvert.SerializeObject(donationResponse)}");
                if (donationResponse != null)
                {
                    log.Info("Converting  donationResponse to PaymentTransactionResponseDTO");
                    PaymentTransactionDTO donationPaymentTransactionDTO = ConvertToPaymentTransactionResponseDTO(donationResponse, transactionPaymentDTO);
                    log.Info($"donationPaymentTransactionDTO{JsonConvert.SerializeObject(donationPaymentTransactionDTO)}");
                    log.Info("Calling SaveDonation usecase to save the donation details");
                    ITransactionUseCases transactionUseCases = TransactionUseCaseFactory.GetTransactionUseCases(ExecutionContext, Guid.NewGuid().ToString());
                    await transactionUseCases.SaveDonation(transactionPaymentDTO.PaymentId, donationPaymentTransactionDTO);
                }
            }
            catch (Exception e)
            {
                log.Error("Error occured in the donation");
                log.Error($"paymentResponseDTO{JsonConvert.SerializeObject(paymentResponseDTO)}");
                log.Error(e);
            }
            log.LogMethodExit();
        }

        private async Task<PaymentResponseDTO> RecoverPaymentResponseUsingStatusCheck(PaymentRequestDTO paymentRequestDTO,
                                                                                      IProgress<PaymentProgressReport> progress,
                                                                                      List<PaymentResponseDTO> paymentResponseList,
                                                                                      CancellationToken cancellationToken,
                                                                                      string paymentStatus,
                                                                                      string errorMessage,
                                                                                      int transactionId)
        {
            log.LogMethodEntry(paymentRequestDTO, progress, paymentResponseList, cancellationToken, paymentStatus);
            StatusCheckRequestDTO statusCheckRequestDTO = null;
            PaymentResponseDTO paymentResponseDTO;

            progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, 4986, transactionId, paymentRequestDTO.Amount), false));
            ///Checking the transaction status of TrxId: &1 Amount: &2



            PaymentResponseDTO statusCheckPaymentResponseDTO = null;
            if (paymentGateway.IsStatusCheckSupported)
            {
                log.Info("Converting transactionPaymentDTO to StatusCheckRequestDTO");
                statusCheckRequestDTO = new StatusCheckRequestDTO(paymentRequestDTO.Amount, paymentRequestDTO.RequestIdentifier, paymentRequestDTO.IntRequestIdentifier, paymentResponseList, paymentStatus,SiteDateTime.GetSiteDateTime(ExecutionContext));
                log.Info($"statusCheckRequestDTO {JsonConvert.SerializeObject(statusCheckRequestDTO)}");
                log.Info("Initiating StatusCheck ");
                statusCheckPaymentResponseDTO = await paymentGateway.StatusCheck(statusCheckRequestDTO, progress, cancellationToken, errorMessage);
                log.Info($"StatusCheck ResponseDTO {JsonConvert.SerializeObject(statusCheckPaymentResponseDTO)}");
            }
            else
            {
                log.Info("IsStatusCheckSupported == false");
                paymentResponseDTO = BuildPaymentTransactionResponseDTO(paymentRequestDTO.RequestIdentifier, paymentRequestDTO.Amount, errorMessage);
                log.LogMethodExit(paymentResponseDTO);
                return paymentResponseDTO;
            }

            if (paymentGateway.SupportsFullStatusCheck)
            {
                log.Info($"Returning StatusCheckPaymentResponseDTO to make the payment sucess {JsonConvert.SerializeObject(statusCheckPaymentResponseDTO)}");
                paymentResponseDTO = statusCheckPaymentResponseDTO;
                log.LogMethodExit(paymentResponseDTO);
                return paymentResponseDTO;
            }
            else
            {
                paymentResponseDTO = BuildPaymentTransactionResponseDTO(paymentRequestDTO.RequestIdentifier, paymentRequestDTO.Amount, errorMessage);
                if (statusCheckPaymentResponseDTO.Status == "SUCCESS")
                {
                    DateTime bussStartTime;
                    DateTime bussEndTime;

                    BusinessDate businessDate = new BusinessDate(ExecutionContext);
                    bussStartTime = businessDate;
                    bussEndTime = bussStartTime.AddDays(1);
                    log.Info($"Refunding the payment as we can't recover from errror");
                    RefundRequestDTO refundRequestDTO = new RefundRequestDTO(Convert.ToDecimal(statusCheckPaymentResponseDTO.Authorize), paymentRequestDTO.RequestIdentifier, paymentRequestDTO.IntRequestIdentifier, string.IsNullOrWhiteSpace(statusCheckPaymentResponseDTO.TipAmount) ? 0 : Convert.ToDecimal(statusCheckPaymentResponseDTO.TipAmount), statusCheckPaymentResponseDTO, paymentResponseList, SiteDateTime.GetSiteDateTime(ExecutionContext),bussStartTime,bussEndTime);
                    log.Error($"Refund RequestDTO = {JsonConvert.SerializeObject(refundRequestDTO)}");
                    log.Info($"refundRequestDTO= {JsonConvert.SerializeObject(refundRequestDTO)}");
                    log.Info("Calling Refund method");
                    PaymentResponseDTO refundPaymentResposeDTO = await paymentGateway.Refund(refundRequestDTO, progress, cancellationToken);
                    if (refundPaymentResposeDTO.Status == "SUCCESS")
                    {
                        paymentResponseDTO = BuildPaymentTransactionResponseDTO(paymentRequestDTO.RequestIdentifier, paymentRequestDTO.Amount, "Transaction Failed. Amount has been refunded.");
                    }
                }
            }

            return paymentResponseDTO;
        }

        private async Task<PaymentResponseDTO> MakePaymentImpl(IProgress<PaymentProgressReport> progress,
                                                               PaymentGatewayTransactionType trxType,
                                                               PaymentRequestDTO paymentRequestDTO,
                                                               CancellationToken cancellationToken)
        {
            log.LogMethodEntry(progress, trxType, paymentRequestDTO, cancellationToken);
            PaymentResponseDTO paymentResponseDTO = null;
            log.Info($"PaymentRequestDTO {JsonConvert.SerializeObject(paymentRequestDTO)}");

            switch (trxType)
            {
                case PaymentGatewayTransactionType.AUTHORIZATION:
                    {
                        log.Info("AUTH transaction has been initiated");
                        paymentResponseDTO = await paymentGateway.Auth(paymentRequestDTO, progress, cancellationToken);
                        break;
                    }
                case PaymentGatewayTransactionType.TATokenRequest:
                    {
                        log.Info("Initiating PREAUTH transaction ");
                        paymentResponseDTO = await paymentGateway.PreAuth(paymentRequestDTO, progress, cancellationToken);
                        break;
                    }
                case PaymentGatewayTransactionType.SALE:
                    {
                        log.Info("Initiating SALE transaction ");
                        paymentResponseDTO = await paymentGateway.Sale(paymentRequestDTO, progress, cancellationToken);
                        break;
                    }
                default:
                    {
                        log.Error($"Invalid Transaction Type{trxType}");
                        string msg = MessageViewContainerList.GetMessage(ExecutionContext, 5737);//Invalid Transaction Type
                        throw new InvalidTransactionTypeException(msg);
                    }
            }



            log.Info($"transaction ResponseDTO {JsonConvert.SerializeObject(paymentResponseDTO)}");
            log.LogMethodExit(paymentResponseDTO);
            return paymentResponseDTO;
        }

        /// <summary>
        /// This method will return the transaction type of the transactionPaymentDTO
        /// </summary>
        /// <param name="transactionPaymentDTO"></param>
        /// <returns></returns>
        private static PaymentGatewayTransactionType GetTransactionType(TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO);
            PaymentGatewayTransactionType trxType = PaymentGatewayTransactionType.SALE;
            if (!string.IsNullOrWhiteSpace(transactionPaymentDTO.PaymentStatus))
            {
                if (transactionPaymentDTO.PaymentStatus.Equals(PaymentStatuses.PRE_AUTHORIZATION_INITIATED.ToString()))
                {
                    trxType = PaymentGatewayTransactionType.TATokenRequest;
                }
                else if (transactionPaymentDTO.PaymentStatus.Equals(PaymentStatuses.AUTHORIZATION_INITIATED.ToString()))
                {
                    trxType = PaymentGatewayTransactionType.AUTHORIZATION;
                }
            }
            log.LogMethodExit(trxType);
            return trxType;
        }

        /// <summary>
        /// Converting listOfTransactionDTO to PaymentResponseDTO
        /// </summary>
        /// <param name="paymentTransactionDTOList"></param>
        /// <returns></returns>

        private static List<PaymentResponseDTO> ConvertToPaymentResponseDTO(List<PaymentTransactionDTO> paymentTransactionDTOList)
        {
            log.LogMethodEntry(paymentTransactionDTOList);
            List<PaymentResponseDTO> result = new List<PaymentResponseDTO>();
            if (paymentTransactionDTOList == null)
            {
                log.LogMethodExit(result, "paymentTransactionDTOList == null");
                return result;
            }
            foreach (PaymentTransactionDTO transaction in paymentTransactionDTOList)
            {
                PaymentResponseDTO paymentResponseDTO = ConvertToPaymentResponseDTO(transaction);
                result.Add(paymentResponseDTO);
            }
            log.LogMethodExit(result);
            return result;

        }
        /// <summary>
        /// Converting transaction to PaymentResponseDTO
        /// </summary>
        /// <param name="listOfTransactionDTO"></param>
        /// <returns></returns>
        private static PaymentResponseDTO ConvertToPaymentResponseDTO(PaymentTransactionDTO paymentTransactionDTO)
        {
            return new PaymentResponseDTO(paymentTransactionDTO.InvoiceNo, paymentTransactionDTO.TokenID,
                                          paymentTransactionDTO.RecordNo, paymentTransactionDTO.DSIXReturnCode, paymentTransactionDTO.TextResponse, paymentTransactionDTO.AcctNo,
                                          paymentTransactionDTO.CardType, paymentTransactionDTO.TranCode, paymentTransactionDTO.RefNo, paymentTransactionDTO.Purchase, paymentTransactionDTO.Authorize,
                                          paymentTransactionDTO.TransactionDatetime, paymentTransactionDTO.AuthCode, paymentTransactionDTO.ProcessData, paymentTransactionDTO.ResponseOrigin, paymentTransactionDTO.UserTraceData,
                                          paymentTransactionDTO.CaptureStatus, paymentTransactionDTO.AcqRefData, paymentTransactionDTO.TipAmount, paymentTransactionDTO.CustomerCopy, paymentTransactionDTO.MerchantCopy,
                                          paymentTransactionDTO.CustomerCardProfileId, paymentTransactionDTO.Status,
                                          paymentTransactionDTO.CreditCardName, paymentTransactionDTO.NameOnCreditCard, paymentTransactionDTO.CreditCardExpiry, paymentTransactionDTO.Amount);
        }


        private PaymentTransactionDTO ConvertToPaymentTransactionResponseDTO(TransactionPaymentDTO transactionPaymentDTO,
                                                                             int parentResponseId,
                                                                             PaymentResponseDTO paymentResponseDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO, parentResponseId, paymentResponseDTO);
            PaymentTransactionDTO paymentTransactionResponseDTO = null;
            //Response Conversion 
            try
            {
                log.Info("Converting paymentResponseDTO to PaymentTransactionResponseDTO");
                paymentTransactionResponseDTO = ConvertToPaymentTransactionResponseDTO(paymentResponseDTO, transactionPaymentDTO);
                log.Info($"paymentTransactionResponseDTO{JsonConvert.SerializeObject(paymentTransactionResponseDTO)}");
                if (paymentResponseDTO.Status == "SUCCESS")
                {
                    paymentTransactionResponseDTO.ParentResponseId = parentResponseId;
                }
            }
            catch (Exception)
            {
                log.Error("Conversion to paymentTransactionResponseDTO from paymentResponseDTO failed in the MakePayment method");
                log.Error($"paymentResponseDTO = {JsonConvert.SerializeObject(paymentResponseDTO)}");
                log.Error($"transactionPaymentDTO = {JsonConvert.SerializeObject(transactionPaymentDTO)}");
                string msg = MessageViewContainerList.GetMessage(ExecutionContext, 5735); //ResponseConversion Failed
                throw new ResponseConversionFailedException(msg);
            }
            log.LogMethodExit(paymentTransactionResponseDTO);
            return paymentTransactionResponseDTO;
        }

        /// <summary>
        /// Converting paymentResponseDTO to PaymentTransactionResponseDTO
        /// </summary>
        /// <param name="listOfTransactionDTO"></param>
        /// <returns></returns>

        private static PaymentTransactionDTO ConvertToPaymentTransactionResponseDTO(PaymentResponseDTO paymentResponseDTO, TransactionPaymentDTO transactionPaymentDTO)
        {
            return new PaymentTransactionDTO(-1, transactionPaymentDTO.TransactionId, transactionPaymentDTO.Guid.ToString(), paymentResponseDTO.TokenID,
                paymentResponseDTO.RecordNo, paymentResponseDTO.DSIXReturnCode, -1, paymentResponseDTO.TextResponse, paymentResponseDTO.AcctNo,
                paymentResponseDTO.CardType, paymentResponseDTO.TranCode, paymentResponseDTO.RefNo, paymentResponseDTO.Purchase,
                paymentResponseDTO.Authorize, paymentResponseDTO.TransactionDatetime, paymentResponseDTO.AuthCode,
                paymentResponseDTO.ProcessData, paymentResponseDTO.ResponseOrigin, paymentResponseDTO.UserTraceData, paymentResponseDTO.CaptureStatus, paymentResponseDTO.AcqRefData,
                paymentResponseDTO.TipAmount, paymentResponseDTO.CustomerCopy, paymentResponseDTO.MerchantCopy, paymentResponseDTO.CustomerCardProfileId,
                transactionPaymentDTO.Guid, true, paymentResponseDTO.Status, paymentResponseDTO.CreditCardName, paymentResponseDTO.NameOnCreditCard,
                paymentResponseDTO.CreditCardExpiry, paymentResponseDTO.Amount, null);
        }



        private PaymentResponseDTO BuildPaymentTransactionResponseDTO(string requestIdentifier, decimal amount, string errorMessage = null)
        {
            log.LogMethodEntry();
            PaymentResponseDTO paymentTransactionDTO = new PaymentResponseDTO(invoiceNo: requestIdentifier,
                                                                              tokenID: null,
                                                                              recordNo: null,
                                                                              dSIXReturnCode: null,
                                                                              textResponse: errorMessage,
                                                                              acctNo: null,
                                                                              cardType: string.Empty,
                                                                              tranCode: string.Empty,
                                                                              refNo: string.Empty,
                                                                              purchase: string.Empty,
                                                                              authorize: amount.ToString(),
                                                                              transactionDatetime: SiteDateTime.GetSiteDateTime(ExecutionContext),
                                                                              authCode: string.Empty,
                                                                              processData: string.Empty,
                                                                              responseOrigin: string.Empty,
                                                                              userTraceData: string.Empty,
                                                                              captureStatus: string.Empty,
                                                                              acqRefData: string.Empty,
                                                                              tipAmount: string.Empty,
                                                                              customerCopy: string.Empty,
                                                                              merchantCopy: string.Empty,
                                                                              customerCardProfileId: string.Empty,
                                                                              status: cancellationToken != null && cancellationToken.IsCancellationRequested ? PaymentTransactionStatuses.CANCELLED.ToString() : PaymentTransactionStatuses.ERROR.ToString(),
                                                                              creditCardName: string.Empty,
                                                                              nameOnCreditCard: string.Empty,
                                                                              creditCardExpiry: string.Empty,
                                                                              amount: amount);
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;

        }
        /// <summary>
        /// Refund Transaction 
        /// </summary>
        /// <param name="transactionPaymentDTO"></param>
        /// <param name="originalPaymentTransactionDTO"></param>
        /// <param name="paymentTransactionDTOList"></param>
        /// <param name="progress"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public override async Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO transactionPaymentDTO,
                                                            PaymentTransactionDTO originalPaymentTransactionDTO,
                                                            List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                            IProgress<PaymentProgressReport> progress,
                                                            CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentDTO, originalPaymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken);
            int parentResponseId = -1;
            PaymentResponseDTO paymentResponseDTO = null;
            PaymentTransactionDTO preAuthPaymentTransactionDTO = null;
            List<PaymentResponseDTO> paymentResponseList = null;

            //fetch preauth transaction from the list
            preAuthPaymentTransactionDTO = FindTATokenRequestTransaction(paymentTransactionDTOList);
            log.Info($"preAuthPaymentTransactionDTO{JsonConvert.SerializeObject(preAuthPaymentTransactionDTO)}");

            if (paymentTransactionDTOList != null)
            {
                paymentResponseList = ConvertToPaymentResponseDTO(paymentTransactionDTOList);
               // log.Info($"paymentResponseList{JsonConvert.SerializeObject(paymentResponseList)}");
            }
            parentResponseId = preAuthPaymentTransactionDTO != null ? preAuthPaymentTransactionDTO.ResponseID : -1;

            progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, 4551), false));//Refund in progress.....
            RefundRequestDTO refundRequestDTO = null;
            try
            {
                decimal tipAmount;
                decimal amount = Math.Abs(transactionPaymentDTO.Amount);
                DateTime bussStartTime;
                DateTime bussEndTime;

                BusinessDate businessDate = new BusinessDate(ExecutionContext);
                bussStartTime = businessDate;
                bussEndTime = bussStartTime.AddDays(1);

                tipAmount =!string.IsNullOrWhiteSpace(originalPaymentTransactionDTO.TipAmount) && originalPaymentTransactionDTO.TipAmount != "0"? Convert.ToDecimal(originalPaymentTransactionDTO.TipAmount): !string.IsNullOrWhiteSpace(transactionPaymentDTO.TipAmount.ToString())? Math.Abs(transactionPaymentDTO.TipAmount): 0;


                log.Info("Converting originalPaymentTransactionDTO to PaymentResponseDTO");
                PaymentResponseDTO paymentTransactionDTO = ConvertToPaymentResponseDTO(originalPaymentTransactionDTO);
                log.Info($"PaymentResponseDTO = {JsonConvert.SerializeObject(paymentTransactionDTO)}");

                refundRequestDTO = new RefundRequestDTO(amount, transactionPaymentDTO.Guid, transactionPaymentDTO.PaymentId, tipAmount, paymentTransactionDTO, paymentResponseList, SiteDateTime.GetSiteDateTime(ExecutionContext),bussStartTime,bussEndTime);
                log.Info($"refundRequestDTO{JsonConvert.SerializeObject(refundRequestDTO)}");
                log.Info("Calling Refund method");
                paymentResponseDTO = await paymentGateway.Refund(refundRequestDTO, progress, cancellationToken);


            }
            catch (Exception ex)
            {
                progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, "Error occured while Refunding the Amount"), false));
                log.Error("Error occured while Refunding the Amount", ex);
                log.Error($"refundRequestDTO = {JsonConvert.SerializeObject(refundRequestDTO)}");
                log.Error($"origPaymentTransactionDTO = {JsonConvert.SerializeObject(originalPaymentTransactionDTO)}");
                log.Error($"paymentTransactionDTOList = {paymentTransactionDTOList}");
                log.LogMethodExit(null, "throwing Exception");

                paymentResponseDTO = BuildPaymentTransactionResponseDTO(refundRequestDTO.RequestIdentifier, refundRequestDTO.Amount, ex.Message);

            }
            //Response Conversion 
            PaymentTransactionDTO paymentTransactionResponseDTO = ConvertToPaymentTransactionResponseDTO(transactionPaymentDTO, parentResponseId, paymentResponseDTO);

            log.LogMethodExit(paymentTransactionResponseDTO);
            return paymentTransactionResponseDTO;

        }

        /// <summary>
        /// Settle the AUTH transaction.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="origPaymentTransactionDTO"></param>
        /// <param name="paymentTransactionDTOList"></param>
        /// <param name="progress"></param>
        /// <param name="cancellationToken"></param>
        /// <param name="IsForcedSettlement"></param>
        /// <returns></returns>
        public override async Task<PaymentTransactionDTO> PerformSettlement(TransactionPaymentDTO transactionPaymentsDTO,
                                                             PaymentTransactionDTO origPaymentTransactionDTO,
                                                             List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                             IProgress<PaymentProgressReport> progress,
                                                             CancellationToken cancellationToken,
                                                             bool IsForcedSettlement = false)
        {
            log.LogMethodEntry(transactionPaymentsDTO, IsForcedSettlement);
            PaymentTransactionDTO preAuthPaymentTransactionDTO = null;

            PaymentResponseDTO paymentResponseDTO = null;
            preAuthPaymentTransactionDTO = FindTATokenRequestTransaction(paymentTransactionDTOList);
            log.Info($"preAuthPaymentTransactionDTO{JsonConvert.SerializeObject(preAuthPaymentTransactionDTO)}");
            int parentResponseId = preAuthPaymentTransactionDTO != null ? preAuthPaymentTransactionDTO.ResponseID : -1;
            List<PaymentResponseDTO> paymentResponseList = ConvertToPaymentResponseDTO(paymentTransactionDTOList);
            log.Info($"paymentResponseList{JsonConvert.SerializeObject(paymentResponseList)}");
            SettlementRequestDTO settlementRequestDTO = null;
            progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, 4712, MessageViewContainerList.GetMessage(ExecutionContext, "Settlement")), false));//Settlement in progress.....

            try
            {
                PaymentResponseDTO originalPaymentResponseDTO = ConvertToPaymentResponseDTO(origPaymentTransactionDTO);
                log.Info($"originalPaymentResponseDTO = {JsonConvert.SerializeObject(originalPaymentResponseDTO)}");
                settlementRequestDTO = new SettlementRequestDTO(transactionPaymentsDTO.Amount, transactionPaymentsDTO.Guid, transactionPaymentsDTO.PaymentId, transactionPaymentsDTO.TipAmount, originalPaymentResponseDTO, paymentResponseList, SiteDateTime.GetSiteDateTime(ExecutionContext));
                log.Info($"settlementRequestDTO= {JsonConvert.SerializeObject(settlementRequestDTO)}");
                log.Info("Calling PerformSettlement method");
                paymentResponseDTO = await paymentGateway.PerformSettlement(settlementRequestDTO, progress, cancellationToken);
            }
            catch (Exception ex)
            {
                progress.Report(new PaymentProgressReport("Error occured while performing settlement", false));
                log.Error(ex);
                log.Error($"settlementRequestDTO = {JsonConvert.SerializeObject(settlementRequestDTO)}");
                log.Error($"transactionPaymentDTO = {JsonConvert.SerializeObject(transactionPaymentsDTO)}");
                log.Error($"origPaymentTransactionDTO = {JsonConvert.SerializeObject(origPaymentTransactionDTO)}");
                log.Error($"paymentTransactionDTOList = {JsonConvert.SerializeObject(paymentTransactionDTOList)}");
                paymentResponseDTO = BuildPaymentTransactionResponseDTO(transactionPaymentsDTO.Guid, transactionPaymentsDTO.Amount, ex.Message);
            }

            PaymentTransactionDTO paymentTransactionResponseDTO = ConvertToPaymentTransactionResponseDTO(transactionPaymentsDTO, parentResponseId, paymentResponseDTO);

            log.LogMethodExit(paymentTransactionResponseDTO);
            return paymentTransactionResponseDTO;

        }

        /// <summary>
        /// Status Check of the transaction
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="paymentTransactionDTOList"></param>
        /// <param name="progress"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async override Task<PaymentTransactionDTO> SendLastTransactionStatusCheckRequestAsync(TransactionPaymentDTO transactionPaymentsDTO,
                                                                                                     List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                                                                    IProgress<PaymentProgressReport> progress,
                                                                                                    System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentTransactionDTOList);
            PaymentTransactionDTO paymentTransactionResponseDTO = null;
            PaymentGatewayTransactionType trxtype = GetTransactionType(transactionPaymentsDTO);
            List<PaymentResponseDTO> paymentResponseList = ConvertToPaymentResponseDTO(paymentTransactionDTOList);
            log.Info($"paymentResponseList{JsonConvert.SerializeObject(paymentResponseList)}");

            PaymentResponseDTO paymentResponseDTO = null;
            StatusCheckRequestDTO statusCheckRequestDTO = null;

            progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, 4986, transactionPaymentsDTO.TransactionId, transactionPaymentsDTO.Amount), false));//Checking the transaction status of TrxId: &1 Amount: &2
            try
            {
                progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, 1008), false));


                PaymentStatuses paymentStatuses;
                Enum.TryParse(transactionPaymentsDTO.PaymentStatus, out paymentStatuses);
                statusCheckRequestDTO = new StatusCheckRequestDTO(transactionPaymentsDTO.Amount, transactionPaymentsDTO.Guid, transactionPaymentsDTO.PaymentId, paymentResponseList, transactionPaymentsDTO.PaymentStatus, SiteDateTime.GetSiteDateTime(ExecutionContext));
                log.Info($"statusCheckRequestDTO = {JsonConvert.SerializeObject(statusCheckRequestDTO)}");
                switch (paymentStatuses)
                {
                    case PaymentStatuses.AUTHORIZATION_INITIATED:
                    case PaymentStatuses.SALE_INITIATED:
                    case PaymentStatuses.PRE_AUTHORIZATION_INITIATED:
                    case PaymentStatuses.SETTLEMENT_INITIATED:
                    case PaymentStatuses.REFUND_INITIATED:
                    case PaymentStatuses.VOID_INITIATED:

                        {
                            log.Info("StatusCheck method initiated.");
                            if (paymentGateway.IsStatusCheckSupported)
                            {
                                paymentResponseDTO = await paymentGateway.StatusCheck(statusCheckRequestDTO, progress, cancellationToken,string.Empty);
                            }

                            log.Info($"paymentResponseDTO = {JsonConvert.SerializeObject(paymentResponseDTO)}");
                            break;
                        }
                }
            }
            catch (Exception ex)
            {
                log.Error("Last transaction check failed");
                log.Error(ex);
                if (!isUnattended)
                {
                    progress.Report(new PaymentProgressReport(MessageViewContainerList.GetMessage(ExecutionContext, "Last transaction status check  failed. :" + " TransactionID:" + transactionPaymentsDTO.TransactionId + " Amount:" + transactionPaymentsDTO.Amount), false));
                }
                throw;
            }

            //Response Conversion 
            try
            {
                if (paymentResponseDTO != null)
                {
                    log.Info("Converting paymentResponseDTO to PaymentTransactionResponseDTO");
                    paymentTransactionResponseDTO = ConvertToPaymentTransactionResponseDTO(paymentResponseDTO, transactionPaymentsDTO);
                    log.Info($"paymentTransactionResponseDTO{JsonConvert.SerializeObject(paymentTransactionResponseDTO)}");
                }
            }
            catch (Exception ex)
            {
                log.Error("Conversion to paymentTransactionResponseDTO from paymentResponseDTO failed.");
                log.Error(ex);
                log.Error($"paymentResponseDTO = {JsonConvert.SerializeObject(paymentResponseDTO)}");
                log.Error($"transactionPaymentDTO = {JsonConvert.SerializeObject(transactionPaymentsDTO)}");
                log.Error($"statusCheckRequestDTO = {JsonConvert.SerializeObject(statusCheckRequestDTO)}");
                log.Error($"paymentTransactionDTOList = {paymentTransactionDTOList}");
                string msg = MessageViewContainerList.GetMessage(ExecutionContext, 5735); //ResponseConversion Failed
                throw new ResponseConversionFailedException(msg);

            }
            finally
            {
                log.Debug("Reached finally.");
            }
            log.LogMethodExit();
            return paymentTransactionResponseDTO;
        }

        public override async Task<PaymentTransactionDTO> PayTip(TransactionPaymentDTO transactionPaymentsDTO,
                                                                         PaymentTransactionDTO paymentTransactionDTO,
                                                                         List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                                         IProgress<PaymentProgressReport> progress,
                                                                         CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentTransactionDTO, progress, cancellationToken);
            paymentTransactionDTO = await PerformSettlement(transactionPaymentsDTO, paymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken, true);
            log.LogMethodExit(transactionPaymentsDTO);
            return paymentTransactionDTO;

        }
    }
}

