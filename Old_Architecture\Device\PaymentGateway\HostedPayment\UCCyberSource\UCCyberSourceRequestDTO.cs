﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.UCCyberSource
{
    public class CaptureContextRequestDTO
    {
        public List<string> targetOrigins { get; set; }
        public string clientVersion { get; set; }
        public List<string> allowedCardNetworks { get; set; }
        public List<string> allowedPaymentTypes { get; set; }
        public string country { get; set; }
        public string locale { get; set; }
        public Capturemandate captureMandate { get; set; }
        public Orderinformation orderInformation { get; set; }
        public Checkoutapiinitialization checkoutApiInitialization { get; set; }
    }

    public class UCCyberSourceRefundRequestDTO
    {
        public string paymentId { get; set; }
    }

    public class Capturemandate
    {
        public string billingType { get; set; }
        public bool requestEmail { get; set; }
        public bool requestPhone { get; set; }
        public bool requestShipping { get; set; }
        public List<string> shipToCountries { get; set; }
        public bool showAcceptedNetworkIcons { get; set; }
    }

    public class Orderinformation
    {
        public billTo billTo { get; set; }
        public Shipto shipTo { get; set; }
        public Amountdetails amountDetails { get; set; }
        public Shippingdetails shippingDetails { get; set; }
        public Invoicedetails invoiceDetails { get; set; }
        public Lineitem[] lineItems { get; set; }
    }

    public class Shippingdetails
    {
    }

    public class Invoicedetails
    {
    }

    public class Lineitem
    {
        public string productCode { get; set; }
        public int taxAmount { get; set; }
        public int quantity { get; set; }
        public float unitPrice { get; set; }
    }

    public class Amountdetails
    {
        public string totalAmount { get; set; }
        public string currency { get; set; }
        public string taxAmount { get; set; }
        public string authorizedAmount { get; set; }
    }

    public class Checkoutapiinitialization
    {
        public string profile_id { get; set; }
        public string access_key { get; set; }
        public string reference_number { get; set; }
        public string transaction_uuid { get; set; }
        public string transaction_type { get; set; }
        public string currency { get; set; }
        public string amount { get; set; }
        public string override_custom_receipt_page { get; set; }
        public string locale { get; set; }
        public string unsigned_field_names { get; set; }
    }

    public class UCCyberSourceRequestDTO
    {
        public string captureContext { get; set; }
        public string clientLibrary { get; set; }
        public string postURL { get; set; }
    }
    public class TxSearchRequestDTO
    {
        public string query { get; set; }
        public string sort { get; set; }
    }

    public class VoidRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
    }

    public class RefundRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public Orderinformation orderInformation { get; set; }
    }
}
