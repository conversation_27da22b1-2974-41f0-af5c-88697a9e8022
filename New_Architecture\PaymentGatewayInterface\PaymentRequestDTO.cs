﻿
/******************************************************************************************************************************
 * Project Name - PaymentGatewayInterface
 * Description  - PaymentRequestDTO
 * 
 **************
 **Version Log
 **************
 *Version     Date          Modified By             Remarks          
 *******************************************************************************************************************************
 *2.190.0     24-Sep-2024   Amrutha                   Created
 ******************************************************************************************************************************/



using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class PaymentRequestDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        
        private decimal amount;       
        private decimal tipAmount;
        private string requestIdentifier;
        private int intRequestIdentifier;
        private string ipAddress;
        private List<PaymentResponseDTO> paymentResponseHistory;
        private PaymentGatewayCustomerDTO paymentGatewayCustomerDTO;
        private List<PaymentGatewayProductDTO> paymentGatewayProductDTOList;
        private string subscriptionAuthorizationMode;
        private bool isManual;
        private DateTime requestDate;
        private DateTime requestUTCDate;
        private string customerCardProfileId;
        private decimal transactionNetAmount;



        //public PaymentRequestDTO()
        //{
        //    subscriptionAuthorizationMode = SubscriptionAuthorizationMode.N;
        //}
        /// <summary>
        /// Constructor with all the data fields
        /// </summary>
        public PaymentRequestDTO(decimal amount, string requestIdentifier , int intRequestIdentifier, List<PaymentResponseDTO> paymentResponseHistory, bool isManual, DateTime requestDate, DateTime requestUTCDate, string subscriptionAuthorizationMode,decimal transactionNetAmount)
        {
            log.LogMethodEntry(amount, requestIdentifier, isManual, intRequestIdentifier, requestDate, requestUTCDate);
            this.amount = amount;
            this.requestIdentifier = requestIdentifier;
            this.intRequestIdentifier = intRequestIdentifier;
            this.paymentResponseHistory = paymentResponseHistory;
            this.isManual = isManual;
            this.requestDate = requestDate;
            this.requestUTCDate = requestUTCDate;
            this.subscriptionAuthorizationMode = subscriptionAuthorizationMode;
            this.transactionNetAmount = transactionNetAmount;
            log.LogMethodExit();
        }

        ///// <summary>
        ///// Get/Set method of the subscriptionAuthorizationMode field
        ///// </summary> 
        //public SubscriptionAuthorizationMode SubscriptionauthorizationMode
        //{
        //    get
        //    {
        //        return subscriptionAuthorizationMode;
        //    }

        //    set
        //    {
        //        // IsChanged = true;
        //        subscriptionAuthorizationMode = value;
        //    }
        //}
        /// <summary>
        /// Get/Set method of the customerCardProfileId field
        /// </summary> 
        public string CustomerCardProfileId
        {
            get
            {
                return customerCardProfileId;
            }

            set
            {
               // IsChanged = true;
                customerCardProfileId = value;
            }
        }

        /// <summary>
        /// Get method of the requestDate field
        /// </summary>
        public DateTime RequestDate
        {
            get
            {
                return requestDate;
            }
            set
            {
               // this.IsChanged = true;
                requestDate = value;
            }
        }

        ///// <summary>
        ///// Get method of the requestUTCDate field
        ///// </summary>
        public DateTime RequestUTCDate
        {
            get
            {
                return requestUTCDate;
            }
            set
            {
                // this.IsChanged = true;
                requestUTCDate = value;
            }
        }

        /// <summary>
        /// Get/Set method of the Amount field
        /// </summary>
        public decimal Amount
        {
            get
            {
                return amount;
            }

            set
            {
                //IsChanged = true;
                amount = value;
            }
        }

        /// <summary>
        /// Get/Set method of the transactionNetAmount field
        /// </summary>
        public decimal TransactionNetAmount
        {
            get
            {
                return transactionNetAmount;
            }

            set
            {
                //IsChanged = true;
                transactionNetAmount = value;
            }
        }
        public bool IsManual
        {
            get
            {
                return isManual;
            }

            set
            {
                //IsChanged = true;
                isManual = value;
            }
        }
        public decimal TipAmount
        {
            get
            {
                return tipAmount;
            }

            set
            {
                //IsChanged = true;
                tipAmount = value;
            }
        }
        public int IntRequestIdentifier
        {
            get
            {
                return intRequestIdentifier;
            }

            set
            {
                //IsChanged = true;
                intRequestIdentifier = value;
            }
        }
        public string RequestIdentifier
        {
            get
            {
                return requestIdentifier;
            }

            set
            {
                //IsChanged = true;
                requestIdentifier = value;
            }
        }
        public string SubscriptionAuthorizationMode
        {
            get
            {
                return subscriptionAuthorizationMode;
            }

            set
            {
                //IsChanged = true;
                subscriptionAuthorizationMode = value;
            }
        }

        public List<PaymentResponseDTO> PaymentResponseHistory
        {
            get { return paymentResponseHistory; }
            set { paymentResponseHistory = value; }
        }


        public PaymentGatewayCustomerDTO PaymentGatewayCustomerDTO
        {
            get { return paymentGatewayCustomerDTO; }
            set { paymentGatewayCustomerDTO = value; }
        }

        public List<PaymentGatewayProductDTO> PaymentGatewayProductDTOList
        {
            get { return paymentGatewayProductDTOList; }
            set { paymentGatewayProductDTOList = value; }
        }

        public string IpAddress
        {
            get { return ipAddress; }
            set { ipAddress = value; }
        }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}

