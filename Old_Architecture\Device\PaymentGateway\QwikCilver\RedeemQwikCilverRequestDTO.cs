﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
    class RedeemQwikCilverRequestDTO
    {
        public int TransactionModeID { get; set; }
        public int TransactionTypeId { get; set; }
        public string IdempotencyKey { get; set; }
        public string InvoiceNumber { get; set; }
        public double InvoiceAmount { get; set; }
        public char InputType { get; set; }
        public int numberOfCards { get; set; }
        public List<CardsRequestDTO> Cards { get; set; }
        public string notes { get; set; }
    }
}
