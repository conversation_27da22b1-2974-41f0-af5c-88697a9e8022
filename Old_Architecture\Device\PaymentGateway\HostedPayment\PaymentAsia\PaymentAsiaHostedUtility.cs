﻿using Newtonsoft.Json;
using PaymentAsiaPOC.PaymentAsia;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PaymentAsia
{
    class PaymentAsiaHostedUtility : HostedPaymentGatewayUtility
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public PaymentAsiaHostedUtility() : base()
        {
            log.LogMethodEntry();
            Initialize();
            log.LogMethodExit();
        }

        private void Initialize()
        {
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_BASE_URL", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_API_URL", "");

        }

        /// <summary>
        /// Initializes and returns an PaymentAsiaHostedCommandHandler instance.
        /// </summary>
        /// <param name="paymentCredentialsList">The payment credentials list.</param>
        /// <returns>An instance of PaymentAsiaHostedCommandHandler.</returns>
        public PaymentAsiaHostedCommandHandler InitializeCommandHandler(Dictionary<string, string> paymentCredentialsList)
        {
            if (paymentCredentialsList == null)
            {
                throw new ArgumentNullException("paymentCredentialsList", "The payment credentials list cannot be null.");
            }
            string baseUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_BASE_URL", out baseUrl) || string.IsNullOrEmpty(baseUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_BASE_URL'.");
            }
            string postUrl;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_API_URL", out postUrl) || string.IsNullOrEmpty(postUrl))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_API_URL'.");
            }
            if (baseUrl.EndsWith("/"))
            {
                baseUrl = baseUrl.Remove(baseUrl.Length - 1);
            }

            string merchantId;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", out merchantId) || string.IsNullOrEmpty(merchantId))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_MERCHANT_ID'.");
            }

            string merchantKey;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", out merchantKey) || string.IsNullOrEmpty(merchantKey))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY'.");
            }

            string secretKey;
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", out secretKey) || string.IsNullOrEmpty(secretKey))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_SECRET_KEY'.");
            }

            string searchTransactionUrl = "/v1.1/online/" + merchantKey + "/transactions/query";
            string voidApiUrl = "/v1.1/online/" + merchantKey + "/transactions/void";
            string refundApiUrl = "/v1.1/online/" + merchantKey + "/transactions/refund";
            string refundDetailsUrl = "/v1.1/online/" + merchantKey + "/transactions/refund-query";
            string searchTrxUrl = baseUrl + searchTransactionUrl;

            return new PaymentAsiaHostedCommandHandler(merchantId, merchantKey, secretKey, searchTrxUrl);
        }
        /// <summary>
        /// Retrieves the payment status search result for a transaction ID.
        /// </summary>
        /// <param name="trxId">The transaction ID to search for.</param>
        /// <returns>The payment status search result.</returns>
        public override TrxSearchUtilityDTO GetPaymentStatusSearch(string trxId)
        {
            log.LogMethodEntry(trxId);
            TrxSearchUtilityDTO trxSearchResult = new TrxSearchUtilityDTO
            {
                TransactionId = trxId
            };

            if (string.IsNullOrWhiteSpace(trxId)) //todo: any case where parafait trxId is an alphanumeric
            {
                log.Error("Invalid transaction ID.");
                trxSearchResult.ErrorMessage = "Invalid transaction ID.";
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.PaymentStatus = "Failed";
                trxSearchResult.TransactionId = "null";
                return trxSearchResult;
            }

            try
            {
                PaymentAsiaHostedCommandHandler paymentAsiaCommandHandler = InitializeCommandHandler(PaymentCredentailsList);
                if (paymentAsiaCommandHandler == null)
                {
                    log.Error("CommandHandler instance is null");
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    return trxSearchResult;
                }

                List<PaymentAsiaQueryTransactionResponseDTO> trxSearchResponseList = paymentAsiaCommandHandler.CreateTxSearchWithType(trxId, "Sale");
                PaymentAsiaQueryTransactionResponseDTO response = trxSearchResponseList.FirstOrDefault(t => t.status == "1") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "0") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "2") ?? trxSearchResponseList.FirstOrDefault(t => t.status == "4");
                log.Debug($"TxSearch Response for TrxId: {trxId}: " + response.ToString());

                if (response == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    return trxSearchResult;
                }

                log.Debug("response status: " + response.status);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(response.status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                string formattedJson = JsonConvert.SerializeObject(response, Formatting.Indented);
                trxSearchResult.FormattedResponse = formattedJson;
                trxSearchResult.PaymentStatus = salePaymentStatus.ToString();
            }
            catch (Exception ex)
            {
                log.Error("Error searching transaction details for trxId: " + trxId);
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.ErrorMessage = ex.Message; // todo: some cases msg is: String reference not set to an instance of a String.
                trxSearchResult.PaymentStatus = "Failed"; // Set payment status to "Failed"
            }
            log.LogMethodExit(trxSearchResult.ToString());
            return trxSearchResult;
        }
        /// <summary>
        /// Maps the payment status from raw response to Semnox payment status.
        /// </summary>
        /// <param name="rawPaymentGatewayStatus">The raw payment gateway status.</param>
        /// <param name="pgwTrxType">The type of payment gateway transaction.</param>
        /// <returns>The mapped payment status type.</returns>
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "1", PaymentStatusType.SUCCESS },
                            { "2", PaymentStatusType.FAILED },
                            { "0", PaymentStatusType.PENDING },
                            { "4", PaymentStatusType.PENDING },
                        };
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "Success", PaymentStatusType.SUCCESS },
                            { "Failed", PaymentStatusType.FAILED },
                            { "Pending", PaymentStatusType.SUCCESS }

                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.FAILED;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.FAILED;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

    }
}
