﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.Transaction.V2;

namespace Semnox.Parafait.PaymentGateway
{
    public class DebitCardPaymentGateway : PaymentGateway
    {
        public DebitCardPaymentGateway(Core.Utilities.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken) : base(executionContext, isUnattended, cancellationToken)
        {
        }

        public override bool IsPrintLastTransactionSupported => base.IsPrintLastTransactionSupported;

        public override bool IsPrinterRequired => base.IsPrinterRequired;

        public override bool IsPartiallyApproved => base.IsPartiallyApproved;

        public override bool IsTipAdjustmentAllowed => base.IsTipAdjustmentAllowed;

        public override void BeginOrder()
        {
            base.BeginOrder();
        }

        public override Task<PaymentTransactionDTO> CheckLastTransactionStatus(TransactionPaymentDTO transactionPaymentsDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            return base.CheckLastTransactionStatus(transactionPaymentsDTO, progress, cancellationToken);
        }

        public override void CleanUp()
        {
            base.CleanUp();
        }

        public override void EndOrder()
        {
            base.EndOrder();
        }

        public override bool Equals(object obj)
        {
            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public override void Initialize(int paymentModeId)
        {
            base.Initialize(paymentModeId);
        }

        public override Task<PaymentTransactionDTO> MakePayment(TransactionPaymentDTO transactionPaymentDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken, TransactionDTO transactionDTO)
        {
            return base.MakePayment(transactionPaymentDTO, paymentTransactionDTOList, progress, cancellationToken, transactionDTO);
        }

        public override Task<PaymentTransactionDTO> MakePaymentForRecurringBilling(TransactionPaymentDTO transactionPaymentsDTO)
        {
            return base.MakePaymentForRecurringBilling(transactionPaymentsDTO);
        }

        public override Task<PaymentTransactionDTO> PayTip(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO paymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            return base.PayTip(transactionPaymentsDTO, paymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken);
        }

        public override Task<PaymentTransactionDTO> PerformSettlement(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO paymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken, bool IsForcedSettlement = false)
        {
            return base.PerformSettlement(transactionPaymentsDTO, paymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken, IsForcedSettlement);
        }

        public override void PrintCCReceipt(List<TransactionPaymentDTO> transactionPaymentDTOList)
        {
            base.PrintCCReceipt(transactionPaymentDTOList);
        }

        public override void PrintLastTransaction()
        {
            base.PrintLastTransaction();
        }

        public override Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO refundTransactionPaymentsDTO, PaymentTransactionDTO originalPaymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            return base.RefundAmount(refundTransactionPaymentsDTO, originalPaymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken);
        }

        public override Task<PaymentTransactionDTO> SendLastTransactionStatusCheckRequestAsync(TransactionPaymentDTO transactionPaymentsDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            return base.SendLastTransactionStatusCheckRequestAsync(transactionPaymentsDTO, paymentTransactionDTOList, progress, cancellationToken);
        }

        public override string ToString()
        {
            return base.ToString();
        }

        public override KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<ValidationError>> ValidateLastTransactionStatus(TransactionPaymentDTO transactionPaymentsDTO)
        {
            return base.ValidateLastTransactionStatus(transactionPaymentsDTO);
        }

        public override Task<List<TransactionPaymentDTO>> ValidatePayments(TransactionPaymentDTO transactionPaymentDTO)
        {
            return base.ValidatePayments(transactionPaymentDTO);
        }

        protected override string GetReceiptText(TransactionPaymentDTO trxPaymentsDTO, PaymentTransactionDTO ccTransactionsPGWDTO, bool IsMerchantCopy)
        {
            return base.GetReceiptText(trxPaymentsDTO, ccTransactionsPGWDTO, IsMerchantCopy);
        }

        protected override void PrintCreditCardReceipt(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO ccTransactionsPGWDTO)
        {
            base.PrintCreditCardReceipt(transactionPaymentsDTO, ccTransactionsPGWDTO);
        }
    }
}
