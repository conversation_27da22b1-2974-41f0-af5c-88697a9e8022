﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class PaymentGatewayResponseDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private string gatewayResponse;
        private DateTime siteDateTime;

        public PaymentGatewayResponseDTO(string gatewayResponse, DateTime siteDateTime)
        {
            log.LogMethodEntry(gatewayResponse, siteDateTime);
            this.gatewayResponse = gatewayResponse;
            this.siteDateTime = siteDateTime;
            log.LogMethodExit();
        }

        public string GatewayResponse
        {
            get
            {
                return gatewayResponse;
            }

            set
            {
                gatewayResponse = value;
            }
        }

        public DateTime SiteDateTime
        {
            get { return siteDateTime; }
            set { siteDateTime = value; }
        }
    }
}
