﻿/********************************************************************************************
 * Project Name - Hosted Payment
 * Description  -Exception classes for web payments
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/
using System;
using System.Runtime.Serialization;
using System.Security.Permissions;
using Semnox.Core.Utilities;

namespace Semnox.Parafait.PaymentGateway
{
    public enum PaymentGatewayErrorCodes
    {
        /// <summary>
        /// PaymentGatewayConfigurationException
        /// </summary>
        PG_00001,
        /// <summary>
        /// PaymentGatewayProcessingException
        /// </summary>
        PG_00002,
        /// <summary>
        /// PaymentGatewayTransactionTimeoutException
        /// </summary>
        PG_00003,
        /// <summary>
        /// PaymentGatewayCaptchaValidationException
        /// </summary>
        PG_00004,
        /// <summary>
        /// PaymentGatewayInvalidCardException
        /// </summary>
        PG_00005,
        PG_00006,
        PG_00007,
        PG_00008,
        PG_00009,
        PG_000010,
    }

    [Serializable]

    /// <summary>
    /// Represents ParafaitApplicationException error that occur during parafait application execution. 
    /// </summary>
    public class PaymentGatewayConfigurationException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayConfigurationException(string message) : base(PaymentGatewayErrorCodes.PG_00001, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayConfigurationException(string message, Exception innerException) : base(PaymentGatewayErrorCodes.PG_00001, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentGatewayConfigurationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    public class PaymentGatewayProcessingException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayProcessingException(string message) : base(PaymentGatewayErrorCodes.PG_00002, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayProcessingException(string message, Exception innerException) : base(PaymentGatewayErrorCodes.PG_00002, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentGatewayProcessingException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    public class PaymentGatewayTransactionTimeoutException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayTransactionTimeoutException(string message) : base(PaymentGatewayErrorCodes.PG_00003, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayTransactionTimeoutException(string message, Exception innerException) : base(PaymentGatewayErrorCodes.PG_00003, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentGatewayTransactionTimeoutException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    public class PaymentGatewayCaptchaValidationException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayCaptchaValidationException(string message) : base(PaymentGatewayErrorCodes.PG_00004, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayCaptchaValidationException(string message, Exception innerException) : base(PaymentGatewayErrorCodes.PG_00004, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentGatewayCaptchaValidationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    public class PaymentGatewayInvalidCardException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayInvalidCardException(string message) : base(PaymentGatewayErrorCodes.PG_00005, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentGatewayInvalidCardException(string message, Exception innerException) : base(PaymentGatewayErrorCodes.PG_00005, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentGatewayInvalidCardException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

}
