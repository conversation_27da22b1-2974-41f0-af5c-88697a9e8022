﻿/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Payment Gateway Messages
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.190.0     24-Sep-2024         Amrutha      Created
 ********************************************************************************************************/
using Semnox.Parafait.Languages;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class ParafaitPaymentMessages : PaymentMessages
    {
        private readonly int siteId;
        private readonly int languageId;
        public ParafaitPaymentMessages(int siteId, int languageId)
        {
            this.siteId = siteId;
            this.languageId = languageId;
        }
        public override string GetMessage(int messageNumber)
        {
            return MessageViewContainerList.GetMessage(siteId, languageId, messageNumber);
        }

        public override string GetTranslatedLabel(string label)
        {
            return MessageViewContainerList.GetMessage(siteId, languageId, label);
        }
        public override string GetMessages(int messageNumber, params object[] parameters)
        {
            return MessageViewContainerList.GetMessage(siteId, languageId, messageNumber, parameters);
        }
    }
}
