﻿/********************************************************************************************
 * Project Name - Payment Gateway
 * Description  - QuikCilverCommandHandler class
 * 
 **************
 **Version Log
 **************
 *Version     Date              Modified By                    Remarks          
 *********************************************************************************************
 *2.152.0     11-March-2024       Amrutha                         Created 
 *******************************************************************************************/

using Newtonsoft.Json;
using Semnox.Core.GenericUtilities;
using Semnox.Core.Utilities;
using Semnox.Parafait.Languages;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Security;
using System.Text;
using System.Text.RegularExpressions;

using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
    class QwikCilverCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        Semnox.Core.Utilities.ExecutionContext ExecutionContext;
        string json = string.Empty;
        string redeem_Host_url;
        string reverse_Hostl_url;
        string cancel_Host_url;
        string formatted_datetime;
        private string balance_inquiry_url;
        ExecutionContext executionContext;
        int timeOutPeriod;

        private static readonly HttpClient httpClient = new HttpClient();

        public QwikCilverCommandHandler(ExecutionContext executionContext)
        {
        }

        public QwikCilverCommandHandler(Semnox.Core.Utilities.ExecutionContext executionContext, string redeem_Host_url, string reverse_Hostl_url, string cancel_Host_url,string balance_inquiry_url)
        {

            if (string.IsNullOrWhiteSpace(redeem_Host_url) || string.IsNullOrWhiteSpace(reverse_Hostl_url)
                || string.IsNullOrWhiteSpace(cancel_Host_url))
            {
                log.Error("Missing authorize_host_url,redeem_Host_url,reverse_Hostl_url,cancel_Host_url");
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5497));//QuickCilver Payment gateway setup is incomplete
            }
            //TrimEndpoints();
            this.redeem_Host_url = redeem_Host_url;
            this.reverse_Hostl_url = reverse_Hostl_url;
            this.cancel_Host_url = cancel_Host_url;
            this.balance_inquiry_url = balance_inquiry_url;
            this.executionContext = executionContext;

            if (ConfigurationManager.AppSettings["QWIKCILVER_TIMEOUT_WAIT_PERIOD"] != null)
            {
                 timeOutPeriod = Convert.ToInt32(ConfigurationManager.AppSettings["QWIKCILVER_TIMEOUT_WAIT_PERIOD"].ToString());
                

                if(timeOutPeriod <= 0 || timeOutPeriod <=3000)
                {
                    timeOutPeriod = 7000;
                }
                log.Info("timeOutPeriod= " + timeOutPeriod);
            }
        }
        public AuthorizeQwikCilverResponseDTO GetAuthorizationCode(AuthorizeQwikCilverRequestDTO requestDTO, string url, DateTime dateTime, ExecutionContext executionContext)
        {
            formatted_datetime = dateTime.ToString("yyyy-MM-ddTHH:mm:ss");

            AuthorizeQwikCilverResponseDTO response = null;
            string responseString = string.Empty;
            log.Info("Entering GetAuthorizationCode mothod");
         
            if (requestDTO == null)
            {
                log.Error("GetAuthorizationCode requestDTO is null");
            }
            try
            {
                json = JsonConvert.SerializeObject(requestDTO);
                log.Info("json = " + json);
            }
            catch (JsonSerializationException ex)
            {
                log.Error("GetAuthorizationCode-Failed to serialize the request DTO", ex);
                throw;
            }
            try
            {

                List<KeyValuePair<string, string>> headers = new List<KeyValuePair<string, string>>();
                headers.Add(new KeyValuePair<string, string>("DateAtClient", formatted_datetime));
                Core.GenericUtilities.WebApiResponse webApiResponse = Post(url, headers, json, "application/json");
                responseString = webApiResponse.Response;
            }
            catch (WebException ex)
            {
                log.Error($"GetAuthorizationCode-WebException occurred: {ex.Message}", ex);
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5503)); // Error communicating with the server
            }
            catch (Exception ex)
            {
                log.Error($"GetAuthorizationCode-General exception occurred: {ex.Message}", ex);
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5503)); // Error communicating with the server
            }

            try
            {
                if (!string.IsNullOrWhiteSpace(responseString))
                {
                    var jsonSettings = new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    };
                    AuthorizeQwikCilverResponseDTO deserializedObject = JsonConvert.DeserializeObject<AuthorizeQwikCilverResponseDTO>(responseString, jsonSettings);
                    if (deserializedObject!=null)
                    {
                        response = deserializedObject;
                        log.Info("response= " + JsonConvert.SerializeObject(response));

                    }
                    else
                    {
                        log.Error("GetAuthorizationCode-Response string is empty");
                    }
                }
                else
                {
                    log.Error("GetAuthorizationCode-Response string is empty");
                }
            }
            catch (JsonSerializationException ex)
            {
                log.Error("RedeemBalance-Failed to deserialize the response", ex);
                //throw new Exception("RedeemBalance-Failed to deserialize the response");
            }
            log.Info("Exit from  GetAuthorizationCode mothod");
            return response;
        }
        public RedeemQwikCilverResponseDTO Makepayment(string cardnumber, string cardpin, double amount, string token, string ccRequestId, DateTime dateTime,string id, ExecutionContext executionContext, decimal overallTransactionAmount)
        {
            log.LogMethodEntry(cardnumber, cardpin, token, ccRequestId, dateTime,id);
            log.Info("Entering CommandHandler Makepayment method with card number" + cardnumber + "and cardpin" + cardpin + " " +  "amount" + amount + " " +  "token" + token + " " + "ccrequestid" + ccRequestId + " " + "overallTransactionAmount" +  " " + overallTransactionAmount);

            RedeemQwikCilverResponseDTO responseDTO = null;
            List<CardsRequestDTO> Cards = new List<CardsRequestDTO>();
            try
            {
                CardsRequestDTO c1 = new CardsRequestDTO()
                {
                    CardNumber = cardnumber,
                    CardPin = cardpin,
                    Amount = amount
                };
                Cards.Add(c1);
                RedeemQwikCilverRequestDTO redeembalancedto = new RedeemQwikCilverRequestDTO
                {
                    TransactionModeID = 0,
                    TransactionTypeId = 302,
                    IdempotencyKey = id, // some random number 
                    InvoiceNumber = ccRequestId, //ccrequestid
                    InvoiceAmount = Convert.ToDouble(overallTransactionAmount),
                    InputType = '1',
                    numberOfCards = 1,
                    notes = "Redeem Tranaction",
                    Cards = Cards
                };

                log.Info("redeembalancerequestdto = " + JsonConvert.SerializeObject(redeembalancedto));
                responseDTO = RedeemBalance(redeembalancedto, token, dateTime, ccRequestId, executionContext);
                log.Info("responseDTO = " + JsonConvert.SerializeObject(responseDTO));

                if (responseDTO == null)
                {
                    log.Error("RedeemBalance response is null ");
                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //transaction failed
                }
                double balance=0;
                int responsecode=0;
                if (responseDTO != null && responseDTO.Cards != null && responseDTO.Cards.Count > 0)
                {
                     balance = responseDTO.Cards[0].Balance;
                     responsecode = responseDTO.Cards[0].ResponseCode;
                    log.Info("balance= " + balance);
                    log.Info("responsecode= " + responsecode);
                }


                if (Convert.ToString(responsecode) == "10010")
                {
                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5504) + " " + balance); //Card balance is insufficient .Available balance is : 
                }
                if (Convert.ToString(responsecode) == "10116")
                {
                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5547)); //Only Whole Amount is Allowed for Redemption
                }
                if (Convert.ToString(responsecode) == "10532")
                {
                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5550)); //Either Card Number or Card Pin is Incorrect
                }
            }
            catch (Exception e)
            {
                log.Error("CommandHandler Makepayment throw error" + e);
                throw;
            }
            log.LogMethodExit(responseDTO);
            log.Info("Exit from CommandHanlder Makepayment method");
            return responseDTO;

        }
        private RedeemQwikCilverResponseDTO RedeemBalance(RedeemQwikCilverRequestDTO redeembalancedto, string token, DateTime dateTime, string ccRequestId, ExecutionContext executionContext)
        {
            log.LogMethodEntry(redeembalancedto, token, dateTime, ccRequestId);
            log.Info("Entering RedeemBalance method");
            formatted_datetime = dateTime.ToString("yyyy-MM-ddTHH:mm:ss");
            log.Info("formatted_datetime = " + formatted_datetime);
            string responseString = string.Empty;
            RedeemQwikCilverResponseDTO responsedto = null;
            bool isSuccessfull = false;

            if (redeembalancedto == null || string.IsNullOrEmpty(token) || formatted_datetime == null || string.IsNullOrEmpty(ccRequestId))
            {
                log.Error("ReverseBalnce- redeembalancedto null");
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //transaction failed
            }
            try
            {
                try
                {
                    json = JsonConvert.SerializeObject(redeembalancedto, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                    log.Info("json request = " + json);
                }
                catch (JsonSerializationException e)
                {
                    log.Error("RedeemBalance-Failed to serialize the request DTO", e);
                }

                
                List<KeyValuePair<string, string>> headers = new List<KeyValuePair<string, string>>();
                headers.Add(new KeyValuePair<string, string>("DateAtClient", formatted_datetime));
                headers.Add(new KeyValuePair<string, string>("TransactionId", ccRequestId));
                headers.Add(new KeyValuePair<string, string>("Authorization", "Bearer " + token));
                Core.GenericUtilities.WebApiResponse webApiResponse = PostRedeem(redeem_Host_url, headers, json, "application/json", redeembalancedto, dateTime, ccRequestId, token, executionContext);
                responseString = webApiResponse.Response;
                log.Info("responseString= " + responseString);
                if (webApiResponse.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(responseString))
                        {
                            var jsonSettings = new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Ignore
                            };
                            responsedto = (RedeemQwikCilverResponseDTO)JsonConvert.DeserializeObject<RedeemQwikCilverResponseDTO>(responseString, jsonSettings);
                            log.Info("responsedto= " + JsonConvert.SerializeObject(responsedto));
                            isSuccessfull = true;
                            log.Info("isSuccessfull = " + isSuccessfull);
                        }
                        else
                        {
                            log.Error("Response string is empty-RedeemBalance");
                        }
                    }
                    catch (JsonSerializationException ex)
                    {
                        log.Error("RedeemBalance-Failed to deserialize the response", ex);
                    }
                }

            }
            catch (WebException ex)
            {
                log.Error($"RedeemBalance-WebException occurred: {ex.Message}", ex);
            }
            catch (Exception e)
            {
                log.Error("RedeemBalance",e);

            }
            if (!isSuccessfull || responsedto.ResponseCode == 10401) 
            {
                log.Info("isSuccessfull = " + isSuccessfull);
                log.Error("Something went wrong in RedeemBalance method Calling ReverseBalance method");
                try
                {

                    ReverseQwikCilverResponseDTO reverseQwikCilver = ReverseBalance(redeembalancedto, dateTime, ccRequestId, token, executionContext);
                    log.Info("ReverseQwikCilverResponseDTO = " + JsonConvert.SerializeObject(reverseQwikCilver));
                    if (reverseQwikCilver == null)
                    {
                        log.Error("ReverseBalance response is null ");
                        throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //Something went wrong....Transaction Failed
                    }

                    if (!isSuccess(reverseQwikCilver))
                    {
                        log.Info("Reversal failed ");
                        throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //Something went wrong....Transaction Failed
                    }

                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //Something went wrong....Transaction Failed
                }
                catch (Exception e)
                {
                    log.Error("ReverseBalance throw exception" + e);
                    throw;
                }
            }
            log.LogMethodExit(responsedto);
            log.Info("Exist from RedeemBalance Method");
            return responsedto;
        }

        private bool isSuccess(ReverseQwikCilverResponseDTO response)
        {
            log.LogMethodEntry(response);
            log.Info("isSuccess method - Enter");
            log.Info("response.ResponseCode = " + response.ResponseCode);
            if (response.ResponseCode == 0 && response.ErrorCode == null && response.ErrorDescription == null)
            {
                return true;
            }
            log.Info("isSuccess method - Exit");
            log.LogMethodExit(response);
            return false;
        }
        private ReverseQwikCilverResponseDTO ReverseBalance(RedeemQwikCilverRequestDTO redeembalancedto, DateTime dateTime, string ccRequestId, string token, ExecutionContext executionContext)
        {
            log.LogMethodEntry(redeembalancedto, dateTime, ccRequestId, token);
            log.Info("Entering ReverseBalance Method ");
            string responseString = string.Empty;
            ReverseQwikCilverResponseDTO responsedto = null;
            formatted_datetime = dateTime.ToString("yyyy-MM-ddTHH:mm:ss");
            log.Info("formatted_datetime = " + formatted_datetime);
            if (redeembalancedto == null || string.IsNullOrEmpty(token) || formatted_datetime == null || string.IsNullOrEmpty(ccRequestId))
            {
                log.Error("ReverseBalnce- redeembalancedto null");
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //transaction failed
            }
            try
            {

                try
                {
                    json = JsonConvert.SerializeObject(redeembalancedto, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                    log.Info("json request = " + json);
                }
                catch (JsonSerializationException e)
                {
                    log.Error("ReverseBalnce-Failed to serialize the request DTO", e);
                }


                List<KeyValuePair<string, string>> headers = new List<KeyValuePair<string, string>>();
                headers.Add(new KeyValuePair<string, string>("DateAtClient", formatted_datetime));
                headers.Add(new KeyValuePair<string, string>("TransactionId", ccRequestId));
                headers.Add(new KeyValuePair<string, string>("Authorization", "Bearer " + token));
                Core.GenericUtilities.WebApiResponse webApiResponse = Post(reverse_Hostl_url, headers, json, "application/json");
                responseString = webApiResponse.Response;
                log.Info("responseString= " + responseString);

                try
                {
                    if (!string.IsNullOrWhiteSpace(responseString))
                    {
                        var jsonSettings = new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        };
                        responsedto = (ReverseQwikCilverResponseDTO)JsonConvert.DeserializeObject<ReverseQwikCilverResponseDTO>(responseString, jsonSettings);
                        log.Info("responsedto= " + JsonConvert.SerializeObject(responsedto));
                    }
                    else
                    {
                        log.Error("ReverseBalnce-Response string is empty");
                    }
                }
                catch (JsonSerializationException ex)
                {
                    log.Error("ReverseBalnce-Failed to deserialize the response", ex);
                }
            }
            catch (WebException ex)
            {
                log.Error($"ReverseBalnce-WebException occurred: {ex.Message}", ex);
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5503)); //Error communicating with the server
            }
            catch (Exception ex)
            {
                log.Error($"ReverseBalnce-General exception occurred: {ex.Message}", ex);
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5503)); //Error communicating with the server
            }
            log.LogMethodExit(responsedto);
            return responsedto;
        }
        public CancelQwikCilverResponseDTO CancelTransaction(string cardnumber, string ccRequestid, string token, DateTime dateTime, List<OriginalTransactionRequestDTO> cancelOriginalTransactionRequestDTO,string id, ExecutionContext executionContext)
        {
            log.LogMethodEntry(cardnumber, ccRequestid, token, dateTime, cancelOriginalTransactionRequestDTO, id);
            log.Info("Entering CancelTransaction Method");
            CancelQwikCilverResponseDTO responsedto = null;

            List<CardsRequestDTO> Cards = new List<CardsRequestDTO>();
            try
            {
                CardsRequestDTO C2 = new CardsRequestDTO()
                {
                    CardNumber = cardnumber
                };
                Cards.Add(C2);
                CancelQwikCilverRequestDTO canceltranactiondto = new CancelQwikCilverRequestDTO
                {
                    InputType = '1',
                    TransactionModeId = 0,
                    IdempotencyKey = id,
                    InvoiceNumber = ccRequestid,
                    Cards = new List<CardsRequestDTO>
               {
                new CardsRequestDTO
                {
                    CardNumber = cardnumber,
                    OriginalRequest = cancelOriginalTransactionRequestDTO.FirstOrDefault()
                }
               },
                    Notes = "Cancel redeem transaction"
                };
                log.Info("canceltranactiondto = " + JsonConvert.SerializeObject(canceltranactiondto));

                responsedto = CancelOriginalTransaction(canceltranactiondto, ccRequestid, dateTime, token, executionContext);
                log.Info("CancelTransactionresponsedto = " + JsonConvert.SerializeObject(responsedto));

                if (responsedto == null)
                {
                    log.Error("CancelOriginalTransaction response is null ");
                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //transaction failed
                }
                int responsecode=0;
                if (responsedto != null && responsedto.Cards != null && responsedto.Cards.Count > 0)
                {
                     responsecode = responsedto.Cards[0].ResponseCode;
                    log.Info("responsecode= " + responsecode);

                }


                if (Convert.ToString(responsecode) == "10052")
                {
                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5549)); //QwikCilver- Partial refund is not allowed.
                }
            }
            catch(Exception e)
            {
                log.Error("CancelTransaction throw error" + e);
                throw;
            }
            log.Info("Exist from CancelTransaction Method");
            return responsedto;
        }

        private CancelQwikCilverResponseDTO CancelOriginalTransaction(CancelQwikCilverRequestDTO canceltranactiondto, string ccrequestid, DateTime dateTime, string token, ExecutionContext executionContext)
        {
            log.LogMethodEntry(canceltranactiondto, ccrequestid, dateTime, token);
            CancelQwikCilverResponseDTO responsedto = null;
            log.Info("Entering CancelOriginalTransaction Method");
            string responseString = string.Empty;
            formatted_datetime = dateTime.ToString("yyyy-MM-ddTHH:mm:ss");
            log.Info("formatted_datetime = " + formatted_datetime);
            if (canceltranactiondto == null || string.IsNullOrEmpty(token) || formatted_datetime == null || string.IsNullOrEmpty(ccrequestid))
            {
                log.Error("CancelOriginalTransaction- redeembalancedto null");
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //transaction failed
            }

            try
            {

                try
                {
                    json = JsonConvert.SerializeObject(canceltranactiondto, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                    log.Info("json = " + json);
                }
                catch (JsonSerializationException e)
                {
                    log.Error("CancelOriginalTransaction-Failed to serialize the request DTO", e);
                    //throw;
                }


                List<KeyValuePair<string, string>> headers = new List<KeyValuePair<string, string>>();
                headers.Add(new KeyValuePair<string, string>("DateAtClient", formatted_datetime));
                headers.Add(new KeyValuePair<string, string>("TransactionId", ccrequestid));
                headers.Add(new KeyValuePair<string, string>("Authorization", "Bearer " + token));
                Core.GenericUtilities.WebApiResponse webApiResponse = Post(cancel_Host_url, headers, json, "application/json");
                responseString = webApiResponse.Response;
                log.Info("responseString = " + JsonConvert.SerializeObject(responseString));
                try
                {
                    if (!string.IsNullOrWhiteSpace(responseString))
                    {
                        var jsonSettings = new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        };
                        responsedto = (CancelQwikCilverResponseDTO)JsonConvert.DeserializeObject<CancelQwikCilverResponseDTO>(responseString, jsonSettings);
                        log.Info("responsedto = " + JsonConvert.SerializeObject(responsedto));
                    }
                    else
                    {
                        log.Error("CancelOriginalTransaction-Response string is empty");
                    }
                }
                catch (JsonSerializationException ex)
                {
                    log.Error("CancelOriginalTransaction-Failed to deserialize the response", ex);
                }

            }
            catch (WebException ex)
            {
                log.Error($"CancelOriginalTransaction-WebException occurred: {ex.Message}", ex);
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5503)); //Error communicating with the server
            }
            catch (Exception ex)
            {
                log.Error($"CancelOriginalTransaction-General exception occurred: {ex.Message}", ex);
                throw new Exception(MessageContainerList.GetMessage(executionContext, 5503)); //Error communicating with the server
            }
            log.Info("Exist CancelOriginalTransaction Method");
            return responsedto;
        }

        public bool IsCardNumberValid(string cardNumber)
        {
            log.LogMethodEntry(cardNumber);
            log.Info("Entering IsCardNumberValid method");
           
            Regex pattern = new Regex(@"^\d+$");

            if (pattern.IsMatch(cardNumber))
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        private Core.GenericUtilities.WebApiResponse Post(string url, List<KeyValuePair<string, string>> headers, string content, string contentType)
        {
            using (NoSynchronizationContextScope.Enter())
            {
                Task<Core.GenericUtilities.WebApiResponse> task = PostImpl(url, headers, content, contentType);
                task.Wait();
                return task.Result;
            }
        }

        private async Task<Core.GenericUtilities.WebApiResponse> PostImpl(string url, List<KeyValuePair<string, string>> headers, string content, string contentType)
        {
            ServicePointManager.ServerCertificateValidationCallback =
        (sender, certificate, chain, sslPolicyErrors) => true;
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate
            {
                return true;
            });
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

            log.LogMethodEntry(url, headers, content, contentType);
            Core.GenericUtilities.WebApiResponse result;
            try
            {
                using (HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, url))
                {
                    if (headers != null)
                    {
                        foreach (var header in headers)
                        {
                            httpRequestMessage.Headers.Add(header.Key, header.Value);
                        }
                    }
                    byte[] buffer = Encoding.UTF8.GetBytes(content);
                    log.Info("Header Content = " + content);
                    ByteArrayContent byteContent = new ByteArrayContent(buffer);
                    byteContent.Headers.ContentType = new MediaTypeHeaderValue(contentType);
                    httpRequestMessage.Content = byteContent;
                    using (HttpResponseMessage responseMessage = await httpClient.SendAsync(httpRequestMessage))
                    {
                        string response = await responseMessage.Content.ReadAsStringAsync();
                        result = new Semnox.Core.GenericUtilities.WebApiResponse(responseMessage.StatusCode, response, responseMessage.Headers);
                        log.Info("result = " + result);
                    }
                }
            }
            catch (Exception ex)
            {
                string errorMessage = "Error occured while communicating with server";
                log.Error(errorMessage, ex);
                result = new Semnox.Core.GenericUtilities.WebApiResponse(errorMessage, ex);
            }
            return result;
        }

        private Core.GenericUtilities.WebApiResponse PostRedeem(string url, List<KeyValuePair<string, string>> headers, string content, string contentType,RedeemQwikCilverRequestDTO redeembalancedto,DateTime dateTime,string ccRequestId,string token,ExecutionContext executionContext)
        {
            log.LogMethodEntry(url, headers, content, contentType, redeembalancedto, dateTime, ccRequestId, token, executionContext);
            log.Info("PostRedeem method- Enter");
            using (NoSynchronizationContextScope.Enter())
            {
                Task<Core.GenericUtilities.WebApiResponse> task = RedeemImpl(url, headers, content, contentType,redeembalancedto,dateTime, ccRequestId, token, executionContext);
                task.Wait();
                return task.Result;
            }
        }

        private async Task<Core.GenericUtilities.WebApiResponse> RedeemImpl(string url, List<KeyValuePair<string, string>> headers, string content, string contentType, RedeemQwikCilverRequestDTO redeembalancedto, DateTime dateTime, string ccRequestId, string token, ExecutionContext executionContext)
        {
            log.LogMethodEntry(url, content, contentType, redeembalancedto, dateTime, ccRequestId, token, executionContext);
            log.Info("PostRedeem method- Enter");
            ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

            log.LogMethodEntry(url, headers, content, contentType);
            Core.GenericUtilities.WebApiResponse result = null;
            log.Info("redeembalancedto = " + JsonConvert.SerializeObject(redeembalancedto));

            int maxRetries = 3;
            int attempts = 0;
            int timeoutSeconds = timeOutPeriod ;
            int decrement = 2000;

            while (attempts <= maxRetries)
            {
                int currentTimeout = timeoutSeconds - (attempts * decrement);
                if (currentTimeout < 3000) currentTimeout = 3000;
                log.Info("currentTimeout = " + currentTimeout);
                attempts++;

                try
                {
                    using (HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, url))
                    {
                        if (headers != null)
                        {
                            foreach (var header in headers)
                            {
                                httpRequestMessage.Headers.Add(header.Key, header.Value);
                            }
                        }

                        byte[] buffer = Encoding.UTF8.GetBytes(content);
                        log.Info("Header Content = " + content);
                        ByteArrayContent byteContent = new ByteArrayContent(buffer);
                        byteContent.Headers.ContentType = new MediaTypeHeaderValue(contentType);
                        httpRequestMessage.Content = byteContent;
                        RedeemQwikCilverResponseDTO responsedto = null;

                        using (var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds/1000)))
                        {
                            using (HttpResponseMessage responseMessage = await httpClient.SendAsync(httpRequestMessage, cts.Token))
                            {
                                string response = await responseMessage.Content.ReadAsStringAsync();
                                result = new Core.GenericUtilities.WebApiResponse(responseMessage.StatusCode, response, responseMessage.Headers);
                                log.Info("result = " + result);
                                log.Info("response = " + response);


                                try
                                {
                                    if (!string.IsNullOrWhiteSpace(response))
                                    {
                                        var jsonSettings = new JsonSerializerSettings
                                        {
                                            NullValueHandling = NullValueHandling.Ignore
                                        };
                                        responsedto = (RedeemQwikCilverResponseDTO)JsonConvert.DeserializeObject<RedeemQwikCilverResponseDTO>(response, jsonSettings);
                                        log.Info("responsedto= " + JsonConvert.SerializeObject(responsedto));

                                    }
                                    else
                                    {
                                        log.Error("Response string is empty-RedeemBalance");
                                        result = null;
                                        break;
                                    }
                                }
                                catch (JsonSerializationException ex)
                                {
                                    log.Error("RedeemBalance-Failed to deserialize the response", ex);
                                    result = null;
                                    break ;
                                }

                                if (responseMessage.IsSuccessStatusCode && responsedto.ResponseCode != 10401)
                                {
                                    log.Info($"Response received successfully on attempt {attempts}. Exiting loop.");
                                    break;
                                }
                            }
                        }
                    }
                }
                catch (TaskCanceledException ex)
                {

                    if (ex.CancellationToken.IsCancellationRequested)
                    {
                       log.Error($"Timeout occurred on attempt {attempts}. Retrying...");
                        if (attempts > maxRetries)
                        {
                            log.Error("Max retries reached. Calling ReverseAPI...");
                            //call ReverseAPI

                            try
                            {

                                ReverseQwikCilverResponseDTO reverseQwikCilver = ReverseBalance(redeembalancedto, dateTime, ccRequestId, token, executionContext);
                                log.Info("reverseQwikCilver = " + JsonConvert.SerializeObject(reverseQwikCilver));
                                if (reverseQwikCilver == null)
                                {
                                    log.Error("ReverseBalance response is null ");
                                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //Transaction Failed. 
                                }

                                if (!isSuccess(reverseQwikCilver))
                                {
                                    throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //Transaction Failed. 
                                }

                                throw new Exception(MessageContainerList.GetMessage(executionContext, 5500)); //Transaction failed
                            }
                            catch (Exception e)
                            {
                                log.Error("ReverseBalance throw exception" + e);
                                throw;
                            }
                        }
                    }
                    else
                    {
                        log.Error($"Task was canceled for another reason: {ex.Message}");
                       
                    }
                }
                catch (Exception ex)
                {
                    log.Error($"Unexpected error: {ex.Message}");
                    throw;
                    
                }
            }
            log.LogMethodExit();
            return result;

        }

        public BalanceEnquiryResponseDTO BalanceEnquiry(string cardNumber,string token,DateTime dateTime)
        {
            log.LogMethodEntry(cardNumber, token);
            log.Info("BalanceEnquiry method - Enter");
            List<CardsRequestDTO> Cards = new List<CardsRequestDTO>();
            BalanceEnquiryResponseDTO responsedto = null;
            string responseString = string.Empty;
            bool isSuccessfull = false;
            formatted_datetime = dateTime.ToString("yyyy-MM-ddTHH:mm:ss");
            CardsRequestDTO c1 = new CardsRequestDTO()
            {
                CardNumber = cardNumber
            };
            Cards.Add(c1);
            BalanceEnquiryRequestDTO balanceEnquiryRequestDTO = new BalanceEnquiryRequestDTO()
            {
                TransactionTypeId = 306,
                InputType = '1',
                Cards = Cards
            };

            log.Info("balanceEnquiryRequestDTO = " + JsonConvert.SerializeObject(balanceEnquiryRequestDTO));
            try
            {
                json = JsonConvert.SerializeObject(balanceEnquiryRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                log.Info("BalanceEnquiry Request = " + json);
            }
            catch (JsonSerializationException e)
            {
                log.Error("RedeemBalance-Failed to serialize the request DTO", e);
            }
            try
            {
                List<KeyValuePair<string, string>> headers = new List<KeyValuePair<string, string>>();
                headers.Add(new KeyValuePair<string, string>("DateAtClient", formatted_datetime));
                headers.Add(new KeyValuePair<string, string>("Authorization", "Bearer " + token));
                Core.GenericUtilities.WebApiResponse webApiResponse = Post(balance_inquiry_url, headers, json, "application/json");
                responseString = webApiResponse.Response;
                log.Info("responseString = " + responseString);
                if (webApiResponse.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(responseString))
                        {
                            var jsonSettings = new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Ignore
                            };
                            responsedto = (BalanceEnquiryResponseDTO)JsonConvert.DeserializeObject<BalanceEnquiryResponseDTO>(responseString, jsonSettings);
                            isSuccessfull = true;
                            log.Info("responsedto = " + JsonConvert.SerializeObject(responsedto));
                        }
                        else
                        {
                            log.Error("Response string is empty-RedeemBalance");
                            isSuccessfull = false;
                        }
                    }
                    catch (JsonSerializationException ex)
                    {
                        log.Error("RedeemBalance-Failed to deserialize the response", ex);
                        isSuccessfull = false;
                    }
                }

            }
            catch (WebException ex)
            {
                log.Error($"RedeemBalance-WebException occurred: {ex.Message}", ex);
                isSuccessfull = false;
            }
            catch (Exception e)
            {
                log.Error("RedeemBalance", e);
                isSuccessfull = false;
            }

            if (responsedto == null || !isSuccessfull)
            {
                log.Error("RedeemBalance response is not received");
                string msg = MessageContainerList.GetMessage(executionContext, 5500); //transaction failed
                msg += MessageContainerList.GetMessage(executionContext, 6035);//Response not received
                throw new Exception(msg); 
            }


            log.LogMethodExit();
            return responsedto;
        }
    }
}
