﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Serialization;

// 
// This source code was auto-generated by wsdl, Version=4.0.30319.33440.
// 


/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Web.Services.WebServiceBindingAttribute(Name = "Transaction_Inquiry_CardDetailsSoap", Namespace = "https://www.mobile88.com/epayment/webservice")]
public partial class Transaction_Inquiry_CardDetails : System.Web.Services.Protocols.SoapHttpClientProtocol
{

    private System.Threading.SendOrPostCallback TxDetailsInquiryCardInfoOperationCompleted;

    /// <remarks/>
    public Transaction_Inquiry_CardDetails()
    {
        this.Url = "https://payment.ipay88.com.my/ePayment/Webservice/TxInquiryCardDetails/TxDetailsI" +
            "nquiry.asmx";
    }

    /// <remarks/>
    public event TxDetailsInquiryCardInfoCompletedEventHandler TxDetailsInquiryCardInfoCompleted;

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/epayment/webservice/TxDetailsInquiryCardInfo", RequestNamespace = "https://www.mobile88.com/epayment/webservice", ResponseNamespace = "https://www.mobile88.com/epayment/webservice", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    public TransactionInquiry TxDetailsInquiryCardInfo(string MerchantCode, string ReferenceNo, string Amount, string Version)
    {
        object[] results = this.Invoke("TxDetailsInquiryCardInfo", new object[] {
                    MerchantCode,
                    ReferenceNo,
                    Amount,
                    Version});
        return ((TransactionInquiry)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginTxDetailsInquiryCardInfo(string MerchantCode, string ReferenceNo, string Amount, string Version, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("TxDetailsInquiryCardInfo", new object[] {
                    MerchantCode,
                    ReferenceNo,
                    Amount,
                    Version}, callback, asyncState);
    }

    /// <remarks/>
    public TransactionInquiry EndTxDetailsInquiryCardInfo(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((TransactionInquiry)(results[0]));
    }

    /// <remarks/>
    public void TxDetailsInquiryCardInfoAsync(string MerchantCode, string ReferenceNo, string Amount, string Version)
    {
        this.TxDetailsInquiryCardInfoAsync(MerchantCode, ReferenceNo, Amount, Version, null);
    }

    /// <remarks/>
    public void TxDetailsInquiryCardInfoAsync(string MerchantCode, string ReferenceNo, string Amount, string Version, object userState)
    {
        if ((this.TxDetailsInquiryCardInfoOperationCompleted == null))
        {
            this.TxDetailsInquiryCardInfoOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTxDetailsInquiryCardInfoOperationCompleted);
        }
        this.InvokeAsync("TxDetailsInquiryCardInfo", new object[] {
                    MerchantCode,
                    ReferenceNo,
                    Amount,
                    Version}, this.TxDetailsInquiryCardInfoOperationCompleted, userState);
    }

    private void OnTxDetailsInquiryCardInfoOperationCompleted(object arg)
    {
        if ((this.TxDetailsInquiryCardInfoCompleted != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.TxDetailsInquiryCardInfoCompleted(this, new TxDetailsInquiryCardInfoCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    public new void CancelAsync(object userState)
    {
        base.CancelAsync(userState);
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "https://www.mobile88.com/epayment/webservice")]
public partial class TransactionInquiry
{

    private RefundTransaction refundTranxField;

    private string merchantCodeField;

    private string paymentIdField;

    private string refNoField;

    private string amountField;

    private string currencyField;

    private string remarkField;

    private string transIdField;

    private string authCodeField;

    private string statusField;

    private string errdescField;

    private RefundTransaction[] refundField;

    private string iplocationField;

    private string creditcardnoField;

    private string u_banknameField;

    private string u_countryField;

    private string s_banknameField;

    private string s_countryField;

    private string threeDStatusField;

    private string bankMIDField;

    private string tranDateField;

    private string dCCStatusField;

    private string originalAmountField;

    private string originalCurrencyField;

    private string settlementAmountField;

    private string dCCConversionRateField;

    private string settlementCurrencyField;

    private string transStatusField;

    private string resultCodeField;

    private string cCNameField;

    private string cardTypeField;

    private string discountField;

    private string paymentTypeField;

    private string signatureField;

    private string tokenidField;

    private string bindCardErrDescField;

    private string cardCategoryField;

    private string xfield1Field;

    private string xfield2Field;

    /// <remarks/>
    public RefundTransaction RefundTranx
    {
        get
        {
            return this.refundTranxField;
        }
        set
        {
            this.refundTranxField = value;
        }
    }

    /// <remarks/>
    public string MerchantCode
    {
        get
        {
            return this.merchantCodeField;
        }
        set
        {
            this.merchantCodeField = value;
        }
    }

    /// <remarks/>
    public string PaymentId
    {
        get
        {
            return this.paymentIdField;
        }
        set
        {
            this.paymentIdField = value;
        }
    }

    /// <remarks/>
    public string RefNo
    {
        get
        {
            return this.refNoField;
        }
        set
        {
            this.refNoField = value;
        }
    }

    /// <remarks/>
    public string Amount
    {
        get
        {
            return this.amountField;
        }
        set
        {
            this.amountField = value;
        }
    }

    /// <remarks/>
    public string Currency
    {
        get
        {
            return this.currencyField;
        }
        set
        {
            this.currencyField = value;
        }
    }

    /// <remarks/>
    public string Remark
    {
        get
        {
            return this.remarkField;
        }
        set
        {
            this.remarkField = value;
        }
    }

    /// <remarks/>
    public string TransId
    {
        get
        {
            return this.transIdField;
        }
        set
        {
            this.transIdField = value;
        }
    }

    /// <remarks/>
    public string AuthCode
    {
        get
        {
            return this.authCodeField;
        }
        set
        {
            this.authCodeField = value;
        }
    }

    /// <remarks/>
    public string Status
    {
        get
        {
            return this.statusField;
        }
        set
        {
            this.statusField = value;
        }
    }

    /// <remarks/>
    public string Errdesc
    {
        get
        {
            return this.errdescField;
        }
        set
        {
            this.errdescField = value;
        }
    }

    /// <remarks/>
    public RefundTransaction[] Refund
    {
        get
        {
            return this.refundField;
        }
        set
        {
            this.refundField = value;
        }
    }

    /// <remarks/>
    public string Iplocation
    {
        get
        {
            return this.iplocationField;
        }
        set
        {
            this.iplocationField = value;
        }
    }

    /// <remarks/>
    public string Creditcardno
    {
        get
        {
            return this.creditcardnoField;
        }
        set
        {
            this.creditcardnoField = value;
        }
    }

    /// <remarks/>
    public string U_bankname
    {
        get
        {
            return this.u_banknameField;
        }
        set
        {
            this.u_banknameField = value;
        }
    }

    /// <remarks/>
    public string U_country
    {
        get
        {
            return this.u_countryField;
        }
        set
        {
            this.u_countryField = value;
        }
    }

    /// <remarks/>
    public string S_bankname
    {
        get
        {
            return this.s_banknameField;
        }
        set
        {
            this.s_banknameField = value;
        }
    }

    /// <remarks/>
    public string S_country
    {
        get
        {
            return this.s_countryField;
        }
        set
        {
            this.s_countryField = value;
        }
    }

    /// <remarks/>
    public string ThreeDStatus
    {
        get
        {
            return this.threeDStatusField;
        }
        set
        {
            this.threeDStatusField = value;
        }
    }

    /// <remarks/>
    public string BankMID
    {
        get
        {
            return this.bankMIDField;
        }
        set
        {
            this.bankMIDField = value;
        }
    }

    /// <remarks/>
    public string TranDate
    {
        get
        {
            return this.tranDateField;
        }
        set
        {
            this.tranDateField = value;
        }
    }

    /// <remarks/>
    public string DCCStatus
    {
        get
        {
            return this.dCCStatusField;
        }
        set
        {
            this.dCCStatusField = value;
        }
    }

    /// <remarks/>
    public string OriginalAmount
    {
        get
        {
            return this.originalAmountField;
        }
        set
        {
            this.originalAmountField = value;
        }
    }

    /// <remarks/>
    public string OriginalCurrency
    {
        get
        {
            return this.originalCurrencyField;
        }
        set
        {
            this.originalCurrencyField = value;
        }
    }

    /// <remarks/>
    public string SettlementAmount
    {
        get
        {
            return this.settlementAmountField;
        }
        set
        {
            this.settlementAmountField = value;
        }
    }

    /// <remarks/>
    public string DCCConversionRate
    {
        get
        {
            return this.dCCConversionRateField;
        }
        set
        {
            this.dCCConversionRateField = value;
        }
    }

    /// <remarks/>
    public string SettlementCurrency
    {
        get
        {
            return this.settlementCurrencyField;
        }
        set
        {
            this.settlementCurrencyField = value;
        }
    }

    /// <remarks/>
    public string TransStatus
    {
        get
        {
            return this.transStatusField;
        }
        set
        {
            this.transStatusField = value;
        }
    }

    /// <remarks/>
    public string ResultCode
    {
        get
        {
            return this.resultCodeField;
        }
        set
        {
            this.resultCodeField = value;
        }
    }

    /// <remarks/>
    public string CCName
    {
        get
        {
            return this.cCNameField;
        }
        set
        {
            this.cCNameField = value;
        }
    }

    /// <remarks/>
    public string CardType
    {
        get
        {
            return this.cardTypeField;
        }
        set
        {
            this.cardTypeField = value;
        }
    }

    /// <remarks/>
    public string Discount
    {
        get
        {
            return this.discountField;
        }
        set
        {
            this.discountField = value;
        }
    }

    /// <remarks/>
    public string PaymentType
    {
        get
        {
            return this.paymentTypeField;
        }
        set
        {
            this.paymentTypeField = value;
        }
    }

    /// <remarks/>
    public string Signature
    {
        get
        {
            return this.signatureField;
        }
        set
        {
            this.signatureField = value;
        }
    }

    /// <remarks/>
    public string Tokenid
    {
        get
        {
            return this.tokenidField;
        }
        set
        {
            this.tokenidField = value;
        }
    }

    /// <remarks/>
    public string BindCardErrDesc
    {
        get
        {
            return this.bindCardErrDescField;
        }
        set
        {
            this.bindCardErrDescField = value;
        }
    }

    /// <remarks/>
    public string CardCategory
    {
        get
        {
            return this.cardCategoryField;
        }
        set
        {
            this.cardCategoryField = value;
        }
    }

    /// <remarks/>
    public string Xfield1
    {
        get
        {
            return this.xfield1Field;
        }
        set
        {
            this.xfield1Field = value;
        }
    }

    /// <remarks/>
    public string Xfield2
    {
        get
        {
            return this.xfield2Field;
        }
        set
        {
            this.xfield2Field = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "https://www.mobile88.com/epayment/webservice")]
public partial class RefundTransaction
{

    private string statusField;

    private string descriptionField;

    private string amountField;

    private string refundTypeField;

    private string xField1Field;

    private string createDateField;

    /// <remarks/>
    public string Status
    {
        get
        {
            return this.statusField;
        }
        set
        {
            this.statusField = value;
        }
    }

    /// <remarks/>
    public string Description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }

    /// <remarks/>
    public string Amount
    {
        get
        {
            return this.amountField;
        }
        set
        {
            this.amountField = value;
        }
    }

    /// <remarks/>
    public string RefundType
    {
        get
        {
            return this.refundTypeField;
        }
        set
        {
            this.refundTypeField = value;
        }
    }

    /// <remarks/>
    public string xField1
    {
        get
        {
            return this.xField1Field;
        }
        set
        {
            this.xField1Field = value;
        }
    }

    /// <remarks/>
    public string CreateDate
    {
        get
        {
            return this.createDateField;
        }
        set
        {
            this.createDateField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void TxDetailsInquiryCardInfoCompletedEventHandler(object sender, TxDetailsInquiryCardInfoCompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class TxDetailsInquiryCardInfoCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal TxDetailsInquiryCardInfoCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public TransactionInquiry Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((TransactionInquiry)(this.results[0]));
        }
    }
}
