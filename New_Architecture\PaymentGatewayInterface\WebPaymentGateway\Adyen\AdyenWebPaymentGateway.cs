using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface.WebPaymentGateway.Adyen
{
    public class AdyenWebPaymentGateway : PaymentGateway, IPaymentGateway
    {
        private readonly Semnox.Parafait.logging.Logger log;

        // Additional actions result codes - matching old architecture exactly
        private static readonly string[] additionalActionsResultCodes = { "ChallengeShopper", "IdentifyShopper", "PresentToShopper", "RedirectShopper" };

        // Status mapping dictionaries - for final states only
        private static readonly Dictionary<string, PaymentTransactionStatuses> SaleStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
        {
            { "Authorised", PaymentTransactionStatuses.SUCCESS },
            { "Pending", PaymentTransactionStatuses.PENDING },
            { "Received", PaymentTransactionStatuses.PENDING },
            { "Cancelled", PaymentTransactionStatuses.FAILED },
            { "Error", PaymentTransactionStatuses.FAILED },
            { "Refused", PaymentTransactionStatuses.FAILED },
            { "AuthenticationNotRequired", PaymentTransactionStatuses.PRE_AUTH },
            { "AuthenticationFinished", PaymentTransactionStatuses.PRE_AUTH }
        };

        private static readonly Dictionary<string, PaymentTransactionStatuses> RefundStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
        {
            { "received", PaymentTransactionStatuses.SUCCESS },
            { "400", PaymentTransactionStatuses.FAILED }, //Ref link - https://docs.adyen.com/api-explorer/Checkout/latest/post/payments/(paymentPspReference)/reversals#responses-400
            { "401", PaymentTransactionStatuses.FAILED },
            { "403", PaymentTransactionStatuses.FAILED },
            { "422", PaymentTransactionStatuses.FAILED },
            { "500", PaymentTransactionStatuses.FAILED },
        };

        private bool redirectToWebsite = false;
        private AdyenWebCommandHandler _adyenWebCommandHandler = null;
        private string COUNTRY_CODE;
        private string SHOPPER_LOCALE;
        private string CURRENCY_CODE;
        private string API_URL;
        private string API_VERSION;
        private string CHECKOUT_FRONT_END_URL;
        private string CLIENT_KEY;
        private string ASSETS_VERSION;
        private string ENVIRONMENT;
        private string SERVICE_INTEGRATOR_NAME;
        private string POS_SUPPLIER_NAME;
        private string MERCHANT_APPLICATION_NAME;
        private string MERCHANT_OS_VERSION;
        private string MERCHANT_OS;
        private string MERCHANT_DEVICE_OS_VERSION;
        private string paymentModeId;
        private bool IsBillingAddressRequired;
        private string STORE_NAME;

        // Subscription and token support
        private bool IsSubscriptionPayment = false; //TODO: Should be coming from TrxPaymentDTO?
        private string userPaymentTokenForSubscription = string.Empty; //TODO: Should be coming from TrxPaymentDTO?
        //private string billingAddressMode;

        //private enum ADYEN_WEB_BILLING_ADDRESS_MODE
        //{
        //    NONE,
        //    PARTIAL
        //}

        public override bool IsRefundSupported
        {
            get
            {
                return true;
            }
        }
        public override bool CanCreateMultipleInstances
        {
            get
            {
                return true;
            }
        }
        public override bool IsCustomerInfoRequired
        {
            get
            {
                return true;
            }
        }
        public override bool IsProductsInfoRequired
        {
            get
            {
                return true;
            }
        }
        public override bool IsPaymentHistoryListRequired
        {
            get
            {
                return true; // Adyen requires payment history for complex flows like 3DS
            }
        }
        public override bool RedirectResponseToWebsite
        {
            get
            {
                return redirectToWebsite;
            }
        }
        public override string CallbackResponseMessage
        {
            get
            {
                return "[accepted]";
            }
        }

        public AdyenWebPaymentGateway(PaymentConfiguration paymentConfiguration, PaymentMessages paymentMessages, Semnox.Parafait.logging.Logger logger)
            : base(paymentConfiguration, paymentMessages)
        {
            log = logger;
            log.LogMethodEntry();

            CLIENT_KEY = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            API_URL = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL");
            CHECKOUT_FRONT_END_URL = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_BASE_URL");

            CURRENCY_CODE = paymentConfiguration.GetConfiguration("CURRENCY_CODE");
            COUNTRY_CODE = paymentConfiguration.GetConfiguration("COUNTRY_CODE");
            IsBillingAddressRequired = paymentConfiguration.GetConfiguration("ENABLE_ADDRESS_VALIDATION").Equals("Y", StringComparison.OrdinalIgnoreCase);
            //billingAddressMode = IsBillingAddressRequired ? ADYEN_WEB_BILLING_ADDRESS_MODE.PARTIAL.ToString() : ADYEN_WEB_BILLING_ADDRESS_MODE.NONE.ToString();

            SHOPPER_LOCALE = paymentConfiguration.GetConfiguration("SHOPPER_LOCALE");
            ENVIRONMENT = paymentConfiguration.GetConfiguration("PAYMENT_ENVIRONMENT");
            API_VERSION = paymentConfiguration.GetConfiguration("API_VERSION");
            ASSETS_VERSION = paymentConfiguration.GetConfiguration("ASSETS_VERSION");
            SERVICE_INTEGRATOR_NAME = paymentConfiguration.GetConfiguration("SERVICE_INTEGRATOR_NAME");
            POS_SUPPLIER_NAME = paymentConfiguration.GetConfiguration("POS_SUPPLIER_NAME");
            MERCHANT_APPLICATION_NAME = paymentConfiguration.GetConfiguration("MERCHANT_APPLICATION_NAME");
            MERCHANT_OS_VERSION = paymentConfiguration.GetConfiguration("MERCHANT_OS_VERSION");
            MERCHANT_DEVICE_OS_VERSION = paymentConfiguration.GetConfiguration("MERCHANT_DEVICE_OS_VERSION");
            STORE_NAME = paymentConfiguration.GetConfiguration("STORE_NAME");
            MERCHANT_OS = paymentConfiguration.GetConfiguration("MERCHANT_OS");
            paymentModeId = paymentConfiguration.GetConfiguration("PaymentModeId");

            _adyenWebCommandHandler = new AdyenWebCommandHandler(paymentConfiguration, log);

            log.LogMethodExit(null);
        }

        public override void ValidateConfiguration()
        {
            log.LogMethodEntry("START - Configuration Validation");
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_BASE_URL")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ADYEN_HOSTED_PAYMENT_API_KEY")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ADYEN_HOSTED_PAYMENT_API_KEY");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ADYEN_HOSTED_PAYMENT_MERCHANT_ID")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ADYEN_HOSTED_PAYMENT_MERCHANT_ID");
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CURRENCY_CODE")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CURRENCY_CODE");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("BUSINESS_DAY_START_TIME")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "BUSINESS_DAY_START_TIME");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ENABLE_ADDRESS_VALIDATION")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ENABLE_ADDRESS_VALIDATION");
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("MERCHANT_DEVICE_OS_VERSION")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "MERCHANT_DEVICE_OS_VERSION");
            }
            //if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("PINCODE")))
            //{
            //    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "PINCODE");
            //}
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("MERCHANT_OS_VERSION")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "MERCHANT_OS_VERSION");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("PaymentModeId")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "PaymentModeId");
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("SHOPPER_LOCALE")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "SHOPPER_LOCALE");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CURRENCY_CONVERSION_FACTOR")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CURRENCY_CONVERSION_FACTOR");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("API_VERSION")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "API_VERSION");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ASSETS_VERSION")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ASSETS_VERSION");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("COUNTRY_CODE")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "COUNTRY_CODE");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("STORE_NAME")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "STORE_NAME");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("PAYMENT_ENVIRONMENT")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "PAYMENT_ENVIRONMENT");
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("POS_SUPPLIER_NAME")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "POS_SUPPLIER_NAME");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("SERVICE_INTEGRATOR_NAME")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "SERVICE_INTEGRATOR_NAME");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("MERCHANT_APPLICATION_NAME")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "MERCHANT_APPLICATION_NAME");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("MERCHANT_OS")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "MERCHANT_OS");
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("STORE_NAME")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "STORE_NAME");
            }
            log.LogMethodExit("END - completed validation");
        }

        public async override Task<PaymentSessionDTO> CreatePaymentSessionDTO(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);

            string paymentMethodsResponseString = _adyenWebCommandHandler.GetPaymentMethods(paymentRequestDTO);
            if (string.IsNullOrWhiteSpace(paymentMethodsResponseString))
            {
                log.Error("GetPaymentMethods API call failed!");
                throw new Exception("Payment request failed");
            }

            log.Debug($"Response for GetPaymentMethods API call= {paymentMethodsResponseString}");

            Dictionary<string, string> shopperDetailsDict = new Dictionary<string, string>
            {
                { "IsBillingAddressRequired", Convert.ToString(IsBillingAddressRequired).ToLower() },
                { "billingAddressMode", "FULL" }, // Default billing address mode
                { "holderName", paymentRequestDTO.PaymentGatewayCustomerDTO.CustomerName },
                { "shopperCountry", COUNTRY_CODE },
                { "shopperLocale", SHOPPER_LOCALE },
                { "shopperReference", !string.IsNullOrWhiteSpace(paymentRequestDTO.PaymentGatewayCustomerDTO.PGIdentifier) ? paymentRequestDTO.PaymentGatewayCustomerDTO.PGIdentifier : "" }, // CustomerGuid detail passed
                { "shopperAddress", !string.IsNullOrWhiteSpace(paymentRequestDTO.PaymentGatewayCustomerDTO.AddressLine1) ? paymentRequestDTO.PaymentGatewayCustomerDTO.AddressLine1 : "" },
                { "shopperPostalCode", !string.IsNullOrWhiteSpace(paymentRequestDTO.PaymentGatewayCustomerDTO.PinCode) ? paymentRequestDTO.PaymentGatewayCustomerDTO.PinCode : "" },
                { "CURRENCY_CODE", CURRENCY_CODE },
            };

            PaymentSessionDTO paymentSessionDTO = new PaymentSessionDTO
            {
                RequestId = paymentRequestDTO.RequestIdentifier,
                IntRequestId = paymentRequestDTO.IntRequestIdentifier,
                PaymentGatewayName = PaymentGateways.AdyenCallbackHostedPayment.ToString()
            };
            paymentSessionDTO.MountedPaymentSessionJSON = JsonConvert.SerializeObject(SetPostParameters(paymentRequestDTO, shopperDetailsDict, paymentMethodsResponseString));
            log.LogMethodExit(paymentSessionDTO);
            return await Task.FromResult(paymentSessionDTO);
        }

        /// <summary>
        /// Creates initial payment request with payment token (equivalent to CreateGatewayPaymentInitialRequest)
        /// </summary>
        /// <param name="paymentRequestDTO">Payment request details</param>
        /// <param name="paymentToken">Payment token for the session</param>
        /// <returns>PaymentSessionDTO with token-based payment session</returns>
        public async Task<PaymentSessionDTO> CreatePaymentSessionWithToken(PaymentRequestDTO paymentRequestDTO, string paymentToken)
        {
            log.LogMethodEntry(paymentRequestDTO, paymentToken);

            try
            {
                if (paymentRequestDTO.Amount <= 0)
                {
                    log.Error($"Order amount must be greater than zero. Order Amount was {paymentRequestDTO.Amount}");
                    throw new Exception("Order amount must be greater than zero");
                }

                // Create request parameters for token-based payment
                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>
                {
                    { "paymentSession", paymentRequestDTO.RequestIdentifier },
                    { "paymentToken", paymentToken },
                    { "amount", paymentRequestDTO.Amount.ToString() },
                    { "currency", CURRENCY_CODE },
                    { "merchantReference", paymentRequestDTO.RequestIdentifier }
                };

                PaymentSessionDTO paymentSessionDTO = new PaymentSessionDTO
                {
                    RequestId = paymentRequestDTO.RequestIdentifier,
                    IntRequestId = paymentRequestDTO.IntRequestIdentifier,
                    PaymentGatewayName = PaymentGateways.AdyenCallbackHostedPayment.ToString()
                };

                paymentSessionDTO.MountedPaymentSessionJSON = JsonConvert.SerializeObject(requestParamsDict);

                log.Debug("Token-based payment session created successfully");
                log.Debug($"Request parameters: {paymentSessionDTO.MountedPaymentSessionJSON}");

                log.LogMethodExit(paymentSessionDTO);
                return await Task.FromResult(paymentSessionDTO);
            }
            catch (Exception ex)
            {
                log.Error("Error creating payment session with token", ex);
                throw;
            }
        }

        private IDictionary<string, string> SetPostParameters(PaymentRequestDTO paymentRequestDTO, Dictionary<string, string> shopperDetails, string paymentMethodsResponseString)
        {
            log.LogMethodEntry(paymentRequestDTO);
            try
            {
                IDictionary<string, string> postparamslistOut = new Dictionary<string, string>();
                postparamslistOut.Clear();

                postparamslistOut.Add("amount", _adyenWebCommandHandler.GetFormattedAmount(Convert.ToDouble(paymentRequestDTO.Amount), convertToMinorUnit: true).ToString());
                postparamslistOut.Add("paymentModeId", paymentModeId);
                postparamslistOut.Add("merchantReference", paymentRequestDTO.RequestIdentifier);
                postparamslistOut.Add("api_post_url", API_URL);
                postparamslistOut.Add("checkout_front_end_url", CHECKOUT_FRONT_END_URL);
                postparamslistOut.Add("api_version", API_VERSION);
                postparamslistOut.Add("client_key", CLIENT_KEY);
                postparamslistOut.Add("assets_version", ASSETS_VERSION);
                postparamslistOut.Add("paymentMethods", paymentMethodsResponseString);

                // Shopper billing fields
                postparamslistOut.Add("holderName", shopperDetails["holderName"]);
                postparamslistOut.Add("CURRENCY_CODE", shopperDetails["CURRENCY_CODE"]);
                postparamslistOut.Add("shopperCountry", shopperDetails["shopperCountry"]);
                postparamslistOut.Add("shopperLocale", shopperDetails["shopperLocale"]);
                postparamslistOut.Add("shopperReference", shopperDetails["shopperReference"]);
                postparamslistOut.Add("shopperAddress", shopperDetails["shopperAddress"]);
                postparamslistOut.Add("shopperPostalCode", shopperDetails["shopperPostalCode"]);
                postparamslistOut.Add("IsBillingAddressRequired", shopperDetails["IsBillingAddressRequired"]);
                postparamslistOut.Add("billingAddressMode", shopperDetails["billingAddressMode"]);
                postparamslistOut.Add("environment", ENVIRONMENT);

                postparamslistOut.Add("shopperEmail", paymentRequestDTO.PaymentGatewayCustomerDTO.CustomerEmail);
                postparamslistOut.Add("shopperIP", paymentRequestDTO.IpAddress);


                return postparamslistOut;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }
        public async override Task<string> GetPaymentIdentifier(string paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);
            string paymentGatewayIdentifier = string.Empty;
            WebhookResponseDto webhookResponse = null;
            Notificationitem responseObj = null;

            if (String.IsNullOrEmpty(paymentResponse))
            {
                log.Debug("Unable to extract GatewayResponseString from " + paymentResponse);
                throw new PaymentResponseNullException(paymentMessages.GetMessage(5984)); // PaymentResponse is Null
            }
            dynamic response = JsonConvert.DeserializeObject(paymentResponse);

            #region Get TransactionId
            //Get TransactionId
            if (response.merchantReference != null)
            {
                log.Debug("Transaction id: " + response.merchantReference.ToString());
                paymentGatewayIdentifier = Convert.ToString(response.merchantReference);

                // Check if this is a hosted gateway response (no requestData and no shouldRedirect)
                if (response.requestData == null && response.shouldRedirect == null)
                {
                    // This indicates it's a hosted gateway response - should redirect to website
                    redirectToWebsite = true;
                    log.Debug("Detected hosted gateway response - no requestData and no shouldRedirect, setting redirectToWebsite = true");
                }
            }
            else if (response.notificationItems != null)
            {
                webhookResponse = JsonConvert.DeserializeObject<WebhookResponseDto>(paymentResponse);

                if (webhookResponse.notificationItems == null || webhookResponse.notificationItems.Count <= 0)
                {
                    log.Error("Webhook Response->notificationItems was null");
                    throw new Exception("Payment failed");
                }

                var paymentResponseList = from notificationItem in webhookResponse.notificationItems
                                          select notificationItem;

                if (paymentResponseList == null)
                {
                    log.Error("Webhook Response->paymentResponse was null");
                    throw new Exception("Payment failed");
                }

                responseObj = paymentResponseList.First();
                if (responseObj == null || responseObj.NotificationRequestItem == null)
                {
                    log.Error("Webhook Response->paymentResponse.First() or NotificationRequestItem was null");
                    throw new Exception("Payment failed");
                }

                // Store validation logic
                if (string.IsNullOrWhiteSpace(responseObj.NotificationRequestItem?.additionalData?.store))
                {
                    log.Error("Store names are empty or doesn't match, not able to confirm it as web transaction. cannot process further.");
                    throw new StoreNameEmptyException("Store names are empty or doesn't match, not able to confirm it as web transaction, cannot process further");
                }
                if (string.IsNullOrWhiteSpace(STORE_NAME) ||
                        responseObj.NotificationRequestItem.additionalData.store.Equals(STORE_NAME, StringComparison.OrdinalIgnoreCase) == false)
                {
                    log.Error("store name in response: " + responseObj.NotificationRequestItem.additionalData.store);
                    log.Error("Store name in config: " + STORE_NAME);
                    log.Error("Store name doesn't match with web store name, not a web transaction, cannot process further.");
                    throw new StoreNameMismatchException("Store names are empty or doesn't match, not able to confirm it as web transaction, cannot process further");
                }

                // Update the hostedDto with TrxId
                if (responseObj.NotificationRequestItem.merchantReference != null)
                {
                    paymentGatewayIdentifier = responseObj.NotificationRequestItem.merchantReference;
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
            }
            else
            {
                log.Error("Sale response doesn't contain TrxId.");
                throw new Exception("Error processing your payment");
            }
            #endregion Get TransactionId

            log.LogMethodExit(paymentGatewayIdentifier);
            return await Task.FromResult(paymentGatewayIdentifier);
        }

        public async override Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO)
        {
            log.LogMethodEntry(paymentGatewayResponseDTO);

            string paymentResponse = paymentGatewayResponseDTO.GatewayResponse;
            int paymentModeId = -1;
            string trxGuid = string.Empty;

            PaymentResponseDTO paymentResponseDTO = null;
            WebhookResponseDto webhookResponse = null;
            Notificationitem responseObj = null;

            try
            {
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("ProcessPaymentResponse(): paymentResponse was null");
                    throw new PaymentResponseNullException("Payment failed");
                }

                log.Info($"paymentResponse={paymentResponse}");

                dynamic tempObj = JsonConvert.DeserializeObject(paymentResponse);
                if (tempObj == null)
                {
                    log.Error("ProcessPaymentResponse(): paymentResponse deserialization failed");
                    throw new Exception("Error: Payment failed");
                }

                if (tempObj.paymentModeId != null)
                {
                    paymentModeId = Convert.ToInt32(tempObj.paymentModeId);
                }

                // Check shouldRedirect flag for backward compatibility
                bool shouldRedirect = true; 
                if (tempObj.shouldRedirect != null)
                {
                    shouldRedirect = Convert.ToBoolean(tempObj.shouldRedirect);
                    log.Debug($"shouldRedirect flag found: {shouldRedirect}");
                }
                else
                {
                    log.Debug("shouldRedirect flag not found, defaulting to true for legacy compatibility");
                }

                #region Get TransactionId
                if (tempObj.merchantReference != null)
                {
                    log.Debug("Transaction id: " + tempObj.merchantReference.ToString());
                    trxGuid = Convert.ToString(tempObj.merchantReference);
                }
                else if (tempObj.notificationItems != null)
                {
                    webhookResponse = JsonConvert.DeserializeObject<WebhookResponseDto>(paymentResponse);

                    if (webhookResponse.notificationItems == null || webhookResponse.notificationItems.Count <= 0)
                    {
                        log.Error("Webhook Response->notificationItems was null or empty");
                        throw new Exception("Payment failed");
                    }

                    var paymentResponseList = from notificationItem in webhookResponse.notificationItems
                                              select notificationItem;

                    if (paymentResponseList == null)
                    {
                        log.Error("Webhook Response->paymentResponse was null");
                        throw new Exception("Payment failed");
                    }

                    responseObj = paymentResponseList.First();
                    if (responseObj == null || responseObj.NotificationRequestItem == null)
                    {
                        log.Error("Webhook Response->paymentResponse.First() or NotificationRequestItem was null");
                        throw new Exception("Payment failed");
                    }

                    // Store validation logic for webhook responses
                    if (string.IsNullOrWhiteSpace(responseObj.NotificationRequestItem?.additionalData?.store))
                    {
                        log.Error("Store names are empty or doesn't match, not able to confirm it as web transaction. cannot process further.");
                        throw new StoreNameEmptyException("Store names are empty or doesn't match, not able to confirm it as web transaction, cannot process further");
                    }
                    if (string.IsNullOrWhiteSpace(STORE_NAME) ||
                            responseObj.NotificationRequestItem.additionalData.store.Equals(STORE_NAME, StringComparison.OrdinalIgnoreCase) == false)
                    {
                        log.Error("store name in response: " + responseObj.NotificationRequestItem.additionalData.store);
                        log.Error("Store name in config: " + STORE_NAME);
                        log.Error("Store name doesn't match with web store name, not a web transaction, cannot process further.");
                        throw new StoreNameMismatchException("Store names are empty or doesn't match, not able to confirm it as web transaction, cannot process further");
                    }

                    if (responseObj.NotificationRequestItem.merchantReference != null)
                    {
                        trxGuid = responseObj.NotificationRequestItem.merchantReference;
                    }
                    else
                    {
                        log.Error("Response for Sale Transaction doesn't contain TrxId.");
                        throw new Exception("Error processing your payment");
                    }
                }
                else
                {
                    log.Error("Sale response doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                #endregion Get TransactionId

                paymentResponseDTO = new PaymentResponseDTO { InvoiceNo = trxGuid };

                string textResponse = string.Empty, dSIXReturnCode = string.Empty;
                bool savePaymentCCTransactionPGW = false;

                // Decide the path /payments or webhook or /payments/details
                if (tempObj.requestData != null)
                {
                    MakePaymentResponseDto makePaymentResponse = null;

                    // /payments call
                    log.Debug("<-----------------------------START /payments call -------------------------->");
                    makePaymentResponse = _adyenWebCommandHandler.MakePayment(paymentResponse, paymentResponseDTO);

                    if (makePaymentResponse == null)
                    {
                        log.Error("ProcessPaymentResponse(): makePaymentResponse was null");
                        throw new Exception("Error: Payment failed");
                    }
                    log.Debug($"Response from MakePayment()= {makePaymentResponse.ToString()}");

                    // Check if additional actions are required - following old architecture pattern
                    log.Debug("Checking if additional actions are required based on resultCode..");
                    PaymentTransactionStatuses paymentStatusForWebsite;

                    if (IsAdditionalActionRequired(makePaymentResponse.resultCode))
                    {
                        log.Debug($"Additional action required for resultCode: {makePaymentResponse.resultCode}");
                        paymentStatusForWebsite = PaymentTransactionStatuses.PRE_AUTH;
                    }
                    else
                    {
                        log.Debug("No additional action required - mapping to final status");
                        paymentStatusForWebsite = MapPaymentStatus(makePaymentResponse.resultCode, PaymentGatewayTransactionType.SALE);
                        if (string.IsNullOrWhiteSpace(paymentStatusForWebsite.ToString()))
                        {
                            log.Error("ProcessPaymentResponse(): paymentStatusForWebsite was empty");
                            throw new Exception("Payment failed");
                        }
                    }
                    log.Debug($"Final payment status for /payments= {paymentStatusForWebsite.ToString()}");

                    // Store action data if additional actions are required
                    if (paymentStatusForWebsite == PaymentTransactionStatuses.PRE_AUTH && makePaymentResponse.action != null)
                    {
                        log.Debug("Storing action data for additional response processing");

                        // Store action object in ProcessData for later conversion to AdditionalResponseDTO
                        var actionData = new
                        {
                            type = makePaymentResponse.action.type,
                            url = makePaymentResponse.action.url,
                            method = makePaymentResponse.action.method,
                            paymentData = makePaymentResponse.paymentData,
                            action = makePaymentResponse.action
                        };
                        paymentResponseDTO.ProcessData = JsonConvert.SerializeObject(actionData);
                        log.Debug($"Action data stored in ProcessData: {paymentResponseDTO.ProcessData}");
                    }

                    makePaymentResponse.TxStatus = paymentStatusForWebsite.ToString();

                    if (paymentStatusForWebsite == PaymentTransactionStatuses.PRE_AUTH)
                    {
                        // additional action required
                        log.Debug("PaymentStatus is PRE_AUTH. Additional action is required to complete payment");
                        paymentResponseDTO.Status = PaymentTransactionStatuses.PRE_AUTH.ToString();
                    }
                    else
                    {
                        // additional action NOT required
                        log.Debug("No Additional action required. Processing to check final payment status.");
                        savePaymentCCTransactionPGW = true;

                        // check the final status
                        if (paymentStatusForWebsite == PaymentTransactionStatuses.SUCCESS)
                        {
                            // payment succeeded
                            log.Debug($"Payment applied successfully");
                            paymentResponseDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                        }
                        else if (paymentStatusForWebsite == PaymentTransactionStatuses.PENDING) //For SEPA Direct Debit, take some time for processing
                        {
                            log.Debug("Payment status is Pending. Waiting for payment response from Adyen");
                            paymentResponseDTO.Status = PaymentTransactionStatuses.PENDING.ToString();
                        }
                        else
                        {
                            log.Error("Payment failed");
                            paymentResponseDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                        }

                        // Set payment response details
                        paymentResponseDTO.AuthCode = (makePaymentResponse != null && makePaymentResponse.additionalData != null) ? makePaymentResponse.additionalData.authCode : "";

                        double amount;
                        if (makePaymentResponse.additionalData != null && !string.IsNullOrWhiteSpace(makePaymentResponse.additionalData.authorisedAmountValue))
                        {
                            log.Debug("Getting amount value from additionalData field");
                            amount = Convert.ToDouble(makePaymentResponse.additionalData.authorisedAmountValue);
                            paymentResponseDTO.Amount = (decimal)_adyenWebCommandHandler.GetFormattedAmount(amount, convertToMinorUnit: false);
                        }
                        else if (makePaymentResponse.amount != null && makePaymentResponse.amount.value > 0)
                        {
                            log.Debug("Getting amount value from makePaymentResponse.amount");
                            amount = Convert.ToDouble(makePaymentResponse.amount.value);
                            paymentResponseDTO.Amount = (decimal)_adyenWebCommandHandler.GetFormattedAmount(amount, convertToMinorUnit: false);
                        }

                        paymentResponseDTO.AcctNo = makePaymentResponse.additionalData != null ? GetMaskedCardNumber(makePaymentResponse.additionalData.cardSummary) : "";
                        paymentResponseDTO.RefNo = makePaymentResponse.pspReference;
                        paymentResponseDTO.CardType = GetCardType(makePaymentResponse);
                        paymentResponseDTO.TransactionDatetime = paymentGatewayResponseDTO.SiteDateTime;

                        textResponse = makePaymentResponse.resultCode;
                        dSIXReturnCode = makePaymentResponse.refusalReason;

                        log.Debug($"Value of textResponse: {textResponse} dSIXReturnCode: {dSIXReturnCode}");
                    }

                    log.Debug("<-----------------------------END /payments call -------------------------->");
                }
                else if (tempObj.notificationItems != null)
                {
                    // Webhook response received
                    log.Debug("<------------------Start Webhook Response processing------------------>");

                    if (responseObj.NotificationRequestItem != null && !string.IsNullOrWhiteSpace(responseObj.NotificationRequestItem.success) && responseObj.NotificationRequestItem.eventCode.ToUpper() == "AUTHORISATION".ToUpper())
                    {
                        log.Debug($"Webhook Notification eventCode is {responseObj.NotificationRequestItem.eventCode}");

                        // Store validation logic for webhook processing
                        if (string.IsNullOrWhiteSpace(responseObj.NotificationRequestItem?.additionalData?.store))
                        {
                            log.Error("Store names are empty or doesn't match, not able to confirm it as web transaction. cannot process further.");
                            throw new StoreNameEmptyException("Store names are empty or doesn't match, not able to confirm it as web transaction. cannot process further");
                        }
                        if (string.IsNullOrWhiteSpace(STORE_NAME) ||
                            responseObj.NotificationRequestItem.additionalData.store.Equals(STORE_NAME, StringComparison.OrdinalIgnoreCase) == false)
                        {
                            log.Error("Store name doesn't match with web store name, not a web transaction. cannot process further.");
                            throw new StoreNameMismatchException("Store names are empty or doesn't match, not able to confirm it as web transaction. cannot process further");
                        }

                        log.Debug("Webhook EventCode is AUTHORISATION. Proceeding to check the payment status");
                        savePaymentCCTransactionPGW = true;

                        paymentResponseDTO.AuthCode = (responseObj != null && responseObj.NotificationRequestItem != null && responseObj.NotificationRequestItem.additionalData != null) ? responseObj.NotificationRequestItem.additionalData.authCode : "";

                        //Get Amount
                        if (responseObj.NotificationRequestItem != null && responseObj.NotificationRequestItem.amount != null)
                        {
                            log.Debug("Getting amount value from NotificationRequestItem.amount.value field");
                            paymentResponseDTO.Amount = (decimal)_adyenWebCommandHandler.GetFormattedAmount(Convert.ToDouble(responseObj.NotificationRequestItem.amount.value), convertToMinorUnit: false);
                        }

                        paymentResponseDTO.AcctNo = responseObj.NotificationRequestItem.additionalData != null ? GetMaskedCardNumber(responseObj.NotificationRequestItem.additionalData.cardSummary) : "";
                        paymentResponseDTO.RefNo = responseObj.NotificationRequestItem.pspReference;
                        paymentResponseDTO.CardType = responseObj.NotificationRequestItem.paymentMethod;
                        paymentResponseDTO.TransactionDatetime = paymentGatewayResponseDTO.SiteDateTime;

                        if (responseObj.NotificationRequestItem.success.ToUpper() == "false".ToUpper())
                        {
                            // payment failed
                            log.Error("WebhookResponse->Payment failed");
                            textResponse = "FAILED";
                            paymentResponseDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                        }
                        else
                        {
                            // payment applied
                            log.Debug("WebhookResponse->Payment success");
                            textResponse = "SUCCESS";
                            paymentResponseDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                        }

                        dSIXReturnCode = responseObj.NotificationRequestItem.additionalData != null ? responseObj.NotificationRequestItem.additionalData.refusalReasonRaw : "";
                        log.Debug($"Value of textResponse: {textResponse} dSIXReturnCode: {dSIXReturnCode}");
                    }
                    else
                    {
                        if (responseObj.NotificationRequestItem != null)
                        {
                            log.Error($"Unknown eventCode:{responseObj.NotificationRequestItem.eventCode.ToUpper()}. Kindly check Adyen Webhook eventCode setup on Adyen dashboard. Only AUTHORISATION event must be enabled");
                        }
                        else
                        {
                            log.Error("NotificationRequestItem was null");
                        }
                        throw new Exception("Payment processing failed!");
                    }
                    log.Debug("<------------------End Webhook Response processing------------------>");
                }
                else
                {
                    MakePaymentResponseDto makePaymentDetailsResponse = null;
                    //payment details call
                    log.Debug("<------------------------------- Start /payments/details ------------------------------->");

                    makePaymentDetailsResponse = _adyenWebCommandHandler.MakePaymentDetails(tempObj);

                    if (makePaymentDetailsResponse == null)
                    {
                        log.Error("No response for makePaymentDetails. Payment processing failed");
                        throw new Exception("Payment failed");
                    }
                    log.Debug($"Response from MakePaymentDetails()= {makePaymentDetailsResponse.ToString()}");

                    // deciding whether to redirect or to send 200 OK based on the type of payment method
                    if (tempObj.shouldRedirect == null)
                    {
                        redirectToWebsite = true; // redirect
                        log.Debug("shouldRedirect is null in payment details - setting redirectToWebsite = true");
                    }

                    // Check if additional actions are required - following old architecture pattern
                    log.Debug("Checking if additional actions are required for payment/details based on resultCode..");
                    PaymentTransactionStatuses paymentDetailsStatusType;

                    if (IsAdditionalActionRequired(makePaymentDetailsResponse.resultCode))
                    {
                        log.Debug($"Additional action required for payment/details resultCode: {makePaymentDetailsResponse.resultCode}");
                        paymentDetailsStatusType = PaymentTransactionStatuses.PRE_AUTH;
                    }
                    else
                    {
                        log.Debug("No additional action required for payment/details - mapping to final status");
                        paymentDetailsStatusType = MapPaymentStatus(makePaymentDetailsResponse.resultCode, PaymentGatewayTransactionType.SALE);
                        if (string.IsNullOrWhiteSpace(paymentDetailsStatusType.ToString()))
                        {
                            log.Error("Unable to get payment status. Payment processing failed");
                            throw new Exception("Payment failed");
                        }
                    }
                    log.Debug($"Final payment status for payment/details= {paymentDetailsStatusType.ToString()}");

                    // Store action data if additional actions are required
                    if (paymentDetailsStatusType == PaymentTransactionStatuses.PRE_AUTH && makePaymentDetailsResponse.action != null)
                    {
                        log.Debug("Storing action data for additional response processing in payment/details");

                        // Store action object in ProcessData for later conversion to AdditionalResponseDTO
                        var actionData = new
                        {
                            type = makePaymentDetailsResponse.action.type,
                            url = makePaymentDetailsResponse.action.url,
                            method = makePaymentDetailsResponse.action.method,
                            paymentData = makePaymentDetailsResponse.paymentData,
                            action = makePaymentDetailsResponse.action
                        };
                        paymentResponseDTO.ProcessData = JsonConvert.SerializeObject(actionData);
                        log.Debug($"Payment details action data stored in ProcessData: {paymentResponseDTO.ProcessData}");
                    }

                    makePaymentDetailsResponse.TxStatus = paymentDetailsStatusType.ToString();

                    if (paymentDetailsStatusType == PaymentTransactionStatuses.PRE_AUTH)
                    {
                        // additional action required
                        log.Debug("In /Payment/Details, additional action required");
                        paymentResponseDTO.Status = PaymentTransactionStatuses.PRE_AUTH.ToString();
                    }
                    else
                    {
                        // additional action NOT required
                        log.Debug("In /Payment/Details no additional action required. Proceeding to check final payment status");
                        savePaymentCCTransactionPGW = true;

                        // check the final status
                        if (paymentDetailsStatusType == PaymentTransactionStatuses.SUCCESS)
                        {
                            // payment succeeded
                            log.Debug($"Payment applied successfully");
                            paymentResponseDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                        }
                        else if (paymentDetailsStatusType == PaymentTransactionStatuses.PENDING)
                        {
                            log.Debug("Payment status is Pending. Waiting for payment response from Adyen");
                            paymentResponseDTO.Status = PaymentTransactionStatuses.PENDING.ToString();
                        }
                        else
                        {
                            log.Error($"Payment failed");
                            paymentResponseDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                        }

                        paymentResponseDTO.AuthCode = (makePaymentDetailsResponse != null && makePaymentDetailsResponse.additionalData != null) ? makePaymentDetailsResponse.additionalData.authCode : "";

                        double amount;
                        if (makePaymentDetailsResponse.additionalData != null && !string.IsNullOrWhiteSpace(makePaymentDetailsResponse.additionalData.authorisedAmountValue))
                        {
                            log.Debug("Getting amount value from additionalData field");
                            amount = Convert.ToDouble(makePaymentDetailsResponse.additionalData.authorisedAmountValue);
                            paymentResponseDTO.Amount = (decimal)_adyenWebCommandHandler.GetFormattedAmount(amount, convertToMinorUnit: false);
                        }
                        else if (makePaymentDetailsResponse.amount != null && makePaymentDetailsResponse.amount.value > 0)
                        {
                            log.Debug("Getting amount value from makePaymentDetailsResponse.amount field");
                            amount = Convert.ToDouble(makePaymentDetailsResponse.amount.value);
                            paymentResponseDTO.Amount = (decimal)_adyenWebCommandHandler.GetFormattedAmount(amount, convertToMinorUnit: false);
                        }

                        paymentResponseDTO.AcctNo = makePaymentDetailsResponse.additionalData != null ? GetMaskedCardNumber(makePaymentDetailsResponse.additionalData.cardSummary) : "";
                        paymentResponseDTO.RefNo = makePaymentDetailsResponse.pspReference; // paymentId
                        paymentResponseDTO.CardType = makePaymentDetailsResponse.paymentMethod != null ? makePaymentDetailsResponse.paymentMethod.brand : "";
                        paymentResponseDTO.TransactionDatetime = paymentGatewayResponseDTO.SiteDateTime;

                        textResponse = makePaymentDetailsResponse.resultCode;
                        dSIXReturnCode = makePaymentDetailsResponse.refusalReason;

                        log.Debug($"Value of textResponse: {textResponse} dSIXReturnCode: {dSIXReturnCode}");
                    }

                    log.Debug($"<------------------------------- END /payments/details ------------------------------->");
                }

                // Set common payment response fields
                paymentResponseDTO.TextResponse = textResponse;
                paymentResponseDTO.DSIXReturnCode = dSIXReturnCode;
                paymentResponseDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                paymentResponseDTO.RecordNo = trxGuid; // parafait TrxId
                paymentResponseDTO.Purchase = paymentResponseDTO.Amount.ToString("0.00");
                paymentResponseDTO.Authorize = paymentResponseDTO.Purchase;

                log.Debug($"Final PaymentResponseDTO: {paymentResponseDTO.ToString()}");
            }
            catch (Exception ex)
            {
                log.Error($"Payment processing failed for payment response. Error: {ex.Message}", ex);
                log.Debug($"Payment response that caused error: {paymentResponse}");
                throw;
            }

            // Note: Additional response handling is done at the LocalWebPaymentsUseCases level
            // The PaymentResponseDTO.ProcessData field can be used to store additional data if needed



            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        /// <summary>
        /// Check if additional actions are required based on resultCode - following old architecture pattern
        /// </summary>
        private bool IsAdditionalActionRequired(string resultCode)
        {
            log.LogMethodEntry(resultCode);
            bool isRequired = !string.IsNullOrWhiteSpace(Array.Find(additionalActionsResultCodes, element => element == resultCode));
            log.Debug($"Additional action required for resultCode '{resultCode}': {isRequired}");
            log.LogMethodExit(isRequired);
            return isRequired;
        }

        private PaymentTransactionStatuses MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            log.Debug($"Mapping payment status - Raw status: '{rawPaymentGatewayStatus}', Transaction type: '{pgwTrxType}'");
            PaymentTransactionStatuses paymentStatusType = PaymentTransactionStatuses.FAILED;
            try
            {
                Dictionary<string, PaymentTransactionStatuses> pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        log.Debug($"Using SaleStatusMappingDict for transaction type: {pgwTrxType}");
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        log.Debug($"Using RefundStatusMappingDict for transaction type: {pgwTrxType}");
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");
                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());
                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in salePaymentStatusMappingDict. Defaulting payment status to pending.");
                    paymentStatusType = PaymentTransactionStatuses.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentTransactionStatuses.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        private string GetMaskedCardNumber(string cardSummary)
        {
            log.LogMethodEntry(cardSummary);
            string maskedCardNumber = string.Empty;

            try
            {
                if (!string.IsNullOrWhiteSpace(cardSummary))
                {
                    // Adyen typically returns card summary in format like "1111"
                    // We'll format it as a masked card number
                    maskedCardNumber = $"xxxxxxxxxxxx{cardSummary}";
                }
            }
            catch (Exception ex)
            {
                log.Error($"Error getting masked card number from '{cardSummary}'", ex);
            }

            log.LogMethodExit(maskedCardNumber);
            return maskedCardNumber;
        }

        private string GetCardType(MakePaymentResponseDto makePaymentResponse)
        {
            log.LogMethodEntry(makePaymentResponse);
            string cardType = string.Empty;

            try
            {
                if (makePaymentResponse?.paymentMethod?.brand != null)
                {
                    cardType = makePaymentResponse.paymentMethod.brand;
                }
                else if (makePaymentResponse?.additionalData?.cardBin != null)
                {
                    // Fallback to determine card type from BIN if available
                    cardType = DetermineCardTypeFromBin(makePaymentResponse.additionalData.cardBin);
                }
            }
            catch (Exception ex)
            {
                log.Error($"Error getting card type from payment response", ex);
            }

            log.LogMethodExit(cardType);
            return cardType;
        }

        private string DetermineCardTypeFromBin(string cardBin)
        {
            if (string.IsNullOrWhiteSpace(cardBin) || cardBin.Length < 4)
                return "Unknown";

            string firstFour = cardBin.Substring(0, 4);
            int bin = int.Parse(firstFour);

            if (bin >= 4000 && bin <= 4999)
                return "Visa";
            else if (bin >= 5100 && bin <= 5599)
                return "MasterCard";
            else if (bin >= 3400 && bin <= 3499)
                return "American Express";
            else if (bin >= 3000 && bin <= 3099)
                return "Diners Club";
            else
                return "Unknown";
        }

        /// <summary>
        /// Initiates payment processing for callback flow - handles initial payment request
        /// </summary>
        /// <param name="paymentResponse">Payment response from frontend</param>
        /// <returns>PaymentResponseDTO with payment details and additional response data in AdditionalData field if needed</returns>
        public async Task<PaymentResponseDTO> InitiatePaymentProcessing(string paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);

            try
            {
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("InitiatePaymentProcessing(): paymentResponse was null");
                    throw new PaymentResponseNullException("Payment failed");
                }

                log.Info($"paymentResponse={paymentResponse}");

                dynamic tempObj = JsonConvert.DeserializeObject(paymentResponse);
                if (tempObj == null)
                {
                    log.Error("InitiatePaymentProcessing(): paymentResponse deserialization failed");
                    throw new Exception("Error: Payment failed");
                }

                // Get transaction ID
                string trxGuid = string.Empty;
                if (tempObj.merchantReference != null)
                {
                    log.Debug("Transaction id: " + tempObj.merchantReference.ToString());
                    trxGuid = Convert.ToString(tempObj.merchantReference);
                }
                else
                {
                    log.Error("Payment response doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }

                PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO
                {
                    InvoiceNo = trxGuid,
                    RecordNo = trxGuid,
                    Status = PaymentTransactionStatuses.PENDING.ToString(),
                    TranCode = PaymentGatewayTransactionType.SALE.ToString()
                };

                // For callback flow, we typically return pending status initially
                // and wait for the actual payment result via webhook or callback
                // Additional response data can be stored in AdditionalData field if needed

                log.LogMethodExit(paymentResponseDTO);
                return await Task.FromResult(paymentResponseDTO);
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }
        }



        /// <summary>
        /// Processes refund request for Adyen payments
        /// </summary>
        /// <param name="refundRequestDTO">Refund request details</param>
        /// <param name="progress">Progress reporter</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>PaymentResponseDTO with refund result</returns>
        public async override Task<PaymentResponseDTO> Refund(RefundRequestDTO refundRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            log.LogMethodEntry(refundRequestDTO, progress, cancellationToken);
            log.Info($"Processing refund request - Amount: {refundRequestDTO?.Amount}, Original Reference: {refundRequestDTO?.PaymentResponses?.RefNo}");

            // Validate input parameters early
            if (refundRequestDTO == null)
            {
                log.Error("refundRequestDTO is null");
                throw new ArgumentNullException(nameof(refundRequestDTO), "Refund request cannot be null");
            }

            // Initialize response variables for better tracking and debugging
            string invoiceNo = refundRequestDTO.RequestIdentifier;
            string recordNo = refundRequestDTO.IntRequestIdentifier.ToString();
            string refNo = "";
            string authCode = "";
            decimal amount = refundRequestDTO.Amount;
            string authorize = "0";
            string purchase = "0";
            string tranCode = PaymentGatewayTransactionType.REFUND.ToString();
            DateTime transactionDatetime = DateTime.UtcNow;
            string status = "";
            string textResponse = "";
            string dSIXReturnCode = "";
            string acqRefData = "";
            string acctNo = refundRequestDTO.PaymentResponses?.AcctNo ?? "";

            PaymentResponseDTO paymentResponseDTO = null;

            try
            {
                progress?.Report(new PaymentProgressReport(paymentMessages.GetMessages(6037, refundRequestDTO.IntRequestIdentifier, refundRequestDTO.Amount), false));
                log.Debug($"Refund progress reported for request: {refundRequestDTO.IntRequestIdentifier}");

                // Check if original payment exists and get payment details
                if (string.IsNullOrWhiteSpace(refundRequestDTO.PaymentResponses?.RefNo))
                {
                    log.Error("Original payment reference (PSP Reference) is required for refund");
                    recordNo = "C"; // Indicate error condition
                    dSIXReturnCode = "REFUND VALIDATION FAILED";
                    textResponse = "Original payment reference is required for refund";
                    status = PaymentTransactionStatuses.FAILED.ToString();

                    paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo,
                                                               null, tranCode, refNo, purchase, authorize, transactionDatetime,
                                                               authCode, null, null, null, null, acqRefData, null, null, null,
                                                               null, status, null, null, null, amount);
                    return await Task.FromResult(paymentResponseDTO);
                }

                log.Debug("----------------------------Refund processing started------------------------------");

                // Check if refund was already attempted (equivalent to old architecture's duplicate check)
                log.Debug("Checking if refund already attempted using payment history");
                if (await IsRefundAlreadyAttempted(refundRequestDTO))
                {
                    log.Error($"Refund already attempted - TrxId: {refundRequestDTO.RequestIdentifier}");
                    recordNo = "C";
                    dSIXReturnCode = "REFUND ALREADY ATTEMPTED";
                    textResponse = "Refund has already been attempted for this transaction";
                    status = PaymentTransactionStatuses.FAILED.ToString();

                    paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo,
                                                               null, tranCode, refNo, purchase, authorize, transactionDatetime,
                                                               authCode, null, null, null, null, acqRefData, null, null, null,
                                                               null, status, null, null, null, amount);
                    return await Task.FromResult(paymentResponseDTO);
                }

                log.Debug($"Refund validation passed - PSP Reference: {refundRequestDTO.PaymentResponses.RefNo}");

                // Determine if this is a partial refund or full refund
                bool isPartialRefund = ShouldProcessAsPartialRefund(refundRequestDTO);
                RefundResponseDto refundResponse = null;

                if (isPartialRefund)
                {
                    log.Info($"Processing as partial refund - Amount: {refundRequestDTO.Amount}");
                    refundResponse = await ProcessPartialRefund(refundRequestDTO);
                }
                else
                {
                    log.Info($"Processing as full refund - Amount: {refundRequestDTO.Amount}");
                    // Create full refund request with detailed logging (equivalent to old architecture's RefundRequestDto)
                    RefundRequestDto adyenRefundRequest = new RefundRequestDto
                    {
                        merchantAccount = _adyenWebCommandHandler.GetMerchantAccount(),
                        reference = refundRequestDTO.RequestIdentifier,
                        paymentId = refundRequestDTO.PaymentResponses.RefNo,
                        amount = new Amount
                        {
                            currency = CURRENCY_CODE,
                            value = (int)_adyenWebCommandHandler.GetFormattedAmount(refundRequestDTO.Amount, convertToMinorUnit: true)
                        },
                        applicationInfo = new ApplicationInfo
                        {
                            externalPlatform = new ExternalPlatform
                            {
                                integrator = SERVICE_INTEGRATOR_NAME,
                                name = POS_SUPPLIER_NAME,
                            },
                            merchantApplication = new MerchantApplication
                            {
                                name = MERCHANT_APPLICATION_NAME,
                                version = MERCHANT_OS_VERSION
                            },
                            merchantDevice = new MerchantDevice
                            {
                                os = MERCHANT_OS,
                                osVersion = MERCHANT_DEVICE_OS_VERSION
                            }
                        }
                    };

                    log.Debug($"Adyen refund request - Merchant: {adyenRefundRequest.merchantAccount}, Reference: {adyenRefundRequest.reference}, Amount: {adyenRefundRequest.amount.value}");

                    // Process full refund through command handler
                    refundResponse = _adyenWebCommandHandler.ProcessRefund(refundRequestDTO.PaymentResponses.RefNo, adyenRefundRequest);
                }

                if (refundResponse != null)
                {
                    // Map refund response to PaymentResponseDTO with detailed variable assignment
                    PaymentTransactionStatuses refundStatus = MapPaymentStatus(refundResponse.status, PaymentGatewayTransactionType.REFUND);

                    refNo = refundResponse.pspReference;
                    textResponse = refundResponse.status;
                    dSIXReturnCode = refundResponse.status;
                    status = refundStatus.ToString();
                    acqRefData = refundResponse.message ?? "";

                    // Set success/failure specific values
                    if (refundStatus == PaymentTransactionStatuses.SUCCESS)
                    {
                        log.Debug("Refund processed successfully");
                        recordNo = "A"; // Success indicator
                        purchase = refundRequestDTO.Amount.ToString("0.00");
                        authorize = purchase;
                        dSIXReturnCode = "REFUND SUCCESS";
                    }
                    else
                    {
                        log.Error($"Refund failed with status: {refundResponse.status}");
                        recordNo = "C"; // Error indicator
                        dSIXReturnCode = "REFUND FAILED";
                        acqRefData = refundResponse.message ?? "Refund failed";
                    }

                    // Create comprehensive response
                    paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo,
                                                               null, tranCode, refNo, purchase, authorize, transactionDatetime,
                                                               authCode, null, null, null, null, acqRefData, null, null, null,
                                                               null, status, null, null, null, amount);
                }
                else
                {
                    log.Error("Refund response was null");
                    recordNo = "C";
                    dSIXReturnCode = "REFUND FAILED";
                    textResponse = "No response received from payment gateway";
                    status = PaymentTransactionStatuses.FAILED.ToString();
                    acqRefData = "No response received from payment gateway";

                    paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo,
                                                           null, tranCode, refNo, purchase, authorize, transactionDatetime,
                                                           authCode, null, null, null, null, acqRefData, null, null, null,
                                                           null, status, null, null, null, amount);
                }
            }
            catch (Exception ex)
            {
                log.Error("Refund request DTO: " + refundRequestDTO.ToString());
                log.Error("Refund processing failed", ex);

                recordNo = "C";
                dSIXReturnCode = ex.Message;
                textResponse = "Error performing Refund!";
                status = PaymentTransactionStatuses.ERROR.ToString();
                acqRefData = ex.Message;

                paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo,
                                                       null, tranCode, refNo, purchase, authorize, transactionDatetime,
                                                       authCode, null, null, null, null, acqRefData, null, null, null,
                                                       null, status, null, null, null, amount);
                throw;
            }

            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        /// <summary>
        /// Enhanced ProcessPaymentResponse with payment history support for complex Adyen flows
        /// </summary>
        /// <param name="paymentGatewayResponseDTO">Payment gateway response</param>
        /// <param name="paymentHistoryList">List of previous payment responses for context</param>
        /// <returns>PaymentResponseDTO with enhanced processing based on payment history</returns>
        public async override Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO, List<PaymentResponseDTO> paymentHistoryList)
        {
            log.LogMethodEntry(paymentGatewayResponseDTO, paymentHistoryList);
            log.Debug($"Processing Adyen payment response with {paymentHistoryList?.Count ?? 0} payment history records");

            // For Adyen, payment history can provide valuable context for:
            // 1. 3DS authentication flows - understanding previous attempts
            // 2. Multi-step payment processes - tracking payment progression
            // 3. Retry logic - analyzing failed payment patterns
            // 4. Fraud detection - payment behavior analysis

            if (paymentHistoryList != null && paymentHistoryList.Count > 0)
            {
                log.Debug("Payment history available - analyzing for enhanced processing");

                // Analyze payment history for patterns
                var failedPayments = paymentHistoryList.Where(p => p.Status == PaymentStatuses.FAILED.ToString()).ToList();
                var pendingPayments = paymentHistoryList.Where(p => p.Status == PaymentStatuses.SALE_INITIATED.ToString()).ToList();
                var successfulPayments = paymentHistoryList.Where(p => p.Status == PaymentStatuses.SUCCESS.ToString()).ToList();

                log.Debug($"Payment history analysis - Failed: {failedPayments.Count}, Pending: {pendingPayments.Count}, Successful: {successfulPayments.Count}");

                // Check for retry scenarios
                if (failedPayments.Count > 0)
                {
                    log.Info($"Found {failedPayments.Count} failed payment attempts - this may be a retry scenario");
                }

                // Check for pending 3DS flows
                if (pendingPayments.Count > 0)
                {
                    log.Info($"Found {pendingPayments.Count} pending payments - may be completing a 3DS flow");
                }
            }
            else
            {
                log.Debug("No payment history available - proceeding with standard processing");
            }

            // Call the standard ProcessPaymentResponse method
            // The payment history context has been logged and can influence future enhancements
            PaymentResponseDTO result = await ProcessPaymentResponse(paymentGatewayResponseDTO);

            log.Debug("Enhanced processing completed - payment history context applied");
            log.LogMethodExit(result);
            return result;
        }

        /// <summary>
        /// Determines if the refund should be processed as a partial refund
        /// Equivalent to old architecture's logic: if (Convert.ToDouble(saleCCTransactionsPGWDTOList[0].Authorize) > transactionPaymentsDTO.Amount)
        /// </summary>
        /// <param name="refundRequestDTO">Refund request details</param>
        /// <returns>True if should be processed as partial refund, false for full refund</returns>
        private bool ShouldProcessAsPartialRefund(RefundRequestDTO refundRequestDTO)
        {
            log.LogMethodEntry(refundRequestDTO);

            try
            {
                // Find the original SALE transaction from payment history (equivalent to old architecture's saleCCTransactionsPGWDTOList search)
                if (refundRequestDTO.PaymentResponseHistory != null && refundRequestDTO.PaymentResponseHistory.Any())
                {
                    var originalSaleTransaction = refundRequestDTO.PaymentResponseHistory
                        .Where(p => p.TranCode == PaymentGatewayTransactionType.SALE.ToString())
                        .FirstOrDefault();

                    if (originalSaleTransaction != null)
                    {
                        // Compare original transaction amount with refund amount (equivalent to old architecture's comparison)
                        if (originalSaleTransaction.Amount > refundRequestDTO.Amount)
                        {
                            log.Debug($"Partial refund detected - Original Sale Amount: {originalSaleTransaction.Amount}, Refund Amount: {refundRequestDTO.Amount}");
                            return true;
                        }
                        else
                        {
                            log.Debug($"Full refund detected - Original Sale Amount: {originalSaleTransaction.Amount}, Refund Amount: {refundRequestDTO.Amount}");
                            return false;
                        }
                    }
                }

                // If we can't find original transaction, check if refund amount is less than any payment in history
                if (refundRequestDTO.PaymentResponseHistory != null && refundRequestDTO.PaymentResponseHistory.Any())
                {
                    var anyOriginalPayment = refundRequestDTO.PaymentResponseHistory.FirstOrDefault();
                    if (anyOriginalPayment != null && anyOriginalPayment.Amount > refundRequestDTO.Amount)
                    {
                        log.Debug($"Partial refund detected based on payment history - Original: {anyOriginalPayment.Amount}, Refund: {refundRequestDTO.Amount}");
                        return true;
                    }
                }

                // Default to full refund if we can't determine
                log.Debug("Processing as full refund - unable to determine original transaction amount");
                return false;
            }
            catch (Exception ex)
            {
                log.Error("Error determining refund type, defaulting to full refund", ex);
                return false;
            }
        }

        /// <summary>
        /// Processes a partial refund using Adyen's partial refund API
        /// Equivalent to old architecture's PerformSyncPartialRefund method
        /// </summary>
        /// <param name="refundRequestDTO">Refund request details</param>
        /// <returns>Refund response from Adyen</returns>
        private async Task<RefundResponseDto> ProcessPartialRefund(RefundRequestDTO refundRequestDTO)
        {
            log.LogMethodEntry(refundRequestDTO);
            log.Info($"Processing partial refund - Amount: {refundRequestDTO.Amount}, PSP Reference: {refundRequestDTO.PaymentResponses?.RefNo}");

            try
            {
                // Get formatted amount value (equivalent to old architecture's amountValue calculation)
                double amountValue = _adyenWebCommandHandler.GetFormattedAmount(refundRequestDTO.Amount, convertToMinorUnit: true);

                // Create partial refund request (equivalent to old architecture's PartialRefundRequestDto)
                PartialRefundRequestDto partialRefundRequest = new PartialRefundRequestDto
                {
                    merchantAccount = _adyenWebCommandHandler.GetMerchantAccount(),
                    reference = refundRequestDTO.RequestIdentifier,
                    paymentId = refundRequestDTO.PaymentResponses.RefNo, // PSP Reference of original payment
                    applicationInfo = new ApplicationInfo
                    {
                        externalPlatform = new ExternalPlatform
                        {
                            integrator = SERVICE_INTEGRATOR_NAME,
                            name = POS_SUPPLIER_NAME,
                        },
                        merchantApplication = new MerchantApplication
                        {
                            name = MERCHANT_APPLICATION_NAME,
                            version = MERCHANT_OS_VERSION
                        },
                        merchantDevice = new MerchantDevice
                        {
                            os = MERCHANT_OS,
                            osVersion = MERCHANT_DEVICE_OS_VERSION
                        }
                    },
                    amount = new PartialRefundAmountDto
                    {
                        currency = CURRENCY_CODE,
                        value = Convert.ToInt32(amountValue)
                    }
                };

                log.Debug($"Partial refund request created: {partialRefundRequest}");

                // Process partial refund through command handler (equivalent to old architecture's MakePartialRefund)
                RefundResponseDto refundResponse = _adyenWebCommandHandler.MakePartialRefund(partialRefundRequest);

                log.Debug($"Partial refund response: {refundResponse}");

                if (refundResponse == null)
                {
                    log.Error("Partial refund response was null");
                    throw new Exception("Partial refund failed");
                }

                log.Info($"Partial refund response received - Status: {refundResponse?.status}, PSP Reference: {refundResponse?.pspReference}");
                return refundResponse;
            }
            catch (Exception ex)
            {
                log.Error("Error processing partial refund", ex);
                throw;
            }
        }

        /// <summary>
        /// Checks if a refund has already been attempted for the given transaction
        /// Equivalent to old architecture's duplicate refund check logic
        /// </summary>
        /// <param name="refundRequestDTO">Refund request details</param>
        /// <returns>True if refund already attempted, false otherwise</returns>
        private async Task<bool> IsRefundAlreadyAttempted(RefundRequestDTO refundRequestDTO)
        {
            log.LogMethodEntry(refundRequestDTO);

            try
            {
                // Check payment history for existing refund attempts
                if (refundRequestDTO.PaymentResponseHistory != null && refundRequestDTO.PaymentResponseHistory.Any())
                {
                    var existingRefunds = refundRequestDTO.PaymentResponseHistory
                        .Where(p => p.TranCode == PaymentGatewayTransactionType.REFUND.ToString())
                        .ToList();

                    if (existingRefunds.Any())
                    {
                        log.Debug($"Found {existingRefunds.Count} existing refund attempts for transaction: {refundRequestDTO.RequestIdentifier}");
                        return true;
                    }
                }

                log.Debug($"No existing refund attempts found for transaction: {refundRequestDTO.RequestIdentifier}");
                return false;
            }
            catch (Exception ex)
            {
                log.Error("Error checking for existing refund attempts", ex);
                // In case of error, assume no previous refund to allow processing
                return false;
            }
        }

        ///// <summary>
        ///// Performs status check for Adyen payments
        ///// Note: Adyen Web doesn't support transaction search APIs. Status checks must be handled via webhooks.
        ///// </summary>
        ///// <param name="statusCheckRequestDTO">Status check request details</param>
        ///// <param name="progress">Progress reporter</param>
        ///// <param name="cancellationToken">Cancellation token</param>
        ///// <returns>PaymentResponseDTO with current payment status</returns>
        ///// <exception cref="NotSupportedException">Adyen Web doesn't support status check operations</exception>
        //public async override Task<PaymentResponseDTO> StatusCheck(StatusCheckRequestDTO statusCheckRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        //{
        //    log.LogMethodEntry(statusCheckRequestDTO, progress, cancellationToken);
        //    log.Info($"Status check requested for transaction: {statusCheckRequestDTO?.OriginalTransactionReference}");

        //    // Adyen Web doesn't support transaction search APIs
        //    // Status updates must be handled via webhooks
        //    string errorMessage = "Adyen Web payment gateway doesn't support status check operations. Payment status updates are handled via webhooks.";
        //    log.Error(errorMessage);
        //    log.Debug($"StatusCheck not supported for Adyen Web - Original Reference: {statusCheckRequestDTO?.OriginalTransactionReference}");

        //    throw new NotSupportedException(errorMessage);
        //}
    }
}
