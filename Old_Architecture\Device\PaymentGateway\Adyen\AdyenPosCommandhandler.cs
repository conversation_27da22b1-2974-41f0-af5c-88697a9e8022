﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway
{
    internal class AdyenPosCommandhandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string posId, terminalId, tapiUrl, webApiUrl, serviceId, apiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, storeName;
        private const string ENABLE_DONATION = "AskGiving";
        private const string ENABLE_PARTIAL_PAYMENT = "AllowPartialAuthorisation";
        private const string ENABLE_TIP = "AskGratuity";
        private bool isDonationEnabled, isPartialPaymentAllowed, isTipAllowed, isManual, isSubscriptionTokenCreation;
        private string strSaleToAcquirerData;
        private Transactionconditions manualKeyEntryParams;
        private Dictionary<string, string> applicationInfo = new Dictionary<string, string>();
        private int adyenTransactionTimeout;
        //private Dictionary<string, string> esdInfo = new Dictionary<string, string>();
        public AdyenPosCommandhandler(string serviceId, string posId, string terminalId, string tapiUrl, string webApiUrl, string apiKey, string merchantAccount, string protocolVersion, string messageClass, string shopperCurrency, bool isDonationEnabled, bool isPartialPaymentAllowed, bool isTipAllowed, bool isManual, bool isSubscriptionTokenCreation, Dictionary<string, string> applicationInfo, int adyenTransactionTimeout, string storeName)
        {
            log.LogMethodEntry(serviceId, posId, terminalId, tapiUrl, webApiUrl, apiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isDonationEnabled, isPartialPaymentAllowed, isTipAllowed, isManual, isSubscriptionTokenCreation, applicationInfo, adyenTransactionTimeout, storeName);
            this.posId = posId;
            this.terminalId = terminalId;
            this.tapiUrl = tapiUrl;
            this.webApiUrl = webApiUrl;
            this.adyenTransactionTimeout = adyenTransactionTimeout;
            //this.webApiVersion = webApiVersion;
            //this.deviceIpAddress = deviceIpAddress;
            //this.devicePort = devicePort;

            this.serviceId = serviceId; // this should be unique within 48 hours
            this.apiKey = apiKey;
            this.merchantAccount = merchantAccount;
            this.protocolVersion = protocolVersion;
            this.messageClass = messageClass;
            this.shopperCurrency = shopperCurrency;
            this.isDonationEnabled = isDonationEnabled;
            this.isPartialPaymentAllowed = isPartialPaymentAllowed;
            this.isTipAllowed = isTipAllowed;
            this.isManual = isManual;
            this.isSubscriptionTokenCreation = isSubscriptionTokenCreation;
            this.applicationInfo = applicationInfo;
            this.storeName = storeName;
            //this.esdInfo = esdInfo;

            strSaleToAcquirerData = GetTenderOptions(isDonationEnabled, isPartialPaymentAllowed, isTipAllowed);
            log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");

            string strApplicationInfoParams = GetApplicationInfoParams(this.applicationInfo);
            log.Debug($"strApplicationInfoParams = {strApplicationInfoParams}");

            //string strESDInfoParams = GetESDInfoParams(this.esdInfo);
            //log.Debug($"strESDInfoParams = {strESDInfoParams}");

           

            if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
            {
                strSaleToAcquirerData = $"{strApplicationInfoParams}&{strSaleToAcquirerData}";
            }
            else
            {
                strSaleToAcquirerData = $"&{strApplicationInfoParams}";
            }

            log.Debug($"strSaleToAcquirerData after adding application and esd info={strSaleToAcquirerData}");

            manualKeyEntryParams = GetManualKeyEntryParams(isManual);
            log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");
            log.LogMethodExit();
        }

        private string GetESDInfoParams(Dictionary<string, string> esdInfo)
        {
            try
            {
                log.LogMethodEntry(applicationInfo);
                string strESDInfoParams = string.Empty;

                if (esdInfo.Keys.Count > 0)
                {
                    foreach (var item in esdInfo)
                    {
                        strESDInfoParams += $"{item.Key}={item.Value}&";
                    }
                    strESDInfoParams.Remove(strESDInfoParams.Length - 1);
                    log.Debug($"strESDInfoParams = {strESDInfoParams}");
                }

                log.LogMethodExit(strESDInfoParams);
                return strESDInfoParams;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        private string GetApplicationInfoParams(Dictionary<string, string> applicationInfo)
        {
            try
            {
                log.LogMethodEntry(applicationInfo);
                string strApplicationInfoParams = string.Empty;

                if (applicationInfo.Keys.Count > 0)
                {
                    foreach (var item in applicationInfo)
                    {
                        strApplicationInfoParams += $"{item.Key}={item.Value}&";
                    }
                    strApplicationInfoParams.Remove(strApplicationInfoParams.Length - 1);
                    log.Debug($"strApplicationInfoParams = {strApplicationInfoParams}");
                }

                log.LogMethodExit(strApplicationInfoParams);
                return strApplicationInfoParams;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public string DoPayment(double amount, string ccRequestId)
        {
            log.LogMethodEntry(amount, ccRequestId);
            string paymentResponse = string.Empty;
            try
            {
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData

                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = amount,
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Payment requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Sale response was null");
                    throw new Exception("Payment failed: could not receive response");
                }

            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoAuthorization(decimal amount, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            log.LogMethodEntry(amount, ccRequestId);
            try
            {
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"authorisationType=FinalAuth&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"authorisationType=FinalAuth";
                }
                log.Debug($"DoAuthorization(): strSaleToAcquirerData={strSaleToAcquirerData}");

                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = Convert.ToDouble(amount)
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Auth requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Auth response was null");
                    throw new Exception("Payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoPreAuthorization(decimal amount, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(amount, ccRequestId);
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"authorisationType=PreAuth&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"authorisationType=PreAuth";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = Convert.ToDouble(amount),
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Preauth requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Preauth response was null");
                    throw new Exception("Payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoPreAuthorizationAdjustment(decimal amount, string paymentId, string adjustAuthBlob, string ccRequestId, int currencyConversionFactor, string apiUrl)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(amount, paymentId, adjustAuthBlob, ccRequestId, currencyConversionFactor, apiUrl);
                int authAmount = GetAmountInMinorUnit(amount, currencyConversionFactor);
                log.Debug($"authAmount={authAmount}");
                AdjustAuthorizationRequestDto requestDto = new AdjustAuthorizationRequestDto
                {
                    merchantAccount = merchantAccount,
                    originalReference = paymentId,
                    modificationAmount = new Amount
                    {
                        currency = shopperCurrency,
                        value = authAmount
                    },
                    reference = ccRequestId,
                    additionalData = new Additionaldata
                    {
                        adjustAuthorisationData = adjustAuthBlob,
                        // ESD
                        //enhancedSchemeData_customerReference = esdInfo["enhancedSchemeData.customerReference"],
                        //enhancedSchemeData_destinationPostalCode = esdInfo["enhancedSchemeData.destinationPostalCode"],
                        //enhancedSchemeData_orderDate = esdInfo["enhancedSchemeData.orderDate"],
                    },
                    allowPartialAuth = isPartialPaymentAllowed ? "true" : "false"
                    
                };

                log.Debug($"Preauth adjust requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteWebApiRequests(requestDto.ToString(), apiUrl);
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Preauth adjust response was null");
                    throw new Exception("Preauth adjust failed: could not receive response");
                }
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoCapture(string paymentId, decimal amount, string ccRequestId, int currencyConversionFactor)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(paymentId, amount, ccRequestId, currencyConversionFactor);
                int captureAmount = GetAmountInMinorUnit(amount, currencyConversionFactor);
                log.Debug($"captureAmount={captureAmount}");
                CapturePaymentRequestDto requestDto = new CapturePaymentRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount,
                    amount = new Amount
                    {
                        value = captureAmount,
                        currency = shopperCurrency
                    }
                };

                log.Debug($"Capture requestDto={requestDto.ToString()}");
                paymentResponse = MakeCaptureRequest(paymentId, requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Capture response was null");
                    throw new Exception("Payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoCancelAuthorization(string paymentId, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {
                CancelAuthRequestDto requestDto = new CancelAuthRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount,
                };

                log.Debug($"CancelAuth requestDto={requestDto.ToString()}");
                paymentResponse = MakeCancelAuthRequest(paymentId, requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("CancelAuth response was null");
                    throw new Exception("Payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            return paymentResponse;
        }

        public string DoAbortTransaction()
        {
            string paymentResponse = string.Empty;
            return paymentResponse;
        }

        public string DoFullRefund(string paymentId, string paymentDate, string ccRequestId)
        {
            string refundResponse = string.Empty;
            try
            {
                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageClass = messageClass,
                            MessageCategory = "Reversal",
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        ReversalRequest = new ReversalRequestDto
                        {
                            OriginalPOITransaction = new OriginalPOITransaction
                            {
                                POITransactionID = new POITransactionID
                                {
                                    TransactionID = paymentId,
                                    TimeStamp = paymentDate
                                }
                            },
                            ReversalReason = "MerchantCancel",
                            SaleData = new Saledata
                            {
                                SaleToAcquirerData = strSaleToAcquirerData,
                            }
                        }
                    }
                };

                log.Debug($"Refund requestDto={requestDto.ToString()}");
                refundResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(refundResponse))
                {
                    log.Error("Refund response was null");
                    throw new Exception("Refund failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            return refundResponse;
        }

        public string DoRefund(string paymentId, string paymentTimestamp, decimal amount, string ccRequestId,string terminal)
        {
            string refundResponse = string.Empty;


            if (!string.IsNullOrWhiteSpace(terminal))
            {
                terminalId = terminal;
            }
            try
            {
                log.LogMethodEntry(paymentId, paymentTimestamp, amount, ccRequestId);
                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageClass = messageClass,
                            MessageCategory = "Reversal",
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        ReversalRequest = new ReversalRequestDto
                        {
                            OriginalPOITransaction = new OriginalPOITransaction
                            {
                                POITransactionID = new POITransactionID
                                {
                                    TransactionID = paymentId,
                                    TimeStamp = paymentTimestamp
                                }
                            },
                            ReversalReason = "MerchantCancel",
                            ReversedAmount =Convert.ToDouble( amount),
                            SaleData = new Saledata
                            {
                                SaleToAcquirerData = strSaleToAcquirerData,
                                SaleTransactionID = new Saletransactionid
                                {
                                    TimeStamp = getUTCDateTime(),
                                    TransactionID = ccRequestId
                                }
                            }
                        }
                    }
                };

                log.Debug($"Refund requestDto={requestDto.ToString()}");
                refundResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"refundResponse={refundResponse}");
                if (string.IsNullOrWhiteSpace(refundResponse))
                {
                    log.Error("Refund response was null");
                    throw new Exception("Refund failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(refundResponse);
            return refundResponse;
        }
        public string DoRefundAuthAdjustPayment(string ccRequestId, string paymentId)
        {
            string responseFromServer = string.Empty;
            log.LogMethodEntry(ccRequestId, paymentId);
            try
            {
                WebRefundRequestDto requestDto = new WebRefundRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount
                };

                string jsonRequest = JsonConvert.SerializeObject(requestDto);
                log.Debug($"jsonRequest = {jsonRequest}");

                string apiUrl = $"{webApiUrl}/payments/{paymentId}/cancels";
                log.Debug($"apiUrl = {apiUrl}");

                responseFromServer = ExecuteWebApiRequests(jsonRequest, apiUrl);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        public string DoRefundSubscriptionPayment(string ccRequestId, string paymentId)
        {
            string responseFromServer = string.Empty;
            log.LogMethodEntry(ccRequestId, paymentId);
            try
            {
                WebRefundRequestDto requestDto = new WebRefundRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount
                };

                string jsonRequest = JsonConvert.SerializeObject(requestDto);
                log.Debug($"jsonRequest = {jsonRequest}");

                string apiUrl = $"{webApiUrl}/payments/{paymentId}/reversals";
                log.Debug($"apiUrl = {apiUrl}");

                responseFromServer = ExecuteWebApiRequests(jsonRequest, apiUrl);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        public string DoIndependantRefund(decimal refundAmount, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(refundAmount, ccRequestId);
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");

                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount =Convert.ToDouble( refundAmount),
                                },
                                TransactionConditions = manualKeyEntryParams
                            },
                            PaymentData = new Paymentdata
                            {
                                PaymentType = "Refund"
                            }
                        }
                    }
                };

                log.Debug($"IndependantRefund requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("IndependantRefund response was null");
                    throw new Exception("IndependantRefund failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoCheckLastTransactionStatus(string saleId, string p_serviceId, string messageCategory)
        {
            string lastTransactionResponse = string.Empty;
            try
            {
                log.LogMethodEntry(saleId, p_serviceId, messageCategory);
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageClass = messageClass,
                            MessageCategory = "TransactionStatus",
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        TransactionStatusRequest = new Transactionstatusrequest
                        {
                            ReceiptReprintFlag = true,
                            DocumentQualifier = new string[]
                            {
                                "CashierReceipt",
                                "CustomerReceipt"
                            },
                            MessageReference = new Messagereference
                            {
                                SaleID = saleId,
                                ServiceID = p_serviceId,
                                MessageCategory = messageCategory
                            }
                        }
                    }
                };

                log.Debug($"LastTransactionStatus requestDto={requestDto.ToString()}");
                lastTransactionResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"lastTransactionResponse={lastTransactionResponse}");
                if (string.IsNullOrWhiteSpace(lastTransactionResponse))
                {
                    log.Error("LastTransactionStatus response was null");
                    throw new Exception("LastTransactionStatus failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(lastTransactionResponse);
            return lastTransactionResponse;
        }

        public string DoCreateCardTokenForSubscription(double amount, string shopperRef, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(amount, shopperRef, ccRequestId);
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"recurringProcessingModel=Subscription&shopperReference={shopperRef}&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"recurringProcessingModel=Subscription&shopperReference={shopperRef}";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId, //This must be diff from ccRequestId and unique for each request. Max 10 alphanumeric chars
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData,
                                TokenRequestedType = "Customer"
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = amount,
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Create Card Token For Subscription requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Create Card Token For Subscription response was null");
                    throw new Exception("Create Card Token For Subscription failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoLaterSubscriptionPayment(decimal amount, string shopperRef, string paymentToken, string ccRequestId, int currencyConversionFactor, Dictionary<string, string> esdInfo)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(amount, shopperRef, paymentToken, ccRequestId);
                int subscriptionAmount = GetAmountInMinorUnit(amount, currencyConversionFactor);
                log.Debug($"subscriptionAmount={subscriptionAmount}");

                ApplicationInfo appInfo = GetAppInfoObject();
                MakeSubscriptionPaymentRequestDto subscriptionPaymentRequestDto = new MakeSubscriptionPaymentRequestDto
                {
                    amount = new Amount
                    {
                        value = subscriptionAmount,
                        currency = shopperCurrency,
                    },
                    reference = ccRequestId,
                    paymentMethod = new Paymentmethod
                    {
                        type = "scheme",
                        storedPaymentMethodId = paymentToken
                    },
                    shopperReference = shopperRef,
                    merchantAccount = merchantAccount,
                    shopperInteraction = "ContAuth",
                    recurringProcessingModel = "Subscription",
                    store = storeName,
                    returnUrl = "https://semnox.com", // TBC,
                    applicationInfo = appInfo,
                    additionalData = new Additionaldata
                    {
                        enhancedSchemeData_customerReference = esdInfo["enhancedSchemeData.customerReference"],
                        enhancedSchemeData_destinationPostalCode = esdInfo["enhancedSchemeData.destinationPostalCode"],
                        enhancedSchemeData_orderDate = esdInfo["enhancedSchemeData.orderDate"],
                    }
                };

                log.Debug($"Subscription payment requestDto={subscriptionPaymentRequestDto.ToString()}");
                paymentResponse = MakeSubcriptionPayment(subscriptionPaymentRequestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Subscription payment response was null");
                    throw new Exception("Subscription payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        private ApplicationInfo GetAppInfoObject()
        {
            ApplicationInfo appInfo = null;
            try
            {
                log.LogMethodEntry(applicationInfo);

                appInfo = new ApplicationInfo
                {
                    externalPlatform = new ExternalPlatform
                    {
                        name = applicationInfo["applicationInfo.externalPlatform.name"],
                        version = applicationInfo["applicationInfo.externalPlatform.version"],
                        integrator = applicationInfo["applicationInfo.externalPlatform.integrator"],
                    },
                    merchantApplication = new MerchantApplication
                    {
                        name = applicationInfo["applicationInfo.merchantApplication.name"],
                        version = applicationInfo["applicationInfo.merchantApplication.version"],
                    },
                    merchantDevice = new MerchantDevice
                    {
                        os = applicationInfo["applicationInfo.merchantDevice.os"],
                        osVersion = applicationInfo["applicationInfo.merchantDevice.osVersion"],
                    }
                };

                log.LogVariableState("appInfo", appInfo);

                log.LogMethodExit(appInfo);
                return appInfo;

            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public string DoQRPayment(double orderAmount, double tipAmount, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId, // we need to maintain a sequence for this. This must be diff from ccRequestId and unique for each request. Max 10 alphanumeric chars
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = orderAmount,
                                    TipAmount = tipAmount
                                },
                                TransactionConditions = new Transactionconditions
                                {
                                    //AllowedPaymentBrand = new string[]
                                    //{
                                    //    //"alipay"
                                    //}
                                }
                            }
                        }
                    }
                };

                log.Debug($"QR Payment requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("QR Payment response was null");
                    throw new Exception("QR Payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            return paymentResponse;
        }

        public string DoPartialPayment(double orderAmount, double tipAmount, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {

                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId, // we need to maintain a sequence for this. This must be diff from ccRequestId and unique for each request. Max 10 alphanumeric chars
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleReferenceID = $"{ccRequestId}-1",
                                SaleToAcquirerData = "tenderOption=AllowPartialAuthorisation"
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = orderAmount,
                                    TipAmount = tipAmount
                                },
                            }
                        }
                    }
                };

                log.Debug($"PartialPayment requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("PartialPayment response was null");
                    throw new Exception("PartialPayment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            return paymentResponse;
        }

        public String getUTCDateTime()
        {
            try
            {
                log.LogMethodEntry();
                DateTime time = DateTime.Now.ToUniversalTime();
                string utcTime = time.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                log.LogMethodExit(utcTime);
                return utcTime;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }
        public string getUTCDateTime(string dateTime)
        {
            try
            {
                log.LogMethodEntry(dateTime);
                DateTime dt = DateTimeOffset.Parse(dateTime).UtcDateTime;
                string utcTime = dt.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                log.LogMethodExit(utcTime);
                return utcTime;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            
        }

        public string ExecuteTerminalRequests(string request)
        {
            string responseFromServer = string.Empty;
            try
            {
                log.LogMethodEntry(request);
                string API_URL = tapiUrl;
                log.Debug($"ExecuteTerminalRequests API_URL={API_URL}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";
                myHttpWebRequest.Timeout = adyenTransactionTimeout;
                log.Debug($"Transaction timeout {adyenTransactionTimeout}");
                log.Debug($"ExecuteTerminalRequests(): request json={request}");

                byte[] data = Encoding.UTF8.GetBytes(request);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", apiKey);
                //myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;
                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"ExecuteTerminalRequests(): responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(responseFromServer);
             return responseFromServer;
        }

        private string ExecuteWebApiRequests(string request, string apiUrl)
        {
            string responseFromServer = string.Empty;
            try
            {
                log.LogMethodEntry(request, apiUrl);
                string API_URL = apiUrl;
                log.Debug($"ExecuteWebApiRequests API_URL={API_URL}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";
                myHttpWebRequest.Timeout = adyenTransactionTimeout;
                log.Debug($"Transaction timeout {adyenTransactionTimeout}");
                log.Debug($"ExecuteWebApiRequests(): request json={request}");

                byte[] data = Encoding.UTF8.GetBytes(request);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", apiKey);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"ExecuteWebApiRequests(): responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        public string MakeCaptureRequest(string paymentId, string jsonRequestDto)
        {
            string response = string.Empty;
            try
            {
                log.LogMethodEntry(paymentId, jsonRequestDto);
                string API_URL = $"{webApiUrl}/payments/{paymentId}/captures";
                log.Debug($"MakeCaptureRequest API_URL={API_URL}");
                response = ExecuteWebApiRequests(jsonRequestDto, API_URL);
                log.Debug($"Capture response={response}");
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(response);
            return response;
        }

        public string MakeCancelAuthRequest(string paymentId, string jsonRequestDto)
        {
            string response = string.Empty;
            try
            {
                log.LogMethodEntry(paymentId, jsonRequestDto);
                string API_URL = $"{webApiUrl}/payments/{paymentId}/cancels";
                log.Debug($"MakeCaptureRequest API_URL={API_URL}");
                response = ExecuteWebApiRequests(jsonRequestDto, API_URL);
                log.Debug($"CancelAuth response={response}");
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(response);
            return response;
        }

        public string MakeSubcriptionPayment(string jsonRequestDto)
        {
            string responseFromServer = string.Empty;
            try
            {
                log.LogMethodEntry(jsonRequestDto);
                string API_URL = $"{webApiUrl}/payments";
                log.Debug($"MakeSubcriptionPayment(): API_URL={API_URL}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";

                log.Debug($"MakeSubcriptionPayment(): request json={jsonRequestDto}");

                byte[] data = Encoding.UTF8.GetBytes(jsonRequestDto);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-api-key", apiKey);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"MakeSubcriptionPayment(): responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        private string GetErrorMessage(WebException ex)
        {
            try
            {
                log.LogMethodEntry(ex);
                using (var stream = ex?.Response?.GetResponseStream())
                    if (stream != null)
                        using (var reader = new StreamReader(stream))
                        {
                            string webException = reader.ReadToEnd();
                            log.Error(webException);
                            dynamic errorObj = JsonConvert.DeserializeObject(webException);
                            if (errorObj != null)
                            {
                                //log.LogMethodExit($"{errorObj.errorCode} | {errorObj.message}");
                                return $"{errorObj.errorCode} | {errorObj.message}";
                            }
                            else
                            {
                                //log.LogMethodExit(ex.Message);
                                return ex.Message;
                            }
                        }
                log.LogMethodExit(ex.Message);
                return ex.Message;

            }
            catch (Exception exp)
            {
                log.Error(exp);
                throw;
            }
        }

        public string GetTransactionId(string transactionId)
        {
            string adyenPspReference = string.Empty;
            try
            {
                log.LogMethodEntry(transactionId);
                if (string.IsNullOrWhiteSpace(transactionId))
                {
                    log.Error("TransactionId was null");
                    throw new Exception("TransactionId was null");
                }

                string[] responseArray = transactionId.Split('.');
                if (responseArray.Length > 0)
                {
                    adyenPspReference = responseArray[1];
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(adyenPspReference);
            return adyenPspReference;
        }



        public Dictionary<string, string> ToDictionary(NameValueCollection nvc)
        {
            try
            {
                log.LogMethodEntry(nvc);
                Dictionary<string, string> keyValuePairs = nvc.AllKeys.ToDictionary(k => k, k => nvc[k]);
                //return nvc.AllKeys.ToDictionary(k => k, k => nvc[k]);
                log.LogMethodExit(keyValuePairs);
                return keyValuePairs;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public int GetAmountInMinorUnit(decimal amount, int currencyConversionFactor)
        {
            int amountInMinorUnit;
            try
            {
                log.LogMethodEntry(amount, currencyConversionFactor);
                switch (currencyConversionFactor)
                {
                    case 1:
                        amountInMinorUnit = Convert.ToInt32(amount * 10); break;
                    case 2:
                        amountInMinorUnit = Convert.ToInt32(amount * 100); break;
                    case 3:
                        amountInMinorUnit = Convert.ToInt32(amount * 1000); break;
                    default:
                        amountInMinorUnit = Convert.ToInt32(amount);
                        break;
                }
                log.LogMethodExit(amountInMinorUnit);
                return amountInMinorUnit;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public double GetAmountInMajorUnit(int amount, int currencyConversionFactor)
        {
            double amountInMajorUnit;
            try
            {
                log.LogMethodEntry(amount, currencyConversionFactor);
                switch (currencyConversionFactor)
                {
                    case 1:
                        amountInMajorUnit = amount * 0.1; break;
                    case 2:
                        amountInMajorUnit = amount * 0.01; break;
                    case 3:
                        amountInMajorUnit = amount * 0.001; break;
                    default:
                        amountInMajorUnit = amount; break;
                }
                log.LogMethodExit(amountInMajorUnit);
                return amountInMajorUnit;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public Dictionary<string, string> GetAdditionalResponseData(string AdditionalResponse)
        {
            try
            {
                log.LogMethodEntry(AdditionalResponse);
                if (string.IsNullOrWhiteSpace(AdditionalResponse))
                {
                    log.Error("AdditionalResponse was empty");
                    throw new Exception("Payment processing failed. Additional response not received.");
                }
                var additionalParamsKeyValue = HttpUtility.ParseQueryString(AdditionalResponse);
                log.Debug($"GetAdditionalResponseData(): additionalParamsKeyValue={additionalParamsKeyValue}");
                Dictionary<string, string> additionalResponseParams = ToDictionary(additionalParamsKeyValue);
                log.LogMethodExit(additionalResponseParams);
                return additionalResponseParams;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

        }

        public string GetRefundErrorMessage(RefundResponseDto responseObject, Dictionary<string, string> additionalResponseParams)
        {
            string errorMessage = "Unknown Error";
            try
            {
                log.LogMethodEntry(responseObject, additionalResponseParams);
                if (additionalResponseParams.Any())
                {
                    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | ";
                    if (additionalResponseParams.ContainsKey("message"))
                    {
                        errorMessage += additionalResponseParams["message"] + " | ";
                    }
                    if (additionalResponseParams.ContainsKey("refusalReason"))
                    {
                        errorMessage += additionalResponseParams["refusalReason"] + " | ";
                    }
                    if (additionalResponseParams.ContainsKey("errors"))
                    {
                        errorMessage += additionalResponseParams["errors"];
                    }
                    //if (!string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["errors"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["message"]} | {additionalResponseParams["errors"]} | {additionalResponseParams["refusalReason"]}";
                    //}
                    //else if (string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["errors"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["errors"]}";
                    //}
                    //else if (!string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]) && string.IsNullOrWhiteSpace(additionalResponseParams["errors"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["message"]}";
                    //}
                    //else if (string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]) && string.IsNullOrWhiteSpace(additionalResponseParams["errors"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["refusalReason"]}";
                    //}
                    //else
                    //{
                    //    errorMessage = "Unknown Error";
                    //}
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(errorMessage);
            return errorMessage;
        }

        private string GetTenderOptions(bool isDonationEnabled, bool isPartialPaymentAllowed, bool isTipAllowed)
        {
            string strTenderOptions = string.Empty;
            try
            {
                log.LogMethodEntry(isDonationEnabled, isPartialPaymentAllowed, isTipAllowed);
                if (isDonationEnabled && isPartialPaymentAllowed && isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION},{ENABLE_PARTIAL_PAYMENT},{ENABLE_TIP}";
                }
                else if (isDonationEnabled && !isPartialPaymentAllowed && !isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION}";
                }
                else if (!isDonationEnabled && !isTipAllowed && isPartialPaymentAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_PARTIAL_PAYMENT}";
                }
                else if (!isDonationEnabled && isTipAllowed && !isPartialPaymentAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_TIP}";
                }
                else if (isDonationEnabled && isPartialPaymentAllowed && !isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION},{ENABLE_PARTIAL_PAYMENT}";
                }
                else if (!isDonationEnabled && isPartialPaymentAllowed && isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_PARTIAL_PAYMENT},{ENABLE_TIP}";
                }
                else if (isDonationEnabled && !isPartialPaymentAllowed && isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION},{ENABLE_TIP}";
                }
                else
                {
                    strTenderOptions = string.Empty;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(strTenderOptions);
            return strTenderOptions;
        }

        private Transactionconditions GetManualKeyEntryParams(bool isManualKeyEntryMode)
        {
            Transactionconditions transactionconditions = null;
            try
            {
                log.LogMethodEntry(isManualKeyEntryMode);
                if (isManualKeyEntryMode)
                {
                    transactionconditions = new Transactionconditions
                    {
                        ForceEntryMode = new string[]
                        {
                            "Keyed"
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(transactionconditions);
            return transactionconditions;
        }

        public bool ValidateCCRequestId(string reqCCRequestId, string respCCRequestId)
        {
            try
            {
                log.LogMethodEntry(reqCCRequestId, respCCRequestId);
                if (reqCCRequestId.Equals(respCCRequestId))
                {
                    log.LogMethodExit(true);
                    return true;
                }
                else
                {
                    log.LogMethodExit(false);
                    return false;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }
    }
}
