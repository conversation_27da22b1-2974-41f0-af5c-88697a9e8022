﻿
/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Adyen Payment Configurations
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.190.0     24-Sep-2024         Amrutha      Created
 ********************************************************************************************************/

using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Site;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace Semnox.Parafait.PaymentGateway
{

    public class ParafaitAdyenPaymentConfigurations : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        int adyenTransactionTimeout;
        Dictionary<string, string> adyenPaymentConfigurationMapping = new Dictionary<string, string>
{
    { "MERCHANT_APPLICATION_NAME", "MERCHANT_APPLICATION_NAME" },
    { "CURRENCY_CONVERSION_FACTOR", "CURRENCY_CONVERSION_FACTOR" },
    { "MERCHANT_OS", "MERCHANT_OS" },
    { "MERCHANT_OS_VERSION", "MERCHANT_OS_VERSION" },
    { "POS_PRE_AUTH_ADJUST_API_URL", "POS_PRE_AUTH_ADJUST_API_URL" },
    { "POS_SUPPLIER_NAME", "POS_SUPPLIER_NAME" },
    { "POS_WEB_API_VERSION", "POS_WEB_API_VERSION" },
    { "SERVICE_INTEGRATOR_NAME", "SERVICE_INTEGRATOR_NAME" },
    { "STORE_NAME", "STORE_NAME" }
};

        Dictionary<string, string> unsupportedRefundMappings = new Dictionary<string, string>
{
    { "bankaccept", "bankaccept" },
    { "dankort", "dankort" },
    { "interac", "interac" },
    { "interac_card", "interac_card" }
};
        string configKey = null;

        public ParafaitAdyenPaymentConfigurations(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO , bool isUnattended)
        {
            string posId = executionContext.POSMachineName;

            SiteContainerDTO siteContainerDTO = SiteViewContainerList.GetCurrentSiteContainerDTO(executionContext);

            string posVersion = string.Empty;
            if (siteContainerDTO != null)
            {
                posVersion = siteContainerDTO.Version;
            }

            if (int.TryParse(ConfigurationManager.AppSettings["AdyenTransactionTimeout"], out adyenTransactionTimeout) == false)
            {
                adyenTransactionTimeout = 150000;
            }

            OperatingSystem os = Environment.OSVersion;
            string osVersion = Convert.ToString(os.Version.Major);
            string operationSystem = os.ToString();

            SetConfiguration("CREDIT_CARD_STORE_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_STORE_ID"));
            SetConfiguration("CREDIT_CARD_HOST_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_HOST_URL"));
            SetConfiguration("CREDIT_CARD_TOKEN_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TOKEN_ID"));
            SetConfiguration("CREDIT_CARD_DEVICE_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_DEVICE_URL"));
            SetConfiguration("CREDIT_CARD_TERMINAL_IP_ADDRESS", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_IP_ADDRESS"));
            SetConfiguration("CREDIT_CARD_TERMINAL_PORT_NO", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_PORT_NO"));
            SetConfiguration("ALLOW_PARTIAL_APPROVAL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "ALLOW_PARTIAL_APPROVAL"));
            SetConfiguration("SHOW_TIP_AMOUNT_KEYPAD", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "SHOW_TIP_AMOUNT_KEYPAD"));
            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
            SetConfiguration("ENABLE_AUTO_CREDIT_CARD_AUTHORIZATION", Convert.ToString(paymentModeContainerDTO.EnableAutoCreditcardAuthorization));
            SetConfiguration("CLOUDMODE", paymentModeContainerDTO.EnableCloudModeCommunication ? "Y" : "N");
            SetConfiguration("DONATION_ENABLED", paymentModeContainerDTO.EnableCreditCardDonation ? "Y" : "N");
            SetConfiguration("MIN_PREAUTH", Convert.ToString(paymentModeContainerDTO.PreAuthAmount));
            SetConfiguration("ALLOW_CREDIT_CARD_AUTHORIZATION", Convert.ToString(paymentModeContainerDTO.AllowCreditCardAuthorization));
            SetConfiguration("POS_VERSION", posVersion);
            SetConfiguration("TRANSACTION_TIMEOUT", Convert.ToString(adyenTransactionTimeout));
            SetConfiguration("MERCHANT_OS_VERSION", osVersion);
            SetConfiguration("POS_MACHINE_NAME", posId);
            SetConfiguration("isUnattended", isUnattended? "Y" : "N");
            SetConfiguration("CREDIT_CARD_TOKEN_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TOKEN_ID"));
            SetConfiguration("CREDIT_CARD_TERMINAL_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_ID"));

            LookupsContainerDTO paymentConfigurationList = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "ADYEN_PAYMENT_CONFIGURATION");

            if (paymentConfigurationList.LookupValuesContainerDTOList != null && paymentConfigurationList.LookupValuesContainerDTOList.Count > 0)
            {
                foreach (var lookupValuesContainer in paymentConfigurationList.LookupValuesContainerDTOList)
                {
                    if (adyenPaymentConfigurationMapping.TryGetValue(lookupValuesContainer.LookupValue, out configKey))
                    {
                        SetConfiguration(configKey, lookupValuesContainer.Description);
                    }
                }
            }

            LookupsContainerDTO unsupportedRefRefundList = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "ADYEN_UN_SUPPORTED_REF_REFUND");
            if (unsupportedRefRefundList.LookupValuesContainerDTOList != null && unsupportedRefRefundList.LookupValuesContainerDTOList.Count > 0)
            {
                foreach (LookupValuesContainerDTO lookupValuesContainer in unsupportedRefRefundList.LookupValuesContainerDTOList)
                {
                    if (unsupportedRefundMappings.TryGetValue(lookupValuesContainer.LookupValue, out configKey))
                    {
                        SetConfiguration(configKey, lookupValuesContainer.Description);
                    }

                }
            }
        }
    }
}
