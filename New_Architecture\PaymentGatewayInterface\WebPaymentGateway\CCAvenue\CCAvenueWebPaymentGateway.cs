﻿using CCA.Util;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class CCAvenueWebPaymentGateway : PaymentGateway, IPaymentGateway
    {
        private readonly Semnox.Parafait.logging.Logger log;

        private string SUCCESS_RESPONSE_API_URL;
        private string FAILURE_RESPONSE_API_URL;
         
        private string currencyCode;
        private string siteId;

        private string merchantId;
        private string accessCode;
        private string workingKey;
        private string CCAVENUE_PAYMENT_URL;
        private string CCAVENUE_API_URL;

        
        public override bool IsRefundSupported
        {
            get
            {
                return true;
            }
        }
        public override bool IsStatusCheckSupported
        {
            get
            {
                return true;
            }
        }
        public override bool CanCreateMultipleInstances
        {
            get
            {
                return true;
            }
        }
        public override bool IsCustomerInfoRequired
        {
            get
            {
                return true;
            }
        }

        public CCAvenueWebPaymentGateway(PaymentConfiguration paymentConfiguration, PaymentMessages paymentMessages, Semnox.Parafait.logging.Logger logger)
            : base(paymentConfiguration, paymentMessages)
        {
            log = logger;
            log.LogMethodEntry();

            merchantId = paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID");
            accessCode = paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE");
            workingKey = paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_WORKING_KEY");
            CCAVENUE_PAYMENT_URL = paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL");
            CCAVENUE_API_URL = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL");

            currencyCode = paymentConfiguration.GetConfiguration("CURRENCY_CODE");
            siteId = paymentConfiguration.GetConfiguration("SITE_ID");

            SUCCESS_RESPONSE_API_URL = paymentConfiguration.GetConfiguration("SUCCESS_RESPONSE_API_URL");
            FAILURE_RESPONSE_API_URL = paymentConfiguration.GetConfiguration("FAILURE_RESPONSE_API_URL");

            log.Debug($"Merchant ID: {merchantId}");
            log.Debug($"Access Code: {accessCode}");
            log.Debug($"CCAvenue Payment URL: {CCAVENUE_PAYMENT_URL}");
            log.Debug($"CCAvenue API URL: {CCAVENUE_API_URL}");
            log.Debug($"Currency Code: {currencyCode}");
            log.Debug($"Success Response API URL: {SUCCESS_RESPONSE_API_URL}");
            log.Debug($"Failure Response API URL: {FAILURE_RESPONSE_API_URL}");

            log.LogMethodExit(null);
        }

        public override void ValidateConfiguration()
        {
            log.LogMethodEntry("START - Configuration Validation");
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID")))
            {
                log.Error("CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE")))
            {
                log.Error("CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_WORKING_KEY")))
            {
                log.Error("CCAVENUE_HOSTED_PAYMENT_WORKING_KEY is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CCAVENUE_HOSTED_PAYMENT_WORKING_KEY" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL")))
            {
                log.Error("CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL")))
            {
                log.Error("HOSTED_PAYMENT_GATEWAY_API_URL is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_API_URL" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CURRENCY_CODE")))
            {
                log.Error("CURRENCY_CODE is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CURRENCY_CODE" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ANGULAR_PAYMENT_API")))
            {
                log.Error("ANGULAR_PAYMENT_API is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ANGULAR_PAYMENT_API" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("SUCCESS_RESPONSE_API_URL")))
            {
                log.Error("SUCCESS_RESPONSE_API_URL is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "SUCCESS_RESPONSE_API_URL" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("FAILURE_RESPONSE_API_URL")))
            {
                log.Error("FAILURE_RESPONSE_API_URL is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "FAILURE_RESPONSE_API_URL" + " SiteId: " + siteId);
            }
            log.LogMethodExit("END - Completed configuration validation");
        }

        public async override Task<PaymentSessionDTO> CreatePaymentSessionDTO(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);

            if (paymentRequestDTO == null)
            {
                log.Error("paymentRequestDTO is null.");
                throw new ArgumentNullException("Null argument" + nameof(paymentRequestDTO));
            }

            PaymentSessionDTO paymentSessionDTO = new PaymentSessionDTO
            {
                RequestId = paymentRequestDTO.RequestIdentifier,
                IntRequestId = paymentRequestDTO.IntRequestIdentifier,
                PaymentGatewayName = PaymentGateways.CCAvenueCallbackHostedPayment.ToString()
            };
            paymentSessionDTO.HostedPaymentSessionForm = SubmitFormKeyValueList(SetPostParameters(paymentRequestDTO), CCAVENUE_PAYMENT_URL, "frmPayPost");
            log.LogMethodExit(paymentSessionDTO);
            return await Task.FromResult(paymentSessionDTO);
        }

        private List<KeyValuePair<string, string>> SetPostParameters(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);
            List<KeyValuePair<string, string>> postparamslist = new List<KeyValuePair<string, string>>();
            postparamslist.Clear();
            postparamslist.Add(new KeyValuePair<string, string>("tid", paymentRequestDTO.RequestDate.Ticks.ToString()));
            postparamslist.Add(new KeyValuePair<string, string>("merchant_id", merchantId));
            postparamslist.Add(new KeyValuePair<string, string>("order_id", paymentRequestDTO.IntRequestIdentifier.ToString()));
            postparamslist.Add(new KeyValuePair<string, string>("amount", paymentRequestDTO.Amount.ToString()));
            postparamslist.Add(new KeyValuePair<string, string>("currency", currencyCode));
            postparamslist.Add(new KeyValuePair<string, string>("redirect_url", SUCCESS_RESPONSE_API_URL));
            postparamslist.Add(new KeyValuePair<string, string>("cancel_url", FAILURE_RESPONSE_API_URL));
            //postparamslist.Add(new KeyValuePair<string, string>("merchant_param1", PaymentGateways.CCAvenueHostedPayment.ToString()));
            //postparamslist.Add(new KeyValuePair<string, string>("merchant_param2", siteId));
            postparamslist.Add(new KeyValuePair<string, string>("merchant_param3", paymentRequestDTO.IntRequestIdentifier.ToString()));
            //Customer Identifier - Customer Profile Id
            postparamslist.Add(new KeyValuePair<string, string>("customer_identifier", paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerIdentifier ?? ""));

            //Customer details
            postparamslist.Add(new KeyValuePair<string, string>("billing_name", paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerName ?? "")); //Customer Name
            postparamslist.Add(new KeyValuePair<string, string>("billing_email", paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerEmail ?? "")); //Customer email
            postparamslist.Add(new KeyValuePair<string, string>("billing_tel", paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerPhone ?? "")); //Customer phone number
            //postparamslist.Add(new KeyValuePair<string, string>("billing_address", paymentRequestDTO.PaymentGatewayCustomerDTO?.AddressLine1 ?? "")); //Customer Address
            //postparamslist.Add(new KeyValuePair<string, string>("billing_city", paymentRequestDTO.PaymentGatewayCustomerDTO?.City ?? "")); //Customer City
            //postparamslist.Add(new KeyValuePair<string, string>("billing_state", paymentRequestDTO.PaymentGatewayCustomerDTO?.State ?? "")); //Customer State
            //postparamslist.Add(new KeyValuePair<string, string>("billing_zip", paymentRequestDTO.PaymentGatewayCustomerDTO?.PinCode ?? "")); //Customer Pincode
            //postparamslist.Add(new KeyValuePair<string, string>("billing_country", paymentRequestDTO.PaymentGatewayCustomerDTO?.Country ?? "")); //Customer Country

            //Shipping details
            //postparamslist.Add(new KeyValuePair<string, string>("delivery_name", paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerName ?? "")); //Customer Name
            //postparamslist.Add(new KeyValuePair<string, string>("delivery_tel", paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerPhone ?? "")); //Customer phone number
            //postparamslist.Add(new KeyValuePair<string, string>("delivery_address", paymentRequestDTO.PaymentGatewayCustomerDTO?.AddressLine1 ?? "")); //Customer Address
            //postparamslist.Add(new KeyValuePair<string, string>("delivery_city", paymentRequestDTO.PaymentGatewayCustomerDTO?.City ?? "")); //Customer City
            //postparamslist.Add(new KeyValuePair<string, string>("delivery_state", paymentRequestDTO.PaymentGatewayCustomerDTO?.State ?? "")); //Customer State
            //postparamslist.Add(new KeyValuePair<string, string>("delivery_zip", paymentRequestDTO.PaymentGatewayCustomerDTO?.PinCode ?? "")); //Customer Pincode
            //postparamslist.Add(new KeyValuePair<string, string>("delivery_country", paymentRequestDTO.PaymentGatewayCustomerDTO?.Country ?? "")); //Customer Country

            log.Debug($"Post parameters before encryption: {JsonConvert.SerializeObject(postparamslist)}");

            string HashData = "";
            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                HashData += param.Key + "=" + param.Value.ToString() + "&";
            }
            CCACrypto ccaCrypto = new CCACrypto();
            string strEncRequest = ccaCrypto.Encrypt(HashData, workingKey);

            List<KeyValuePair<string, string>> postparamslistOut = new List<KeyValuePair<string, string>>();
            postparamslistOut.Clear();
            postparamslistOut.Add(new KeyValuePair<string, string>("encRequest", strEncRequest));
            postparamslistOut.Add(new KeyValuePair<string, string>("access_code", accessCode));

            log.LogMethodExit(postparamslist);
            return postparamslistOut;
        }

        /// Generate form
        /// </summary>
        /// <param name="postparamslist">postparamslist</param>
        /// <param name="URL">URL</param>   
        /// <param name="hashedvalue">hashedvalue</param>
        /// <returns> returns string</returns>
        private string SubmitFormKeyValueList(List<KeyValuePair<string, string>> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            log.LogMethodEntry(postparamslist);
            log.Debug($"URL: {URL}, FormName: {FormName}, Method: {submitMethod}, PostParams: {postparamslist}");

            string Method = submitMethod;
            StringBuilder builder = new StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append(string.Format("</head><body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }
            builder.Append("</form>");
            builder.Append("</body></html>");
            log.LogMethodExit();
            return builder.ToString();
        }

        /// <summary>
        /// gets Payment Identifer from response
        /// </summary>
        /// <param name="paymentResponse"></param>
        /// <returns></returns>
        public async override Task<string> GetPaymentIdentifier(string paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);
            string paymentGatewayIdentifier = string.Empty;

            if (string.IsNullOrWhiteSpace(paymentResponse))
            {
                log.Error($"Payment failed. Payment response is null or whitespace");
                throw new PaymentResponseNullException(paymentMessages.GetMessage(5984));
            }

            string[] responseList = paymentResponse.Split('&');
            for (int i = 0; i < responseList.Length; i++)
            {
                string response = responseList[i];
                if (response.Contains("orderNo"))
                {
                    paymentGatewayIdentifier = response.Substring(response.IndexOf("=") + 1);
                }
                else if (response.Contains("order_id"))
                {
                    paymentGatewayIdentifier = response.Substring(response.IndexOf("=") + 1);
                }
            }
            
            log.Debug("OrderNo from response: " + paymentGatewayIdentifier);

            log.LogMethodExit(paymentGatewayIdentifier);
            return await Task.FromResult(paymentGatewayIdentifier);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="paymentResponse"></param>
        /// <returns></returns>
        public async override Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO)
        {
            string paymentResponse = paymentGatewayResponseDTO.GatewayResponse;
            log.LogMethodEntry(paymentResponse);

            string payEncResp = "";
            string respOrderNo = "";

            if (string.IsNullOrWhiteSpace(paymentResponse))
            {
                log.Error($"Payment failed: Payment response is null or whitespace.");
                throw new PaymentResponseNullException(paymentMessages.GetMessage(5984)); // PaymentResponse is Null
            }

            string[] responseList = paymentResponse.Split('&');
            for (int i = 0; i < responseList.Length; i++)
            {
                string response = responseList[i];
                if (response.Contains("encResp"))
                {
                    payEncResp = response.Substring(response.IndexOf("=") + 1);
                }
                else if (response.Contains("orderNo"))
                {
                    respOrderNo = response.Substring(response.IndexOf("=") + 1);
                }
                else if (response.Contains("order_id"))
                {
                    respOrderNo = response.Substring(response.IndexOf("=") + 1);
                }
            }

            log.Debug("Encrypted Response: " + payEncResp);
            log.Debug("OrderNo from response: " + respOrderNo);

            CCACrypto ccaCrypto = new CCACrypto();
            string encResponse = "";
            try
            {
                encResponse = ccaCrypto.Decrypt(payEncResp, workingKey);
                log.Info("Successfully decrypted payment response.");
            }
            catch (Exception ex)
            {
                log.Error("Exception during CCArypto.Decrypt in ProcessPaymentResponse.", ex);
                throw new PaymentResponseProcessingException(paymentMessages.GetMessage(6151), ex); //Failed to decrypt payment response
            }

            log.Debug("Decrypted Response: " + encResponse);

            string[] segments = encResponse.Split('&');

            string statusMessage = "";
            string failureMessage = "";
            string cardName = "";
            string payment_mode = "";
            string acctNo = "";
            string cardType = "";
            string invoiceNo = "";
            string recordNo = "";
            string refNo = "";
            string authCode = "";
            decimal amount = 0;
            string authorize = "";
            string tranCode = "";
            string status = "";
            string textResponse = "";
            string dSIXReturnCode = "";

            foreach (string seg in segments)
            {
                string[] parts = seg.Split('=');
                if (parts.Length > 1)
                {
                    string Key = parts[0].Trim();
                    string Value = parts[1].Trim();
                    if (Key == "failure_message")
                    {
                        failureMessage = Value.ToString();
                    }
                    else if (Key == "status_message")
                    {
                        statusMessage = Value.ToString();
                    }
                    else if (Key == "order_id")
                    {
                        invoiceNo = Value;
                    }
                    else if (Key == "tracking_id")
                    {
                        refNo = Value;
                    }
                    else if (Key == "card_name")
                    {
                        cardName = Value;
                    }
                    else if (Key == "payment_mode")
                    {
                        payment_mode = Value;
                    }
                    else if (Key == "bank_ref_no")
                    {
                        authCode = Value;
                    }
                    else if (Key == "amount")
                    {
                        amount = Convert.ToDecimal(Value);
                        authorize = Value;
                    }
                    else if (Key == "merchant_param3" && string.IsNullOrWhiteSpace(Value) == false)
                    {
                        recordNo = Value;
                    }
                }
            }

            acctNo = "XXXXXXXXXXXXXXXX";
            tranCode = PaymentGatewayTransactionType.SALE.ToString();

            //Check whether response TrxID are matching
            if (invoiceNo != respOrderNo)
            {
                log.Error("Payment Rejected - PaymentId doesn't match with response orderId");
                log.Error($"PaymentId: {invoiceNo} response orderId: {respOrderNo}");
                throw new PaymentResponseProcessingException(paymentMessages.GetMessage(6091)); // Payment Rejected - Payment Guid/Id doesn't match with response orderId!
            }

            //Call Status API to reconfirm payment status
            log.Debug("Calling status api to confirm order status");
            OrderStatusResult orderStatusResult = GetOrderStatus(invoiceNo);

            if (orderStatusResult == null)
            {
                log.Error($"Order status for PaymentId: {invoiceNo} failed.");
                log.Error("OrderStatus result: " + orderStatusResult);
                throw new StatusAPIResponseNullException(paymentMessages.GetMessages(6092, invoiceNo)); //Status API response is null for Payment Guid/Id: &1
            }

            cardType = $"{cardName} {payment_mode}";
            cardType = string.IsNullOrWhiteSpace(cardType) ? orderStatusResult.order_card_name : cardType;

            string OrderStatus = orderStatusResult.order_status;
            log.Debug("OrderStatus: " + OrderStatus);

            PaymentTransactionStatuses mappedPaymentStatus = MapPaymentStatus(OrderStatus, PaymentGatewayTransactionType.SALE);

            if (mappedPaymentStatus == PaymentTransactionStatuses.SUCCESS)
            {
                dSIXReturnCode = "SUCCESS " + OrderStatus;
                textResponse = OrderStatus;
                status = PaymentTransactionStatuses.SUCCESS.ToString();
            }
            else if (mappedPaymentStatus == PaymentTransactionStatuses.PENDING)
            {
                log.Info("Payment status is PENDING");
                dSIXReturnCode = "PENDING " + OrderStatus;
                textResponse = OrderStatus;
                status = PaymentTransactionStatuses.PENDING.ToString();
            }
            else
            {
                tranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                dSIXReturnCode = failureMessage + OrderStatus;
                textResponse = statusMessage + OrderStatus;
                status = PaymentTransactionStatuses.FAILED.ToString();
            }
            PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, cardType, tranCode, refNo, authorize, authorize, DateTime.MinValue,
                                                                            authCode, null, null, null, null, null, null, null, null, null, status, null, null, null, amount);

            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        /// <summary>
        /// Method to get the payment details for the a specific trxId
        /// </summary>
        /// <param name="trxId"></param>
        /// <returns></returns>
        private OrderStatusResult GetOrderStatus(string transactionPaymentId)
        {
            log.LogMethodEntry(transactionPaymentId);

            if (string.IsNullOrWhiteSpace(transactionPaymentId))
            {
                log.Error("transactionPaymentGuid (order_id) is null or empty for GetOrderStatus.");
                throw new ArgumentNullException("Null argument " + nameof(transactionPaymentId));
            }

            CCACrypto ccaCrypto = new CCACrypto();
            string orderStatusQueryJson = "";
            string strEncRequest = "";
            string authQueryUrlParam = "";
            string message = "";
            OrderStatusResponseDTO orderStatusResponseDTO = null;
            OrderStatusResult orderStatusResult = null;

            orderStatusQueryJson = "{ \"order_no\":\"" + transactionPaymentId + "\" }";
            log.Debug("Status API orderStatusQueryJson: " + orderStatusQueryJson);

            strEncRequest = ccaCrypto.Encrypt(orderStatusQueryJson, workingKey);
            log.Debug($"Status API Encrypted Request (strEncRequest): {strEncRequest}");// Sensitive

            authQueryUrlParam = "enc_request=" + strEncRequest + "&access_code=" + accessCode + "&command=orderStatusTracker&request_type=JSON&response_type=JSON";
            log.Debug("Status API authQueryUrlParam: " + authQueryUrlParam);

            try
            {
                message = PostPaymentRequestToGateway(CCAVENUE_API_URL, authQueryUrlParam);
                log.Debug("Initial response from Status API: " + message);
            }
            catch (Exception ex)
            {
                log.Error($"CCAVENUE_API_URL: {CCAVENUE_API_URL}");
                log.Error($"authQueryUrlParam: {authQueryUrlParam}");
                log.Error($"Exception during PostPaymentRequestToGateway. {ex.Message}");
                throw new ResponseNotReceivedException(paymentMessages.GetMessage(6152) + ex.Message); //Response not recieved from Status API
            }

            try
            {
                orderStatusResponseDTO = ExtractResponse<OrderStatusResponseDTO>(message, ccaCrypto);
                orderStatusResult = orderStatusResponseDTO.Order_Status_Result;
            }
            catch (Exception ex)
            {
                log.Error($"Exception during ExtractResponse for order status. {ex.Message}");
                throw new ResponseParsingFailedException(paymentMessages.GetMessage(6153) + ex.Message); //Failed to parse OrderStatusResponsDTO
            }
            log.LogMethodExit(orderStatusResult);
            return orderStatusResult;
        }

        private string PostPaymentRequestToGateway(string queryUrl, string urlParam)
        {
            string message = "";
            try
            {
                StreamWriter myWriter = null;// it will open a http connection with provided url
                WebRequest objRequest = WebRequest.Create(queryUrl);//send data using objxmlhttp object
                objRequest.Method = "POST";
                objRequest.ContentType = "application/x-www-form-urlencoded";//to set content type
                myWriter = new System.IO.StreamWriter(objRequest.GetRequestStream());
                myWriter.Write(urlParam);//send data
                myWriter.Close();//closed the myWriter object

                // Getting Response
                System.Net.HttpWebResponse objResponse = (System.Net.HttpWebResponse)objRequest.GetResponse();//receive the responce from objxmlhttp object 
                using (System.IO.StreamReader sr = new System.IO.StreamReader(objResponse.GetResponseStream()))
                {
                    message = sr.ReadToEnd();
                }
            }
            catch (Exception ex)
            {
                log.Error($"QueryUrl: {queryUrl}");
                log.Error($"UrlParam: {urlParam}");
                log.Error("Exception occured while connection." + ex.Message);
                throw new Exception("Exception occured while connection." + ex.Message);
            }
            return message;

        }

        private NameValueCollection GetResponseMap(string message)
        {
            NameValueCollection Params = new NameValueCollection();
            if (message != null || !"".Equals(message))
            {
                string[] segments = message.Split('&');
                foreach (string seg in segments)
                {
                    string[] parts = seg.Split('=');
                    if (parts.Length > 1)
                    {
                        string Key = parts[0].Trim();
                        string Value = parts[1].Trim();
                        Params.Add(Key, Value);
                    }
                }
            }
            return Params;
        }

        public T ExtractResponse<T>(string response, CCACrypto ccaCrypto)
        {
            NameValueCollection param = GetResponseMap(response);
            string status = "";
            string encResJson = "";
            T deserializedDTO = default(T);

            if (param != null && param.Count == 2)
            {
                for (int i = 0; i < param.Count; i++)
                {
                    if ("status".Equals(param.Keys[i]))
                    {
                        status = param[i];
                    }
                    if ("enc_response".Equals(param.Keys[i]))
                    {
                        encResJson = param[i];
                    }
                }
                if (!string.IsNullOrEmpty(status) && status.Equals("0"))
                {
                    string ResJson = ccaCrypto.Decrypt(encResJson, workingKey);
                    log.Debug("Extracted Response: " + ResJson);

                    T obj = JsonConvert.DeserializeObject<T>(ResJson);
                    deserializedDTO = (T)obj;
                }
                else if (!string.IsNullOrEmpty(status) && status.Equals("1"))
                {
                    log.Error("ExtractResponse | failure response from ccAvenues: " + encResJson + "Status: " + status);
                    throw new Exception("ExtractResponse failed!");
                }
            }

            return deserializedDTO;
        }

        /// <summary>
        /// Refunds the open transaction
        /// </summary>
        /// <param name="refundTransactionPaymentsDTO"></param>
        /// <param name="originalPaymentTransactionDTO"></param>
        /// <param name="progress"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async override Task<PaymentResponseDTO> Refund(RefundRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            log.LogMethodEntry(paymentRequestDTO, progress, cancellationToken);
            CCACrypto ccaCrypto = new CCACrypto();
            CCAvenueRefundRequestDTO refundRequestDTO = null;
            PaymentResponseDTO paymentResponseDTO = null;
            CCAvenueRefundResponseDTO refundResponseDTO = null;
            string strEncRefundReq;
            string refundRequestSerialize;
            string refundQueryUrlParam;
            string refundResponse;

            RefundOrderResult refundOrderResult = null;

            if (paymentRequestDTO == null)
            {
                log.Error("paymentRequestDTO is null");
                throw new RefundRequestNullException(paymentMessages.GetMessage(6095)); // Refund request is null.
            }

            try
            {
                refundRequestDTO = new CCAvenueRefundRequestDTO
                {
                    reference_no = paymentRequestDTO.PaymentResponses.RefNo,
                    refund_amount = string.Format("{0:0.00}", paymentRequestDTO.PaymentResponses.Amount),
                    refund_ref_no = paymentRequestDTO.PaymentResponses.RecordNo
                };

                refundRequestSerialize = JsonConvert.SerializeObject(refundRequestDTO);
                log.Debug("refundRequestDTO: " + refundRequestSerialize);

                strEncRefundReq = ccaCrypto.Encrypt(refundRequestSerialize, workingKey);
                log.Debug("Refund request refundRequestSerialize: " + strEncRefundReq);

                refundQueryUrlParam = "enc_request=" + strEncRefundReq + "&access_code=" + accessCode + "&command=refundOrder&request_type=JSON&response_type=JSON";
                log.Debug("Final Refund request refundQueryUrlParam: " + refundQueryUrlParam);

                try
                {
                    refundResponse = PostPaymentRequestToGateway(CCAVENUE_API_URL, refundQueryUrlParam);
                    log.Debug("Initial response from Refund API: " + refundResponse);
                }
                catch (Exception ex)
                {
                    log.Error($"CCAVENUE_API_URL: {CCAVENUE_API_URL}");
                    log.Error($"refundQueryUrlParam: {refundQueryUrlParam}");
                    log.Error($"Exception during PostPaymentRequestToGateway for refund. {ex.Message}");
                    throw new ResponseNotReceivedException(paymentMessages.GetMessage(6093), ex); // Refund response is null.
                }

                //Extract and decrypt the refund response
                refundResponseDTO = ExtractResponse<CCAvenueRefundResponseDTO>(refundResponse, ccaCrypto);
                refundOrderResult = refundResponseDTO.Refund_Order_Result;

                if (refundOrderResult == null)
                {
                    log.Error("Refund order result is null");
                    throw new RefundResponseNullException(paymentMessages.GetMessage(6093)); // Refund response is null
                }

                string invoiceNo = "";
                string recordNo = "";
                string refNo = "";
                string authCode = "";
                decimal amount = 0;
                string authorize = "";
                string tranCode = "";
                DateTime transactionDatetime;
                string status = "";
                string textResponse = "";
                string dSIXReturnCode = "";
                string acqRefData = "";
                string acctNo = "";

                invoiceNo = paymentRequestDTO.RequestIdentifier;
                log.Debug($"invoiceNo {invoiceNo}");
                tranCode = PaymentGatewayTransactionType.REFUND.ToString();
                log.Debug($"tranCode {tranCode}");
                acctNo = paymentRequestDTO.PaymentResponses.AcctNo;
                log.Debug($"acctNo {acctNo}");
                authorize = paymentRequestDTO.Amount.ToString();
                log.Debug($"authorize {authorize}");
                amount = paymentRequestDTO.Amount;
                log.Debug($"amount {amount}");
                transactionDatetime = paymentRequestDTO.RequestDate;
                log.Debug($"transactionDatetime {transactionDatetime}");
                recordNo = paymentRequestDTO.IntRequestIdentifier.ToString(); //parafait TrxId
                log.Debug($"recordNo {recordNo}");
                refNo = paymentRequestDTO.PaymentResponses.RefNo; //paymentId
                log.Debug($"refNo {refNo}");

                string RefundStatus = Convert.ToString(refundOrderResult.refund_status);
                PaymentTransactionStatuses mappedPaymentStatus = MapPaymentStatus(RefundStatus, PaymentGatewayTransactionType.REFUND);

                if (mappedPaymentStatus == PaymentTransactionStatuses.SUCCESS)
                {
                    log.Debug($"Refund Success, {mappedPaymentStatus}");
                    textResponse = "SUCCESS";
                    dSIXReturnCode = refundOrderResult.error_code;
                    authCode = refundOrderResult.refund_status.ToString();
                    acqRefData = refundOrderResult.reason;
                    status = PaymentTransactionStatuses.SUCCESS.ToString();
                }
                else
                {
                    log.Error("Refund Failed, Reason: " + refundOrderResult?.reason);
                    textResponse = "FALIED";
                    dSIXReturnCode = refundOrderResult?.error_code;
                    authCode = refundOrderResult?.refund_status.ToString();
                    acqRefData = refundOrderResult?.reason;
                    status = PaymentTransactionStatuses.FAILED.ToString();
                }
                paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, null, tranCode, refNo, authorize, authorize, transactionDatetime,
                                                            authCode, null, null, null, null, acqRefData, null, null, null, null, status, null, null, null, amount);
            }
            catch (Exception ex)
            {
                log.Error($"Refun failed. {ex.Message}");
                throw new RefundFailedException(paymentMessages.GetMessage(5172), ex);
            }

            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        public async override Task<PaymentResponseDTO> StatusCheck(StatusCheckRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken,string errorMsg)
        {
            log.LogMethodEntry(paymentRequestDTO, progress, cancellationToken);
            PaymentResponseDTO paymentResponseDTO;
            OrderStatusResult orderStatusResult = null;

            if (paymentRequestDTO == null)
            {
                log.Error("paymentRequestDTO is null for StatusCheck.");
                throw new ArgumentNullException("Null argument" + nameof(paymentRequestDTO));
            }

            try
            {
                if (paymentRequestDTO.IntRequestIdentifier <= 0)
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    throw new TransactionIdNullException(paymentMessages.GetMessage(5944));
                }

                //Call TxSearch API
                orderStatusResult = GetOrderStatus(paymentRequestDTO.IntRequestIdentifier.ToString());

                if (orderStatusResult == null)
                {
                    log.Error($"Order status for payment guid: {paymentRequestDTO.IntRequestIdentifier} failed.");
                    throw new StatusCheckResponseNullException(paymentMessages.GetMessages(6094, paymentRequestDTO.IntRequestIdentifier)); // Status Check Response is null for payment guid: &1
                }
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                string statusMessage = "";
                string failureMessage = "";
                string invoiceNo = "";
                string recordNo = "";
                string refNo = "";
                string authCode = "";
                decimal amount = 0;
                string authorize = "";
                string purchase = "";
                string currency = "";
                string tranCode = "";
                DateTime transactionDatetime;
                string status = "";
                string textResponse = "";
                string dSIXReturnCode = "";
                string captureStatus = "";
                //string acqRefData = "";
                //string responseOrigin = "";
                string acctNo = "";
                string cardType = "";

                string OrderStatus = orderStatusResult.order_status;
                log.Debug("Raw OrderStatus: " + OrderStatus);

                PaymentTransactionStatuses mappedPaymentStatus = MapPaymentStatus(OrderStatus, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of mappedPaymentStatus: " + mappedPaymentStatus.ToString());

                amount = Convert.ToDecimal(orderStatusResult.order_amt);
                invoiceNo = paymentRequestDTO.RequestIdentifier;
                authorize = Convert.ToString(orderStatusResult.order_capt_amt);
                purchase = Convert.ToString(orderStatusResult.order_amt);
                transactionDatetime = paymentRequestDTO.RequestDate;
                acctNo = orderStatusResult.order_bank_ref_no; // here card number is not received
                refNo = orderStatusResult.reference_no;
                recordNo = orderStatusResult.order_no;
                textResponse = orderStatusResult.order_bank_response;
                authCode = Convert.ToString(orderStatusResult.status);
                captureStatus = orderStatusResult.order_status;
                dSIXReturnCode = orderStatusResult.error_desc;
                tranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                cardType = orderStatusResult.order_card_name;
                status = mappedPaymentStatus.ToString(); //Based on payment status job decides whether payment os applied or not

                paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, cardType, tranCode, refNo, purchase, authorize, transactionDatetime, 
                                                            authCode, null, null, null, captureStatus, null, null, null, null, null, status, null, null, null, amount);

                log.Debug("Result paymentTransactionDTO: " + paymentResponseDTO.ToString());
                
            }
            catch (Exception ex)
            {
                log.Error("Payment request DTO: " + paymentRequestDTO.ToString());
                log.Error("Error performing GetPaymentStatus. Error message: " + ex);

                paymentResponseDTO = new PaymentResponseDTO
                {
                    InvoiceNo = paymentRequestDTO.RequestIdentifier,
                    Purchase = "0",
                    Authorize = "0",
                    Amount = 0.0M,
                    TransactionDatetime = paymentRequestDTO.RequestDate,
                    TextResponse = "Error performing GetPaymentStatus!",
                    DSIXReturnCode = ex.Message,
                    Status = PaymentTransactionStatuses.ERROR.ToString(),
                    TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString()
                };
            }

            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        private PaymentTransactionStatuses MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentTransactionStatuses defaultStatus = PaymentTransactionStatuses.PENDING;
            PaymentTransactionStatuses paymentTransactionStatus = defaultStatus;

            if (string.IsNullOrWhiteSpace(rawPaymentGatewayStatus))
            {
                log.Warn($"Raw payment gateway status is null or empty for TrxType: {pgwTrxType}. Defaulting to {defaultStatus}.");
                log.LogMethodExit(defaultStatus);
                return defaultStatus;
            }

            try
            {
                Dictionary<string, PaymentTransactionStatuses> pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "Shipped", PaymentTransactionStatuses.SUCCESS },
                            { "Successful", PaymentTransactionStatuses.SUCCESS },

                            { "Refunded", PaymentTransactionStatuses.FAILED },
                            { "Systemrefund", PaymentTransactionStatuses.FAILED },
                            { "Aborted", PaymentTransactionStatuses.FAILED },
                            { "Auto-Cancelled", PaymentTransactionStatuses.FAILED },
                            { "Reversed", PaymentTransactionStatuses.FAILED }, // two identical transactions for same order number, both were successful at bank'send but we got response for only one of them, then next dayduring reconciliation we mark one of the transaction as auto reversed.
                            { "Cancelled", PaymentTransactionStatuses.FAILED },
                            { "Chargeback", PaymentTransactionStatuses.FAILED },
                            { "Invalid", PaymentTransactionStatuses.FAILED },
                            { "Fraud", PaymentTransactionStatuses.FAILED },
                            { "Unsuccessful", PaymentTransactionStatuses.FAILED },
                            { "Timeout", PaymentTransactionStatuses.FAILED },
                            { "Auto-Refunded", PaymentTransactionStatuses.FAILED },

                            { "Awaited", PaymentTransactionStatuses.PENDING },
                            { "Initiated", PaymentTransactionStatuses.PENDING }
                        };
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "0", PaymentTransactionStatuses.SUCCESS },
                            { "1", PaymentTransactionStatuses.FAILED }
                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        log.Error("Raw Payment Gateway Status: " + rawPaymentGatewayStatus);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentTransactionStatus);
                log.Debug("Value of transformed payment status: " + paymentTransactionStatus.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to "+ defaultStatus.ToString());
                    paymentTransactionStatus = defaultStatus;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to " + defaultStatus.ToString() + ". " + ex);
                paymentTransactionStatus = defaultStatus;
            }

            log.LogMethodExit(paymentTransactionStatus);
            return paymentTransactionStatus;
        }
    }
}
