﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Kount
{
    public class KountRisApiClient
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HttpClient httpClient;
        private readonly string kountMerchantId;
        private readonly string kountApiKey;
        private readonly string kountUrl;
        private readonly string KountConfigKey;
        private readonly string kountVersion = "0720";
        private readonly string siteId = "DEFAULT";
        private readonly string formate = "JSON";
        private readonly string mack = "Y";
        KountRisRequestDTO kountRisRequestDTO = null;


        public KountRisApiClient(ExecutionContext executionContext)
        {
            httpClient = new HttpClient();
            log.LogMethodEntry(executionContext);
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12; // comparable to modern browsers
            kountMerchantId = ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "KOUNT_MERCHANT_ID");
            kountApiKey = ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "KOUNT_API_KEY");
            kountUrl = ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "KOUNT_URL");
            KountConfigKey = ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "KOUNT_CONFIG_KEY");
            log.Debug("KOUNT_MERCHANT_ID" + kountMerchantId);

            httpClient.BaseAddress = new Uri(kountUrl);
            httpClient.DefaultRequestHeaders.Add("X-Kount-Api-Key", kountApiKey);

            log.LogMethodExit(null);
        }

        public async Task<string> MakeKountRisCallAsync(TransactionPaymentsDTO transactionPaymentsDTO, KountConfigDTO kountConfigDTO, string CCDetails = "", string Penc = "")

        {

            try
            {
                kountRisRequestDTO = new KountRisRequestDTO
                {
                    Merc = kountMerchantId,
                    Vers = kountVersion,
                    SiteId = siteId,
                    Mack = mack,
                    Formt = formate,
                };
                var requestData = KountRisCommandHandler.PrepareRequestData(transactionPaymentsDTO, kountConfigDTO, KountConfigKey, kountRisRequestDTO, CCDetails, Penc);

                // Create the HTTP request content
                var requestContent = new FormUrlEncodedContent(requestData);

                // Send the POST request
                var response = await httpClient.PostAsync(kountUrl, requestContent);

                // Check if the request was successful
                if (response.IsSuccessStatusCode)
                {
                    // Read the response content
                    var responseContent = await response.Content.ReadAsStringAsync();

                    KountRisResponseDTO responseDTO = JsonConvert.DeserializeObject<KountRisResponseDTO>(responseContent);
                    log.Info("Kount Ris Response: " + responseContent);
                    if (responseDTO != null && responseDTO.MODE == "Q")
                    {
                        CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                        List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                        searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, transactionPaymentsDTO.TransactionId.ToString()));
                        CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.RefNo = responseDTO?.SCOR ?? null;
                        cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.KOUNT.ToString();
                        cCTransactionsPGWDTO.AuthCode = responseDTO?.OMNISCORE.ToString() ?? "";
                        cCTransactionsPGWDTO.ResponseOrigin = responseDTO?.SESS.ToString() ?? "";
                        cCTransactionsPGWDTO.UserTraceData = responseDTO?.BRND.ToString() ?? "";
                        if (responseDTO.AUTO == "D")
                        {
                            cCTransactionsPGWDTO.DSIXReturnCode = "Payment has been declined by kount";
                            cCTransactionsPGWDTO.TextResponse = TransactionStatus.Decline.ToString();

                        }
                        else if (responseDTO.AUTO == "A")
                        {
                            cCTransactionsPGWDTO.DSIXReturnCode = "Payment has been approved by kount";
                            cCTransactionsPGWDTO.TextResponse = TransactionStatus.Approve.ToString();
                        }
                        else if (responseDTO.AUTO == "R")
                        {
                            cCTransactionsPGWDTO.DSIXReturnCode = "Payment has been approved by kount";
                            cCTransactionsPGWDTO.TextResponse = TransactionStatus.Review.ToString();
                        }
                        else if (responseDTO.AUTO == "E")
                        {
                            cCTransactionsPGWDTO.DSIXReturnCode = "Payment has been declined by kount";
                            cCTransactionsPGWDTO.TextResponse = TransactionStatus.Escalate.ToString();
                        }
                        else
                        {
                            cCTransactionsPGWDTO.DSIXReturnCode = "Payment has been declined by kount";
                            cCTransactionsPGWDTO.TextResponse = TransactionStatus.Decline.ToString();
                        }
                        cCTransactionsPGWDTO.AcqRefData = responseDTO?.TRAN ?? "";
                        cCTransactionsPGWDTO.CardType = responseDTO?.CARDS ?? "";
                        cCTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString();
                        cCTransactionsPGWDTO.Authorize = responseDTO?.KAPT ?? "";
                        cCTransactionsPGWDTO.CaptureStatus = responseDTO?.RULES_TRIGGERED.ToString() ?? "";
                        if (responseDTO.RULE_DESCRIPTION != null && responseDTO.RULE_DESCRIPTION.Count > 0)
                        {
                            cCTransactionsPGWDTO.ProcessData = string.Join(", ", responseDTO.RULE_DESCRIPTION);
                        }
                        else
                        {
                            cCTransactionsPGWDTO.ProcessData = string.Empty;
                        }
                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();
                    }
                    else if (responseDTO != null && responseDTO.MODE == "E")
                    {
                        CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                        List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                        searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, transactionPaymentsDTO.TransactionId.ToString()));
                        CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.KOUNT.ToString();
                        cCTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString();
                        cCTransactionsPGWDTO.CaptureStatus = responseDTO?.ERROR_COUNT.ToString() ?? "";
                        cCTransactionsPGWDTO.DSIXReturnCode = responseDTO?.ERROR_0 ?? "";
                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();
                    }


                    if (!string.IsNullOrEmpty(responseDTO.AUTO))
                    {
                        await UpdateKountRisAsync(responseDTO.SESS, responseDTO.AUTO, responseDTO.TRAN);
                    }

                    return responseContent;

                }
                else
                {
                    log.Debug($"Request failed with status code {response.StatusCode}: {response.ReasonPhrase}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                log.Debug($"An error occurred: {ex.Message}");
                return null;
            }
        }

        public async Task<string> UpdateKountRisAsync(string sess, string auth, string tran)
        {
            try
            {
                var updateRequestData = KountRisCommandHandler.PrepareRequestDataUpdate(sess, auth, tran, kountRisRequestDTO);

                // Create the HTTP request content for the update
                var updateRequestContent = new FormUrlEncodedContent(updateRequestData);

                // Send the POST request for the update
                var updateResponse = await httpClient.PostAsync(kountUrl, updateRequestContent);

                // Check if the update request was successful
                if (updateResponse.IsSuccessStatusCode)
                {
                    // Read the update response content
                    var updateResponseContent = await updateResponse.Content.ReadAsStringAsync();

                    // Log or handle the update response content as needed
                    log.Info("Kount Response has been updated: " + updateResponseContent);

                    return updateResponseContent;
                }
                else
                {
                    // Handle the error or log the update response status code and reason
                    log.Debug($"Update request failed with status code {updateResponse.StatusCode}: {updateResponse.ReasonPhrase}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
                log.Debug($"An error occurred during Kount update: {ex.Message}");
                return null;
            }
        }
    }
}


