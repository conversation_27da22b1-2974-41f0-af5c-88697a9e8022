﻿
/********************************************************************************************
 * Project Name - Payment Gateway
 * Description  - QuikCilver gateway class
 * 
 **************
 **Version Log
 **************
 *Version     Date              Modified By                    Remarks          
 *********************************************************************************************
 *2.152.0     11-March-2024       Amrutha                         Created 
 *******************************************************************************************/
using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Semnox.Parafait.Device.PaymentGateway;
using Semnox.Parafait.Languages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Semnox.Parafait.Device.PaymentGateway
{
    public class QwikCilverPaymentGateway : PaymentGateway
    {
        #region members
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        bool isAuthEnabled;
        bool enableAutoAuthorization;
        IDisplayStatusUI statusDisplayUi;
        public delegate void DisplayWindow();
        string username;
        string password;
        string terminalId;
        string authorize_host_url;
        string redeem_host_url;
        string reverse_host_url;
        string cancel_host_url;
        string token;
        DateTime lastAuthorizationRefreshedTime;
        private int isPrintReceiptEnabled;
        private string balance_inquiry_url;
        #endregion

        #region properties
        public override bool IsTipAdjustmentAllowed
        {
            get { return true; }
        }
        public enum Alignment
        {
            Left,
            Right,
            Center
        }
        enum TransactionType
        {
            TATokenRequest,
            SALE,
            REFUND,
            AUTHORIZATION,
            VOID,
            PREAUTH,
            CAPTURE
        }
        #endregion

        public QwikCilverPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);
            this.showMessageDelegate = showMessageDelegate;
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel   
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12; // comparable to modern browsers
            isAuthEnabled = utilities.getParafaitDefaults("ALLOW_CREDIT_CARD_AUTHORIZATION").Equals("Y");
            enableAutoAuthorization = utilities.getParafaitDefaults("ENABLE_AUTO_CREDITCARD_AUTHORIZATION").Equals("Y");
            username = GetQwikCilverConfigurationLookupValue("QWIKCILVER_USER_NAME");
            password = GetQwikCilverConfigurationLookupValue("QWIKCILVER_USER_PASSWORD");
            terminalId = GetQwikCilverConfigurationLookupValue("QWIKCILVER_TERMINAL_ID");
            authorize_host_url = GetQwikCilverConfigurationLookupValue("QWIKCILVER_AUTHORIZE_HOST_URL");
            redeem_host_url = GetQwikCilverConfigurationLookupValue("QWIKCILVER_REDEEM_HOST_URL");
            reverse_host_url = GetQwikCilverConfigurationLookupValue("QWIKCILVER_REVERSE_HOST_URL");
            cancel_host_url = GetQwikCilverConfigurationLookupValue("QWIKCILVER_CANCEL_HOST_URL");
            balance_inquiry_url = GetQwikCilverConfigurationLookupValue("QWIKCILVER_BALANCE_ENQUIRY_URL");
            log.LogVariableState("isUnattended", isUnattended);
            log.LogVariableState("authorization", isAuthEnabled);
            log.LogVariableState("enableAutoAuthorization", enableAutoAuthorization);
            log.Info("username" + username);
            log.Info("password" + password);
            log.Info("terminalId" + terminalId);
            log.Info("authorize_host_url" + authorize_host_url);
            log.Info("redeem_host_url" + redeem_host_url);
            log.Info("reverse_host_url" + reverse_host_url);
            log.Info("cancel_host_url" + cancel_host_url);
            log.Info("balance_inquiry_url" + balance_inquiry_url);

            PrintReceipt = false;
            if (utilities.getParafaitDefaults("CC_PAYMENT_RECEIPT_PRINT").Equals("N"))//If CC_PAYMENT_RECEIPT_PRINT which comes from POS is set as false then terminal should print, If you need terminal to print the receipt then set CC_PAYMENT_RECEIPT_PRINT value as N
            {
                PrintReceipt = true;
            }
            isPrintReceiptEnabled = PrintReceipt == true ? 1 : 0;

            log.LogMethodExit(null);
        }

        #region methods
        public override void Initialize()
        {
            log.LogMethodEntry();
            try
            {
                token = BuildAuthorizationCode();
                log.Info("token = " + token);
            }
            catch (Exception e)
            {
                log.Error("Initialize method throw error" + e);
                throw e;
            }

            log.LogMethodExit();
        }
        private string GetQwikCilverConfigurationLookupValue(string lookupValue)
        {
            log.LogMethodEntry(lookupValue);
            string description = string.Empty;
            LookupValuesContainerDTO lookupValuesContainerDTO = LookupsContainerList.GetLookupValuesContainerDTO(utilities.ExecutionContext.SiteId, "QWIKCILVER_PAYMENT_CONFIGURATIONS", lookupValue);
            if (lookupValuesContainerDTO != null && !string.IsNullOrWhiteSpace(lookupValuesContainerDTO.Description))
            {
                description = lookupValuesContainerDTO.Description;
            }
            else
            {
                log.Error($"Configuration {lookupValue} is not set.");
                throw new Exception(utilities.MessageUtils.getMessage($"Configuration {lookupValue} is not set."));
            }
            log.LogMethodExit(description);
            return description;
        }
        private string BuildAuthorizationCode()
        {
            log.LogMethodEntry();
            log.Info("BuildAuthorizationCode method Enter");

            QwikCilverCommandHandler quickCilverHandler = new QwikCilverCommandHandler(utilities.ExecutionContext);
            AuthorizeQwikCilverResponseDTO reponseDTO = null;
            string authtoken = string.Empty;

            try
            {

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(authorize_host_url) || string.IsNullOrWhiteSpace(terminalId))
                {
                    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5497)); //QuickCilver Payment gateway setup is incomplete
                }
                AuthorizeQwikCilverRequestDTO authorizeqwikcilverrequest = new AuthorizeQwikCilverRequestDTO
                {
                    UserName = username,
                    Password = password,
                    TerminalId = terminalId

                };
                log.Info("authorizeqwikcilverrequest = " + JsonConvert.SerializeObject(authorizeqwikcilverrequest));

                reponseDTO = quickCilverHandler.GetAuthorizationCode(authorizeqwikcilverrequest, authorize_host_url, DateTime.Now, utilities.ExecutionContext);
                if (reponseDTO == null)
                {
                    log.Error("Something went wrong...No response from the server");
                    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5498));//QuikCilver- Transaction Response was null
                }
                log.Info("reponseDTO = " + JsonConvert.SerializeObject(reponseDTO));
                lastAuthorizationRefreshedTime = DateTime.Now;
                authtoken = reponseDTO.AuthToken;
            }
            catch (Exception e)
            {
                log.Error("QwikCilver token validation failed" + e);
                throw;
            }
            log.Info("BuildAuthorizationCode method Exit");
            log.LogMethodExit();
            return authtoken;
        }
        public override TransactionPaymentsDTO MakePayment(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.Info("Entering MakePayment method");
            statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(1839, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "QwikCilver  Payment Gateway");
            statusDisplayUi.EnableCancelButton(false);
            Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
            thr.Start();
            TransactionType trxType = TransactionType.SALE;
            double amount = transactionPaymentsDTO.Amount;
            decimal transactionAmount = overallTransactionAmount;
            string cardpin = transactionPaymentsDTO.CreditCardAuthorization.Trim();
            log.Info("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));


            QwikCilverCommandHandler commandHandler = new QwikCilverCommandHandler(utilities.ExecutionContext, redeem_host_url, reverse_host_url, cancel_host_url, balance_inquiry_url);
            log.Info("transactionPaymentsDTO.CreditCardNumber = " + transactionPaymentsDTO.CreditCardNumber);

            try
            {
                if (transactionPaymentsDTO != null)
                {
                    if (transactionPaymentsDTO.Amount <= 0)
                    {
                        log.Info("transactionPaymentsDTO.Amount  is less than 0");
                        throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5499));//Amount should be greater than 0
                    }

                    try
                    {
                        //Token generation
                        log.Info("Fetching token- GetAuthorizationCode method initiated");
                        token = GetAuthorizationCode();
                        log.Info("token=" + token);

                        if (string.IsNullOrWhiteSpace(token))
                        {
                            log.Error("QwikCilver token is null");
                            throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5499));//token validation failed

                        }
                    }
                    catch (Exception e)
                    {
                        log.Error("Token generaton failed" + e);
                        throw;
                    }


                    //Check valid card number 
                    try
                    {
                        log.Info("IsCardNumberValid method initiated");
                        bool flag = commandHandler.IsCardNumberValid(transactionPaymentsDTO.CreditCardNumber);
                        log.Info("flag  = " + flag);

                        if (!flag)
                        {
                            log.Error("Card number has special character - " + transactionPaymentsDTO.CreditCardNumber);
                            throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5505));//Invalid card number 
                        }
                    }
                    catch (Exception e)
                    {
                        log.Error("IsCardNumberValid throw error" + e);
                        throw;
                    }

                    double balanceAmount = 0;

                    //Balance Check 
                    try
                    {
                        log.Info("BalanceCheck method initiated");
                        BalanceEnquiryResponseDTO balanceEnquiryResponseDTO = BalanceCheck(transactionPaymentsDTO.CreditCardNumber, token);

                        if (balanceEnquiryResponseDTO == null)
                        {
                            log.Error("BalanceCheck method response is null");
                            throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5501));//No response from the server
                        }
                        if (!isSuccess(balanceEnquiryResponseDTO))
                        {
                            log.Info("Geterrormessage method initiated");
                            string errorMsg = Geterrormessage(balanceEnquiryResponseDTO);
                            log.Error("errorMsg =" + errorMsg);
                            throw new Exception(errorMsg);
                        }

                        if (balanceEnquiryResponseDTO.Cards != null && balanceEnquiryResponseDTO.Cards.Count > 0)
                        {
                            balanceAmount = balanceEnquiryResponseDTO.Cards[0].Balance;
                            log.Info("balanceAmount  = " + balanceAmount);
                        }
                    
                else
                {
                        log.Error("balanceEnquiryResponseDTO.Cards is null or balanceEnquiryResponseDTO.Cards count is not greater than 0");
                        throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6038)); //Balance Check Failed.No response received
                    }

                }
                    catch (Exception e)
                    {
                        log.Error("Balance Check method failed" + e);
                        throw;

                    }
                    try
                    {

                        if (balanceAmount > 0 && balanceAmount >= transactionPaymentsDTO.Amount)
                        {

                            log.LogVariableState("Enters into CreateCCRequestPGW method", 1);
                            CCRequestPGWDTO ccRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, trxType.ToString());
                            log.LogVariableState("SALE: end ccRequestPGWDTO", JsonConvert.SerializeObject(ccRequestPGWDTO));
                            string id = ccRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 6);
                            statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                            log.LogVariableState("id", id);
                            statusDisplayUi.DisplayText(MessageContainerList.GetMessage(utilities.ExecutionContext, 5508)); //Redeeming
                            RedeemQwikCilverResponseDTO response = commandHandler.Makepayment(transactionPaymentsDTO.CreditCardNumber.Trim(), cardpin, amount, token, ccRequestPGWDTO.RequestID.ToString(), DateTime.Now, id, utilities.ExecutionContext, overallTransactionAmount);
                            if (response != null && response.Cards != null && response.Cards.Count > 0)
                            {
                                double balance = response.Cards[0].Balance;
                                log.LogVariableState("balance", balance);
                            }

                            log.Info("response = " + JsonConvert.SerializeObject(response));

                            if (response == null)
                            {
                                log.Error("Makepayment method response is null");
                                throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5500));//Payment Request Failed
                            }
                            if (!isSuccess(response))
                            {
                                log.Info("Geterrormessage method initiated");
                                string errorMsg = Geterrormessage(response);
                                log.Error("errorMsg =" + errorMsg);
                                throw new Exception(errorMsg);
                            }
                            else
                            {
                                //insert to DB 
                                log.Info("Inserting into DB");
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();//ccrequestid
                                cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(response.Cards[0].CardNumber.ToString());

                                cCTransactionsPGWDTO.CardType = response.Cards[0].CardType; // CS GiftCard 
                                cCTransactionsPGWDTO.TextResponse = "Successful";
                                cCTransactionsPGWDTO.TranCode = trxType.ToString();
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.Authorize = amount.ToString();
                                cCTransactionsPGWDTO.RefNo = ccRequestPGWDTO.RequestID.ToString();

                                cCTransactionsPGWDTO.AcqRefData = "|CurrentBatchNumber:" + Convert.ToString(response.CurrentBatchNumber) + "|Cardnumber:" + response.Cards[0].CardNumber.ToString() + "|Amount:" + response.Cards[0].Balance + "|ApprovalCode:" + response.Cards[0].ApprovalCode;

                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                ccTransactionsPGWBL.Save();
                                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.Amount = amount;
                                transactionPaymentsDTO.CreditCardAuthorization = Convert.ToString(ccTransactionsPGWBL.CCTransactionsPGWDTO.Amount);
                                transactionPaymentsDTO.NameOnCreditCard = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                log.Info("Completed");
                                statusDisplayUi.DisplayText(MessageContainerList.GetMessage(utilities.ExecutionContext, 5507, response.Cards[0].Balance)); // your available balance is  &1
                                Thread.Sleep(3000);
                            }
                        }
                        else
                        {
                            log.Error("Card doesn't have sufficient balance");
                            throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5504) + " " + balanceAmount);
                        }

                    }
                    catch (Exception e)
                    {
                        log.Error("Error in makepayment method inside try catch ");
                        log.Error(e);
                        throw;

                    }

                }
                else
                {
                    log.Error("transactionPaymentsDTO is null");
                    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6037));//Transaction failed: Payment details could not be retrieved. Please try again or contact support.
                }
            }
            catch (Exception e)
            {
                log.Error("Error in makepayment method");
                log.Error(e);
                throw;
            }

            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                }
            }

            log.LogMethodExit(transactionPaymentsDTO);
            log.Info("Exit Makepayment method");
            return transactionPaymentsDTO;
        }

        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.Info("Entering RefundAmount method");
            double amount = transactionPaymentsDTO.Amount;
            log.Info("amount = " + amount);
            log.Info("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));

            try
            {
                try
                {
                    //Token generation
                    log.Info("Fetching token- GetAuthorizationCode method initiated");
                    token = GetAuthorizationCode();
                    log.Info("token=" + token);

                    if (string.IsNullOrWhiteSpace(token))
                    {
                        log.Error("QwikCilver token is null");
                        throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5499));//token validation failed

                    }
                }
                catch (Exception e)
                {
                    log.Error("Token generaton failed" + e);
                    throw;
                }

                if (transactionPaymentsDTO.Amount < 0)
                {
                    log.Error("Variable Refund Not Supported");
                    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5506)); //Partial Refund is not allowed.
                }
                if (transactionPaymentsDTO != null)
                {
                    try
                    {
                        CCTransactionsPGWBL ccOrigTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
                        CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = ccOrigTransactionsPGWBL.CCTransactionsPGWDTO;
                        log.Info("ccOrigTransactionsPGWDTO = " + JsonConvert.SerializeObject(ccOrigTransactionsPGWDTO));

                        statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(1839, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "QwikCilver  Payment Gateway");
                        QwikCilverCommandHandler commandHandler = new QwikCilverCommandHandler(utilities.ExecutionContext, redeem_host_url, reverse_host_url, cancel_host_url, balance_inquiry_url);
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.REFUND.ToString());
                        log.Info("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));

                        string id = cCRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 6);
                        log.LogVariableState("id", id);

                        string currencycode = transactionPaymentsDTO.CurrencyCode;
                        string AcqRefData = ccOrigTransactionsPGWDTO.AcqRefData;

                        log.Info("currencycode= " + currencycode);
                        log.Info("AcqRefData= " + AcqRefData);

                        string[] segments = AcqRefData.Split('|');
                        Dictionary<string, string> values = new Dictionary<string, string>();
                        foreach (string segment in segments)
                        {
                            string[] keyValue = segment.Split(':');
                            if (keyValue.Length == 2)
                            {
                                values[keyValue[0]] = keyValue[1];
                            }
                        }

                        string batchNumberValue = values["CurrentBatchNumber"];
                        string cardnumber = values["Cardnumber"];
                        //string balanceValue = values["Balance"];
                        string ApprovalCode = values["ApprovalCode"];

                        log.Info("batchNumberValue= " + batchNumberValue);
                        log.Info("cardnumber= " + cardnumber);
                        log.Info("ApprovalCode= " + ApprovalCode);


                        OriginalTransactionRequestDTO cancelOriginalTransactionRequestDTO = new OriginalTransactionRequestDTO()
                        {

                            OriginalTransactionId = Convert.ToInt64(ccOrigTransactionsPGWDTO.InvoiceNo),
                            OriginalApprovalCode = ApprovalCode,
                            OriginalAmount = amount,
                            OriginalCurrencyCode = currencycode,
                            OriginalBatchNumber = Convert.ToInt32(batchNumberValue),
                            OriginalInvoiceNumber = ccOrigTransactionsPGWDTO.InvoiceNo
                        };

                        log.Info("cancelOriginalTransactionRequestDTO= " + JsonConvert.SerializeObject(cancelOriginalTransactionRequestDTO));
                        List<OriginalTransactionRequestDTO> cancelOriginalTransactionRequestDTOList = new List<OriginalTransactionRequestDTO>();
                        cancelOriginalTransactionRequestDTOList.Add(cancelOriginalTransactionRequestDTO);


                        CancelQwikCilverResponseDTO response = commandHandler.CancelTransaction(cardnumber.Trim(), cCRequestPGWDTO.RequestID.ToString(), token, DateTime.Now, cancelOriginalTransactionRequestDTOList, id, utilities.ExecutionContext);
                        log.Info("response= " + JsonConvert.SerializeObject(response));

                        if (response == null)
                        {
                            log.Error("CancelTransaction response is null");
                            throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5501));//no response from server
                        }

                        if (!isSuccess(response))
                        {
                            log.Info("Geterrormessage method initiated");
                            string errorMsg = Geterrormessage(response);
                            log.Info("errorMsg= " + errorMsg);
                            throw new Exception(errorMsg);
                        }
                        else
                        {
                            //insert to DB 
                            log.Info("Inserting into DB");
                            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();//ccrequestid
                            cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(response.Cards[0].CardNumber.ToString());

                            cCTransactionsPGWDTO.CardType = response.Cards[0].CardType; // CS GiftCard 
                            cCTransactionsPGWDTO.TextResponse = "Successful";
                            cCTransactionsPGWDTO.TranCode = TransactionType.VOID.ToString();
                            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            cCTransactionsPGWDTO.Authorize = amount.ToString();
                            cCTransactionsPGWDTO.RefNo = cCRequestPGWDTO.RequestID.ToString();

                            cCTransactionsPGWDTO.AcqRefData = "|CurrentBatchNumber:" + Convert.ToString(response.CurrentBatchNumber) + "|Cardnumber:" + response.Cards[0].CardNumber.ToString() + "|Amount:" + response.Cards[0].Balance + "|ApprovalCode:" + response.Cards[0].ApprovalCode;

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                            ccTransactionsPGWBL.Save();
                            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                            transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                            transactionPaymentsDTO.Amount = amount;
                            transactionPaymentsDTO.CreditCardAuthorization = Convert.ToString(ccTransactionsPGWBL.CCTransactionsPGWDTO.Amount);
                            transactionPaymentsDTO.NameOnCreditCard = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;

                            log.Info("Completed");
                            statusDisplayUi.DisplayText(MessageContainerList.GetMessage(utilities.ExecutionContext, 5507, response.Cards[0].Balance)); // your available balance is  &1
                            Thread.Sleep(3000);
                        }
                    }
                    catch (Exception ex)
                    {
                        log.Error(ex);
                        statusDisplayUi.DisplayText("Error occured while Refunding the Amount");
                        log.Error("Error occured while Refunding the Amount", ex);
                        log.Fatal("Ends -RefundAmount(transactionPaymentsDTO) method " + ex.ToString());
                        log.LogMethodExit(null, "throwing Exception");
                        throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5502) + " " + ex.Message);//Refund failed 
                    }

                }

                else
                {
                    log.Error("transactionPaymentsDTO is null");
                    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6037));//Transaction failed: Payment details could not be retrieved. Please try again
                }
            }
            catch (Exception e)
            {
                log.Error("Eroor in Refund transaction");
                log.Error(e);
                throw;
            }
            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                }
            }
            log.LogMethodExit(transactionPaymentsDTO);
            log.Info("Exit RefundAmount");
            return transactionPaymentsDTO;

        }
        private bool isSuccess(RedeemQwikCilverResponseDTO response)
        {
            log.LogMethodEntry(response);
            log.Info("isSuccess- Enter");
            log.Info("response.ResponseCode = " + response.ResponseCode);
            if (response.ResponseCode == 0 && response.ErrorCode == null && response.ErrorDescription == null)
            {
                return true;
            }
            log.Info("isSuccess- Exit");
            log.LogMethodExit(response);
            return false;
        }
        private bool isSuccess(BalanceEnquiryResponseDTO response)
        {
            log.LogMethodEntry(response);
            log.Info("isSuccess- Enter");
            log.Info("response.ResponseCode = " + response.ResponseCode);
            if (response.ResponseCode == 0 && response.ErrorCode == null && response.ErrorDescription == null)
            {
                return true;
            }
            log.Info("isSuccess- Exit");
            log.LogMethodExit(response);
            return false;
        }
        private string Geterrormessage(RedeemQwikCilverResponseDTO response)
        {
            log.LogMethodEntry(response);
            log.Info("Geterrormessage method - Enter");
            log.Info("response= " + JsonConvert.SerializeObject(response));
            string message = string.Empty;

            try
            {

                if (response != null && response.Cards != null)
                {
                    message = response.Cards[0].ResponseMessage;
                    log.Info("message= " + message);
                }
                else
                {
                    log.Error("response is null or response.cards is null");
                    message = MessageContainerList.GetMessage(utilities.ExecutionContext, 5500); //transaction failed
                }

            }
            catch (Exception e)
            {
                log.Error("RedeemQwikCilverResponseDTO Geterrormessage throw error" + e);
                throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5500)); //transaction failed
            }

            log.LogMethodExit(response.Cards[0].ResponseCode, response.Cards[0].ResponseMessage);
            log.Info("Exit RedeemQwikCilverResponseDTO Geterrormessage");
            return message;
        }
        private string Geterrormessage(BalanceEnquiryResponseDTO response)
        {
            log.LogMethodEntry(response);
            log.Info("BalanceEnquiryResponseDTO Geterrormessage method - Enter");
            log.Info("response= " + JsonConvert.SerializeObject(response));
            string message = string.Empty;

            try
            {

                if (response != null && response.Cards != null)
                {
                    message = response.Cards[0].ResponseMessage;
                    log.Info("message= " + message);
                }
                else
                {
                    log.Error("response is null or response.cards is null");
                    message = MessageContainerList.GetMessage(utilities.ExecutionContext, 5500); //transaction failed
                }

            }
            catch (Exception e)
            {
                log.Error("RedeemQwikCilverResponseDTO Geterrormessage throw error" + e);
                throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5500)); //transaction failed
            }

            log.LogMethodExit(response.Cards[0].ResponseCode, response.Cards[0].ResponseMessage);
            log.Info("Exit RedeemQwikCilverResponseDTO Geterrormessage");
            return message;
        }
        private bool isSuccess(CancelQwikCilverResponseDTO response)
        {
            log.LogMethodEntry(response);
            log.Info("isSuccess- Enter");
            log.Info("response.ResponseCode = " + response.ResponseCode);
            if (response.ResponseCode == 0 && response.ErrorCode == null && response.ErrorDescription == null)
            {
                return true;
            }
            log.Info("isSuccess- Exit");
            log.LogMethodExit(response);
            return false;
        }
        private string Geterrormessage(CancelQwikCilverResponseDTO response)
        {
            log.LogMethodEntry(response);
            log.Info("Entering CancelQwikCilverResponseDTO Geterrormessage");
            string message = string.Empty;

            try
            {
                if (response != null)
                {
                    message = response.Cards[0].ResponseMessage;
                    log.Info("message= " + message);
                }
                else
                {
                    message = MessageContainerList.GetMessage(utilities.ExecutionContext, 5500); //transaction failed
                }
            }
            catch (Exception)
            {
                log.Error("CancelQwikCilverResponseDTO Geterrormessage throw error");
                throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 5500)); //transaction failed
            }




            log.LogMethodExit(response.Cards[0].ResponseCode, response.Cards[0].ResponseMessage);
            log.Info("Exit CancelQwikCilverResponseDTO Geterrormessage");
            return message;
        }

        public string GetAuthorizationCode()
        {
            log.LogMethodEntry();
            log.Info("GetAuthorizationCode method Enter");
            try
            {
                var isAuthorizationCodeNull = token == null;
                var isRefreshNeeded = lastAuthorizationRefreshedTime.AddHours(8) < DateTime.Now;

                log.Info("isAuthorizationCodeNull= " + isAuthorizationCodeNull);
                log.Info("isRefreshNeeded= " + isRefreshNeeded);

                if (isAuthorizationCodeNull || isRefreshNeeded)
                {
                    token = BuildAuthorizationCode();
                    log.Info("token= " + token);
                }
            }
            catch
            {
                log.Error("Token generation failed");
            }

            log.Info("GetAuthorizationCode method Exit");
            log.LogMethodExit();
            return token;
        }
        private string GetMaskedCardNumber(string cardNumber)
        {
            log.LogMethodEntry(cardNumber);
            log.Info("Entering  GetMaskedCardNumber");
            try
            {
                // TBC assumption => card has 16 digits
                string last4 = 4 > cardNumber.Length ? cardNumber : cardNumber.Substring(Math.Max(0, cardNumber.Length - 4));

                string card = string.Format("************{0}", last4);
                log.Info("card= " + card);
                log.LogMethodExit(card);
                return card;
            }
            catch (Exception ex)
            {
                log.Error("GetMaskedCardNumber" + ex);
                statusDisplayUi.DisplayText(MessageContainerList.GetMessage(utilities.ExecutionContext, ex.Message));
                throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, ex.Message));
            }
        }

        public BalanceEnquiryResponseDTO BalanceCheck(string cardNumber, string token)
        {
            log.LogMethodEntry(cardNumber);
            log.Info("BalanceCheck method - Enter");
            log.Info("cardNumber = " + cardNumber);
            log.Info("token = " + token);
            DateTime dateTime = DateTime.Now;
            BalanceEnquiryResponseDTO balanceEnquiryResponseDTO;



            double balanceAmount = 0;

            try
            {
                QwikCilverCommandHandler commandHandler = new QwikCilverCommandHandler(utilities.ExecutionContext, redeem_host_url, reverse_host_url, cancel_host_url, balance_inquiry_url);
                log.Info("commandHandler.BalanceEnquiry initiated");
                balanceEnquiryResponseDTO = commandHandler.BalanceEnquiry(cardNumber, token, dateTime);

                if (balanceEnquiryResponseDTO == null)
                {
                    log.Error("BalanceCheck method response is null");
                    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6038)); //Balance Check Failed.No response received
                }
                if (!isSuccess(balanceEnquiryResponseDTO))
                {
                    log.Info("Geterrormessage method initiated");
                    string errorMsg = Geterrormessage(balanceEnquiryResponseDTO);
                    log.Error("errorMsg =" + errorMsg);
                    throw new Exception(errorMsg);
                }

                if (balanceEnquiryResponseDTO.Cards != null && balanceEnquiryResponseDTO.Cards.Count > 0)
                {
                    balanceAmount = balanceEnquiryResponseDTO.Cards[0].Balance;
                    log.Info("balanceAmount  = " + balanceAmount);
                }
                else
                {
                    log.Error("balanceEnquiryResponseDTO.Cards is null or balanceEnquiryResponseDTO.Cards count is not greater than 0");
                    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6038)); //Balance Check Failed.No response received
                }

                
            }
            catch (Exception ex)
            {
                log.Error("Eroor in BalanceCheck method" + ex);
                throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, ex.Message));
            }

            log.LogMethodExit();
            return balanceEnquiryResponseDTO;

        }
        #endregion
    }
}

