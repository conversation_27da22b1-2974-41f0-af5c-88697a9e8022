﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class PaymentProgressReport
    {
        string message;
        private bool showCancelButton;

        public PaymentProgressReport(string message, bool showCancelButton)
        {
            this.message = message;
            this.showCancelButton = showCancelButton;
        }

        public string Message
        {
            get
            {
                return message;
            }
        }
        public bool ShowCancelButton
        {
            get
            {
                return showCancelButton;
            }

        }
    }
}
