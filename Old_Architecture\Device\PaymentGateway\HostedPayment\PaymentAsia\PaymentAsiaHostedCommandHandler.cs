﻿using Newtonsoft.Json;
using PaymentAsiaPOC.PaymentAsia;
using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PaymentAsia
{

    public class PaymentAsiaHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private readonly string merchantId;
        private readonly string merchantToken;
        private readonly string searchTransactionUrl;
        private readonly string secretCode;



        public PaymentAsiaHostedCommandHandler(string merchantId, string merchantToken, string secretCode, string searchTransactionUrl)
        {
            this.merchantId = merchantId;
            this.secretCode = secretCode;
            this.searchTransactionUrl = searchTransactionUrl;
            this.merchantToken = merchantToken;
        }



        public string SendApiRequest(string apiUrl, List<KeyValuePair<string, string>> requestBody)
        {

            WebRequestClient webRequestClient = new WebRequestClient(apiUrl, HttpVerb.POST, requestBody);
            webRequestClient.ContentType = "application/x-www-form-urlencoded";
            webRequestClient.IsBasicAuthentication = false;

            string txSearchResponse = webRequestClient.MakeRequest();

            return txSearchResponse;
        }

        /// <summary>
        /// Generates a signature for a given set of fields using SHA-512 hashing.
        /// </summary>
        /// <param name="fields">The dictionary of fields to be included in the signature.</param>
        /// <returns>The generated signature as a lowercase hexadecimal string.</returns>
        public string GenerateSignature(Dictionary<string, string> fields)
        {
            log.LogMethodEntry(fields);

            Dictionary<string, string> sortedFields = fields.OrderBy(kv => kv.Key)
                .ToDictionary(kv => kv.Key, kv => kv.Value);

            string queryString = string.Join("&", sortedFields
                .Select(kv => $"{System.Net.WebUtility.UrlEncode(kv.Key)}={System.Net.WebUtility.UrlEncode(kv.Value)}"));

            string hashData = queryString + secretCode;

            using (SHA512 sha512 = SHA512.Create())
            {
                byte[] data = Encoding.UTF8.GetBytes(hashData);
                byte[] hashBytes = sha512.ComputeHash(data);
                log.LogMethodExit("hash data:" + hashBytes);

                return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            }
        }


        /// <summary>
        /// Searches for transactions based on merchant reference and transaction type.
        /// </summary>
        /// <param name="merchantReference">The merchant reference for the transactions.</param>
        /// <param name="trxType">The type of transaction (e.g., "Sale" or "Refund").</param>
        /// <returns>
        /// Returns a list of transaction responses filtered by the specified merchant reference and transaction type.
        /// </returns>

        public List<PaymentAsiaQueryTransactionResponseDTO> CreateTxSearchWithType(string merchantReference, string trxType)
        {
            log.LogMethodEntry(merchantReference, trxType);

            List<PaymentAsiaQueryTransactionResponseDTO> filteredTransactions = new List<PaymentAsiaQueryTransactionResponseDTO>();
            string signature = null;

            try
            {
                PaymentAsiaQueryTransactionsRequestDTO requestDto = new PaymentAsiaQueryTransactionsRequestDTO
                {
                    merchant_reference = merchantReference,
                };

                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("merchant_reference", requestDto.merchant_reference));
                signature = GenerateSignature(formData.ToDictionary(kv => kv.Key, kv => kv.Value));
                requestDto.sign = signature;
                formData.Add(new KeyValuePair<string, string>("sign", requestDto.sign));

                Dictionary<string, string> formDataDictionary = formData.ToDictionary(kv => kv.Key, kv => kv.Value);

                string jsonResponse = SendApiRequest(searchTransactionUrl, formData);

                List<PaymentAsiaQueryTransactionResponseDTO> transactionDetailList = JsonConvert.DeserializeObject<List<PaymentAsiaQueryTransactionResponseDTO>>(jsonResponse);
                log.Debug("Transaction result: " + transactionDetailList);

                // Filter based on trxType
                if (string.Equals(trxType, "Sale", StringComparison.OrdinalIgnoreCase))
                {
                    filteredTransactions = transactionDetailList
                        .Where(t => t.type.Equals("Sale", StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }
                else if (string.Equals(trxType, "Refund", StringComparison.OrdinalIgnoreCase))
                {
                    filteredTransactions = transactionDetailList
                        .Where(t => t.type.Equals("Refund", StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }

                log.LogVariableState("Value of filteredTransactions: ", filteredTransactions);

                if (filteredTransactions.Count == 0)
                {
                    log.Error("No transactions found for the specified conditions.");
                }
                else
                {
                    log.Debug("Transaction Details for the type " + trxType + " : " + filteredTransactions);
                }

                log.LogMethodExit("transaction Details:" + filteredTransactions);

                return filteredTransactions;
            }
            catch (Exception ex)
            {
                // Log and rethrow generic exception
                log.Error("An error occurred: " + ex.Message);
                throw;
            }

        }


        /// <summary>
        /// Performs a refund or void operation based on the provided parameters.
        /// </summary>
        /// <param name="merchantReference">The merchant reference associated with the transaction.</param>
        /// <param name="apiUrl">The API URL for the refund or void operation.</param>
        /// <param name="isVoid">A flag indicating whether the operation is a void (true) or a refund (false).</param>
        /// <param name="amount">The refund amount (applicable only for refund operations).</param>
        /// <returns>
        /// Returns a response DTO containing the result of the refund or void operation.
        /// </returns>

        public PaymentAsiaVoidRefundResponseDTO PerformRefundOrVoid(string merchantReference, string apiUrl, bool isVoid, string amount = "0")
        {
            log.LogMethodEntry(merchantReference, apiUrl);
            try

            {
                PaymentAsiaVoidRefundRequestDTO requestDto = new PaymentAsiaVoidRefundRequestDTO();
                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                if (isVoid)
                {
                    requestDto = new PaymentAsiaVoidRefundRequestDTO
                    {
                        merchant_reference = merchantReference,
                    };

                    formData.Add(new KeyValuePair<string, string>("merchant_reference", requestDto.merchant_reference));
                }
                else
                {
                    requestDto = new PaymentAsiaVoidRefundRequestDTO
                    {
                        merchant_reference = merchantReference,
                        amount = amount,
                    };

                    formData.Add(new KeyValuePair<string, string>("merchant_reference", requestDto.merchant_reference));
                    formData.Add(new KeyValuePair<string, string>("amount", requestDto.amount));
                }

                Dictionary<string, string> formDataDictionary = formData.ToDictionary(kv => kv.Key, kv => kv.Value);
                string signature = GenerateSignature(formDataDictionary);
                formData.Add(new KeyValuePair<string, string>("sign", signature));

                string requestBodyJson = JsonConvert.SerializeObject(requestDto);

                string jsonResponse = SendApiRequest(apiUrl, formData);
                PaymentAsiaVoidRefundResponseDTO responseDto = JsonConvert.DeserializeObject<PaymentAsiaVoidRefundResponseDTO>(jsonResponse);


                log.LogMethodExit(responseDto);
                return responseDto;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Initiates a void operation for a transaction with the specified merchant reference.
        /// </summary>
        /// <param name="merchantReference">The merchant reference for the transaction.</param>
        /// <param name="apiUrl">The API URL for the void operation.</param>
        /// <returns>
        /// Returns a response DTO containing the result of the void operation.
        /// </returns>
        public PaymentAsiaVoidRefundResponseDTO MakeVoid(string merchantReference, string apiUrl)
        {
            log.LogMethodEntry(merchantReference);
            log.Info("makevoid process started");
            PaymentAsiaVoidRefundResponseDTO voidResponse = PerformRefundOrVoid(merchantReference, apiUrl, true);
            log.LogMethodExit(voidResponse);
            return voidResponse;
        }

        /// <summary>
        /// Initiates a refund operation for a transaction with the specified merchant reference and amount.
        /// </summary>
        /// <param name="merchantReference">The merchant reference for the transaction.</param>
        /// <param name="apiUrl">The API URL for the refund operation.</param>
        /// <param name="amount">The amount to be refunded.</param>
        /// <returns>
        /// Returns a response DTO containing the result of the refund operation.
        /// </returns>
        public PaymentAsiaVoidRefundResponseDTO MakeRefund(string merchantReference, string apiUrl, string amount)
        {
            log.LogMethodEntry(merchantReference);
            log.Info("makerefund process started");
            PaymentAsiaVoidRefundResponseDTO refundResponse = PerformRefundOrVoid(merchantReference, apiUrl, false, amount);
            log.LogMethodExit(refundResponse);
            return refundResponse;
        }

        /// <summary>
        /// Retrieves the status of a refund with the specified refund reference.
        /// </summary>
        /// <param name="refundReference">The refund reference for the transaction.</param>
        /// <param name="apiUrl">The API URL for retrieving the refund status.</param>
        /// <returns>
        /// Returns a response DTO containing the details of the refund status.
        /// </returns>
        public PaymentAsiaVoidRefundResponseDTO GetRefundStatus(string refundReference, string apiUrl)
        {
            log.LogMethodEntry(refundReference);
            string signature = null;

            try
            {
                PaymentAsiaRefundStatusRequestDTO requestDto = new PaymentAsiaRefundStatusRequestDTO
                {
                    refund_reference = refundReference,
                };

                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();
                formData.Add(new KeyValuePair<string, string>("refund_reference", requestDto.refund_reference));
                Dictionary<string, string> formDataDictionary = formData.ToDictionary(kv => kv.Key, kv => kv.Value);
                signature = GenerateSignature(formDataDictionary);
                formData.Add(new KeyValuePair<string, string>("sign", signature));

                string requestBodyJson = JsonConvert.SerializeObject(requestDto);
                string jsonResponse = SendApiRequest(apiUrl, formData);
                PaymentAsiaVoidRefundResponseDTO refundDetails = JsonConvert.DeserializeObject<PaymentAsiaVoidRefundResponseDTO>(jsonResponse);
                log.LogMethodExit("transaction Detail:" + refundDetails);

                return refundDetails;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                throw;
            }

        }

        /// <summary>
        /// Retrieves the settlement status for a specific date and network using the provided API URL.
        /// </summary>
        /// <param name="settlementDate">The date for which settlement status is requested.</param>
        /// <param name="network">The network associated with the settlement.</param>
        /// <param name="apiUrl">The API URL for obtaining settlement details.</param>
        /// <returns>
        /// Returns a response DTO containing the settlement details for the specified date and network.
        /// </returns>
        public PaymentAsiaSettlementDetailsResponseDto GetSettlementStatus(string settlementDate, string network, string apiUrl)
        {
            log.LogMethodEntry(settlementDate);

            string jsonResponse = null;
            PaymentAsiaSettlementDetailsResponseDto responseDto = JsonConvert.DeserializeObject<PaymentAsiaSettlementDetailsResponseDto>(jsonResponse);

            try
            {
                PaymentAsiaSettlementDetailsRequestDTO requestDto = new PaymentAsiaSettlementDetailsRequestDTO
                {
                    settlement_date = settlementDate,
                    network = network,
                };
                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("settlement_date", requestDto.settlement_date));
                formData.Add(new KeyValuePair<string, string>("network", requestDto.network));
                Dictionary<string, string> formDataDictionary = formData.ToDictionary(kv => kv.Key, kv => kv.Value);

                string signature = GenerateSignature(formDataDictionary);

                formData.Add(new KeyValuePair<string, string>("sign", signature));

                string requestBodyJson = JsonConvert.SerializeObject(requestDto);
                jsonResponse = SendApiRequest(apiUrl, formData);
                if (!string.IsNullOrEmpty(jsonResponse))
                {
                    return responseDto;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                throw;
            }
            log.LogMethodExit(responseDto);

            return responseDto;
        }


        public string ErrorForm(Dictionary<string, string> errorParams)
        {
            try
            {
                StringBuilder builder = new StringBuilder();
                builder.Append("<html><head>");
                builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
                builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
                builder.Append("</head><body onload=\"document.PaymentFailureForm.submit()\">");
                builder.Append("<form name=\"PaymentFailureForm\" method=\"GET\" action=\"/account/checkouterror\">");

                foreach (KeyValuePair<string, string> param in errorParams)
                {
                    builder.Append(string.Format("<input type=\"hidden\" name=\"{0}\" value=\"{1}\">", param.Key, param.Value));
                }

                builder.Append("</form>");
                builder.Append("</body></html>");

                log.LogMethodExit();
                return builder.ToString();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public string GetIsoCode(string countryName)
        {
            log.LogMethodEntry("countryName: " + countryName);
            // Dictionary mapping country phone codes to ISO country codes
            Dictionary<string, string> countryToIso = new Dictionary<string, string>
        {
           { "USA", "US" },
            { "AFGHANISTAN", "AF" },
            { "ARGENTINA", "AR" },
            { "AUSTRALIA", "AU" },
            { "AUSTRIA", "AT" },
            { "BAHRAIN", "BH" },
            { "BANGLADESH", "BD" },
            { "BELGIUM", "BE" },
            { "BHUTAN", "BT" },
            { "BRAZIL", "BR" },
            { "CANADA", "CA" },
            { "CHILE", "CL" },
            { "CHINA", "CN" },
            { "COLOMBIA", "CO" },
            { "CROATIA", "HR" },
            { "DENMARK", "DK" },
            { "EGYPT", "EG" },
            { "FINLAND", "FI" },
            { "FRANCE", "FR" },
            { "GERMANY", "DE" },
            { "GREECE", "GR" },
            { "HONG KONG", "HK" },
            { "HUNGARY", "HU" },
            { "ICELAND", "IS" },
            { "INDIA", "IN" },
            { "INDONESIA", "ID" },
            { "IRAN", "IR" },
            { "IRAQ", "IQ" },
            { "IRELAND", "IE" },
            { "ITALY", "IT" },
            { "JAPAN", "JP" },
            { "KUWAIT", "KW" },
            { "MALAYSIA", "MY" },
            { "MALI", "ML" },
            { "MAURITIUS", "MU" },
            { "MEXICO", "MX" },
            { "MYANMAR", "MM" },
            { "NEPAL", "NP" },
            { "NETHERLANDS", "NL" },
            { "NEW ZEALAND", "NZ" },
            { "NORTH KOREA", "KP" },
            { "NORWAY", "NO" },
            { "OMAN", "OM" },
            { "PERU", "PE" },
            { "PHILIPPINES", "PH" },
            { "POLAND", "PL" },
            { "PORTUGAL", "PT" },
            { "QATAR", "QA" },
            { "ROMANIA", "RO" },
            { "RUSSIA", "RU" },
            { "SAUDI ARABIA", "SA" },
            { "SINGAPORE", "SG" },
            { "SOUTH AFRICA", "ZA" },
            { "SOUTH KOREA", "KR" },
            { "SPAIN", "ES" },
            { "SRILANKA", "LK" },
            { "SWEDEN", "SE" },
            { "THAILAND", "TH" },
            { "TURKEY", "TR" },
            { "UKARAIN", "UA" },
            { "UAE", "AE" },
            { "UK", "GB" },
        };
            string isoCode;
            // Try to get the ISO code from the dictionary
            if (countryToIso.TryGetValue(countryName, out isoCode))
            {
                log.LogMethodExit("isoCode: " + isoCode);
                return isoCode;
            }
            else
            {
                log.LogMethodExit("default isoCode: HK");
                return "HK";
            }
        }

    }

}

