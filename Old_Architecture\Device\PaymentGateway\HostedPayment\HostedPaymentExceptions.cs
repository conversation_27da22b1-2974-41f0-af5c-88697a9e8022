﻿using System;
using System.Runtime.Serialization;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Exceptions
{
    [Serializable]
    public class StoreNameEmptyException : Exception
    {

        public StoreNameEmptyException(string message) : base(message)
        {
        }

        public StoreNameEmptyException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected StoreNameEmptyException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }

    [Serializable]
    public class StoreNameMismatchException : Exception
    {

        public StoreNameMismatchException(string message) : base(message)
        {
        }

        public StoreNameMismatchException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected StoreNameMismatchException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}