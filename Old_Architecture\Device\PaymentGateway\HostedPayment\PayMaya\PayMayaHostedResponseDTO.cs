﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PayMaya
{
    public class PayMayaHostedCheckoutResponseDto
    {
        public string checkoutId { get; set; }
        public string redirectUrl { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class PayMayaHostedPaymentResponseDto
    {
        public string id { get; set; }
        public bool isPaid { get; set; }
        public string status { get; set; }
        public decimal amount { get; set; }
        public string currency { get; set; }
        public bool canVoid { get; set; }
        public bool canRefund { get; set; }
        public bool canCapture { get; set; }
        public string createdAt { get; set; }
        public string updatedAt { get; set; }
        public string description { get; set; }
        public string paymentTokenId { get; set; }
        public FundSourceDto fundSource { get; set; }
        public ReceiptDto receipt { get; set; }
        public MetaData metadata { get; set; }
        public string approvalCode { get; set; }
        public string receiptNumber { get; set; }
        public string requestReferenceNumber { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

    public class FundSourceDto
    {
        public string type { get; set; }
        public string id { get; set; }
        public string description { get; set; }
        public FundSourceDetailsDto details { get; set; }
    }

    public class FundSourceDetailsDto
    {
        public string scheme { get; set; }
        public string last4 { get; set; }
        public string first6 { get; set; }
        public string masked { get; set; }
        public string issuer { get; set; }
    }

    public class ReceiptDto
    {
        public string transactionId { get; set; }
        public string receiptNo { get; set; }
        public string approvalCode { get; set; }
    }

    public class PayMayaHostedRefundVoidResponseDto
    {
        public string id { get; set; }
        public string reason { get; set; }
        public decimal amount { get; set; }
        public string currency { get; set; }
        public string status { get; set; }
        public string payment { get; set; }
        public string requestReferenceNumber { get; set; }
        public string refundAt { get; set; }
        public string createdAt { get; set; }
        public string updatedAt { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }

}
