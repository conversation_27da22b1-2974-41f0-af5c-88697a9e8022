﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;


namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Paystack
{
    class PaystackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string SECRET_CODE;
        private string PUBLIC_KEY;

        private string POST_URL;
        private string BASE_URL;
        private string CURRENCY;

        private string trxInitializeUrl;
        private string verifyTrxApiUrl;
        private string refundApiUrl;

        private string paystackWebPostUrl = "/Account/Paystack";
        private int CURRENCY_MINOR_UNIT = 100;

        const string SUCCESS = "success";
        const string FAILED = "failed";
        const string ABANDONED = "abandoned";
        const string REVERSED = "reversed";
        const string PENDING = "pending";
        const string PROCESSING = "processing";
        const string ONGOING = "ongoing";


        PaystackHostedCommandHandler paystackCommandHandler;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Success", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
            { "Abandoned", PaymentStatusType.FAILED },
            { "Reversed", PaymentStatusType.FAILED },
            { "Ongoing", PaymentStatusType.PENDING },
            { "Pending", PaymentStatusType.PENDING },
            { "Processing", PaymentStatusType.PENDING },
            { "Queued", PaymentStatusType.PENDING },
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Success", PaymentStatusType.SUCCESS },
            { "Pending", PaymentStatusType.SUCCESS },
            { "Processing", PaymentStatusType.SUCCESS },
            { "Processed", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
        };

        public PaystackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            Initialize();
            log.LogMethodExit(null);
        }
        public override void Initialize()
        {
            log.LogMethodEntry();

            SECRET_CODE = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            PUBLIC_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");

            POST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");

            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            CURRENCY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");

            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }
            trxInitializeUrl = BASE_URL + $"/transaction/initialize";

            verifyTrxApiUrl = BASE_URL + $"/transaction/verify/";
            refundApiUrl = BASE_URL + $"/refund";

            paystackCommandHandler = new PaystackHostedCommandHandler(SECRET_CODE, BASE_URL, trxInitializeUrl, verifyTrxApiUrl, refundApiUrl);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;



            if (string.IsNullOrWhiteSpace(SECRET_CODE))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }

            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));

            List<LookupValuesDTO> lookupValuesDTOlist = lookupValuesList.GetAllLookupValues(searchParameters);

            hostedGatewayDTO.SuccessURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "SUCCESS_URL").Description.Replace("@gateway", PaymentGateways.PaystackHostedPayment.ToString());

            //FAILED_URL
            hostedGatewayDTO.FailureURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "FAILED_URL").Description.Replace("@gateway", PaymentGateways.PaystackHostedPayment.ToString());

            //CALLBACK_URL
            hostedGatewayDTO.CallBackURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CALLBACK_URL").Description.Replace("@gateway", PaymentGateways.PaystackHostedPayment.ToString());

            //CANCEL_URL
            hostedGatewayDTO.CancelURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), "WEB_SITE_CONFIGURATION", "CANCEL_URL").Description.Replace("@gateway", PaymentGateways.PaystackHostedPayment.ToString());

            if (string.IsNullOrWhiteSpace(hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.FailureURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.CallBackURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/CALLBACK_URL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_URL/FAILED_URL/CANCEL_URL/."));
            }


            log.LogMethodExit();
        }
        private IDictionary<string, string> SetPostParameters(PaystackHostedCommandHandler paystackCommandHandler, CCRequestPGWDTO cCRequestPGWDTO, TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(paystackCommandHandler, transactionPaymentsDTO);

            try
            {
                Dictionary<string, string> postparamslist = new Dictionary<string, string>();
                var metadataObject = new
                {
                    paymentMode_id = transactionPaymentsDTO.PaymentModeId,
                    //customer_name = transactionPaymentsDTO.CreditCardName, // CreditCardName contains customer name
                    //customer_email = transactionPaymentsDTO.NameOnCreditCard, // NameOnCreditCard contains e-mail Id
                    //customer_phonenumber = transactionPaymentsDTO.CardEntitlementType, // CardEntitlementType contains phone number
                    //order_id = transactionPaymentsDTO.TransactionId,
                    //cancel_action = hostedGatewayDTO.FailureURL + $"?reference={transactionPaymentsDTO.TransactionId}",
                    //cancel_action = "https://lostsageshop5.conveyor.cloud/account/PaystackHostedPayment/checkoutSuccess",

                };

                string stringifiedMetadata = JsonConvert.SerializeObject(metadataObject);

                postparamslist.Clear();
                postparamslist.Add("key", PUBLIC_KEY);
                postparamslist.Add("email", transactionPaymentsDTO.NameOnCreditCard);
                postparamslist.Add("ref", transactionPaymentsDTO.TransactionId.ToString());
                postparamslist.Add("amount", (transactionPaymentsDTO.Amount * CURRENCY_MINOR_UNIT).ToString());
                postparamslist.Add("currency", CURRENCY);
                postparamslist.Add("redirectUrl", hostedGatewayDTO.SuccessURL);
                postparamslist.Add("paystackCCRequestGuid", cCRequestPGWDTO.Guid.ToString());

                log.LogMethodExit(postparamslist);

                return postparamslist;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            log.LogMethodEntry(postparamslist, URL, FormName, submitMethod);
            try
            {
                string Method = submitMethod;
                StringBuilder builder = new StringBuilder();
                builder.Clear();
                builder.Append("<html>");
                builder.Append($"<body onload=\"document.{FormName}.submit()\">");
                builder.Append($"<form name=\"{FormName}\" method=\"{Method}\" action=\"{URL}\">");

                foreach (KeyValuePair<string, string> param in postparamslist)
                {
                    builder.AppendFormat("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value);
                }

                builder.Append("</form>");
                builder.Append("</body></html>");
                log.Debug("builder.ToString()" + builder.ToString());
                log.LogMethodExit(builder.ToString());
                return builder.ToString();
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            try
            {
                if (transactionPaymentsDTO.Amount <= 0)
                {
                    log.Error($"Order amount must be greater than zero. Order Amount was {transactionPaymentsDTO.Amount}");
                    throw new Exception("Order amount must be greater than zero");
                }
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;

                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                hostedGatewayDTO.GatewayRequestString = GetSubmitFormKeyValueList(SetPostParameters(paystackCommandHandler, cCRequestPGWDTO, transactionPaymentsDTO), paystackWebPostUrl, "paymentForm");

                log.Debug("Gateway Request string: " + hostedGatewayDTO.GatewayRequestString);
                hostedGatewayDTO.FailureURL = "/account/checkouterror";
                hostedGatewayDTO.SuccessURL = "/account/receipt";
                hostedGatewayDTO.CancelURL = "/account/checkoutstatus";
                LookupsList lookupList = new LookupsList(utilities.ExecutionContext);
                List<KeyValuePair<LookupsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<LookupsDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
                List<LookupsDTO> lookups = lookupList.GetAllLookups(searchParameters, true);
                if (lookups != null && lookups.Any())
                {
                    List<LookupValuesDTO> lookupValuesDTOList = lookups[0].LookupValuesDTOList;
                    LookupValuesDTO temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_FAILURE_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.FailureURL = temp.Description;
                    }

                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_SUCCESS_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.SuccessURL = temp.Description;
                    }

                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_CANCEL_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.CancelURL = temp.Description;
                    }
                    // pending url
                    temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_PENDING_URL"));
                    if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                    {
                        hostedGatewayDTO.PendingURL = temp.Description;
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }


        /// <summary>
        /// Processes the response received from the payment gateway and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the updated payment details and status.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment or updating the payment status.</exception>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            PaystackCallbackResponse PaystackResponse = null;
            bool isStatusUpdated;
            try
            {
                PaystackResponse = JsonConvert.DeserializeObject<PaystackCallbackResponse>(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + PaystackResponse.ToString());


                if (PaystackResponse.reference != null)
                {
                    log.Debug("Transaction id: " + PaystackResponse.reference);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(PaystackResponse.reference);
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                PayStackVerifyTransactionResponseDTO trxSearchResponse = paystackCommandHandler.VerifyPayment(PaystackResponse.reference);

                if (!trxSearchResponse.status)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (trxSearchResponse.data == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (trxSearchResponse.data.authorization == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }


                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(trxSearchResponse.data.amount * 0.01);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = trxSearchResponse.data.currency;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.data.id.ToString();//paystack transaction id
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = trxSearchResponse.data.metadata.paymentMode_id;//paymentmode id
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = trxSearchResponse.data.authorization.authorization_code; //auth_codec
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = trxSearchResponse.data.authorization.last4;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardExpiry = trxSearchResponse.data.authorization.exp_month + "/" + trxSearchResponse.data.authorization.exp_month;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = trxSearchResponse.data.authorization.card_type;
                hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = trxSearchResponse.data.authorization.account_name;


                hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PROCESSING;
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);

                if (!isStatusUpdated)
                {
                    log.Error("ProcessGatewayResponse():Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }

                //check if ccTransactionPGW updated
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;
                if (!String.IsNullOrEmpty(cCRequestsPGWDTO.ReferenceNo))
                {
                    string[] resvalues = cCRequestsPGWDTO.ReferenceNo.ToString().Split('|');
                    foreach (string word in resvalues)
                    {
                        if (word.Contains("PaymentModeId") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(word.Split(':')[1]);
                        }
                        else if (word.Contains("CurrencyCode") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = word.Split(':')[1];
                        }
                    }
                }

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Error("No reference id/Transaction present in PaymentAsia receipt response");
                    cCTransactionsPGWDTOList = null;
                }


                if (cCTransactionsPGWDTOList == null)
                {
                    PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.data.status, PaymentGatewayTransactionType.SALE);
                    log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                    if (trxSearchResponse.status)
                    {

                        if (salePaymentStatus == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Payment status is success");
                            hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;

                            hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                        }
                        else if (salePaymentStatus == PaymentStatusType.PENDING)
                        {
                            log.Debug("Payment status is pending");
                            hostedGatewayDTO.PaymentStatus = PaymentStatusType.PENDING;

                            hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                        }
                        else if (salePaymentStatus == PaymentStatusType.FAILED)
                        {
                            log.Debug("Payment status is failed");
                            hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;

                            hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                        }
                        else
                        {
                            log.Error("Payment status is unknown. Considering status as failed Status: " + salePaymentStatus.ToString());
                            hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                            hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                        }
                    }
                    if (cCTransactionsPGWDTOList == null)
                    {  // update the CCTransactionsPGWDTO
                        log.Debug("No CC Transactions found");
                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                        cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                        cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (hostedGatewayDTO.TransactionPaymentsDTO.Amount).ToString("0.00"));
                        cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                        cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                        cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        cCTransactionsPGWDTO.TextResponse = trxSearchResponse.data.status;
                        cCTransactionsPGWDTO.DSIXReturnCode = trxSearchResponse.message + " | Payment: " + trxSearchResponse.data.status;
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                        cCTransactionsPGWDTO.CardType = trxSearchResponse.data.authorization.card_type;
                        cCTransactionsPGWDTO.AcctNo = trxSearchResponse.data.authorization != null ? "XXXXXXXXXXXX" + trxSearchResponse.data.authorization.last4 : null;
                        cCTransactionsPGWDTO.AuthCode = trxSearchResponse.data.authorization.authorization_code;
                        cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                        cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse);

                        hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                    }
                    else
                    {
                        //if YES
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.ERROR;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                        isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                    }

                    isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                }

                if (!isStatusUpdated)
                {
                    log.Error("Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        private DateTime GetPaymentDate(PayStackVerifyTransactionResponseDTO paystackResponse)
        {
            log.LogMethodEntry(paystackResponse);
            DateTime paymentDate = new DateTime();

            if (paystackResponse.data != null)
            {
                log.Debug("Payment Date from response: " + paystackResponse.data.createdAt);
                if (DateTime.TryParseExact(paystackResponse.data.createdAt, "yyyyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }
        /// <summary>
        /// Retrieves the status of a transaction based on the provided transaction ID.
        /// </summary>
        /// <param name="trxId">The ID of the transaction to retrieve status for.</param>
        /// <returns>
        /// Returns a JSON string containing the status information of the transaction, including success or failure status, transaction amount, reference number, account number, and any relevant messages.
        /// </returns>
        /// <exception cref="Exception">Thrown when there are insufficient parameters passed to the request or when an error occurs during the processing of the transaction.</exception>
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;

            try
            {
                if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                PayStackVerifyTransactionResponseDTO txSearchResponseDTO = paystackCommandHandler.VerifyPayment(trxId);
                if (!txSearchResponseDTO.status)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (txSearchResponseDTO.data == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (txSearchResponseDTO.data.authorization == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                log.Debug($"TxSearch Response for TrxId: {trxId}: " + txSearchResponseDTO);

                if (txSearchResponseDTO != null)
                {


                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.data.amount * 0.01));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.data.amount * 0.01));
                    cCTransactionsPGWDTO.RefNo = txSearchResponseDTO.data.id.ToString(); //paymentId
                    cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                    cCTransactionsPGWDTO.TextResponse = txSearchResponseDTO.data.status;
                    cCTransactionsPGWDTO.DSIXReturnCode = txSearchResponseDTO.message + " | Payment: " + txSearchResponseDTO.data.status;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                    cCTransactionsPGWDTO.CardType = txSearchResponseDTO.data.authorization.card_type;

                    cCTransactionsPGWDTO.AcctNo = txSearchResponseDTO.data.authorization.last4 != null ? "XXXXXXXXXXXX" + txSearchResponseDTO.data.authorization.last4 : null;
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(txSearchResponseDTO);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    if (txSearchResponseDTO.data.status == SUCCESS)
                    {
                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else if (txSearchResponseDTO.data.status == PENDING || txSearchResponseDTO.data.status == PROCESSING || txSearchResponseDTO.data.status == ONGOING)
                    {
                        log.Error("GetTransactionStatus(): Error updating the payment status");

                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (txSearchResponseDTO.data.status));
                        dict.Add("orderId", trxId);
                        //throw new Exception("redirect checkoutmessage");
                    }

                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (txSearchResponseDTO.data.status));
                        dict.Add("orderId", trxId);
                    }

                }
                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// Initiates a refund process for a transaction based on the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details for initiating the refund.</param>
        /// <returns>
        /// Returns the updated TransactionPaymentsDTO after processing the refund.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefund = false;
            refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
            PaymentStatusType refundPaymentStatus;
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    // refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }

                log.Debug("Refund processing started");
                PayStackRefundDetailsDTO refundResponseDTO = null;
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                PayStackRefundRequestDTO requestDto = new PayStackRefundRequestDTO
                {
                    transaction = refundTrxId,
                    currency = CURRENCY,
                };

                log.Debug("Paystack Refund Request has been created, RequestDTO: " + requestDto);

                refundResponseDTO = paystackCommandHandler.CreateRefund(requestDto);
                log.Debug("Paystack Refund Response refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }


                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                ccTransactionsPGWDTO.DSIXReturnCode = refundResponseDTO.message;
                ccTransactionsPGWDTO.RefNo = refundResponseDTO.data.id.ToString(); //paystack paymentId

                if (refundResponseDTO.status)
                {
                    log.Debug("Refund Success for trxId: " + refundTrxId);
                    isRefund = true;
                    ccTransactionsPGWDTO.TextResponse = refundResponseDTO.data.status;
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.data.reason;
                    ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", refundResponseDTO.data.amount * 0.01);
                    ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", refundResponseDTO.data.amount * 0.01);
                    ccTransactionsPGWDTO.AcctNo = refundResponseDTO.data.transaction.authorization.last4;
                    refundPaymentStatus = MapPaymentStatus(refundResponseDTO.data.status, PaymentGatewayTransactionType.REFUND);
                    log.Debug("Value of txSearchPaymentStatus: " + refundPaymentStatus.ToString());
                    ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();


                }
                else
                {
                    //refund failed
                    isRefund = false;
                    string errorMessage = refundResponseDTO.message;
                    log.Error($"Refund Failed. Error Message received: {errorMessage}");
                    ccTransactionsPGWDTO.TextResponse = "FAILED";
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.message;
                    ccTransactionsPGWDTO.PaymentStatus = PaymentStatusType.FAILED.ToString();
                  
                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }


            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PayStackVerifyTransactionResponseDTO orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    //throw new Exception("Insufficient Params passed to the request");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = paystackCommandHandler.VerifyPayment(trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (!orderStatusResult.status)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (orderStatusResult.data == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                if (orderStatusResult.data.authorization == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                log.Debug($"TxSearch Response for TrxId: {orderStatusResult.data.reference}: " + orderStatusResult);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(orderStatusResult.data.status, PaymentGatewayTransactionType.SALE);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());


                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO != null ? cCRequestsPGWDTO.RequestID.ToString() : trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (orderStatusResult.data.amount * 0.01));
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (orderStatusResult.data.amount * 0.01));
                cCTransactionsPGWDTO.RefNo = orderStatusResult.data.id.ToString();
                cCTransactionsPGWDTO.RecordNo = orderStatusResult.data.reference;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.data.status;
                cCTransactionsPGWDTO.DSIXReturnCode = orderStatusResult.message + " | Payment: " + orderStatusResult.data.status;
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.CardType = orderStatusResult.data.authorization.card_type;
                cCTransactionsPGWDTO.AcctNo = orderStatusResult.data.authorization.last4; //Based on payment status job decides whether payment os applied or not

                cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();

            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }
    }
}
