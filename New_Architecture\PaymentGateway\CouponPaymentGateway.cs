﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.ViewContainer;

namespace Semnox.Parafait.PaymentGateway
{
    public class CouponPaymentGateway : PaymentGateway
    {

        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public override bool IsPrintLastTransactionSupported => base.IsPrintLastTransactionSupported;

        public override bool IsPrinterRequired => base.IsPrinterRequired;

        public override bool IsPartiallyApproved => base.IsPartiallyApproved;

        public override bool IsTipAdjustmentAllowed => base.IsTipAdjustmentAllowed;

        public override void BeginOrder()
        {
            base.BeginOrder();
        }

        public CouponPaymentGateway(Core.Utilities.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken) : base(executionContext, isUnattended, cancellationToken)
        {
        }

        public override Task<PaymentTransactionDTO> CheckLastTransactionStatus(TransactionPaymentDTO transactionPaymentsDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            return base.CheckLastTransactionStatus(transactionPaymentsDTO, progress, cancellationToken);
        }

        public override void CleanUp()
        {
            base.CleanUp();
        }

        public override void EndOrder()
        {
            base.EndOrder();
        }

        public override bool Equals(object obj)
        {
            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public override void Initialize(int paymentModeId)
        {
            base.Initialize(paymentModeId);
        }

        public override async Task<PaymentTransactionDTO> MakePayment(TransactionPaymentDTO transactionPaymentDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken,TransactionDTO transactionDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO, paymentTransactionDTOList);
            log.LogMethodExit();
            return  await base.MakePayment(transactionPaymentDTO, paymentTransactionDTOList, progress, cancellationToken, transactionDTO);
        }
        
        public override Task<PaymentTransactionDTO> MakePaymentForRecurringBilling(TransactionPaymentDTO transactionPaymentsDTO)
        {
            return base.MakePaymentForRecurringBilling(transactionPaymentsDTO);
        }

        public override Task<PaymentTransactionDTO> PayTip(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO paymentTransactionDTO,List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            return base.PayTip(transactionPaymentsDTO, paymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken);
        }

        public override Task<PaymentTransactionDTO> PerformSettlement(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO paymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken, bool IsForcedSettlement = false)
        {
            return base.PerformSettlement(transactionPaymentsDTO, paymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken, IsForcedSettlement);
        }

        public override void PrintCCReceipt(List<TransactionPaymentDTO> transactionPaymentDTOList)
        {
            base.PrintCCReceipt(transactionPaymentDTOList);
        }

        public override void PrintLastTransaction()
        {
            base.PrintLastTransaction();
        }

        public override async Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO refundTransactionPaymentsDTO, PaymentTransactionDTO originalPaymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOLis, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            log.LogMethodEntry(refundTransactionPaymentsDTO, originalPaymentTransactionDTO);
            log.LogMethodExit(originalPaymentTransactionDTO);
            return originalPaymentTransactionDTO;
        }
      
        public override Task<PaymentTransactionDTO> SendLastTransactionStatusCheckRequestAsync(TransactionPaymentDTO transactionPaymentsDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            return base.SendLastTransactionStatusCheckRequestAsync(transactionPaymentsDTO, paymentTransactionDTOList, progress, cancellationToken);
        }

        public override string ToString()
        {
            return base.ToString();
        }

        public override KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<ValidationError>> ValidateLastTransactionStatus(TransactionPaymentDTO transactionPaymentsDTO)
        {
            return base.ValidateLastTransactionStatus(transactionPaymentsDTO);
        }

        protected override string GetReceiptText(TransactionPaymentDTO trxPaymentsDTO, PaymentTransactionDTO ccTransactionsPGWDTO, bool IsMerchantCopy)
        {
            return base.GetReceiptText(trxPaymentsDTO, ccTransactionsPGWDTO, IsMerchantCopy);
        }

        protected override void PrintCreditCardReceipt(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO ccTransactionsPGWDTO)
        {
            base.PrintCreditCardReceipt(transactionPaymentsDTO, ccTransactionsPGWDTO);
        }
        public async override Task<List<TransactionPaymentDTO>> ValidatePayments(TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodExit(transactionPaymentDTO);
            return await base.ValidatePayments(transactionPaymentDTO);
            //string paymentIdentifier = transactionPaymentDTO.CouponNumber;
            //TransactionPaymentDTO result = new TransactionPaymentDTO();
            //IPaymentModeUseCases paymentModeUseCases = PaymentModeUseCaseFactory.GetPaymentModeUseCases(ExecutionContext);

            //DateTime startDate =  SiteDateTime.GetSiteDateTime(ExecutionContext);
            //DateTime expiryDate = SiteDateTime.GetSiteDateTime(ExecutionContext);

            //List<PaymentCouponDTO> paymentCouponDTOList = await paymentModeUseCases.GetPaymentCoupons(paymentModeId: transactionPaymentDTO.PaymentModeId,
            //    couponNumber: transactionPaymentDTO.CouponNumber, buildChildRecords : true, startDateLessThan: startDate, expiryDateGreaterThan: expiryDate);

            //string errorMessage = MessageViewContainerList.GetMessage(ExecutionContext, 5060);
            ////Invalid or used Coupon Number

            //if (paymentCouponDTOList != null && paymentCouponDTOList.Any())
            //{
            //    if (paymentCouponDTOList[0].Count <= paymentCouponDTOList[0].UsedCount)
            //    {
            //        log.Error(errorMessage);
            //        throw new ValidationException(errorMessage);
            //    }

            //    List<PaymentCouponsUsedDTO> discountCouponsUsedDTOList = paymentCouponDTOList[0].PaymentCouponsUsedDTOList;
            //    if (discountCouponsUsedDTOList != null)
            //    {
            //        if (paymentCouponDTOList[0].Count > 0 && paymentCouponDTOList[0].Count <= discountCouponsUsedDTOList.Count)
            //        {
            //            log.Error(errorMessage);
            //            throw new ValidationException(errorMessage);
            //        }
            //    }
            //    result.CouponSetId = paymentCouponDTOList[0].CouponSetId;
            //    result.CouponValue = paymentCouponDTOList[0].CouponValue;
            //    result.CouponNumber = paymentIdentifier;
            //}
            //else
            //{
            //    throw new ValidationException(errorMessage);
            //}

            //log.LogMethodExit(result);
            //return new List<TransactionPaymentDTO>() { result };
        }

    }
}
