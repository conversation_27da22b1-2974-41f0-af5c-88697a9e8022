﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
    class RedeemQwikCilverResponseDTO
    {
        private RedeemQwikCilverRequestDTO redeembalancedto;
        private ReverseQwikCilverResponseDTO reverseQwikCilver;

        [JsonConstructor]
        public RedeemQwikCilverResponseDTO() { }
        public RedeemQwikCilverResponseDTO(ReverseQwikCilverResponseDTO reverseQwikCilver)
        {
            this.TransactionId = reverseQwikCilver.TransactionId;
            this.TransactionTypeId = reverseQwikCilver.TransactionTypeId;
            this.TotalAmount = reverseQwikCilver.TotalAmount;
            this.ApprovalCode = reverseQwikCilver.ApprovalCode;
            this.ResponseCode = reverseQwikCilver.ResponseCode;
            this.ResponseMessage = reverseQwikCilver.ResponseMessage;
            this.ErrorCode = reverseQwikCilver.ErrorCode;
            this.ErrorDescription = reverseQwikCilver.ErrorDescription;
            this.InputType = reverseQwikCilver.InputType;

            this.Cards = Cards;

            // this.reverseQwikCilver = reverseQwikCilver;
        }

        public long TransactionId { get; set; }
        public int CurrentBatchNumber { get; set; }
        public int TransactionTypeId { get; set; }
        public double TotalAmount { get; set; }
        public string Notes { get; set; }
        public string ApprovalCode { get; set; }
        public int ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorDescription { get; set; }
        public char InputType { get; set; }
        public int TotalCards { get; set; }
        public int NumberOfCards { get; set; }
        public List<CardsResponseDTO> Cards { get; set; }
        public string BusinessReferenceNumber { get; set; }
        public string IdempotencyKey { get; set; }
        public object GeneralLedger { get; set; }
        public string CostCentre { get; set; }
        public int ExecutionMode { get; set; }
    }
}
