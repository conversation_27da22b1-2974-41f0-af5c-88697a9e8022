/********************************************************************************************
 * Project Name - CommnonAPI
 * Description  - Transaction use case controller to apply Web Payment for transaction.
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/

using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Semnox.Core.Utilities;
using Semnox.Core.GenericUtilities;
using System.Threading.Tasks;
using Semnox.CommonAPI.Helpers;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.WebPayments;
using Semnox.Parafait.Site;
using System.IO;
using System.Web;
using Semnox.Parafait.PaymentGateway;
using System.Collections.Generic;
using Newtonsoft.Json;
using Semnox.Parafait.ViewContainer;
using Semnox.Parafait.PaymentGatewayInterface;

namespace Semnox.CommonAPI.Controllers.WebPayment
{
    public class HostedPaymentResponseController : ApiController
    {
        private Semnox.Parafait.logging.Logger log;

        /// <summary>
        /// Start Hosted Payment
        /// </summary>
        [HttpPost]
        [Route("api/WebPayment/HostedPaymentResponse/{PaymentGateway}/{Caller}")]
        [AllowAnonymous]
        public async Task<HttpResponseMessage> Post([FromUri] String PaymentGateway, [FromUri] String Caller, [FromBody] dynamic gatewayResponse)
        {
            ExecutionContext executionContext = null;
            HttpResponseMessage response = null;
            HostedPaymentResponseDTO hostedPaymentResponseDTO = null;
            try
            {
                //System User Execution Builder
                log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                int siteId = SiteContainerList.GetMasterSiteId();
                if (PaymentGateway.Contains("-"))
                {
                    log.Debug("Decrypting payment gateway " + PaymentGateway);
                    string[] tempArray = PaymentGateway.Split(new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries);
                    if (tempArray != null && tempArray.Length == 2)
                    {
                        log.Debug("Site is part of the response: " + PaymentGateway);
                        int.TryParse(tempArray[tempArray.Length - 1], out siteId);
                        //Don't overwrite the gateway, it will fail in processing
                        //PaymentGateway = tempArray[0];
                    }
                }

                log.Debug("SiteId: " + siteId + ", PaymentGateway: " + PaymentGateway);

                executionContext = new ExecutionContext("", siteId, -1, -1, SiteContainerList.IsCorporate(), -1, -1);
                log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                log.LogMethodEntry(PaymentGateway, gatewayResponse);

                string requestBody = "";
                HttpRequestMessage request = this.Request;
                log.Debug("Request URL " + request.RequestUri.ToString());
                //Stream receiveStream = await request.Content.ReadAsStreamAsync();
                StreamReader readStream = new StreamReader(HttpContext.Current.Request.InputStream);
                readStream.BaseStream.Position = 0;
                requestBody = readStream.ReadToEnd();
                log.Debug("Request Body " + requestBody);

                if (string.IsNullOrWhiteSpace(requestBody))
                {
                    log.Error("Gateway response body is empty");
                    throw new GatewayResponseBodyEmptyException(MessageViewContainerList.GetMessage(executionContext, 6089)); // Gateway response body is empty
                }

                String requestIdentifier = Guid.NewGuid().ToString();
                IWebPaymentsUseCases webPaymentUseCases = WebPaymentsUseCaseFactory.GetWebPaymentsUseCases(executionContext, requestIdentifier);
                hostedPaymentResponseDTO = await webPaymentUseCases.ProcessGatewayResponse(PaymentGateway, requestBody, Caller);
            }
            catch (Exception ex)
            {
                string customException = GenericExceptionMessage.GetValidCustomExeptionMessage(ex, executionContext);
                log.Error(customException);
                hostedPaymentResponseDTO = new HostedPaymentResponseDTO();
                // Nitin to do: build a default message
                hostedPaymentResponseDTO.RedirectURL = "";
            }

            // Check if additional response data is present - if so, return it
            if (hostedPaymentResponseDTO.AdditionalResponseDTO != null)
            {
                log.Debug("AdditionalResponseDTO found - returning additional response data");
                response = Request.CreateResponse(HttpStatusCode.OK, hostedPaymentResponseDTO.AdditionalResponseDTO);
            }
            // Otherwise, handle redirect logic
            else if (hostedPaymentResponseDTO.RedirectResponseToWebsite)
            {
                log.Debug("This is a hosted gateway, creating a redirect response");
                response = Request.CreateResponse(HttpStatusCode.Redirect);
                log.Debug("RedirectURL: " + hostedPaymentResponseDTO.RedirectURL);
                response.Headers.Add("Location", hostedPaymentResponseDTO.RedirectURL);
            }
            else
            {
                log.Debug("This is not a hosted gateway. Send the response back as 200 status code");
                response = Request.CreateResponse(HttpStatusCode.OK, new { data = hostedPaymentResponseDTO.RedirectURL });
            }
            return response;
        }

        [HttpGet]
        [Route("api/WebPayment/HostedPaymentResponse/{PaymentGateway}/{Caller}")]
        [AllowAnonymous]
        public async Task<HttpResponseMessage> Get([FromUri] String PaymentGateway, [FromUri] String Caller)
        {
            ExecutionContext executionContext = null;
            HttpResponseMessage response = null;
            HostedPaymentResponseDTO hostedPaymentResponseDTO = null;
            try
            {
                //System User Execution Builder
                log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                int siteId = SiteContainerList.GetMasterSiteId();
                if (PaymentGateway.Contains("-"))
                {
                    log.Debug("Decrypting payment gateway " + PaymentGateway);
                    string[] tempArray = PaymentGateway.Split(new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries);
                    if (tempArray != null && tempArray.Length == 2)
                    {
                        log.Debug("Site is part of the response: " + PaymentGateway);
                        int.TryParse(tempArray[tempArray.Length - 1], out siteId);
                        //Don't overwrite the gateway, it will fail in processing
                        //PaymentGateway = tempArray[0];
                    }
                }

                log.Debug("SiteId: " + siteId + ", PaymentGateway: " + PaymentGateway);


                executionContext = new ExecutionContext("", siteId, -1, -1, SiteContainerList.IsCorporate(), -1, -1);
                log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                log.LogMethodEntry(PaymentGateway);

                string requestBody = "";
                HttpRequestMessage request = this.Request;
                log.Debug("Request URL " + request.RequestUri.ToString());
                var queryParams = Request.GetQueryNameValuePairs(); // queryParams Format: {Key: client_reference_id, Value: 18810}

                Dictionary<string, string> queryParamsDict = new Dictionary<string, string>();
                foreach (KeyValuePair<string, string> item in queryParams)
                {
                    queryParamsDict.Add(item.Key, item.Value);
                }
                requestBody = JsonConvert.SerializeObject(queryParamsDict); // format: {client_reference_id: 18810}
                
                log.Debug("Request Body " + requestBody);

                if (string.IsNullOrWhiteSpace(requestBody))
                {
                    log.Error("Gateway response body is empty");
                    throw new GatewayResponseEmptyException(MessageViewContainerList.GetMessage(executionContext, 6090)); // Gateway response is empty
                }

                String requestIdentifier = Guid.NewGuid().ToString();
                IWebPaymentsUseCases webPaymentUseCases = WebPaymentsUseCaseFactory.GetWebPaymentsUseCases(executionContext, requestIdentifier);
                hostedPaymentResponseDTO = await webPaymentUseCases.ProcessGatewayResponse(PaymentGateway, requestBody, Caller);
            }
            catch (Exception ex)
            {
                string customException = GenericExceptionMessage.GetValidCustomExeptionMessage(ex, executionContext);
                log.Error(customException);
                hostedPaymentResponseDTO = new HostedPaymentResponseDTO();
                // Nitin to do: build a default message
                hostedPaymentResponseDTO.RedirectURL = "";
            }

            // Check if additional response data is present - if so, return it
            if (hostedPaymentResponseDTO.AdditionalResponseDTO != null)
            {
                log.Debug("AdditionalResponseDTO found - returning additional response data");
                response = Request.CreateResponse(HttpStatusCode.OK, hostedPaymentResponseDTO.AdditionalResponseDTO);
            }
            // Otherwise, handle redirect logic
            else if (hostedPaymentResponseDTO.RedirectResponseToWebsite)
            {
                log.Debug("This is a hosted gateway, creating a redirect response");
                response = Request.CreateResponse(HttpStatusCode.Redirect);
                response.Headers.Add("Location", hostedPaymentResponseDTO.RedirectURL);
            }
            else
            {
                log.Debug("This is not a hosted gateway. Send the response back as 200 status code");
                response = Request.CreateResponse(HttpStatusCode.OK, new { data = hostedPaymentResponseDTO.RedirectURL });
            }
            return response;
        }
    }
}