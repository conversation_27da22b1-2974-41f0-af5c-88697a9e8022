//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:2.0.50727.8009
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Xml.Serialization;

// 
// This source code was auto-generated by xsd, Version=2.0.50727.3038.
// 


/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PymtType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PymtTypeType
{

    /// <remarks/>
    AltCNP,

    /// <remarks/>
    Check,

    /// <remarks/>
    Credit,

    /// <remarks/>
    Debit,

    /// <remarks/>
    EBT,

    /// <remarks/>
    Fleet,

    /// <remarks/>
    PLDebit,

    /// <remarks/>
    Prepaid,

    /// <remarks/>
    PvtLabl,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ReversalInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ReversalIndType
{

    /// <remarks/>
    Timeout,

    /// <remarks/>
    Void,

    /// <remarks/>
    VoidFr,

    /// <remarks/>
    TORVoid,

    /// <remarks/>
    Partial,

    /// <remarks/>
    EditErr,

    /// <remarks/>
    MACVeri,

    /// <remarks/>
    MACSync,

    /// <remarks/>
    EncrErr,

    /// <remarks/>
    SystErr,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("TxnType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum TxnTypeType
{

    /// <remarks/>
    Activation,

    /// <remarks/>
    Authorization,

    /// <remarks/>
    BalanceInquiry,

    /// <remarks/>
    BalanceLock,

    /// <remarks/>
    BatchSettleDetail,

    /// <remarks/>
    BatchSettleL3,

    /// <remarks/>
    CanadaKeyRequest,

    /// <remarks/>
    CashAdvance,

    /// <remarks/>
    Cashout,

    /// <remarks/>
    CashoutActiveStatus,

    /// <remarks/>
    Change,

    /// <remarks/>
    CloseBatch,

    /// <remarks/>
    Completion,

    /// <remarks/>
    Custom,

    /// <remarks/>
    DisableInternetUse,

    /// <remarks/>
    EchoTest,

    /// <remarks/>
    FileDownload,

    /// <remarks/>
    FraudScore,

    /// <remarks/>
    GenerateKey,

    /// <remarks/>
    History,

    /// <remarks/>
    HostTotals,

    /// <remarks/>
    InternetActivation,

    /// <remarks/>
    Load,

    /// <remarks/>
    OpenBatch,

    /// <remarks/>
    PCL3AddDetail,

    /// <remarks/>
    Redemption,

    /// <remarks/>
    RedemptionUnlock,

    /// <remarks/>
    Refund,

    /// <remarks/>
    Reload,

    /// <remarks/>
    Sale,

    /// <remarks/>
    TACertAuthority,

    /// <remarks/>
    TAKeyRequest,

    /// <remarks/>
    TATokenRequest,

    /// <remarks/>
    Verification,

    /// <remarks/>
    VoucherClear,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("POSCondCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum POSCondCodeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("00")]
    Item00,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("01")]
    Item01,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("02")]
    Item02,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("03")]
    Item03,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("04")]
    Item04,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("05")]
    Item05,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("06")]
    Item06,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("08")]
    Item08,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("59")]
    Item59,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("71")]
    Item71,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("73")]
    Item73,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("TermCatCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum TermCatCodeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("00")]
    Item00,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("01")]
    Item01,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("05")]
    Item05,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("06")]
    Item06,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("07")]
    Item07,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("08")]
    Item08,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("09")]
    Item09,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("12")]
    Item12,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("13")]
    Item13,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("17")]
    Item17,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("18")]
    Item18,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("TermEntryCapablt", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum TermEntryCapabltType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("00")]
    Item00,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("01")]
    Item01,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("02")]
    Item02,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("03")]
    Item03,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("04")]
    Item04,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("05")]
    Item05,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("06")]
    Item06,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("07")]
    Item07,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("08")]
    Item08,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("09")]
    Item09,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("10")]
    Item10,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("11")]
    Item11,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("12")]
    Item12,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("TermLocInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum TermLocIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CardCaptCap", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CardCaptCapType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ProgramID", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ProgramIDType
{

    /// <remarks/>
    InComm,

    /// <remarks/>
    Solspark,

    /// <remarks/>
    Blackhawk,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SettleInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SettleIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PLPOSDebitFlg", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PLPOSDebitFlgType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    C,

    /// <remarks/>
    D,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NetAccInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NetAccIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    C,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NonUSMerch", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NonUSMerchType
{

    /// <remarks/>
    Canadian,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("BillPymtTxnInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum BillPymtTxnIndType
{

    /// <remarks/>
    Single,

    /// <remarks/>
    Recurring,

    /// <remarks/>
    Installment,

    /// <remarks/>
    Deferred,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PFInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PFIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CardType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CardTypeType
{

    /// <remarks/>
    Amex,

    /// <remarks/>
    Diners,

    /// <remarks/>
    Discover,

    /// <remarks/>
    JCB,

    /// <remarks/>
    MaestroInt,

    /// <remarks/>
    MasterCard,

    /// <remarks/>
    Visa,

    /// <remarks/>
    GiftCard,

    /// <remarks/>
    PPayCL,

    /// <remarks/>
    CarCareOne,

    /// <remarks/>
    CostPlus,

    /// <remarks/>
    Dicks,

    /// <remarks/>
    Exxon,

    /// <remarks/>
    GenProp,

    /// <remarks/>
    Gulf,

    /// <remarks/>
    Shell,

    /// <remarks/>
    Sinclair,

    /// <remarks/>
    SpeedPass,

    /// <remarks/>
    Sunoco,

    /// <remarks/>
    ValeroUCC,

    /// <remarks/>
    BPBusiness,

    /// <remarks/>
    Buypass,

    /// <remarks/>
    EssoFleet,

    /// <remarks/>
    ExxonFleet,

    /// <remarks/>
    FleetCor,

    /// <remarks/>
    FleetOne,

    /// <remarks/>
    MCFleet,

    /// <remarks/>
    ValeroFlt,

    /// <remarks/>
    VisaFleet,

    /// <remarks/>
    Voyager,

    /// <remarks/>
    Wex,

    /// <remarks/>
    Paypal,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("AVSResultCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum AVSResultCodeType
{

    /// <remarks/>
    A,

    /// <remarks/>
    B,

    /// <remarks/>
    C,

    /// <remarks/>
    D,

    /// <remarks/>
    E,

    /// <remarks/>
    F,

    /// <remarks/>
    G,

    /// <remarks/>
    I,

    /// <remarks/>
    K,

    /// <remarks/>
    L,

    /// <remarks/>
    M,

    /// <remarks/>
    N,

    /// <remarks/>
    O,

    /// <remarks/>
    P,

    /// <remarks/>
    R,

    /// <remarks/>
    S,

    /// <remarks/>
    T,

    /// <remarks/>
    U,

    /// <remarks/>
    W,

    /// <remarks/>
    X,

    /// <remarks/>
    Y,

    /// <remarks/>
    Z,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CCVInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CCVIndType
{

    /// <remarks/>
    Ntprvd,

    /// <remarks/>
    Prvded,

    /// <remarks/>
    Illegible,

    /// <remarks/>
    NtOnCrd,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CCVResultCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CCVResultCodeType
{

    /// <remarks/>
    Match,

    /// <remarks/>
    NoMtch,

    /// <remarks/>
    NotPrc,

    /// <remarks/>
    NotPrv,

    /// <remarks/>
    NotPrt,

    /// <remarks/>
    Unknwn,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("InfoReqInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum InfoReqIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ChkSvcPvdr", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ChkSvcPvdrType
{

    /// <remarks/>
    TeleCheck,

    /// <remarks/>
    TeleCheckECA,

    /// <remarks/>
    TeleCheckICA,

    /// <remarks/>
    TeleCheckCBP,

    /// <remarks/>
    TeleCheckPPD,

    /// <remarks/>
    Certegy,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ChkEntryMethod", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ChkEntryMethodType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ChkType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ChkTypeType
{

    /// <remarks/>
    P,

    /// <remarks/>
    C,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ECAStatus", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ECAStatusType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NFTFChkType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NFTFChkTypeType
{

    /// <remarks/>
    P,

    /// <remarks/>
    C,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NFTFRelType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NFTFRelTypeType
{

    /// <remarks/>
    D,

    /// <remarks/>
    P,

    /// <remarks/>
    S,

    /// <remarks/>
    X,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NFTFPrimIDType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NFTFPrimIDTypeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NFTFSecIDType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NFTFSecIDTypeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NFTFVIPInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NFTFVIPIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NFTFACHStat", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NFTFACHStatType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NFTFPreFlag", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NFTFPreFlagType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("AddAmtType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum AddAmtTypeType
{

    /// <remarks/>
    Cashback,

    /// <remarks/>
    Surchrg,

    /// <remarks/>
    Hltcare,

    /// <remarks/>
    Transit,

    /// <remarks/>
    RX,

    /// <remarks/>
    Vision,

    /// <remarks/>
    Clinical,

    /// <remarks/>
    Dental,

    /// <remarks/>
    Copay,

    /// <remarks/>
    FirstAuthAmt,

    /// <remarks/>
    PreAuthAmt,

    /// <remarks/>
    TotalAuthAmt,

    /// <remarks/>
    Tax,

    /// <remarks/>
    Fee,

    /// <remarks/>
    BegBal,

    /// <remarks/>
    EndingBal,

    /// <remarks/>
    AvailBal,

    /// <remarks/>
    LedgerBal,

    /// <remarks/>
    HoldBal,

    /// <remarks/>
    OrigReqAmt,

    /// <remarks/>
    OpenToBuy,

    /// <remarks/>
    Fuel,

    /// <remarks/>
    Service,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PartAuthrztnApprvlCapablt", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PartAuthrztnApprvlCapabltType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("FinAmtInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum FinAmtIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SctyLvl", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SctyLvlType
{

    /// <remarks/>
    Tknizatn,

    /// <remarks/>
    EncrptTknizatn,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("EncrptType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum EncrptTypeType
{

    /// <remarks/>
    RSA,

    /// <remarks/>
    Verifone,

    /// <remarks/>
    VSP,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3DES")]
    Item3DES,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("EncrptTrgt", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum EncrptTrgtType
{

    /// <remarks/>
    Track1,

    /// <remarks/>
    Track2,

    /// <remarks/>
    BothTracks,

    /// <remarks/>
    PAN,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("DeviceType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum DeviceTypeType
{

    /// <remarks/>
    INGENICO,

    /// <remarks/>
    VERIFONE,

    /// <remarks/>
    MAGTEK,

    /// <remarks/>
    IDTECH,

    /// <remarks/>
    EQUINOX,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("HCEInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum HCEIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PropTknFlg", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PropTknFlgType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("POSOfferCap", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum POSOfferCapType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("EcommTxnInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum EcommTxnIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("01")]
    Item01,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("02")]
    Item02,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("03")]
    Item03,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("04")]
    Item04,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("05")]
    Item05,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("06")]
    Item06,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("07")]
    Item07,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CAVVResultCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CAVVResultCodeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("9")]
    Item9,

    /// <remarks/>
    A,

    /// <remarks/>
    B,

    /// <remarks/>
    C,

    /// <remarks/>
    D,

    /// <remarks/>
    E,

    /// <remarks/>
    I,

    /// <remarks/>
    U,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("Safekey", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SafekeyType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("9")]
    Item9,

    /// <remarks/>
    A,

    /// <remarks/>
    B,

    /// <remarks/>
    C,

    /// <remarks/>
    D,

    /// <remarks/>
    I,

    /// <remarks/>
    U,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("UCAFCollectInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum UCAFCollectIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("DiscAuthType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum DiscAuthTypeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SecDataDowngrade", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SecDataDowngradeType
{

    /// <remarks/>
    SecDataMissing,

    /// <remarks/>
    SecDataInvalid,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ACI", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ACIType
{

    /// <remarks/>
    P,

    /// <remarks/>
    I,

    /// <remarks/>
    Y,

    /// <remarks/>
    R,

    /// <remarks/>
    A,

    /// <remarks/>
    B,

    /// <remarks/>
    C,

    /// <remarks/>
    E,

    /// <remarks/>
    F,

    /// <remarks/>
    J,

    /// <remarks/>
    K,

    /// <remarks/>
    N,

    /// <remarks/>
    S,

    /// <remarks/>
    T,

    /// <remarks/>
    U,

    /// <remarks/>
    V,

    /// <remarks/>
    W,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("MrktSpecificDataInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum MrktSpecificDataIndType
{

    /// <remarks/>
    BillPayment,

    /// <remarks/>
    Healthcare,

    /// <remarks/>
    Transit,

    /// <remarks/>
    EcomAgg,

    /// <remarks/>
    B2B,

    /// <remarks/>
    Hotel,

    /// <remarks/>
    AutoRental,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ExistingDebtInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ExistingDebtIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("TaxAmtCapablt", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum TaxAmtCapabltType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    VB,

    /// <remarks/>
    VC,

    /// <remarks/>
    VP,

    /// <remarks/>
    TX,

    /// <remarks/>
    NA,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CheckoutInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CheckoutIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("QCI", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum QCIType
{

    /// <remarks/>
    Y,

    /// <remarks/>
    N,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("VisaAuthInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum VisaAuthIndType
{

    /// <remarks/>
    ReAuth,

    /// <remarks/>
    ReSubmit,

    /// <remarks/>
    EstAuth,

    /// <remarks/>
    CrdOnFile,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("MCMSDI", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum MCMSDIType
{

    /// <remarks/>
    BillPayment,

    /// <remarks/>
    Healthcare,

    /// <remarks/>
    Transit,

    /// <remarks/>
    EcomAgg,

    /// <remarks/>
    B2B,

    /// <remarks/>
    Hotel,

    /// <remarks/>
    AutoRental,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CCVErrorCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CCVErrorCodeType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("POSEntryModeChg", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum POSEntryModeChgType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("DevTypeInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum DevTypeIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("9")]
    Item9,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("10")]
    Item10,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("11")]
    Item11,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("12")]
    Item12,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("13")]
    Item13,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("MCACI", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum MCACIType
{

    /// <remarks/>
    P,

    /// <remarks/>
    I,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("FinAuthInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum FinAuthIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("MOTOInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum MOTOIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("RegUserInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum RegUserIndType
{

    /// <remarks/>
    Y,

    /// <remarks/>
    N,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("DiscAuthInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum DiscAuthIndType
{

    /// <remarks/>
    ReAuth,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PartShipInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PartShipIndType
{

    /// <remarks/>
    Partial,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("GdSoldCd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum GdSoldCdType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1000")]
    Item1000,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ReAuthInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ReAuthIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("EBTType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum EBTTypeType
{

    /// <remarks/>
    EBTCash,

    /// <remarks/>
    SNAP,

    /// <remarks/>
    eWIC,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("UPCPLUInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum UPCPLUIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CanDebitTransCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CanDebitTransCodeType
{

    /// <remarks/>
    KeyRequest,

    /// <remarks/>
    Saving,

    /// <remarks/>
    Checking,

    /// <remarks/>
    RefundSaving,

    /// <remarks/>
    RefundChecking,

    /// <remarks/>
    AdjRefundSaving,

    /// <remarks/>
    AdjRefundChecking,

    /// <remarks/>
    AdjSaving,

    /// <remarks/>
    AdjChecking,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("TaxInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum TaxIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ComCardType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ComCardTypeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("01")]
    Item01,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("02")]
    Item02,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("03")]
    Item03,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("04")]
    Item04,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("L3ItemQtyExp", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum L3ItemQtyExpType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("L3TaxInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum L3TaxIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("L3TaxRtExp", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum L3TaxRtExpType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("L3DbCrInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum L3DbCrIndType
{

    /// <remarks/>
    D,

    /// <remarks/>
    C,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("L3LineItmDtlInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum L3LineItmDtlIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("L3TaxTypeId", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum L3TaxTypeIdType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("00")]
    Item00,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("01")]
    Item01,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("02")]
    Item02,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("03")]
    Item03,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("04")]
    Item04,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("05")]
    Item05,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("06")]
    Item06,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("07")]
    Item07,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("08")]
    Item08,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("09")]
    Item09,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("10")]
    Item10,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("11")]
    Item11,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("12")]
    Item12,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("13")]
    Item13,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CHFullNmRes", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CHFullNmResType
{

    /// <remarks/>
    M,

    /// <remarks/>
    F,

    /// <remarks/>
    L,

    /// <remarks/>
    N,

    /// <remarks/>
    W,

    /// <remarks/>
    U,

    /// <remarks/>
    P,

    /// <remarks/>
    K,

    /// <remarks/>
    B,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CustTelType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CustTelTypeType
{

    /// <remarks/>
    D,

    /// <remarks/>
    H,

    /// <remarks/>
    N,

    /// <remarks/>
    W,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ShipToTelType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ShipToTelTypeType
{

    /// <remarks/>
    D,

    /// <remarks/>
    H,

    /// <remarks/>
    N,

    /// <remarks/>
    W,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("DelivType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum DelivTypeType
{

    /// <remarks/>
    CNC,

    /// <remarks/>
    DIG,

    /// <remarks/>
    PHY,

    /// <remarks/>
    SVC,

    /// <remarks/>
    TBD,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ShippingCarrier", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ShippingCarrierType
{

    /// <remarks/>
    DHL,

    /// <remarks/>
    FedEx,

    /// <remarks/>
    Greyhound,

    /// <remarks/>
    Other,

    /// <remarks/>
    Purolator,

    /// <remarks/>
    USPS,

    /// <remarks/>
    UPS,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ShippingMthd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ShippingMthdType
{

    /// <remarks/>
    C,

    /// <remarks/>
    D,

    /// <remarks/>
    E,

    /// <remarks/>
    G,

    /// <remarks/>
    I,

    /// <remarks/>
    M,

    /// <remarks/>
    N,

    /// <remarks/>
    O,

    /// <remarks/>
    P,

    /// <remarks/>
    S,

    /// <remarks/>
    T,

    /// <remarks/>
    W,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("EschtblTxn", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum EschtblTxnType
{

    /// <remarks/>
    No,

    /// <remarks/>
    Yes,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SVActType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SVActTypeType
{

    /// <remarks/>
    C,

    /// <remarks/>
    F,

    /// <remarks/>
    P,

    /// <remarks/>
    S,

    /// <remarks/>
    V,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SAACI", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SAACIType
{

    /// <remarks/>
    I,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SAMrktSpecificDataInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SAMrktSpecificDataIndType
{

    /// <remarks/>
    B,

    /// <remarks/>
    G,

    /// <remarks/>
    H,

    /// <remarks/>
    Q,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("DCCInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum DCCIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("TotReqDate", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum TotReqDateType
{

    /// <remarks/>
    ShiftRPT,

    /// <remarks/>
    CloseBatch,

    /// <remarks/>
    CurDayRPT,

    /// <remarks/>
    PrevDayRPT,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3rdDayRPT")]
    Item3rdDayRPT,

    /// <remarks/>
    CloseShift,

    /// <remarks/>
    SiteCurDay,

    /// <remarks/>
    SitePrvDay,

    /// <remarks/>
    Site3rdDay,

    /// <remarks/>
    SettleDate,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CardTag", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CardTagType
{

    /// <remarks/>
    CC,

    /// <remarks/>
    TE,

    /// <remarks/>
    DS,

    /// <remarks/>
    AO,

    /// <remarks/>
    DB,

    /// <remarks/>
    FL,

    /// <remarks/>
    CS,

    /// <remarks/>
    PR,

    /// <remarks/>
    CK,

    /// <remarks/>
    EF,

    /// <remarks/>
    EC,

    /// <remarks/>
    SV1,

    /// <remarks/>
    SV2,

    /// <remarks/>
    SV3,

    /// <remarks/>
    SV4,

    /// <remarks/>
    ECA,

    /// <remarks/>
    WIC,

    /// <remarks/>
    EK,

    /// <remarks/>
    MC,

    /// <remarks/>
    VS,

    /// <remarks/>
    AMX,

    /// <remarks/>
    DIS,

    /// <remarks/>
    DBC,

    /// <remarks/>
    PPD,

    /// <remarks/>
    PP1,

    /// <remarks/>
    PP3,

    /// <remarks/>
    PP4,

    /// <remarks/>
    SP,

    /// <remarks/>
    HD,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ServLvl", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ServLvlType
{

    /// <remarks/>
    F,

    /// <remarks/>
    S,

    /// <remarks/>
    N,

    /// <remarks/>
    X,

    /// <remarks/>
    O,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("9")]
    Item9,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("NumOfProds", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum NumOfProdsType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("01")]
    Item01,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("02")]
    Item02,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("03")]
    Item03,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("04")]
    Item04,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("05")]
    Item05,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("06")]
    Item06,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("07")]
    Item07,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("08")]
    Item08,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("09")]
    Item09,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("10")]
    Item10,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("UnitOfMsure", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum UnitOfMsureType
{

    /// <remarks/>
    C,

    /// <remarks/>
    G,

    /// <remarks/>
    K,

    /// <remarks/>
    L,

    /// <remarks/>
    P,

    /// <remarks/>
    Q,

    /// <remarks/>
    U,

    /// <remarks/>
    Z,

    /// <remarks/>
    O,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("9")]
    Item9,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("FileType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum FileTypeType
{

    /// <remarks/>
    EMV2KEY,

    /// <remarks/>
    MAIL,

    /// <remarks/>
    CARDTABLE,

    /// <remarks/>
    DYNCRDTBL,

    /// <remarks/>
    SITECFG,

    /// <remarks/>
    FUELRPT,

    /// <remarks/>
    HOSTDISC,

    /// <remarks/>
    RECTXT,

    /// <remarks/>
    TABLE,

    /// <remarks/>
    TERMAIL,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SubFileType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SubFileTypeType
{

    /// <remarks/>
    BIN,

    /// <remarks/>
    RULES,

    /// <remarks/>
    RESTRCTION,

    /// <remarks/>
    SAF,

    /// <remarks/>
    PROMPT,

    /// <remarks/>
    PRODUCT,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("FunCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum FunCodeType
{

    /// <remarks/>
    R,

    /// <remarks/>
    D,

    /// <remarks/>
    F,

    /// <remarks/>
    U,

    /// <remarks/>
    N,

    /// <remarks/>
    C,

    /// <remarks/>
    L,

    /// <remarks/>
    E,

    /// <remarks/>
    M,

    /// <remarks/>
    P,

    /// <remarks/>
    X,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("ProgramInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum ProgramIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("HotelNoShow", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum HotelNoShowType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("LodChargeType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum LodChargeTypeType
{

    /// <remarks/>
    Lodging,

    /// <remarks/>
    Restaurant,

    /// <remarks/>
    GiftShop,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("AutoNoShow", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum AutoNoShowType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("RentalTaxInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum RentalTaxIndType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("0")]
    Item0,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("DelChrgInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum DelChrgIndType
{

    /// <remarks/>
    DelChrg,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SettleTxnType", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SettleTxnTypeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("9")]
    Item9,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("10")]
    Item10,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("11")]
    Item11,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("12")]
    Item12,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("13")]
    Item13,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("14")]
    Item14,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("RPS", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum RPSType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("1")]
    Item1,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("2")]
    Item2,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("3")]
    Item3,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("4")]
    Item4,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("5")]
    Item5,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("6")]
    Item6,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("7")]
    Item7,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("8")]
    Item8,

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("9")]
    Item9,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PrintInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PrintIndType
{

    /// <remarks/>
    Y,

    /// <remarks/>
    N,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("CustMatInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum CustMatIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("AddChgInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum AddChgIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("FrdScoreInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum FrdScoreIndType
{

    /// <remarks/>
    Y,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("MembInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum MembIndType
{

    /// <remarks/>
    M,

    /// <remarks/>
    G,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("MTProcessInd", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum MTProcessIndType
{

    /// <remarks/>
    Funding,

    /// <remarks/>
    Payment,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("PmntTypeId", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum PmntTypeIdType
{

    /// <remarks/>
    P2P,

    /// <remarks/>
    P2PBankInit,

    /// <remarks/>
    MerchDisb,

    /// <remarks/>
    GovDisb,

    /// <remarks/>
    FundsDisb,

    /// <remarks/>
    OLGambling,

    /// <remarks/>
    WltTrnsfr,

    /// <remarks/>
    LoyaltyOfrs,

    /// <remarks/>
    GamblingPay,

    /// <remarks/>
    B2B,

    /// <remarks/>
    PayrollDisb,

    /// <remarks/>
    MerchInitMT,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("FndSrc", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum FndSrcType
{

    /// <remarks/>
    Credit,

    /// <remarks/>
    Debit,

    /// <remarks/>
    Prepaid,

    /// <remarks/>
    DepAcct,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("RecvStateCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum RecvStateCodeType
{

    /// <remarks/>
    AL,

    /// <remarks/>
    AK,

    /// <remarks/>
    AZ,

    /// <remarks/>
    AR,

    /// <remarks/>
    CA,

    /// <remarks/>
    CO,

    /// <remarks/>
    CT,

    /// <remarks/>
    DE,

    /// <remarks/>
    DC,

    /// <remarks/>
    FL,

    /// <remarks/>
    GA,

    /// <remarks/>
    HI,

    /// <remarks/>
    ID,

    /// <remarks/>
    IL,

    /// <remarks/>
    IN,

    /// <remarks/>
    IA,

    /// <remarks/>
    KS,

    /// <remarks/>
    KY,

    /// <remarks/>
    LA,

    /// <remarks/>
    ME,

    /// <remarks/>
    MD,

    /// <remarks/>
    MA,

    /// <remarks/>
    MI,

    /// <remarks/>
    MN,

    /// <remarks/>
    MS,

    /// <remarks/>
    MO,

    /// <remarks/>
    MT,

    /// <remarks/>
    NE,

    /// <remarks/>
    NV,

    /// <remarks/>
    NH,

    /// <remarks/>
    NJ,

    /// <remarks/>
    NM,

    /// <remarks/>
    NY,

    /// <remarks/>
    NC,

    /// <remarks/>
    ND,

    /// <remarks/>
    OH,

    /// <remarks/>
    OK,

    /// <remarks/>
    OR,

    /// <remarks/>
    PA,

    /// <remarks/>
    MY,

    /// <remarks/>
    RI,

    /// <remarks/>
    SC,

    /// <remarks/>
    SD,

    /// <remarks/>
    TN,

    /// <remarks/>
    TX,

    /// <remarks/>
    UT,

    /// <remarks/>
    VT,

    /// <remarks/>
    VA,

    /// <remarks/>
    WA,

    /// <remarks/>
    WV,

    /// <remarks/>
    WI,

    /// <remarks/>
    WY,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("RecvCtryCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum RecvCtryCodeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("840")]
    Item840,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SendStateCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SendStateCodeType
{

    /// <remarks/>
    AL,

    /// <remarks/>
    AK,

    /// <remarks/>
    AZ,

    /// <remarks/>
    AR,

    /// <remarks/>
    CA,

    /// <remarks/>
    CO,

    /// <remarks/>
    CT,

    /// <remarks/>
    DE,

    /// <remarks/>
    DC,

    /// <remarks/>
    FL,

    /// <remarks/>
    GA,

    /// <remarks/>
    HI,

    /// <remarks/>
    ID,

    /// <remarks/>
    IL,

    /// <remarks/>
    IN,

    /// <remarks/>
    IA,

    /// <remarks/>
    KS,

    /// <remarks/>
    KY,

    /// <remarks/>
    LA,

    /// <remarks/>
    ME,

    /// <remarks/>
    MD,

    /// <remarks/>
    MA,

    /// <remarks/>
    MI,

    /// <remarks/>
    MN,

    /// <remarks/>
    MS,

    /// <remarks/>
    MO,

    /// <remarks/>
    MT,

    /// <remarks/>
    NE,

    /// <remarks/>
    NV,

    /// <remarks/>
    NH,

    /// <remarks/>
    NJ,

    /// <remarks/>
    NM,

    /// <remarks/>
    NY,

    /// <remarks/>
    NC,

    /// <remarks/>
    ND,

    /// <remarks/>
    OH,

    /// <remarks/>
    OK,

    /// <remarks/>
    OR,

    /// <remarks/>
    PA,

    /// <remarks/>
    MY,

    /// <remarks/>
    RI,

    /// <remarks/>
    SC,

    /// <remarks/>
    SD,

    /// <remarks/>
    TN,

    /// <remarks/>
    TX,

    /// <remarks/>
    UT,

    /// <remarks/>
    VT,

    /// <remarks/>
    VA,

    /// <remarks/>
    WA,

    /// <remarks/>
    WV,

    /// <remarks/>
    WI,

    /// <remarks/>
    WY,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("SendCtryCode", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public enum SendCtryCodeType
{

    /// <remarks/>
    [System.Xml.Serialization.XmlEnumAttribute("840")]
    Item840,
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
[System.Xml.Serialization.XmlRootAttribute("GMF", Namespace = "com/firstdata/Merchant/gmfV6.10", IsNullable = false)]
public partial class GMFMessageVariants
{

    private object itemField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AdminRequest", typeof(AdminRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("AdminResponse", typeof(AdminResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("AltCNPRequest", typeof(AltCNPRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("AltCNPResponse", typeof(AltCNPResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("BatchRequest", typeof(BatchRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("BatchResponse", typeof(BatchResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("CheckRequest", typeof(CheckRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("CheckResponse", typeof(CheckResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("CreditRequest", typeof(CreditRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("CreditResponse", typeof(CreditResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("DebitRequest", typeof(DebitRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("DebitResponse", typeof(DebitResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("EBTRequest", typeof(EBTRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("EBTResponse", typeof(EBTResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("FleetRequest", typeof(FleetRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("FleetResponse", typeof(FleetResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("GenPrepaidRequest", typeof(GenPrepaidRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("GenPrepaidResponse", typeof(GenPrepaidResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("MTRequest", typeof(MTRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("MTResponse", typeof(MTResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("PinlessDebitRequest", typeof(PinlessDebitRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("PinlessDebitResponse", typeof(PinlessDebitResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("PrepaidRequest", typeof(PrepaidRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("PrepaidResponse", typeof(PrepaidResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("PrivateLabelRequest", typeof(PrivateLabelRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("PrivateLabelResponse", typeof(PrivateLabelResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("RejectResponse", typeof(RejectResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("ReversalRequest", typeof(VoidTOReversalRequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("ReversalResponse", typeof(VoidTOReversalResponseDetails))]
    [System.Xml.Serialization.XmlElementAttribute("TransArmorRequest", typeof(TARequestDetails))]
    [System.Xml.Serialization.XmlElementAttribute("TransArmorResponse", typeof(TAResponseDetails))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AdminRequestDetails
{

    private CommonGrp commonGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CardGrp cardGrpField;

    private TAGrp tAGrpField;

    private CanDebitGrp canDebitGrpField;

    private HostTotGrp hostTotGrpField;

    private HostTotDetGrp[] hostTotDetGrpField;

    private FileDLGrp fileDLGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public CanDebitGrp CanDebitGrp
    {
        get
        {
            return this.canDebitGrpField;
        }
        set
        {
            this.canDebitGrpField = value;
        }
    }

    /// <remarks/>
    public HostTotGrp HostTotGrp
    {
        get
        {
            return this.hostTotGrpField;
        }
        set
        {
            this.hostTotGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("HostTotDetGrp")]
    public HostTotDetGrp[] HostTotDetGrp
    {
        get
        {
            return this.hostTotDetGrpField;
        }
        set
        {
            this.hostTotDetGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CommonGrp
{

    private PymtTypeType pymtTypeField;

    private bool pymtTypeFieldSpecified;

    private ReversalIndType reversalIndField;

    private bool reversalIndFieldSpecified;

    private TxnTypeType txnTypeField;

    private bool txnTypeFieldSpecified;

    private string localDateTimeField;

    private string trnmsnDateTimeField;

    private string sTANField;

    private string refNumField;

    private string orderNumField;

    private string tPPIDField;

    private string termIDField;

    private string merchIDField;

    private string altMerchIDField;

    private string merchCatCodeField;

    private string pOSEntryModeField;

    private POSCondCodeType pOSCondCodeField;

    private bool pOSCondCodeFieldSpecified;

    private TermCatCodeType termCatCodeField;

    private bool termCatCodeFieldSpecified;

    private TermEntryCapabltType termEntryCapabltField;

    private bool termEntryCapabltFieldSpecified;

    private string txnAmtField;

    private string txnCrncyField;

    private TermLocIndType termLocIndField;

    private bool termLocIndFieldSpecified;

    private CardCaptCapType cardCaptCapField;

    private bool cardCaptCapFieldSpecified;

    private ProgramIDType programIDField;

    private bool programIDFieldSpecified;

    private string groupIDField;

    private string pOSIDField;

    private SettleIndType settleIndField;

    private bool settleIndFieldSpecified;

    private string clerkIDField;

    private string supIDField;

    private string sENumField;

    private PLPOSDebitFlgType pLPOSDebitFlgField;

    private bool pLPOSDebitFlgFieldSpecified;

    private string uPCField;

    private NetAccIndType netAccIndField;

    private bool netAccIndFieldSpecified;

    private string merchEchoField;

    private string wltIDField;

    private string cashAdvSerNumField;

    private NonUSMerchType nonUSMerchField;

    private bool nonUSMerchFieldSpecified;

    private string futureField;

    private string devBatchIDField;

    /// <remarks/>
    public PymtTypeType PymtType
    {
        get
        {
            return this.pymtTypeField;
        }
        set
        {
            this.pymtTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PymtTypeSpecified
    {
        get
        {
            return this.pymtTypeFieldSpecified;
        }
        set
        {
            this.pymtTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public ReversalIndType ReversalInd
    {
        get
        {
            return this.reversalIndField;
        }
        set
        {
            this.reversalIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ReversalIndSpecified
    {
        get
        {
            return this.reversalIndFieldSpecified;
        }
        set
        {
            this.reversalIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public TxnTypeType TxnType
    {
        get
        {
            return this.txnTypeField;
        }
        set
        {
            this.txnTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TxnTypeSpecified
    {
        get
        {
            return this.txnTypeFieldSpecified;
        }
        set
        {
            this.txnTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string LocalDateTime
    {
        get
        {
            return this.localDateTimeField;
        }
        set
        {
            this.localDateTimeField = value;
        }
    }

    /// <remarks/>
    public string TrnmsnDateTime
    {
        get
        {
            return this.trnmsnDateTimeField;
        }
        set
        {
            this.trnmsnDateTimeField = value;
        }
    }

    /// <remarks/>
    public string STAN
    {
        get
        {
            return this.sTANField;
        }
        set
        {
            this.sTANField = value;
        }
    }

    /// <remarks/>
    public string RefNum
    {
        get
        {
            return this.refNumField;
        }
        set
        {
            this.refNumField = value;
        }
    }

    /// <remarks/>
    public string OrderNum
    {
        get
        {
            return this.orderNumField;
        }
        set
        {
            this.orderNumField = value;
        }
    }

    /// <remarks/>
    public string TPPID
    {
        get
        {
            return this.tPPIDField;
        }
        set
        {
            this.tPPIDField = value;
        }
    }

    /// <remarks/>
    public string TermID
    {
        get
        {
            return this.termIDField;
        }
        set
        {
            this.termIDField = value;
        }
    }

    /// <remarks/>
    public string MerchID
    {
        get
        {
            return this.merchIDField;
        }
        set
        {
            this.merchIDField = value;
        }
    }

    /// <remarks/>
    public string AltMerchID
    {
        get
        {
            return this.altMerchIDField;
        }
        set
        {
            this.altMerchIDField = value;
        }
    }

    /// <remarks/>
    public string MerchCatCode
    {
        get
        {
            return this.merchCatCodeField;
        }
        set
        {
            this.merchCatCodeField = value;
        }
    }

    /// <remarks/>
    public string POSEntryMode
    {
        get
        {
            return this.pOSEntryModeField;
        }
        set
        {
            this.pOSEntryModeField = value;
        }
    }

    /// <remarks/>
    public POSCondCodeType POSCondCode
    {
        get
        {
            return this.pOSCondCodeField;
        }
        set
        {
            this.pOSCondCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool POSCondCodeSpecified
    {
        get
        {
            return this.pOSCondCodeFieldSpecified;
        }
        set
        {
            this.pOSCondCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public TermCatCodeType TermCatCode
    {
        get
        {
            return this.termCatCodeField;
        }
        set
        {
            this.termCatCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TermCatCodeSpecified
    {
        get
        {
            return this.termCatCodeFieldSpecified;
        }
        set
        {
            this.termCatCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public TermEntryCapabltType TermEntryCapablt
    {
        get
        {
            return this.termEntryCapabltField;
        }
        set
        {
            this.termEntryCapabltField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TermEntryCapabltSpecified
    {
        get
        {
            return this.termEntryCapabltFieldSpecified;
        }
        set
        {
            this.termEntryCapabltFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string TxnAmt
    {
        get
        {
            return this.txnAmtField;
        }
        set
        {
            this.txnAmtField = value;
        }
    }

    /// <remarks/>
    public string TxnCrncy
    {
        get
        {
            return this.txnCrncyField;
        }
        set
        {
            this.txnCrncyField = value;
        }
    }

    /// <remarks/>
    public TermLocIndType TermLocInd
    {
        get
        {
            return this.termLocIndField;
        }
        set
        {
            this.termLocIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TermLocIndSpecified
    {
        get
        {
            return this.termLocIndFieldSpecified;
        }
        set
        {
            this.termLocIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public CardCaptCapType CardCaptCap
    {
        get
        {
            return this.cardCaptCapField;
        }
        set
        {
            this.cardCaptCapField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CardCaptCapSpecified
    {
        get
        {
            return this.cardCaptCapFieldSpecified;
        }
        set
        {
            this.cardCaptCapFieldSpecified = value;
        }
    }

    /// <remarks/>
    public ProgramIDType ProgramID
    {
        get
        {
            return this.programIDField;
        }
        set
        {
            this.programIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ProgramIDSpecified
    {
        get
        {
            return this.programIDFieldSpecified;
        }
        set
        {
            this.programIDFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string GroupID
    {
        get
        {
            return this.groupIDField;
        }
        set
        {
            this.groupIDField = value;
        }
    }

    /// <remarks/>
    public string POSID
    {
        get
        {
            return this.pOSIDField;
        }
        set
        {
            this.pOSIDField = value;
        }
    }

    /// <remarks/>
    public SettleIndType SettleInd
    {
        get
        {
            return this.settleIndField;
        }
        set
        {
            this.settleIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SettleIndSpecified
    {
        get
        {
            return this.settleIndFieldSpecified;
        }
        set
        {
            this.settleIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string ClerkID
    {
        get
        {
            return this.clerkIDField;
        }
        set
        {
            this.clerkIDField = value;
        }
    }

    /// <remarks/>
    public string SupID
    {
        get
        {
            return this.supIDField;
        }
        set
        {
            this.supIDField = value;
        }
    }

    /// <remarks/>
    public string SENum
    {
        get
        {
            return this.sENumField;
        }
        set
        {
            this.sENumField = value;
        }
    }

    /// <remarks/>
    public PLPOSDebitFlgType PLPOSDebitFlg
    {
        get
        {
            return this.pLPOSDebitFlgField;
        }
        set
        {
            this.pLPOSDebitFlgField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLPOSDebitFlgSpecified
    {
        get
        {
            return this.pLPOSDebitFlgFieldSpecified;
        }
        set
        {
            this.pLPOSDebitFlgFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string UPC
    {
        get
        {
            return this.uPCField;
        }
        set
        {
            this.uPCField = value;
        }
    }

    /// <remarks/>
    public NetAccIndType NetAccInd
    {
        get
        {
            return this.netAccIndField;
        }
        set
        {
            this.netAccIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NetAccIndSpecified
    {
        get
        {
            return this.netAccIndFieldSpecified;
        }
        set
        {
            this.netAccIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string MerchEcho
    {
        get
        {
            return this.merchEchoField;
        }
        set
        {
            this.merchEchoField = value;
        }
    }

    /// <remarks/>
    public string WltID
    {
        get
        {
            return this.wltIDField;
        }
        set
        {
            this.wltIDField = value;
        }
    }

    /// <remarks/>
    public string CashAdvSerNum
    {
        get
        {
            return this.cashAdvSerNumField;
        }
        set
        {
            this.cashAdvSerNumField = value;
        }
    }

    /// <remarks/>
    public NonUSMerchType NonUSMerch
    {
        get
        {
            return this.nonUSMerchField;
        }
        set
        {
            this.nonUSMerchField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NonUSMerchSpecified
    {
        get
        {
            return this.nonUSMerchFieldSpecified;
        }
        set
        {
            this.nonUSMerchFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string Future
    {
        get
        {
            return this.futureField;
        }
        set
        {
            this.futureField = value;
        }
    }

    /// <remarks/>
    public string DevBatchID
    {
        get
        {
            return this.devBatchIDField;
        }
        set
        {
            this.devBatchIDField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class MTResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private TknGrp tknGrpField;

    private SecrTxnGrp secrTxnGrpField;

    private object itemField;

    private RespGrp respGrpField;

    private OrigAuthGrp origAuthGrpField;

    private MnySndGrp mnySndGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public SecrTxnGrp SecrTxnGrp
    {
        get
        {
            return this.secrTxnGrpField;
        }
        set
        {
            this.secrTxnGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public MnySndGrp MnySndGrp
    {
        get
        {
            return this.mnySndGrpField;
        }
        set
        {
            this.mnySndGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CardGrp
{

    private string acctNumField;

    private string cardActivDateField;

    private string cardExpiryDateField;

    private string track1DataField;

    private string track2DataField;

    private CardTypeType cardTypeField;

    private bool cardTypeFieldSpecified;

    private AVSResultCodeType aVSResultCodeField;

    private bool aVSResultCodeFieldSpecified;

    private CCVIndType cCVIndField;

    private bool cCVIndFieldSpecified;

    private string cCVDataField;

    private CCVResultCodeType cCVResultCodeField;

    private bool cCVResultCodeFieldSpecified;

    private string mVVMAIDField;

    private InfoReqIndType infoReqIndField;

    private bool infoReqIndFieldSpecified;

    /// <remarks/>
    public string AcctNum
    {
        get
        {
            return this.acctNumField;
        }
        set
        {
            this.acctNumField = value;
        }
    }

    /// <remarks/>
    public string CardActivDate
    {
        get
        {
            return this.cardActivDateField;
        }
        set
        {
            this.cardActivDateField = value;
        }
    }

    /// <remarks/>
    public string CardExpiryDate
    {
        get
        {
            return this.cardExpiryDateField;
        }
        set
        {
            this.cardExpiryDateField = value;
        }
    }

    /// <remarks/>
    public string Track1Data
    {
        get
        {
            return this.track1DataField;
        }
        set
        {
            this.track1DataField = value;
        }
    }

    /// <remarks/>
    public string Track2Data
    {
        get
        {
            return this.track2DataField;
        }
        set
        {
            this.track2DataField = value;
        }
    }

    /// <remarks/>
    public CardTypeType CardType
    {
        get
        {
            return this.cardTypeField;
        }
        set
        {
            this.cardTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CardTypeSpecified
    {
        get
        {
            return this.cardTypeFieldSpecified;
        }
        set
        {
            this.cardTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public AVSResultCodeType AVSResultCode
    {
        get
        {
            return this.aVSResultCodeField;
        }
        set
        {
            this.aVSResultCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AVSResultCodeSpecified
    {
        get
        {
            return this.aVSResultCodeFieldSpecified;
        }
        set
        {
            this.aVSResultCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public CCVIndType CCVInd
    {
        get
        {
            return this.cCVIndField;
        }
        set
        {
            this.cCVIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CCVIndSpecified
    {
        get
        {
            return this.cCVIndFieldSpecified;
        }
        set
        {
            this.cCVIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string CCVData
    {
        get
        {
            return this.cCVDataField;
        }
        set
        {
            this.cCVDataField = value;
        }
    }

    /// <remarks/>
    public CCVResultCodeType CCVResultCode
    {
        get
        {
            return this.cCVResultCodeField;
        }
        set
        {
            this.cCVResultCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CCVResultCodeSpecified
    {
        get
        {
            return this.cCVResultCodeFieldSpecified;
        }
        set
        {
            this.cCVResultCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string MVVMAID
    {
        get
        {
            return this.mVVMAIDField;
        }
        set
        {
            this.mVVMAIDField = value;
        }
    }

    /// <remarks/>
    public InfoReqIndType InfoReqInd
    {
        get
        {
            return this.infoReqIndField;
        }
        set
        {
            this.infoReqIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool InfoReqIndSpecified
    {
        get
        {
            return this.infoReqIndFieldSpecified;
        }
        set
        {
            this.infoReqIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class TknGrp
{

    private string tknReqIDField;

    private string tknLvlField;

    private string tknLstFourField;

    private string tknBlkAField;

    private string tknBlkBField;

    private HCEIndType hCEIndField;

    private bool hCEIndFieldSpecified;

    private PropTknFlgType propTknFlgField;

    private bool propTknFlgFieldSpecified;

    /// <remarks/>
    public string TknReqID
    {
        get
        {
            return this.tknReqIDField;
        }
        set
        {
            this.tknReqIDField = value;
        }
    }

    /// <remarks/>
    public string TknLvl
    {
        get
        {
            return this.tknLvlField;
        }
        set
        {
            this.tknLvlField = value;
        }
    }

    /// <remarks/>
    public string TknLstFour
    {
        get
        {
            return this.tknLstFourField;
        }
        set
        {
            this.tknLstFourField = value;
        }
    }

    /// <remarks/>
    public string TknBlkA
    {
        get
        {
            return this.tknBlkAField;
        }
        set
        {
            this.tknBlkAField = value;
        }
    }

    /// <remarks/>
    public string TknBlkB
    {
        get
        {
            return this.tknBlkBField;
        }
        set
        {
            this.tknBlkBField = value;
        }
    }

    /// <remarks/>
    public HCEIndType HCEInd
    {
        get
        {
            return this.hCEIndField;
        }
        set
        {
            this.hCEIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool HCEIndSpecified
    {
        get
        {
            return this.hCEIndFieldSpecified;
        }
        set
        {
            this.hCEIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public PropTknFlgType PropTknFlg
    {
        get
        {
            return this.propTknFlgField;
        }
        set
        {
            this.propTknFlgField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PropTknFlgSpecified
    {
        get
        {
            return this.propTknFlgFieldSpecified;
        }
        set
        {
            this.propTknFlgFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class SecrTxnGrp
{

    private string visaXIDField;

    private string visaSecrTxnADField;

    private CAVVResultCodeType cAVVResultCodeField;

    private bool cAVVResultCodeFieldSpecified;

    private string amexXIDField;

    private string amexSecrADField;

    private SafekeyType safekeyField;

    private bool safekeyFieldSpecified;

    private UCAFCollectIndType uCAFCollectIndField;

    private bool uCAFCollectIndFieldSpecified;

    private string mCSecrADField;

    private DiscAuthTypeType discAuthTypeField;

    private bool discAuthTypeFieldSpecified;

    private string discSecDataField;

    private SecDataDowngradeType secDataDowngradeField;

    private bool secDataDowngradeFieldSpecified;

    /// <remarks/>
    public string VisaXID
    {
        get
        {
            return this.visaXIDField;
        }
        set
        {
            this.visaXIDField = value;
        }
    }

    /// <remarks/>
    public string VisaSecrTxnAD
    {
        get
        {
            return this.visaSecrTxnADField;
        }
        set
        {
            this.visaSecrTxnADField = value;
        }
    }

    /// <remarks/>
    public CAVVResultCodeType CAVVResultCode
    {
        get
        {
            return this.cAVVResultCodeField;
        }
        set
        {
            this.cAVVResultCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CAVVResultCodeSpecified
    {
        get
        {
            return this.cAVVResultCodeFieldSpecified;
        }
        set
        {
            this.cAVVResultCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string AmexXID
    {
        get
        {
            return this.amexXIDField;
        }
        set
        {
            this.amexXIDField = value;
        }
    }

    /// <remarks/>
    public string AmexSecrAD
    {
        get
        {
            return this.amexSecrADField;
        }
        set
        {
            this.amexSecrADField = value;
        }
    }

    /// <remarks/>
    public SafekeyType Safekey
    {
        get
        {
            return this.safekeyField;
        }
        set
        {
            this.safekeyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SafekeySpecified
    {
        get
        {
            return this.safekeyFieldSpecified;
        }
        set
        {
            this.safekeyFieldSpecified = value;
        }
    }

    /// <remarks/>
    public UCAFCollectIndType UCAFCollectInd
    {
        get
        {
            return this.uCAFCollectIndField;
        }
        set
        {
            this.uCAFCollectIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool UCAFCollectIndSpecified
    {
        get
        {
            return this.uCAFCollectIndFieldSpecified;
        }
        set
        {
            this.uCAFCollectIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string MCSecrAD
    {
        get
        {
            return this.mCSecrADField;
        }
        set
        {
            this.mCSecrADField = value;
        }
    }

    /// <remarks/>
    public DiscAuthTypeType DiscAuthType
    {
        get
        {
            return this.discAuthTypeField;
        }
        set
        {
            this.discAuthTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DiscAuthTypeSpecified
    {
        get
        {
            return this.discAuthTypeFieldSpecified;
        }
        set
        {
            this.discAuthTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string DiscSecData
    {
        get
        {
            return this.discSecDataField;
        }
        set
        {
            this.discSecDataField = value;
        }
    }

    /// <remarks/>
    public SecDataDowngradeType SecDataDowngrade
    {
        get
        {
            return this.secDataDowngradeField;
        }
        set
        {
            this.secDataDowngradeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SecDataDowngradeSpecified
    {
        get
        {
            return this.secDataDowngradeFieldSpecified;
        }
        set
        {
            this.secDataDowngradeFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class MCGrp
{

    private string banknetDataField;

    private MCMSDIType mCMSDIField;

    private bool mCMSDIFieldSpecified;

    private CCVErrorCodeType cCVErrorCodeField;

    private bool cCVErrorCodeFieldSpecified;

    private POSEntryModeChgType pOSEntryModeChgField;

    private bool pOSEntryModeChgFieldSpecified;

    private string tranEditErrCodeField;

    private string mCPOSDataField;

    private DevTypeIndType devTypeIndField;

    private bool devTypeIndFieldSpecified;

    private MCACIType mCACIField;

    private bool mCACIFieldSpecified;

    private string mCAddDataField;

    private FinAuthIndType finAuthIndField;

    private bool finAuthIndFieldSpecified;

    /// <remarks/>
    public string BanknetData
    {
        get
        {
            return this.banknetDataField;
        }
        set
        {
            this.banknetDataField = value;
        }
    }

    /// <remarks/>
    public MCMSDIType MCMSDI
    {
        get
        {
            return this.mCMSDIField;
        }
        set
        {
            this.mCMSDIField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MCMSDISpecified
    {
        get
        {
            return this.mCMSDIFieldSpecified;
        }
        set
        {
            this.mCMSDIFieldSpecified = value;
        }
    }

    /// <remarks/>
    public CCVErrorCodeType CCVErrorCode
    {
        get
        {
            return this.cCVErrorCodeField;
        }
        set
        {
            this.cCVErrorCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CCVErrorCodeSpecified
    {
        get
        {
            return this.cCVErrorCodeFieldSpecified;
        }
        set
        {
            this.cCVErrorCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public POSEntryModeChgType POSEntryModeChg
    {
        get
        {
            return this.pOSEntryModeChgField;
        }
        set
        {
            this.pOSEntryModeChgField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool POSEntryModeChgSpecified
    {
        get
        {
            return this.pOSEntryModeChgFieldSpecified;
        }
        set
        {
            this.pOSEntryModeChgFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string TranEditErrCode
    {
        get
        {
            return this.tranEditErrCodeField;
        }
        set
        {
            this.tranEditErrCodeField = value;
        }
    }

    /// <remarks/>
    public string MCPOSData
    {
        get
        {
            return this.mCPOSDataField;
        }
        set
        {
            this.mCPOSDataField = value;
        }
    }

    /// <remarks/>
    public DevTypeIndType DevTypeInd
    {
        get
        {
            return this.devTypeIndField;
        }
        set
        {
            this.devTypeIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DevTypeIndSpecified
    {
        get
        {
            return this.devTypeIndFieldSpecified;
        }
        set
        {
            this.devTypeIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public MCACIType MCACI
    {
        get
        {
            return this.mCACIField;
        }
        set
        {
            this.mCACIField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MCACISpecified
    {
        get
        {
            return this.mCACIFieldSpecified;
        }
        set
        {
            this.mCACIFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string MCAddData
    {
        get
        {
            return this.mCAddDataField;
        }
        set
        {
            this.mCAddDataField = value;
        }
    }

    /// <remarks/>
    public FinAuthIndType FinAuthInd
    {
        get
        {
            return this.finAuthIndField;
        }
        set
        {
            this.finAuthIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FinAuthIndSpecified
    {
        get
        {
            return this.finAuthIndFieldSpecified;
        }
        set
        {
            this.finAuthIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class VisaGrp
{

    private ACIType aCIField;

    private bool aCIFieldSpecified;

    private MrktSpecificDataIndType mrktSpecificDataIndField;

    private bool mrktSpecificDataIndFieldSpecified;

    private ExistingDebtIndType existingDebtIndField;

    private bool existingDebtIndFieldSpecified;

    private string cardLevelResultField;

    private string sourceReasonCodeField;

    private string transIDField;

    private string[] visaBIDField;

    private string[] visaAUARField;

    private TaxAmtCapabltType taxAmtCapabltField;

    private bool taxAmtCapabltFieldSpecified;

    private string spendQIndField;

    private CheckoutIndType checkoutIndField;

    private bool checkoutIndFieldSpecified;

    private QCIType qCIField;

    private bool qCIFieldSpecified;

    private VisaAuthIndType visaAuthIndField;

    private bool visaAuthIndFieldSpecified;

    /// <remarks/>
    public ACIType ACI
    {
        get
        {
            return this.aCIField;
        }
        set
        {
            this.aCIField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ACISpecified
    {
        get
        {
            return this.aCIFieldSpecified;
        }
        set
        {
            this.aCIFieldSpecified = value;
        }
    }

    /// <remarks/>
    public MrktSpecificDataIndType MrktSpecificDataInd
    {
        get
        {
            return this.mrktSpecificDataIndField;
        }
        set
        {
            this.mrktSpecificDataIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MrktSpecificDataIndSpecified
    {
        get
        {
            return this.mrktSpecificDataIndFieldSpecified;
        }
        set
        {
            this.mrktSpecificDataIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public ExistingDebtIndType ExistingDebtInd
    {
        get
        {
            return this.existingDebtIndField;
        }
        set
        {
            this.existingDebtIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ExistingDebtIndSpecified
    {
        get
        {
            return this.existingDebtIndFieldSpecified;
        }
        set
        {
            this.existingDebtIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string CardLevelResult
    {
        get
        {
            return this.cardLevelResultField;
        }
        set
        {
            this.cardLevelResultField = value;
        }
    }

    /// <remarks/>
    public string SourceReasonCode
    {
        get
        {
            return this.sourceReasonCodeField;
        }
        set
        {
            this.sourceReasonCodeField = value;
        }
    }

    /// <remarks/>
    public string TransID
    {
        get
        {
            return this.transIDField;
        }
        set
        {
            this.transIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("VisaBID")]
    public string[] VisaBID
    {
        get
        {
            return this.visaBIDField;
        }
        set
        {
            this.visaBIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("VisaAUAR")]
    public string[] VisaAUAR
    {
        get
        {
            return this.visaAUARField;
        }
        set
        {
            this.visaAUARField = value;
        }
    }

    /// <remarks/>
    public TaxAmtCapabltType TaxAmtCapablt
    {
        get
        {
            return this.taxAmtCapabltField;
        }
        set
        {
            this.taxAmtCapabltField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TaxAmtCapabltSpecified
    {
        get
        {
            return this.taxAmtCapabltFieldSpecified;
        }
        set
        {
            this.taxAmtCapabltFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string SpendQInd
    {
        get
        {
            return this.spendQIndField;
        }
        set
        {
            this.spendQIndField = value;
        }
    }

    /// <remarks/>
    public CheckoutIndType CheckoutInd
    {
        get
        {
            return this.checkoutIndField;
        }
        set
        {
            this.checkoutIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CheckoutIndSpecified
    {
        get
        {
            return this.checkoutIndFieldSpecified;
        }
        set
        {
            this.checkoutIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public QCIType QCI
    {
        get
        {
            return this.qCIField;
        }
        set
        {
            this.qCIField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool QCISpecified
    {
        get
        {
            return this.qCIFieldSpecified;
        }
        set
        {
            this.qCIFieldSpecified = value;
        }
    }

    /// <remarks/>
    public VisaAuthIndType VisaAuthInd
    {
        get
        {
            return this.visaAuthIndField;
        }
        set
        {
            this.visaAuthIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool VisaAuthIndSpecified
    {
        get
        {
            return this.visaAuthIndFieldSpecified;
        }
        set
        {
            this.visaAuthIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class RespGrp
{

    private string respCodeField;

    private string authIDField;

    private string responseDateField;

    private string addtlRespDataField;

    private string sttlmDateField;

    private string athNtwkIDField;

    private string athNtwkNmField;

    private string rtIndField;

    private string sigIndField;

    private string errorDataField;

    private string debitTraceNumField;

    /// <remarks/>
    public string RespCode
    {
        get
        {
            return this.respCodeField;
        }
        set
        {
            this.respCodeField = value;
        }
    }

    /// <remarks/>
    public string AuthID
    {
        get
        {
            return this.authIDField;
        }
        set
        {
            this.authIDField = value;
        }
    }

    /// <remarks/>
    public string ResponseDate
    {
        get
        {
            return this.responseDateField;
        }
        set
        {
            this.responseDateField = value;
        }
    }

    /// <remarks/>
    public string AddtlRespData
    {
        get
        {
            return this.addtlRespDataField;
        }
        set
        {
            this.addtlRespDataField = value;
        }
    }

    /// <remarks/>
    public string SttlmDate
    {
        get
        {
            return this.sttlmDateField;
        }
        set
        {
            this.sttlmDateField = value;
        }
    }

    /// <remarks/>
    public string AthNtwkID
    {
        get
        {
            return this.athNtwkIDField;
        }
        set
        {
            this.athNtwkIDField = value;
        }
    }

    /// <remarks/>
    public string AthNtwkNm
    {
        get
        {
            return this.athNtwkNmField;
        }
        set
        {
            this.athNtwkNmField = value;
        }
    }

    /// <remarks/>
    public string RtInd
    {
        get
        {
            return this.rtIndField;
        }
        set
        {
            this.rtIndField = value;
        }
    }

    /// <remarks/>
    public string SigInd
    {
        get
        {
            return this.sigIndField;
        }
        set
        {
            this.sigIndField = value;
        }
    }

    /// <remarks/>
    public string ErrorData
    {
        get
        {
            return this.errorDataField;
        }
        set
        {
            this.errorDataField = value;
        }
    }

    /// <remarks/>
    public string DebitTraceNum
    {
        get
        {
            return this.debitTraceNumField;
        }
        set
        {
            this.debitTraceNumField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class OrigAuthGrp
{

    private string origAuthIDField;

    private string origResponseDateField;

    private string origLocalDateTimeField;

    private string origTranDateTimeField;

    private string origSTANField;

    private string origRespCodeField;

    private string origAthNtwkIDField;

    private string origDebitTraceNumField;

    /// <remarks/>
    public string OrigAuthID
    {
        get
        {
            return this.origAuthIDField;
        }
        set
        {
            this.origAuthIDField = value;
        }
    }

    /// <remarks/>
    public string OrigResponseDate
    {
        get
        {
            return this.origResponseDateField;
        }
        set
        {
            this.origResponseDateField = value;
        }
    }

    /// <remarks/>
    public string OrigLocalDateTime
    {
        get
        {
            return this.origLocalDateTimeField;
        }
        set
        {
            this.origLocalDateTimeField = value;
        }
    }

    /// <remarks/>
    public string OrigTranDateTime
    {
        get
        {
            return this.origTranDateTimeField;
        }
        set
        {
            this.origTranDateTimeField = value;
        }
    }

    /// <remarks/>
    public string OrigSTAN
    {
        get
        {
            return this.origSTANField;
        }
        set
        {
            this.origSTANField = value;
        }
    }

    /// <remarks/>
    public string OrigRespCode
    {
        get
        {
            return this.origRespCodeField;
        }
        set
        {
            this.origRespCodeField = value;
        }
    }

    /// <remarks/>
    public string OrigAthNtwkID
    {
        get
        {
            return this.origAthNtwkIDField;
        }
        set
        {
            this.origAthNtwkIDField = value;
        }
    }

    /// <remarks/>
    public string OrigDebitTraceNum
    {
        get
        {
            return this.origDebitTraceNumField;
        }
        set
        {
            this.origDebitTraceNumField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class MnySndGrp
{

    private MTProcessIndType mTProcessIndField;

    private bool mTProcessIndFieldSpecified;

    private PmntTypeIdType pmntTypeIdField;

    private bool pmntTypeIdFieldSpecified;

    private FndSrcType fndSrcField;

    private bool fndSrcFieldSpecified;

    private string partIDField;

    private string fndRefNumField;

    private string recvFirstNmField;

    private string recvMidInitField;

    private string recvLastNmField;

    private string recvStrAddrField;

    private string recvCityField;

    private RecvStateCodeType recvStateCodeField;

    private bool recvStateCodeFieldSpecified;

    private RecvCtryCodeType recvCtryCodeField;

    private bool recvCtryCodeFieldSpecified;

    private string recvPostalCodeField;

    private string recvPhoneNumberField;

    private string sendFirstNmField;

    private string sendMidInitField;

    private string sendLastNmField;

    private string sendBusNameField;

    private string sendStrAddrField;

    private string sendCityField;

    private SendStateCodeType sendStateCodeField;

    private bool sendStateCodeFieldSpecified;

    private SendCtryCodeType sendCtryCodeField;

    private bool sendCtryCodeFieldSpecified;

    private string sendPostalCodeField;

    private string sendPhoneNumField;

    private string sendDateOfBirthField;

    private string sendAcctNumField;

    /// <remarks/>
    public MTProcessIndType MTProcessInd
    {
        get
        {
            return this.mTProcessIndField;
        }
        set
        {
            this.mTProcessIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MTProcessIndSpecified
    {
        get
        {
            return this.mTProcessIndFieldSpecified;
        }
        set
        {
            this.mTProcessIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public PmntTypeIdType PmntTypeId
    {
        get
        {
            return this.pmntTypeIdField;
        }
        set
        {
            this.pmntTypeIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PmntTypeIdSpecified
    {
        get
        {
            return this.pmntTypeIdFieldSpecified;
        }
        set
        {
            this.pmntTypeIdFieldSpecified = value;
        }
    }

    /// <remarks/>
    public FndSrcType FndSrc
    {
        get
        {
            return this.fndSrcField;
        }
        set
        {
            this.fndSrcField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FndSrcSpecified
    {
        get
        {
            return this.fndSrcFieldSpecified;
        }
        set
        {
            this.fndSrcFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string PartID
    {
        get
        {
            return this.partIDField;
        }
        set
        {
            this.partIDField = value;
        }
    }

    /// <remarks/>
    public string FndRefNum
    {
        get
        {
            return this.fndRefNumField;
        }
        set
        {
            this.fndRefNumField = value;
        }
    }

    /// <remarks/>
    public string RecvFirstNm
    {
        get
        {
            return this.recvFirstNmField;
        }
        set
        {
            this.recvFirstNmField = value;
        }
    }

    /// <remarks/>
    public string RecvMidInit
    {
        get
        {
            return this.recvMidInitField;
        }
        set
        {
            this.recvMidInitField = value;
        }
    }

    /// <remarks/>
    public string RecvLastNm
    {
        get
        {
            return this.recvLastNmField;
        }
        set
        {
            this.recvLastNmField = value;
        }
    }

    /// <remarks/>
    public string RecvStrAddr
    {
        get
        {
            return this.recvStrAddrField;
        }
        set
        {
            this.recvStrAddrField = value;
        }
    }

    /// <remarks/>
    public string RecvCity
    {
        get
        {
            return this.recvCityField;
        }
        set
        {
            this.recvCityField = value;
        }
    }

    /// <remarks/>
    public RecvStateCodeType RecvStateCode
    {
        get
        {
            return this.recvStateCodeField;
        }
        set
        {
            this.recvStateCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RecvStateCodeSpecified
    {
        get
        {
            return this.recvStateCodeFieldSpecified;
        }
        set
        {
            this.recvStateCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public RecvCtryCodeType RecvCtryCode
    {
        get
        {
            return this.recvCtryCodeField;
        }
        set
        {
            this.recvCtryCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RecvCtryCodeSpecified
    {
        get
        {
            return this.recvCtryCodeFieldSpecified;
        }
        set
        {
            this.recvCtryCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string RecvPostalCode
    {
        get
        {
            return this.recvPostalCodeField;
        }
        set
        {
            this.recvPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string RecvPhoneNumber
    {
        get
        {
            return this.recvPhoneNumberField;
        }
        set
        {
            this.recvPhoneNumberField = value;
        }
    }

    /// <remarks/>
    public string SendFirstNm
    {
        get
        {
            return this.sendFirstNmField;
        }
        set
        {
            this.sendFirstNmField = value;
        }
    }

    /// <remarks/>
    public string SendMidInit
    {
        get
        {
            return this.sendMidInitField;
        }
        set
        {
            this.sendMidInitField = value;
        }
    }

    /// <remarks/>
    public string SendLastNm
    {
        get
        {
            return this.sendLastNmField;
        }
        set
        {
            this.sendLastNmField = value;
        }
    }

    /// <remarks/>
    public string SendBusName
    {
        get
        {
            return this.sendBusNameField;
        }
        set
        {
            this.sendBusNameField = value;
        }
    }

    /// <remarks/>
    public string SendStrAddr
    {
        get
        {
            return this.sendStrAddrField;
        }
        set
        {
            this.sendStrAddrField = value;
        }
    }

    /// <remarks/>
    public string SendCity
    {
        get
        {
            return this.sendCityField;
        }
        set
        {
            this.sendCityField = value;
        }
    }

    /// <remarks/>
    public SendStateCodeType SendStateCode
    {
        get
        {
            return this.sendStateCodeField;
        }
        set
        {
            this.sendStateCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SendStateCodeSpecified
    {
        get
        {
            return this.sendStateCodeFieldSpecified;
        }
        set
        {
            this.sendStateCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public SendCtryCodeType SendCtryCode
    {
        get
        {
            return this.sendCtryCodeField;
        }
        set
        {
            this.sendCtryCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SendCtryCodeSpecified
    {
        get
        {
            return this.sendCtryCodeFieldSpecified;
        }
        set
        {
            this.sendCtryCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string SendPostalCode
    {
        get
        {
            return this.sendPostalCodeField;
        }
        set
        {
            this.sendPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string SendPhoneNum
    {
        get
        {
            return this.sendPhoneNumField;
        }
        set
        {
            this.sendPhoneNumField = value;
        }
    }

    /// <remarks/>
    public string SendDateOfBirth
    {
        get
        {
            return this.sendDateOfBirthField;
        }
        set
        {
            this.sendDateOfBirthField = value;
        }
    }

    /// <remarks/>
    public string SendAcctNum
    {
        get
        {
            return this.sendAcctNumField;
        }
        set
        {
            this.sendAcctNumField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AltCNPResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private TAGrp tAGrpField;

    private RespGrp respGrpField;

    private FraudMitRespGrp fraudMitRespGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitRespGrp FraudMitRespGrp
    {
        get
        {
            return this.fraudMitRespGrpField;
        }
        set
        {
            this.fraudMitRespGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class TAGrp
{

    private SctyLvlType sctyLvlField;

    private bool sctyLvlFieldSpecified;

    private EncrptTypeType encrptTypeField;

    private bool encrptTypeFieldSpecified;

    private EncrptTrgtType encrptTrgtField;

    private bool encrptTrgtFieldSpecified;

    private string keyIDField;

    private string encrptBlockField;

    private string tknTypeField;

    private string tknField;

    private string sctyKeyUpdIndField;

    private string tASctyKeyField;

    private string tAExpDateField;

    private string cAKeyIDField;

    private DeviceTypeType deviceTypeField;

    private bool deviceTypeFieldSpecified;

    /// <remarks/>
    public SctyLvlType SctyLvl
    {
        get
        {
            return this.sctyLvlField;
        }
        set
        {
            this.sctyLvlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SctyLvlSpecified
    {
        get
        {
            return this.sctyLvlFieldSpecified;
        }
        set
        {
            this.sctyLvlFieldSpecified = value;
        }
    }

    /// <remarks/>
    public EncrptTypeType EncrptType
    {
        get
        {
            return this.encrptTypeField;
        }
        set
        {
            this.encrptTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool EncrptTypeSpecified
    {
        get
        {
            return this.encrptTypeFieldSpecified;
        }
        set
        {
            this.encrptTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public EncrptTrgtType EncrptTrgt
    {
        get
        {
            return this.encrptTrgtField;
        }
        set
        {
            this.encrptTrgtField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool EncrptTrgtSpecified
    {
        get
        {
            return this.encrptTrgtFieldSpecified;
        }
        set
        {
            this.encrptTrgtFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string KeyID
    {
        get
        {
            return this.keyIDField;
        }
        set
        {
            this.keyIDField = value;
        }
    }

    /// <remarks/>
    public string EncrptBlock
    {
        get
        {
            return this.encrptBlockField;
        }
        set
        {
            this.encrptBlockField = value;
        }
    }

    /// <remarks/>
    public string TknType
    {
        get
        {
            return this.tknTypeField;
        }
        set
        {
            this.tknTypeField = value;
        }
    }

    /// <remarks/>
    public string Tkn
    {
        get
        {
            return this.tknField;
        }
        set
        {
            this.tknField = value;
        }
    }

    /// <remarks/>
    public string SctyKeyUpdInd
    {
        get
        {
            return this.sctyKeyUpdIndField;
        }
        set
        {
            this.sctyKeyUpdIndField = value;
        }
    }

    /// <remarks/>
    public string TASctyKey
    {
        get
        {
            return this.tASctyKeyField;
        }
        set
        {
            this.tASctyKeyField = value;
        }
    }

    /// <remarks/>
    public string TAExpDate
    {
        get
        {
            return this.tAExpDateField;
        }
        set
        {
            this.tAExpDateField = value;
        }
    }

    /// <remarks/>
    public string CAKeyID
    {
        get
        {
            return this.cAKeyIDField;
        }
        set
        {
            this.cAKeyIDField = value;
        }
    }

    /// <remarks/>
    public DeviceTypeType DeviceType
    {
        get
        {
            return this.deviceTypeField;
        }
        set
        {
            this.deviceTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DeviceTypeSpecified
    {
        get
        {
            return this.deviceTypeFieldSpecified;
        }
        set
        {
            this.deviceTypeFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class FraudMitRespGrp
{

    private string zipMtchField;

    private string strMtchField;

    private string nmMtchField;

    private string teleMtchField;

    private string emailMtchField;

    private string addrChgField;

    private string scoreResField;

    private string scoreField;

    private string revAtField;

    /// <remarks/>
    public string ZipMtch
    {
        get
        {
            return this.zipMtchField;
        }
        set
        {
            this.zipMtchField = value;
        }
    }

    /// <remarks/>
    public string StrMtch
    {
        get
        {
            return this.strMtchField;
        }
        set
        {
            this.strMtchField = value;
        }
    }

    /// <remarks/>
    public string NmMtch
    {
        get
        {
            return this.nmMtchField;
        }
        set
        {
            this.nmMtchField = value;
        }
    }

    /// <remarks/>
    public string TeleMtch
    {
        get
        {
            return this.teleMtchField;
        }
        set
        {
            this.teleMtchField = value;
        }
    }

    /// <remarks/>
    public string EmailMtch
    {
        get
        {
            return this.emailMtchField;
        }
        set
        {
            this.emailMtchField = value;
        }
    }

    /// <remarks/>
    public string AddrChg
    {
        get
        {
            return this.addrChgField;
        }
        set
        {
            this.addrChgField = value;
        }
    }

    /// <remarks/>
    public string ScoreRes
    {
        get
        {
            return this.scoreResField;
        }
        set
        {
            this.scoreResField = value;
        }
    }

    /// <remarks/>
    public string Score
    {
        get
        {
            return this.scoreField;
        }
        set
        {
            this.scoreField = value;
        }
    }

    /// <remarks/>
    public string RevAt
    {
        get
        {
            return this.revAtField;
        }
        set
        {
            this.revAtField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class BatchResponseDetails
{

    private CommonGrp commonGrpField;

    private TAGrp tAGrpField;

    private RespGrp respGrpField;

    private BatchGrp batchGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public BatchGrp BatchGrp
    {
        get
        {
            return this.batchGrpField;
        }
        set
        {
            this.batchGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class BatchGrp
{

    private string batchIDField;

    private SettleTxnTypeType settleTxnTypeField;

    private bool settleTxnTypeFieldSpecified;

    private string batchProdCdField;

    private string batchSeqNumField;

    private string batchL3CntField;

    private string batchTktNumField;

    private RPSType rPSField;

    private bool rPSFieldSpecified;

    private string instSeqCtField;

    private string instSeqNumField;

    private PrintIndType printIndField;

    private bool printIndFieldSpecified;

    private string batchCtField;

    private string batchAmtField;

    /// <remarks/>
    public string BatchID
    {
        get
        {
            return this.batchIDField;
        }
        set
        {
            this.batchIDField = value;
        }
    }

    /// <remarks/>
    public SettleTxnTypeType SettleTxnType
    {
        get
        {
            return this.settleTxnTypeField;
        }
        set
        {
            this.settleTxnTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SettleTxnTypeSpecified
    {
        get
        {
            return this.settleTxnTypeFieldSpecified;
        }
        set
        {
            this.settleTxnTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string BatchProdCd
    {
        get
        {
            return this.batchProdCdField;
        }
        set
        {
            this.batchProdCdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
    public string BatchSeqNum
    {
        get
        {
            return this.batchSeqNumField;
        }
        set
        {
            this.batchSeqNumField = value;
        }
    }

    /// <remarks/>
    public string BatchL3Cnt
    {
        get
        {
            return this.batchL3CntField;
        }
        set
        {
            this.batchL3CntField = value;
        }
    }

    /// <remarks/>
    public string BatchTktNum
    {
        get
        {
            return this.batchTktNumField;
        }
        set
        {
            this.batchTktNumField = value;
        }
    }

    /// <remarks/>
    public RPSType RPS
    {
        get
        {
            return this.rPSField;
        }
        set
        {
            this.rPSField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RPSSpecified
    {
        get
        {
            return this.rPSFieldSpecified;
        }
        set
        {
            this.rPSFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
    public string InstSeqCt
    {
        get
        {
            return this.instSeqCtField;
        }
        set
        {
            this.instSeqCtField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
    public string InstSeqNum
    {
        get
        {
            return this.instSeqNumField;
        }
        set
        {
            this.instSeqNumField = value;
        }
    }

    /// <remarks/>
    public PrintIndType PrintInd
    {
        get
        {
            return this.printIndField;
        }
        set
        {
            this.printIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PrintIndSpecified
    {
        get
        {
            return this.printIndFieldSpecified;
        }
        set
        {
            this.printIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
    public string BatchCt
    {
        get
        {
            return this.batchCtField;
        }
        set
        {
            this.batchCtField = value;
        }
    }

    /// <remarks/>
    public string BatchAmt
    {
        get
        {
            return this.batchAmtField;
        }
        set
        {
            this.batchAmtField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class VoidTOReversalResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private TeleCheckECAGrp teleCheckECAGrpField;

    private TCNFTFGrp tCNFTFGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private TknGrp tknGrpField;

    private object itemField;

    private PrvLblGrp prvLblGrpField;

    private EbtGrp[] ebtGrpField;

    private EWICDetGrp[] eWICDetGrpField;

    private DebitGrp debitGrpField;

    private CanDebitGrp canDebitGrpField;

    private StoredValueGrp storedValueGrpField;

    private RespGrp respGrpField;

    private OrigAuthGrp origAuthGrpField;

    private FileDLGrp fileDLGrpField;

    private FltGrp fltGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TeleCheckECAGrp TeleCheckECAGrp
    {
        get
        {
            return this.teleCheckECAGrpField;
        }
        set
        {
            this.teleCheckECAGrpField = value;
        }
    }

    /// <remarks/>
    public TCNFTFGrp TCNFTFGrp
    {
        get
        {
            return this.tCNFTFGrpField;
        }
        set
        {
            this.tCNFTFGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AmexGrp", typeof(AmexGrp))]
    [System.Xml.Serialization.XmlElementAttribute("DSGrp", typeof(DSGrp))]
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public PrvLblGrp PrvLblGrp
    {
        get
        {
            return this.prvLblGrpField;
        }
        set
        {
            this.prvLblGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("EbtGrp")]
    public EbtGrp[] EbtGrp
    {
        get
        {
            return this.ebtGrpField;
        }
        set
        {
            this.ebtGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("EWICDetGrp")]
    public EWICDetGrp[] EWICDetGrp
    {
        get
        {
            return this.eWICDetGrpField;
        }
        set
        {
            this.eWICDetGrpField = value;
        }
    }

    /// <remarks/>
    public DebitGrp DebitGrp
    {
        get
        {
            return this.debitGrpField;
        }
        set
        {
            this.debitGrpField = value;
        }
    }

    /// <remarks/>
    public CanDebitGrp CanDebitGrp
    {
        get
        {
            return this.canDebitGrpField;
        }
        set
        {
            this.canDebitGrpField = value;
        }
    }

    /// <remarks/>
    public StoredValueGrp StoredValueGrp
    {
        get
        {
            return this.storedValueGrpField;
        }
        set
        {
            this.storedValueGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }

    /// <remarks/>
    public FltGrp FltGrp
    {
        get
        {
            return this.fltGrpField;
        }
        set
        {
            this.fltGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class TeleCheckECAGrp
{

    private ChkTypeType chkTypeField;

    private bool chkTypeFieldSpecified;

    private string eCAProdCdField;

    private string dnlRecNumField;

    private string eCAPhnNumField;

    private string eCATrcIDField;

    private string eCABCNField;

    private string respChkNumField;

    private string retChkFeeField;

    private string retChkNoteField;

    private ECAStatusType eCAStatusField;

    private bool eCAStatusFieldSpecified;

    /// <remarks/>
    public ChkTypeType ChkType
    {
        get
        {
            return this.chkTypeField;
        }
        set
        {
            this.chkTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ChkTypeSpecified
    {
        get
        {
            return this.chkTypeFieldSpecified;
        }
        set
        {
            this.chkTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string ECAProdCd
    {
        get
        {
            return this.eCAProdCdField;
        }
        set
        {
            this.eCAProdCdField = value;
        }
    }

    /// <remarks/>
    public string DnlRecNum
    {
        get
        {
            return this.dnlRecNumField;
        }
        set
        {
            this.dnlRecNumField = value;
        }
    }

    /// <remarks/>
    public string ECAPhnNum
    {
        get
        {
            return this.eCAPhnNumField;
        }
        set
        {
            this.eCAPhnNumField = value;
        }
    }

    /// <remarks/>
    public string ECATrcID
    {
        get
        {
            return this.eCATrcIDField;
        }
        set
        {
            this.eCATrcIDField = value;
        }
    }

    /// <remarks/>
    public string ECABCN
    {
        get
        {
            return this.eCABCNField;
        }
        set
        {
            this.eCABCNField = value;
        }
    }

    /// <remarks/>
    public string RespChkNum
    {
        get
        {
            return this.respChkNumField;
        }
        set
        {
            this.respChkNumField = value;
        }
    }

    /// <remarks/>
    public string RetChkFee
    {
        get
        {
            return this.retChkFeeField;
        }
        set
        {
            this.retChkFeeField = value;
        }
    }

    /// <remarks/>
    public string RetChkNote
    {
        get
        {
            return this.retChkNoteField;
        }
        set
        {
            this.retChkNoteField = value;
        }
    }

    /// <remarks/>
    public ECAStatusType ECAStatus
    {
        get
        {
            return this.eCAStatusField;
        }
        set
        {
            this.eCAStatusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ECAStatusSpecified
    {
        get
        {
            return this.eCAStatusFieldSpecified;
        }
        set
        {
            this.eCAStatusFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class TCNFTFGrp
{

    private NFTFChkTypeType nFTFChkTypeField;

    private bool nFTFChkTypeFieldSpecified;

    private string nFTFBnkRtNumField;

    private string nFTFAccNumField;

    private string nFTFChkNumField;

    private string nFTFMICRDataField;

    private string nFTFTraceIdField;

    private string nFTFProdCodeField;

    private NFTFRelTypeType nFTFRelTypeField;

    private bool nFTFRelTypeFieldSpecified;

    private string nFTFGftCrdAmtField;

    private string nFTFPrimIDField;

    private NFTFPrimIDTypeType nFTFPrimIDTypeField;

    private bool nFTFPrimIDTypeFieldSpecified;

    private string nFTFSecIDField;

    private NFTFSecIDTypeType nFTFSecIDTypeField;

    private bool nFTFSecIDTypeFieldSpecified;

    private NFTFVIPIndType nFTFVIPIndField;

    private bool nFTFVIPIndFieldSpecified;

    private string nFTFRegNumField;

    private string nFTFRegDateField;

    private string nFTFMobDevIDField;

    private string nFTFTermIDField;

    private string nFTFSesIDField;

    private NFTFACHStatType nFTFACHStatField;

    private bool nFTFACHStatFieldSpecified;

    private string nFTFDenRecNumField;

    private string nFTFDelShpIdField;

    private string nFTFDelShpExpField;

    private NFTFPreFlagType nFTFPreFlagField;

    private bool nFTFPreFlagFieldSpecified;

    /// <remarks/>
    public NFTFChkTypeType NFTFChkType
    {
        get
        {
            return this.nFTFChkTypeField;
        }
        set
        {
            this.nFTFChkTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NFTFChkTypeSpecified
    {
        get
        {
            return this.nFTFChkTypeFieldSpecified;
        }
        set
        {
            this.nFTFChkTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string NFTFBnkRtNum
    {
        get
        {
            return this.nFTFBnkRtNumField;
        }
        set
        {
            this.nFTFBnkRtNumField = value;
        }
    }

    /// <remarks/>
    public string NFTFAccNum
    {
        get
        {
            return this.nFTFAccNumField;
        }
        set
        {
            this.nFTFAccNumField = value;
        }
    }

    /// <remarks/>
    public string NFTFChkNum
    {
        get
        {
            return this.nFTFChkNumField;
        }
        set
        {
            this.nFTFChkNumField = value;
        }
    }

    /// <remarks/>
    public string NFTFMICRData
    {
        get
        {
            return this.nFTFMICRDataField;
        }
        set
        {
            this.nFTFMICRDataField = value;
        }
    }

    /// <remarks/>
    public string NFTFTraceId
    {
        get
        {
            return this.nFTFTraceIdField;
        }
        set
        {
            this.nFTFTraceIdField = value;
        }
    }

    /// <remarks/>
    public string NFTFProdCode
    {
        get
        {
            return this.nFTFProdCodeField;
        }
        set
        {
            this.nFTFProdCodeField = value;
        }
    }

    /// <remarks/>
    public NFTFRelTypeType NFTFRelType
    {
        get
        {
            return this.nFTFRelTypeField;
        }
        set
        {
            this.nFTFRelTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NFTFRelTypeSpecified
    {
        get
        {
            return this.nFTFRelTypeFieldSpecified;
        }
        set
        {
            this.nFTFRelTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string NFTFGftCrdAmt
    {
        get
        {
            return this.nFTFGftCrdAmtField;
        }
        set
        {
            this.nFTFGftCrdAmtField = value;
        }
    }

    /// <remarks/>
    public string NFTFPrimID
    {
        get
        {
            return this.nFTFPrimIDField;
        }
        set
        {
            this.nFTFPrimIDField = value;
        }
    }

    /// <remarks/>
    public NFTFPrimIDTypeType NFTFPrimIDType
    {
        get
        {
            return this.nFTFPrimIDTypeField;
        }
        set
        {
            this.nFTFPrimIDTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NFTFPrimIDTypeSpecified
    {
        get
        {
            return this.nFTFPrimIDTypeFieldSpecified;
        }
        set
        {
            this.nFTFPrimIDTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string NFTFSecID
    {
        get
        {
            return this.nFTFSecIDField;
        }
        set
        {
            this.nFTFSecIDField = value;
        }
    }

    /// <remarks/>
    public NFTFSecIDTypeType NFTFSecIDType
    {
        get
        {
            return this.nFTFSecIDTypeField;
        }
        set
        {
            this.nFTFSecIDTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NFTFSecIDTypeSpecified
    {
        get
        {
            return this.nFTFSecIDTypeFieldSpecified;
        }
        set
        {
            this.nFTFSecIDTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public NFTFVIPIndType NFTFVIPInd
    {
        get
        {
            return this.nFTFVIPIndField;
        }
        set
        {
            this.nFTFVIPIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NFTFVIPIndSpecified
    {
        get
        {
            return this.nFTFVIPIndFieldSpecified;
        }
        set
        {
            this.nFTFVIPIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string NFTFRegNum
    {
        get
        {
            return this.nFTFRegNumField;
        }
        set
        {
            this.nFTFRegNumField = value;
        }
    }

    /// <remarks/>
    public string NFTFRegDate
    {
        get
        {
            return this.nFTFRegDateField;
        }
        set
        {
            this.nFTFRegDateField = value;
        }
    }

    /// <remarks/>
    public string NFTFMobDevID
    {
        get
        {
            return this.nFTFMobDevIDField;
        }
        set
        {
            this.nFTFMobDevIDField = value;
        }
    }

    /// <remarks/>
    public string NFTFTermID
    {
        get
        {
            return this.nFTFTermIDField;
        }
        set
        {
            this.nFTFTermIDField = value;
        }
    }

    /// <remarks/>
    public string NFTFSesID
    {
        get
        {
            return this.nFTFSesIDField;
        }
        set
        {
            this.nFTFSesIDField = value;
        }
    }

    /// <remarks/>
    public NFTFACHStatType NFTFACHStat
    {
        get
        {
            return this.nFTFACHStatField;
        }
        set
        {
            this.nFTFACHStatField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NFTFACHStatSpecified
    {
        get
        {
            return this.nFTFACHStatFieldSpecified;
        }
        set
        {
            this.nFTFACHStatFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string NFTFDenRecNum
    {
        get
        {
            return this.nFTFDenRecNumField;
        }
        set
        {
            this.nFTFDenRecNumField = value;
        }
    }

    /// <remarks/>
    public string NFTFDelShpId
    {
        get
        {
            return this.nFTFDelShpIdField;
        }
        set
        {
            this.nFTFDelShpIdField = value;
        }
    }

    /// <remarks/>
    public string NFTFDelShpExp
    {
        get
        {
            return this.nFTFDelShpExpField;
        }
        set
        {
            this.nFTFDelShpExpField = value;
        }
    }

    /// <remarks/>
    public NFTFPreFlagType NFTFPreFlag
    {
        get
        {
            return this.nFTFPreFlagField;
        }
        set
        {
            this.nFTFPreFlagField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NFTFPreFlagSpecified
    {
        get
        {
            return this.nFTFPreFlagFieldSpecified;
        }
        set
        {
            this.nFTFPreFlagFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AddtlAmtGrp
{

    private string addAmtField;

    private string addAmtCrncyField;

    private AddAmtTypeType addAmtTypeField;

    private bool addAmtTypeFieldSpecified;

    private string addAmtAcctTypeField;

    private string holdInfoField;

    private PartAuthrztnApprvlCapabltType partAuthrztnApprvlCapabltField;

    private bool partAuthrztnApprvlCapabltFieldSpecified;

    /// <remarks/>
    public string AddAmt
    {
        get
        {
            return this.addAmtField;
        }
        set
        {
            this.addAmtField = value;
        }
    }

    /// <remarks/>
    public string AddAmtCrncy
    {
        get
        {
            return this.addAmtCrncyField;
        }
        set
        {
            this.addAmtCrncyField = value;
        }
    }

    /// <remarks/>
    public AddAmtTypeType AddAmtType
    {
        get
        {
            return this.addAmtTypeField;
        }
        set
        {
            this.addAmtTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AddAmtTypeSpecified
    {
        get
        {
            return this.addAmtTypeFieldSpecified;
        }
        set
        {
            this.addAmtTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string AddAmtAcctType
    {
        get
        {
            return this.addAmtAcctTypeField;
        }
        set
        {
            this.addAmtAcctTypeField = value;
        }
    }

    /// <remarks/>
    public string HoldInfo
    {
        get
        {
            return this.holdInfoField;
        }
        set
        {
            this.holdInfoField = value;
        }
    }

    /// <remarks/>
    public PartAuthrztnApprvlCapabltType PartAuthrztnApprvlCapablt
    {
        get
        {
            return this.partAuthrztnApprvlCapabltField;
        }
        set
        {
            this.partAuthrztnApprvlCapabltField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PartAuthrztnApprvlCapabltSpecified
    {
        get
        {
            return this.partAuthrztnApprvlCapabltFieldSpecified;
        }
        set
        {
            this.partAuthrztnApprvlCapabltFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EMVGrp
{

    private string eMVDataField;

    private string cardSeqNumField;

    private string xCodeRespField;

    private string servCodeField;

    private string appExpDateField;

    private string cARCField;

    private string procIndField;

    private string procInfoField;

    private FinAmtIndType finAmtIndField;

    private bool finAmtIndFieldSpecified;

    /// <remarks/>
    public string EMVData
    {
        get
        {
            return this.eMVDataField;
        }
        set
        {
            this.eMVDataField = value;
        }
    }

    /// <remarks/>
    public string CardSeqNum
    {
        get
        {
            return this.cardSeqNumField;
        }
        set
        {
            this.cardSeqNumField = value;
        }
    }

    /// <remarks/>
    public string XCodeResp
    {
        get
        {
            return this.xCodeRespField;
        }
        set
        {
            this.xCodeRespField = value;
        }
    }

    /// <remarks/>
    public string ServCode
    {
        get
        {
            return this.servCodeField;
        }
        set
        {
            this.servCodeField = value;
        }
    }

    /// <remarks/>
    public string AppExpDate
    {
        get
        {
            return this.appExpDateField;
        }
        set
        {
            this.appExpDateField = value;
        }
    }

    /// <remarks/>
    public string CARC
    {
        get
        {
            return this.cARCField;
        }
        set
        {
            this.cARCField = value;
        }
    }

    /// <remarks/>
    public string ProcInd
    {
        get
        {
            return this.procIndField;
        }
        set
        {
            this.procIndField = value;
        }
    }

    /// <remarks/>
    public string ProcInfo
    {
        get
        {
            return this.procInfoField;
        }
        set
        {
            this.procInfoField = value;
        }
    }

    /// <remarks/>
    public FinAmtIndType FinAmtInd
    {
        get
        {
            return this.finAmtIndField;
        }
        set
        {
            this.finAmtIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FinAmtIndSpecified
    {
        get
        {
            return this.finAmtIndFieldSpecified;
        }
        set
        {
            this.finAmtIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AmexGrp
{

    private string amExPOSDataField;

    private string amExTranIDField;

    private GdSoldCdType gdSoldCdField;

    private bool gdSoldCdFieldSpecified;

    private ReAuthIndType reAuthIndField;

    private bool reAuthIndFieldSpecified;

    /// <remarks/>
    public string AmExPOSData
    {
        get
        {
            return this.amExPOSDataField;
        }
        set
        {
            this.amExPOSDataField = value;
        }
    }

    /// <remarks/>
    public string AmExTranID
    {
        get
        {
            return this.amExTranIDField;
        }
        set
        {
            this.amExTranIDField = value;
        }
    }

    /// <remarks/>
    public GdSoldCdType GdSoldCd
    {
        get
        {
            return this.gdSoldCdField;
        }
        set
        {
            this.gdSoldCdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GdSoldCdSpecified
    {
        get
        {
            return this.gdSoldCdFieldSpecified;
        }
        set
        {
            this.gdSoldCdFieldSpecified = value;
        }
    }

    /// <remarks/>
    public ReAuthIndType ReAuthInd
    {
        get
        {
            return this.reAuthIndField;
        }
        set
        {
            this.reAuthIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ReAuthIndSpecified
    {
        get
        {
            return this.reAuthIndFieldSpecified;
        }
        set
        {
            this.reAuthIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class DSGrp
{

    private string discProcCodeField;

    private string discPOSEntryField;

    private string discRespCodeField;

    private string discPOSDataField;

    private string discTransQualifierField;

    private string discNRIDField;

    private MOTOIndType mOTOIndField;

    private bool mOTOIndFieldSpecified;

    private RegUserIndType regUserIndField;

    private bool regUserIndFieldSpecified;

    private string regUserDateField;

    private DiscAuthIndType discAuthIndField;

    private bool discAuthIndFieldSpecified;

    private PartShipIndType partShipIndField;

    private bool partShipIndFieldSpecified;

    /// <remarks/>
    public string DiscProcCode
    {
        get
        {
            return this.discProcCodeField;
        }
        set
        {
            this.discProcCodeField = value;
        }
    }

    /// <remarks/>
    public string DiscPOSEntry
    {
        get
        {
            return this.discPOSEntryField;
        }
        set
        {
            this.discPOSEntryField = value;
        }
    }

    /// <remarks/>
    public string DiscRespCode
    {
        get
        {
            return this.discRespCodeField;
        }
        set
        {
            this.discRespCodeField = value;
        }
    }

    /// <remarks/>
    public string DiscPOSData
    {
        get
        {
            return this.discPOSDataField;
        }
        set
        {
            this.discPOSDataField = value;
        }
    }

    /// <remarks/>
    public string DiscTransQualifier
    {
        get
        {
            return this.discTransQualifierField;
        }
        set
        {
            this.discTransQualifierField = value;
        }
    }

    /// <remarks/>
    public string DiscNRID
    {
        get
        {
            return this.discNRIDField;
        }
        set
        {
            this.discNRIDField = value;
        }
    }

    /// <remarks/>
    public MOTOIndType MOTOInd
    {
        get
        {
            return this.mOTOIndField;
        }
        set
        {
            this.mOTOIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MOTOIndSpecified
    {
        get
        {
            return this.mOTOIndFieldSpecified;
        }
        set
        {
            this.mOTOIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public RegUserIndType RegUserInd
    {
        get
        {
            return this.regUserIndField;
        }
        set
        {
            this.regUserIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RegUserIndSpecified
    {
        get
        {
            return this.regUserIndFieldSpecified;
        }
        set
        {
            this.regUserIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string RegUserDate
    {
        get
        {
            return this.regUserDateField;
        }
        set
        {
            this.regUserDateField = value;
        }
    }

    /// <remarks/>
    public DiscAuthIndType DiscAuthInd
    {
        get
        {
            return this.discAuthIndField;
        }
        set
        {
            this.discAuthIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DiscAuthIndSpecified
    {
        get
        {
            return this.discAuthIndFieldSpecified;
        }
        set
        {
            this.discAuthIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public PartShipIndType PartShipInd
    {
        get
        {
            return this.partShipIndField;
        }
        set
        {
            this.partShipIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PartShipIndSpecified
    {
        get
        {
            return this.partShipIndFieldSpecified;
        }
        set
        {
            this.partShipIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PrvLblGrp
{

    private string spdPssIDField;

    private string dSTChlngInptField;

    private string dSTChlngRspField;

    private string lgValPrdField;

    private string actAcctNumField;

    private string actExpiryDateField;

    /// <remarks/>
    public string SpdPssID
    {
        get
        {
            return this.spdPssIDField;
        }
        set
        {
            this.spdPssIDField = value;
        }
    }

    /// <remarks/>
    public string DSTChlngInpt
    {
        get
        {
            return this.dSTChlngInptField;
        }
        set
        {
            this.dSTChlngInptField = value;
        }
    }

    /// <remarks/>
    public string DSTChlngRsp
    {
        get
        {
            return this.dSTChlngRspField;
        }
        set
        {
            this.dSTChlngRspField = value;
        }
    }

    /// <remarks/>
    public string LgValPrd
    {
        get
        {
            return this.lgValPrdField;
        }
        set
        {
            this.lgValPrdField = value;
        }
    }

    /// <remarks/>
    public string ActAcctNum
    {
        get
        {
            return this.actAcctNumField;
        }
        set
        {
            this.actAcctNumField = value;
        }
    }

    /// <remarks/>
    public string ActExpiryDate
    {
        get
        {
            return this.actExpiryDateField;
        }
        set
        {
            this.actExpiryDateField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EbtGrp
{

    private EBTTypeType eBTTypeField;

    private bool eBTTypeFieldSpecified;

    private string merchFNSNumField;

    private string eBTCardSeqNumField;

    private string voucherNumField;

    /// <remarks/>
    public EBTTypeType EBTType
    {
        get
        {
            return this.eBTTypeField;
        }
        set
        {
            this.eBTTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool EBTTypeSpecified
    {
        get
        {
            return this.eBTTypeFieldSpecified;
        }
        set
        {
            this.eBTTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string MerchFNSNum
    {
        get
        {
            return this.merchFNSNumField;
        }
        set
        {
            this.merchFNSNumField = value;
        }
    }

    /// <remarks/>
    public string EBTCardSeqNum
    {
        get
        {
            return this.eBTCardSeqNumField;
        }
        set
        {
            this.eBTCardSeqNumField = value;
        }
    }

    /// <remarks/>
    public string VoucherNum
    {
        get
        {
            return this.voucherNumField;
        }
        set
        {
            this.voucherNumField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EWICDetGrp
{

    private UPCPLUIndType uPCPLUIndField;

    private bool uPCPLUIndFieldSpecified;

    private string uPCPLUDataField;

    private string uPCPriceField;

    private string uPCQtyField;

    private string actionCdField;

    private string origPriceField;

    private string origQtyField;

    /// <remarks/>
    public UPCPLUIndType UPCPLUInd
    {
        get
        {
            return this.uPCPLUIndField;
        }
        set
        {
            this.uPCPLUIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool UPCPLUIndSpecified
    {
        get
        {
            return this.uPCPLUIndFieldSpecified;
        }
        set
        {
            this.uPCPLUIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string UPCPLUData
    {
        get
        {
            return this.uPCPLUDataField;
        }
        set
        {
            this.uPCPLUDataField = value;
        }
    }

    /// <remarks/>
    public string UPCPrice
    {
        get
        {
            return this.uPCPriceField;
        }
        set
        {
            this.uPCPriceField = value;
        }
    }

    /// <remarks/>
    public string UPCQty
    {
        get
        {
            return this.uPCQtyField;
        }
        set
        {
            this.uPCQtyField = value;
        }
    }

    /// <remarks/>
    public string ActionCd
    {
        get
        {
            return this.actionCdField;
        }
        set
        {
            this.actionCdField = value;
        }
    }

    /// <remarks/>
    public string OrigPrice
    {
        get
        {
            return this.origPriceField;
        }
        set
        {
            this.origPriceField = value;
        }
    }

    /// <remarks/>
    public string OrigQty
    {
        get
        {
            return this.origQtyField;
        }
        set
        {
            this.origQtyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class DebitGrp
{

    private string payeePhoneNumField;

    private string payeeAcctNumField;

    private string payeeIDField;

    private string billingIndField;

    private string fPIField;

    /// <remarks/>
    public string PayeePhoneNum
    {
        get
        {
            return this.payeePhoneNumField;
        }
        set
        {
            this.payeePhoneNumField = value;
        }
    }

    /// <remarks/>
    public string PayeeAcctNum
    {
        get
        {
            return this.payeeAcctNumField;
        }
        set
        {
            this.payeeAcctNumField = value;
        }
    }

    /// <remarks/>
    public string PayeeID
    {
        get
        {
            return this.payeeIDField;
        }
        set
        {
            this.payeeIDField = value;
        }
    }

    /// <remarks/>
    public string BillingInd
    {
        get
        {
            return this.billingIndField;
        }
        set
        {
            this.billingIndField = value;
        }
    }

    /// <remarks/>
    public string FPI
    {
        get
        {
            return this.fPIField;
        }
        set
        {
            this.fPIField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CanDebitGrp
{

    private CanDebitTransCodeType canDebitTransCodeField;

    private bool canDebitTransCodeFieldSpecified;

    private string canNetRespCodeField;

    private string mACField;

    private string mAWrkKeyChkDgtsField;

    private string mARespCodeField;

    private string mACWrkKeyField;

    private string msgEncrptWrkKeyField;

    private string pINEncrptWrkKeyField;

    /// <remarks/>
    public CanDebitTransCodeType CanDebitTransCode
    {
        get
        {
            return this.canDebitTransCodeField;
        }
        set
        {
            this.canDebitTransCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CanDebitTransCodeSpecified
    {
        get
        {
            return this.canDebitTransCodeFieldSpecified;
        }
        set
        {
            this.canDebitTransCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string CanNetRespCode
    {
        get
        {
            return this.canNetRespCodeField;
        }
        set
        {
            this.canNetRespCodeField = value;
        }
    }

    /// <remarks/>
    public string MAC
    {
        get
        {
            return this.mACField;
        }
        set
        {
            this.mACField = value;
        }
    }

    /// <remarks/>
    public string MAWrkKeyChkDgts
    {
        get
        {
            return this.mAWrkKeyChkDgtsField;
        }
        set
        {
            this.mAWrkKeyChkDgtsField = value;
        }
    }

    /// <remarks/>
    public string MARespCode
    {
        get
        {
            return this.mARespCodeField;
        }
        set
        {
            this.mARespCodeField = value;
        }
    }

    /// <remarks/>
    public string MACWrkKey
    {
        get
        {
            return this.mACWrkKeyField;
        }
        set
        {
            this.mACWrkKeyField = value;
        }
    }

    /// <remarks/>
    public string MsgEncrptWrkKey
    {
        get
        {
            return this.msgEncrptWrkKeyField;
        }
        set
        {
            this.msgEncrptWrkKeyField = value;
        }
    }

    /// <remarks/>
    public string PINEncrptWrkKey
    {
        get
        {
            return this.pINEncrptWrkKeyField;
        }
        set
        {
            this.pINEncrptWrkKeyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class StoredValueGrp
{

    private EschtblTxnType eschtblTxnField;

    private bool eschtblTxnFieldSpecified;

    private string cardClassField;

    private string cardCostField;

    private string fACodeField;

    private string frstTrnNumField;

    private string txnCntField;

    private string txnHstDtlField;

    private string txnRtnField;

    private SVActTypeType sVActTypeField;

    private bool sVActTypeFieldSpecified;

    private string sCVField;

    private string eANField;

    private string promoCodeField;

    private string postDateField;

    /// <remarks/>
    public EschtblTxnType EschtblTxn
    {
        get
        {
            return this.eschtblTxnField;
        }
        set
        {
            this.eschtblTxnField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool EschtblTxnSpecified
    {
        get
        {
            return this.eschtblTxnFieldSpecified;
        }
        set
        {
            this.eschtblTxnFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string CardClass
    {
        get
        {
            return this.cardClassField;
        }
        set
        {
            this.cardClassField = value;
        }
    }

    /// <remarks/>
    public string CardCost
    {
        get
        {
            return this.cardCostField;
        }
        set
        {
            this.cardCostField = value;
        }
    }

    /// <remarks/>
    public string FACode
    {
        get
        {
            return this.fACodeField;
        }
        set
        {
            this.fACodeField = value;
        }
    }

    /// <remarks/>
    public string FrstTrnNum
    {
        get
        {
            return this.frstTrnNumField;
        }
        set
        {
            this.frstTrnNumField = value;
        }
    }

    /// <remarks/>
    public string TxnCnt
    {
        get
        {
            return this.txnCntField;
        }
        set
        {
            this.txnCntField = value;
        }
    }

    /// <remarks/>
    public string TxnHstDtl
    {
        get
        {
            return this.txnHstDtlField;
        }
        set
        {
            this.txnHstDtlField = value;
        }
    }

    /// <remarks/>
    public string TxnRtn
    {
        get
        {
            return this.txnRtnField;
        }
        set
        {
            this.txnRtnField = value;
        }
    }

    /// <remarks/>
    public SVActTypeType SVActType
    {
        get
        {
            return this.sVActTypeField;
        }
        set
        {
            this.sVActTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SVActTypeSpecified
    {
        get
        {
            return this.sVActTypeFieldSpecified;
        }
        set
        {
            this.sVActTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string SCV
    {
        get
        {
            return this.sCVField;
        }
        set
        {
            this.sCVField = value;
        }
    }

    /// <remarks/>
    public string EAN
    {
        get
        {
            return this.eANField;
        }
        set
        {
            this.eANField = value;
        }
    }

    /// <remarks/>
    public string PromoCode
    {
        get
        {
            return this.promoCodeField;
        }
        set
        {
            this.promoCodeField = value;
        }
    }

    /// <remarks/>
    public string PostDate
    {
        get
        {
            return this.postDateField;
        }
        set
        {
            this.postDateField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class FileDLGrp
{

    private FileTypeType fileTypeField;

    private bool fileTypeFieldSpecified;

    private string penFileTypeField;

    private SubFileTypeType subFileTypeField;

    private bool subFileTypeFieldSpecified;

    private string ldSeqNumField;

    private string dMTblVerField;

    private string dBINTblVerField;

    private string dRuTblVerField;

    private string dReTblVerField;

    private string dSAFTblVerField;

    private string dPrTblVerField;

    private string dPdTblVerField;

    private string hMTblVerField;

    private string hBINTblVerField;

    private string hRuTblVerField;

    private string hReTblVerField;

    private string hSAFTblVerField;

    private string hPrTblVerField;

    private string hPdTblVerField;

    private string hWVerField;

    private string sWVerField;

    private string fWVerField;

    private FunCodeType funCodeField;

    private bool funCodeFieldSpecified;

    private string currFileCreationDtField;

    private string fileSizeField;

    private string fileCRC16Field;

    private string ldCtrlKeyField;

    private string fBSeqField;

    private string reqFBMaxSizeField;

    private string reqFileOffsetField;

    private string nextFileDLOffsetField;

    private string fBDataField;

    private string tLMerchNameField;

    private string tLStNumField;

    private string tLMerchAddrField;

    private string tLMerchCityField;

    private string tLMerchStateField;

    private string tLMerchPostalCodeField;

    private string tLSAFBlkField;

    private string tTDayField;

    private string tTDateTimeField;

    private string tTCutTimeField;

    private string tTPasswordField;

    private string rctTxtStrDtField;

    private string rctTxtEndDtField;

    /// <remarks/>
    public FileTypeType FileType
    {
        get
        {
            return this.fileTypeField;
        }
        set
        {
            this.fileTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FileTypeSpecified
    {
        get
        {
            return this.fileTypeFieldSpecified;
        }
        set
        {
            this.fileTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string PenFileType
    {
        get
        {
            return this.penFileTypeField;
        }
        set
        {
            this.penFileTypeField = value;
        }
    }

    /// <remarks/>
    public SubFileTypeType SubFileType
    {
        get
        {
            return this.subFileTypeField;
        }
        set
        {
            this.subFileTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SubFileTypeSpecified
    {
        get
        {
            return this.subFileTypeFieldSpecified;
        }
        set
        {
            this.subFileTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string LdSeqNum
    {
        get
        {
            return this.ldSeqNumField;
        }
        set
        {
            this.ldSeqNumField = value;
        }
    }

    /// <remarks/>
    public string DMTblVer
    {
        get
        {
            return this.dMTblVerField;
        }
        set
        {
            this.dMTblVerField = value;
        }
    }

    /// <remarks/>
    public string DBINTblVer
    {
        get
        {
            return this.dBINTblVerField;
        }
        set
        {
            this.dBINTblVerField = value;
        }
    }

    /// <remarks/>
    public string DRuTblVer
    {
        get
        {
            return this.dRuTblVerField;
        }
        set
        {
            this.dRuTblVerField = value;
        }
    }

    /// <remarks/>
    public string DReTblVer
    {
        get
        {
            return this.dReTblVerField;
        }
        set
        {
            this.dReTblVerField = value;
        }
    }

    /// <remarks/>
    public string DSAFTblVer
    {
        get
        {
            return this.dSAFTblVerField;
        }
        set
        {
            this.dSAFTblVerField = value;
        }
    }

    /// <remarks/>
    public string DPrTblVer
    {
        get
        {
            return this.dPrTblVerField;
        }
        set
        {
            this.dPrTblVerField = value;
        }
    }

    /// <remarks/>
    public string DPdTblVer
    {
        get
        {
            return this.dPdTblVerField;
        }
        set
        {
            this.dPdTblVerField = value;
        }
    }

    /// <remarks/>
    public string HMTblVer
    {
        get
        {
            return this.hMTblVerField;
        }
        set
        {
            this.hMTblVerField = value;
        }
    }

    /// <remarks/>
    public string HBINTblVer
    {
        get
        {
            return this.hBINTblVerField;
        }
        set
        {
            this.hBINTblVerField = value;
        }
    }

    /// <remarks/>
    public string HRuTblVer
    {
        get
        {
            return this.hRuTblVerField;
        }
        set
        {
            this.hRuTblVerField = value;
        }
    }

    /// <remarks/>
    public string HReTblVer
    {
        get
        {
            return this.hReTblVerField;
        }
        set
        {
            this.hReTblVerField = value;
        }
    }

    /// <remarks/>
    public string HSAFTblVer
    {
        get
        {
            return this.hSAFTblVerField;
        }
        set
        {
            this.hSAFTblVerField = value;
        }
    }

    /// <remarks/>
    public string HPrTblVer
    {
        get
        {
            return this.hPrTblVerField;
        }
        set
        {
            this.hPrTblVerField = value;
        }
    }

    /// <remarks/>
    public string HPdTblVer
    {
        get
        {
            return this.hPdTblVerField;
        }
        set
        {
            this.hPdTblVerField = value;
        }
    }

    /// <remarks/>
    public string HWVer
    {
        get
        {
            return this.hWVerField;
        }
        set
        {
            this.hWVerField = value;
        }
    }

    /// <remarks/>
    public string SWVer
    {
        get
        {
            return this.sWVerField;
        }
        set
        {
            this.sWVerField = value;
        }
    }

    /// <remarks/>
    public string FWVer
    {
        get
        {
            return this.fWVerField;
        }
        set
        {
            this.fWVerField = value;
        }
    }

    /// <remarks/>
    public FunCodeType FunCode
    {
        get
        {
            return this.funCodeField;
        }
        set
        {
            this.funCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FunCodeSpecified
    {
        get
        {
            return this.funCodeFieldSpecified;
        }
        set
        {
            this.funCodeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string CurrFileCreationDt
    {
        get
        {
            return this.currFileCreationDtField;
        }
        set
        {
            this.currFileCreationDtField = value;
        }
    }

    /// <remarks/>
    public string FileSize
    {
        get
        {
            return this.fileSizeField;
        }
        set
        {
            this.fileSizeField = value;
        }
    }

    /// <remarks/>
    public string FileCRC16
    {
        get
        {
            return this.fileCRC16Field;
        }
        set
        {
            this.fileCRC16Field = value;
        }
    }

    /// <remarks/>
    public string LdCtrlKey
    {
        get
        {
            return this.ldCtrlKeyField;
        }
        set
        {
            this.ldCtrlKeyField = value;
        }
    }

    /// <remarks/>
    public string FBSeq
    {
        get
        {
            return this.fBSeqField;
        }
        set
        {
            this.fBSeqField = value;
        }
    }

    /// <remarks/>
    public string ReqFBMaxSize
    {
        get
        {
            return this.reqFBMaxSizeField;
        }
        set
        {
            this.reqFBMaxSizeField = value;
        }
    }

    /// <remarks/>
    public string ReqFileOffset
    {
        get
        {
            return this.reqFileOffsetField;
        }
        set
        {
            this.reqFileOffsetField = value;
        }
    }

    /// <remarks/>
    public string NextFileDLOffset
    {
        get
        {
            return this.nextFileDLOffsetField;
        }
        set
        {
            this.nextFileDLOffsetField = value;
        }
    }

    /// <remarks/>
    public string FBData
    {
        get
        {
            return this.fBDataField;
        }
        set
        {
            this.fBDataField = value;
        }
    }

    /// <remarks/>
    public string TLMerchName
    {
        get
        {
            return this.tLMerchNameField;
        }
        set
        {
            this.tLMerchNameField = value;
        }
    }

    /// <remarks/>
    public string TLStNum
    {
        get
        {
            return this.tLStNumField;
        }
        set
        {
            this.tLStNumField = value;
        }
    }

    /// <remarks/>
    public string TLMerchAddr
    {
        get
        {
            return this.tLMerchAddrField;
        }
        set
        {
            this.tLMerchAddrField = value;
        }
    }

    /// <remarks/>
    public string TLMerchCity
    {
        get
        {
            return this.tLMerchCityField;
        }
        set
        {
            this.tLMerchCityField = value;
        }
    }

    /// <remarks/>
    public string TLMerchState
    {
        get
        {
            return this.tLMerchStateField;
        }
        set
        {
            this.tLMerchStateField = value;
        }
    }

    /// <remarks/>
    public string TLMerchPostalCode
    {
        get
        {
            return this.tLMerchPostalCodeField;
        }
        set
        {
            this.tLMerchPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string TLSAFBlk
    {
        get
        {
            return this.tLSAFBlkField;
        }
        set
        {
            this.tLSAFBlkField = value;
        }
    }

    /// <remarks/>
    public string TTDay
    {
        get
        {
            return this.tTDayField;
        }
        set
        {
            this.tTDayField = value;
        }
    }

    /// <remarks/>
    public string TTDateTime
    {
        get
        {
            return this.tTDateTimeField;
        }
        set
        {
            this.tTDateTimeField = value;
        }
    }

    /// <remarks/>
    public string TTCutTime
    {
        get
        {
            return this.tTCutTimeField;
        }
        set
        {
            this.tTCutTimeField = value;
        }
    }

    /// <remarks/>
    public string TTPassword
    {
        get
        {
            return this.tTPasswordField;
        }
        set
        {
            this.tTPasswordField = value;
        }
    }

    /// <remarks/>
    public string RctTxtStrDt
    {
        get
        {
            return this.rctTxtStrDtField;
        }
        set
        {
            this.rctTxtStrDtField = value;
        }
    }

    /// <remarks/>
    public string RctTxtEndDt
    {
        get
        {
            return this.rctTxtEndDtField;
        }
        set
        {
            this.rctTxtEndDtField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class FltGrp
{

    private string odoField;

    private string vehNumField;

    private string jobNumField;

    private string drvNumField;

    private string fltEmpNumField;

    private string licNumField;

    private string jobIDField;

    private string deptNumField;

    private string custDataField;

    private string userIDField;

    private string vehIDNumField;

    private string servResCdField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
    public string Odo
    {
        get
        {
            return this.odoField;
        }
        set
        {
            this.odoField = value;
        }
    }

    /// <remarks/>
    public string VehNum
    {
        get
        {
            return this.vehNumField;
        }
        set
        {
            this.vehNumField = value;
        }
    }

    /// <remarks/>
    public string JobNum
    {
        get
        {
            return this.jobNumField;
        }
        set
        {
            this.jobNumField = value;
        }
    }

    /// <remarks/>
    public string DrvNum
    {
        get
        {
            return this.drvNumField;
        }
        set
        {
            this.drvNumField = value;
        }
    }

    /// <remarks/>
    public string FltEmpNum
    {
        get
        {
            return this.fltEmpNumField;
        }
        set
        {
            this.fltEmpNumField = value;
        }
    }

    /// <remarks/>
    public string LicNum
    {
        get
        {
            return this.licNumField;
        }
        set
        {
            this.licNumField = value;
        }
    }

    /// <remarks/>
    public string JobID
    {
        get
        {
            return this.jobIDField;
        }
        set
        {
            this.jobIDField = value;
        }
    }

    /// <remarks/>
    public string DeptNum
    {
        get
        {
            return this.deptNumField;
        }
        set
        {
            this.deptNumField = value;
        }
    }

    /// <remarks/>
    public string CustData
    {
        get
        {
            return this.custDataField;
        }
        set
        {
            this.custDataField = value;
        }
    }

    /// <remarks/>
    public string UserID
    {
        get
        {
            return this.userIDField;
        }
        set
        {
            this.userIDField = value;
        }
    }

    /// <remarks/>
    public string VehIDNum
    {
        get
        {
            return this.vehIDNumField;
        }
        set
        {
            this.vehIDNumField = value;
        }
    }

    /// <remarks/>
    public string ServResCd
    {
        get
        {
            return this.servResCdField;
        }
        set
        {
            this.servResCdField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class RejectResponseDetails
{

    private CommonGrp commonGrpField;

    private RespGrp respGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class HostDiscDetGrp
{

    private string hDStrDtField;

    private string hDEndDtField;

    private string hDCardTypeField;

    private string hDBINBegField;

    private string hDBINEndField;

    private string hDProdCdField;

    private string hDProdDiscAmtField;

    private string hDDiscTypeField;

    private string hDQtyLtField;

    private string hDProgDescField;

    /// <remarks/>
    public string HDStrDt
    {
        get
        {
            return this.hDStrDtField;
        }
        set
        {
            this.hDStrDtField = value;
        }
    }

    /// <remarks/>
    public string HDEndDt
    {
        get
        {
            return this.hDEndDtField;
        }
        set
        {
            this.hDEndDtField = value;
        }
    }

    /// <remarks/>
    public string HDCardType
    {
        get
        {
            return this.hDCardTypeField;
        }
        set
        {
            this.hDCardTypeField = value;
        }
    }

    /// <remarks/>
    public string HDBINBeg
    {
        get
        {
            return this.hDBINBegField;
        }
        set
        {
            this.hDBINBegField = value;
        }
    }

    /// <remarks/>
    public string HDBINEnd
    {
        get
        {
            return this.hDBINEndField;
        }
        set
        {
            this.hDBINEndField = value;
        }
    }

    /// <remarks/>
    public string HDProdCd
    {
        get
        {
            return this.hDProdCdField;
        }
        set
        {
            this.hDProdCdField = value;
        }
    }

    /// <remarks/>
    public string HDProdDiscAmt
    {
        get
        {
            return this.hDProdDiscAmtField;
        }
        set
        {
            this.hDProdDiscAmtField = value;
        }
    }

    /// <remarks/>
    public string HDDiscType
    {
        get
        {
            return this.hDDiscTypeField;
        }
        set
        {
            this.hDDiscTypeField = value;
        }
    }

    /// <remarks/>
    public string HDQtyLt
    {
        get
        {
            return this.hDQtyLtField;
        }
        set
        {
            this.hDQtyLtField = value;
        }
    }

    /// <remarks/>
    public string HDProgDesc
    {
        get
        {
            return this.hDProgDescField;
        }
        set
        {
            this.hDProgDescField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class RctTxtDetGrp
{

    private string rctTxtDataField;

    /// <remarks/>
    public string RctTxtData
    {
        get
        {
            return this.rctTxtDataField;
        }
        set
        {
            this.rctTxtDataField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class TLGrp
{

    private string tLCardTypeField;

    /// <remarks/>
    public string TLCardType
    {
        get
        {
            return this.tLCardTypeField;
        }
        set
        {
            this.tLCardTypeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AdminResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private CanDebitGrp canDebitGrpField;

    private StoredValueGrp storedValueGrpField;

    private RespGrp respGrpField;

    private HostTotGrp hostTotGrpField;

    private HostTotDetGrp[] hostTotDetGrpField;

    private FileDLGrp fileDLGrpField;

    private TLGrp[] tLGrpField;

    private RctTxtDetGrp[] rctTxtDetGrpField;

    private HostDiscDetGrp[] hostDiscDetGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public CanDebitGrp CanDebitGrp
    {
        get
        {
            return this.canDebitGrpField;
        }
        set
        {
            this.canDebitGrpField = value;
        }
    }

    /// <remarks/>
    public StoredValueGrp StoredValueGrp
    {
        get
        {
            return this.storedValueGrpField;
        }
        set
        {
            this.storedValueGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public HostTotGrp HostTotGrp
    {
        get
        {
            return this.hostTotGrpField;
        }
        set
        {
            this.hostTotGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("HostTotDetGrp")]
    public HostTotDetGrp[] HostTotDetGrp
    {
        get
        {
            return this.hostTotDetGrpField;
        }
        set
        {
            this.hostTotDetGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("TLGrp")]
    public TLGrp[] TLGrp
    {
        get
        {
            return this.tLGrpField;
        }
        set
        {
            this.tLGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("RctTxtDetGrp")]
    public RctTxtDetGrp[] RctTxtDetGrp
    {
        get
        {
            return this.rctTxtDetGrpField;
        }
        set
        {
            this.rctTxtDetGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("HostDiscDetGrp")]
    public HostDiscDetGrp[] HostDiscDetGrp
    {
        get
        {
            return this.hostDiscDetGrpField;
        }
        set
        {
            this.hostDiscDetGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class HostTotGrp
{

    private TotReqDateType totReqDateField;

    private bool totReqDateFieldSpecified;

    private string passwordField;

    private string netSettleAmtField;

    private string feeAmtField;

    private string hTSettleDateField;

    /// <remarks/>
    public TotReqDateType TotReqDate
    {
        get
        {
            return this.totReqDateField;
        }
        set
        {
            this.totReqDateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TotReqDateSpecified
    {
        get
        {
            return this.totReqDateFieldSpecified;
        }
        set
        {
            this.totReqDateFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string Password
    {
        get
        {
            return this.passwordField;
        }
        set
        {
            this.passwordField = value;
        }
    }

    /// <remarks/>
    public string NetSettleAmt
    {
        get
        {
            return this.netSettleAmtField;
        }
        set
        {
            this.netSettleAmtField = value;
        }
    }

    /// <remarks/>
    public string FeeAmt
    {
        get
        {
            return this.feeAmtField;
        }
        set
        {
            this.feeAmtField = value;
        }
    }

    /// <remarks/>
    public string HTSettleDate
    {
        get
        {
            return this.hTSettleDateField;
        }
        set
        {
            this.hTSettleDateField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class HostTotDetGrp
{

    private CardTagType cardTagField;

    private bool cardTagFieldSpecified;

    private string txnCtField;

    private string cardTxnAmtField;

    private string sumPymtTypeTagField;

    /// <remarks/>
    public CardTagType CardTag
    {
        get
        {
            return this.cardTagField;
        }
        set
        {
            this.cardTagField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CardTagSpecified
    {
        get
        {
            return this.cardTagFieldSpecified;
        }
        set
        {
            this.cardTagFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string TxnCt
    {
        get
        {
            return this.txnCtField;
        }
        set
        {
            this.txnCtField = value;
        }
    }

    /// <remarks/>
    public string CardTxnAmt
    {
        get
        {
            return this.cardTxnAmtField;
        }
        set
        {
            this.cardTxnAmtField = value;
        }
    }

    /// <remarks/>
    public string SumPymtTypeTag
    {
        get
        {
            return this.sumPymtTypeTagField;
        }
        set
        {
            this.sumPymtTypeTagField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class TAResponseDetails
{

    private CommonGrp commonGrpField;

    private TAGrp tAGrpField;

    private RespGrp respGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class FleetResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private object itemField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    private FltGrp fltGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }

    /// <remarks/>
    public FltGrp FltGrp
    {
        get
        {
            return this.fltGrpField;
        }
        set
        {
            this.fltGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PrivateLabelResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private object itemField;

    private PrvLblGrp prvLblGrpField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AmexGrp", typeof(AmexGrp))]
    [System.Xml.Serialization.XmlElementAttribute("DSGrp", typeof(DSGrp))]
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public PrvLblGrp PrvLblGrp
    {
        get
        {
            return this.prvLblGrpField;
        }
        set
        {
            this.prvLblGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class GenPrepaidResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PrepaidResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private StoredValueGrp storedValueGrpField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    private FraudMitRespGrp fraudMitRespGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public StoredValueGrp StoredValueGrp
    {
        get
        {
            return this.storedValueGrpField;
        }
        set
        {
            this.storedValueGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitRespGrp FraudMitRespGrp
    {
        get
        {
            return this.fraudMitRespGrpField;
        }
        set
        {
            this.fraudMitRespGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EWICBalInfoGrp
{

    private string pBCatCdField;

    private string pBSubCatCdField;

    private string pBQtyField;

    /// <remarks/>
    public string PBCatCd
    {
        get
        {
            return this.pBCatCdField;
        }
        set
        {
            this.pBCatCdField = value;
        }
    }

    /// <remarks/>
    public string PBSubCatCd
    {
        get
        {
            return this.pBSubCatCdField;
        }
        set
        {
            this.pBSubCatCdField = value;
        }
    }

    /// <remarks/>
    public string PBQty
    {
        get
        {
            return this.pBQtyField;
        }
        set
        {
            this.pBQtyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EWICGrp
{

    private string eBenExDtField;

    /// <remarks/>
    public string EBenExDt
    {
        get
        {
            return this.eBenExDtField;
        }
        set
        {
            this.eBenExDtField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EBTResponseDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private EbtGrp ebtGrpField;

    private EWICGrp eWICGrpField;

    private EWICBalInfoGrp[] eWICBalInfoGrpField;

    private EWICDetGrp[] eWICDetGrpField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public EbtGrp EbtGrp
    {
        get
        {
            return this.ebtGrpField;
        }
        set
        {
            this.ebtGrpField = value;
        }
    }

    /// <remarks/>
    public EWICGrp EWICGrp
    {
        get
        {
            return this.eWICGrpField;
        }
        set
        {
            this.eWICGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("EWICBalInfoGrp")]
    public EWICBalInfoGrp[] EWICBalInfoGrp
    {
        get
        {
            return this.eWICBalInfoGrpField;
        }
        set
        {
            this.eWICBalInfoGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("EWICDetGrp")]
    public EWICDetGrp[] EWICDetGrp
    {
        get
        {
            return this.eWICDetGrpField;
        }
        set
        {
            this.eWICDetGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CheckResponseDetails
{

    private CommonGrp commonGrpField;

    private TeleCheckECAGrp teleCheckECAGrpField;

    private TCNFTFGrp tCNFTFGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    private FraudMitRespGrp fraudMitRespGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public TeleCheckECAGrp TeleCheckECAGrp
    {
        get
        {
            return this.teleCheckECAGrpField;
        }
        set
        {
            this.teleCheckECAGrpField = value;
        }
    }

    /// <remarks/>
    public TCNFTFGrp TCNFTFGrp
    {
        get
        {
            return this.tCNFTFGrpField;
        }
        set
        {
            this.tCNFTFGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitRespGrp FraudMitRespGrp
    {
        get
        {
            return this.fraudMitRespGrpField;
        }
        set
        {
            this.fraudMitRespGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PinlessDebitResponseDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private DebitGrp debitGrpField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    private FraudMitRespGrp fraudMitRespGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public DebitGrp DebitGrp
    {
        get
        {
            return this.debitGrpField;
        }
        set
        {
            this.debitGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitRespGrp FraudMitRespGrp
    {
        get
        {
            return this.fraudMitRespGrpField;
        }
        set
        {
            this.fraudMitRespGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class BillPayGrp
{

    private BillPymtTxnIndType billPymtTxnIndField;

    private bool billPymtTxnIndFieldSpecified;

    private string merchAdviceCodeField;

    /// <remarks/>
    public BillPymtTxnIndType BillPymtTxnInd
    {
        get
        {
            return this.billPymtTxnIndField;
        }
        set
        {
            this.billPymtTxnIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool BillPymtTxnIndSpecified
    {
        get
        {
            return this.billPymtTxnIndFieldSpecified;
        }
        set
        {
            this.billPymtTxnIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string MerchAdviceCode
    {
        get
        {
            return this.merchAdviceCodeField;
        }
        set
        {
            this.merchAdviceCodeField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class DebitResponseDetails
{

    private CommonGrp commonGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private DebitGrp debitGrpField;

    private CanDebitGrp canDebitGrpField;

    private TknGrp tknGrpField;

    private OfferGrp offerGrpField;

    private RespGrp respGrpField;

    private FileDLGrp fileDLGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public DebitGrp DebitGrp
    {
        get
        {
            return this.debitGrpField;
        }
        set
        {
            this.debitGrpField = value;
        }
    }

    /// <remarks/>
    public CanDebitGrp CanDebitGrp
    {
        get
        {
            return this.canDebitGrpField;
        }
        set
        {
            this.canDebitGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public OfferGrp OfferGrp
    {
        get
        {
            return this.offerGrpField;
        }
        set
        {
            this.offerGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class OfferGrp
{

    private POSOfferCapType pOSOfferCapField;

    private bool pOSOfferCapFieldSpecified;

    private string offerDescField;

    private string offerResIndField;

    private string offerIDField;

    private string offerProvNameField;

    private string offerAmountField;

    private string receiptCopyField;

    private string varCustRctTxtField;

    private string offerProvIDField;

    private string offerPubIDField;

    private string offerPubNameField;

    /// <remarks/>
    public POSOfferCapType POSOfferCap
    {
        get
        {
            return this.pOSOfferCapField;
        }
        set
        {
            this.pOSOfferCapField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool POSOfferCapSpecified
    {
        get
        {
            return this.pOSOfferCapFieldSpecified;
        }
        set
        {
            this.pOSOfferCapFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string OfferDesc
    {
        get
        {
            return this.offerDescField;
        }
        set
        {
            this.offerDescField = value;
        }
    }

    /// <remarks/>
    public string OfferResInd
    {
        get
        {
            return this.offerResIndField;
        }
        set
        {
            this.offerResIndField = value;
        }
    }

    /// <remarks/>
    public string OfferID
    {
        get
        {
            return this.offerIDField;
        }
        set
        {
            this.offerIDField = value;
        }
    }

    /// <remarks/>
    public string OfferProvName
    {
        get
        {
            return this.offerProvNameField;
        }
        set
        {
            this.offerProvNameField = value;
        }
    }

    /// <remarks/>
    public string OfferAmount
    {
        get
        {
            return this.offerAmountField;
        }
        set
        {
            this.offerAmountField = value;
        }
    }

    /// <remarks/>
    public string ReceiptCopy
    {
        get
        {
            return this.receiptCopyField;
        }
        set
        {
            this.receiptCopyField = value;
        }
    }

    /// <remarks/>
    public string VarCustRctTxt
    {
        get
        {
            return this.varCustRctTxtField;
        }
        set
        {
            this.varCustRctTxtField = value;
        }
    }

    /// <remarks/>
    public string OfferProvID
    {
        get
        {
            return this.offerProvIDField;
        }
        set
        {
            this.offerProvIDField = value;
        }
    }

    /// <remarks/>
    public string OfferPubID
    {
        get
        {
            return this.offerPubIDField;
        }
        set
        {
            this.offerPubIDField = value;
        }
    }

    /// <remarks/>
    public string OfferPubName
    {
        get
        {
            return this.offerPubNameField;
        }
        set
        {
            this.offerPubNameField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CardInfoRespGrp
{

    private string issBankField;

    private string issCtryCodeField;

    private string cardBrndField;

    private string cardIndField;

    private string detProdIDField;

    /// <remarks/>
    public string IssBank
    {
        get
        {
            return this.issBankField;
        }
        set
        {
            this.issBankField = value;
        }
    }

    /// <remarks/>
    public string IssCtryCode
    {
        get
        {
            return this.issCtryCodeField;
        }
        set
        {
            this.issCtryCodeField = value;
        }
    }

    /// <remarks/>
    public string CardBrnd
    {
        get
        {
            return this.cardBrndField;
        }
        set
        {
            this.cardBrndField = value;
        }
    }

    /// <remarks/>
    public string CardInd
    {
        get
        {
            return this.cardIndField;
        }
        set
        {
            this.cardIndField = value;
        }
    }

    /// <remarks/>
    public string DetProdID
    {
        get
        {
            return this.detProdIDField;
        }
        set
        {
            this.detProdIDField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CreditResponseDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private TknGrp tknGrpField;

    private OfferGrp offerGrpField;

    private EcommGrp ecommGrpField;

    private SecrTxnGrp secrTxnGrpField;

    private object itemField;

    private CustInfoGrp custInfoGrpField;

    private SAGrp sAGrpField;

    private RespGrp respGrpField;

    private CardInfoRespGrp cardInfoRespGrpField;

    private OrigAuthGrp origAuthGrpField;

    private FileDLGrp fileDLGrpField;

    private FraudMitRespGrp fraudMitRespGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public OfferGrp OfferGrp
    {
        get
        {
            return this.offerGrpField;
        }
        set
        {
            this.offerGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public SecrTxnGrp SecrTxnGrp
    {
        get
        {
            return this.secrTxnGrpField;
        }
        set
        {
            this.secrTxnGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AmexGrp", typeof(AmexGrp))]
    [System.Xml.Serialization.XmlElementAttribute("DSGrp", typeof(DSGrp))]
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public SAGrp SAGrp
    {
        get
        {
            return this.sAGrpField;
        }
        set
        {
            this.sAGrpField = value;
        }
    }

    /// <remarks/>
    public RespGrp RespGrp
    {
        get
        {
            return this.respGrpField;
        }
        set
        {
            this.respGrpField = value;
        }
    }

    /// <remarks/>
    public CardInfoRespGrp CardInfoRespGrp
    {
        get
        {
            return this.cardInfoRespGrpField;
        }
        set
        {
            this.cardInfoRespGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public FileDLGrp FileDLGrp
    {
        get
        {
            return this.fileDLGrpField;
        }
        set
        {
            this.fileDLGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitRespGrp FraudMitRespGrp
    {
        get
        {
            return this.fraudMitRespGrpField;
        }
        set
        {
            this.fraudMitRespGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EcommGrp
{

    private EcommTxnIndType ecommTxnIndField;

    private bool ecommTxnIndFieldSpecified;

    private string custSvcPhoneNumberField;

    private string ecommURLField;

    private string mCSNField;

    private string mCSCField;

    /// <remarks/>
    public EcommTxnIndType EcommTxnInd
    {
        get
        {
            return this.ecommTxnIndField;
        }
        set
        {
            this.ecommTxnIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool EcommTxnIndSpecified
    {
        get
        {
            return this.ecommTxnIndFieldSpecified;
        }
        set
        {
            this.ecommTxnIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string CustSvcPhoneNumber
    {
        get
        {
            return this.custSvcPhoneNumberField;
        }
        set
        {
            this.custSvcPhoneNumberField = value;
        }
    }

    /// <remarks/>
    public string EcommURL
    {
        get
        {
            return this.ecommURLField;
        }
        set
        {
            this.ecommURLField = value;
        }
    }

    /// <remarks/>
    public string MCSN
    {
        get
        {
            return this.mCSNField;
        }
        set
        {
            this.mCSNField = value;
        }
    }

    /// <remarks/>
    public string MCSC
    {
        get
        {
            return this.mCSCField;
        }
        set
        {
            this.mCSCField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CustInfoGrp
{

    private string aVSBillingAddrField;

    private string aVSBillingPostalCodeField;

    private string cHFirstNmField;

    private string cHLastNmField;

    private CHFullNmResType cHFullNmResField;

    private bool cHFullNmResFieldSpecified;

    private string dateOfBirthField;

    private string custEmailAddrField;

    private string giftEmailAddrField;

    private string custIPAddrField;

    private string custHostNameField;

    private string custBrwsrNameField;

    private string custANIField;

    private string custANIIIField;

    private string billingAddr2Field;

    private string billingCityField;

    private string billingStateField;

    private string billingCtryField;

    private string custTelNumField;

    private CustTelTypeType custTelTypeField;

    private bool custTelTypeFieldSpecified;

    /// <remarks/>
    public string AVSBillingAddr
    {
        get
        {
            return this.aVSBillingAddrField;
        }
        set
        {
            this.aVSBillingAddrField = value;
        }
    }

    /// <remarks/>
    public string AVSBillingPostalCode
    {
        get
        {
            return this.aVSBillingPostalCodeField;
        }
        set
        {
            this.aVSBillingPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string CHFirstNm
    {
        get
        {
            return this.cHFirstNmField;
        }
        set
        {
            this.cHFirstNmField = value;
        }
    }

    /// <remarks/>
    public string CHLastNm
    {
        get
        {
            return this.cHLastNmField;
        }
        set
        {
            this.cHLastNmField = value;
        }
    }

    /// <remarks/>
    public CHFullNmResType CHFullNmRes
    {
        get
        {
            return this.cHFullNmResField;
        }
        set
        {
            this.cHFullNmResField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CHFullNmResSpecified
    {
        get
        {
            return this.cHFullNmResFieldSpecified;
        }
        set
        {
            this.cHFullNmResFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string DateOfBirth
    {
        get
        {
            return this.dateOfBirthField;
        }
        set
        {
            this.dateOfBirthField = value;
        }
    }

    /// <remarks/>
    public string CustEmailAddr
    {
        get
        {
            return this.custEmailAddrField;
        }
        set
        {
            this.custEmailAddrField = value;
        }
    }

    /// <remarks/>
    public string GiftEmailAddr
    {
        get
        {
            return this.giftEmailAddrField;
        }
        set
        {
            this.giftEmailAddrField = value;
        }
    }

    /// <remarks/>
    public string CustIPAddr
    {
        get
        {
            return this.custIPAddrField;
        }
        set
        {
            this.custIPAddrField = value;
        }
    }

    /// <remarks/>
    public string CustHostName
    {
        get
        {
            return this.custHostNameField;
        }
        set
        {
            this.custHostNameField = value;
        }
    }

    /// <remarks/>
    public string CustBrwsrName
    {
        get
        {
            return this.custBrwsrNameField;
        }
        set
        {
            this.custBrwsrNameField = value;
        }
    }

    /// <remarks/>
    public string CustANI
    {
        get
        {
            return this.custANIField;
        }
        set
        {
            this.custANIField = value;
        }
    }

    /// <remarks/>
    public string CustANIII
    {
        get
        {
            return this.custANIIIField;
        }
        set
        {
            this.custANIIIField = value;
        }
    }

    /// <remarks/>
    public string BillingAddr2
    {
        get
        {
            return this.billingAddr2Field;
        }
        set
        {
            this.billingAddr2Field = value;
        }
    }

    /// <remarks/>
    public string BillingCity
    {
        get
        {
            return this.billingCityField;
        }
        set
        {
            this.billingCityField = value;
        }
    }

    /// <remarks/>
    public string BillingState
    {
        get
        {
            return this.billingStateField;
        }
        set
        {
            this.billingStateField = value;
        }
    }

    /// <remarks/>
    public string BillingCtry
    {
        get
        {
            return this.billingCtryField;
        }
        set
        {
            this.billingCtryField = value;
        }
    }

    /// <remarks/>
    public string CustTelNum
    {
        get
        {
            return this.custTelNumField;
        }
        set
        {
            this.custTelNumField = value;
        }
    }

    /// <remarks/>
    public CustTelTypeType CustTelType
    {
        get
        {
            return this.custTelTypeField;
        }
        set
        {
            this.custTelTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CustTelTypeSpecified
    {
        get
        {
            return this.custTelTypeFieldSpecified;
        }
        set
        {
            this.custTelTypeFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class SAGrp
{

    private string sATranSeqField;

    private string sACondCodeField;

    private string sAEntryModeField;

    private string sASysTraceField;

    private string sARRNField;

    private SAACIType sAACIField;

    private bool sAACIFieldSpecified;

    private SAMrktSpecificDataIndType sAMrktSpecificDataIndField;

    private bool sAMrktSpecificDataIndFieldSpecified;

    /// <remarks/>
    public string SATranSeq
    {
        get
        {
            return this.sATranSeqField;
        }
        set
        {
            this.sATranSeqField = value;
        }
    }

    /// <remarks/>
    public string SACondCode
    {
        get
        {
            return this.sACondCodeField;
        }
        set
        {
            this.sACondCodeField = value;
        }
    }

    /// <remarks/>
    public string SAEntryMode
    {
        get
        {
            return this.sAEntryModeField;
        }
        set
        {
            this.sAEntryModeField = value;
        }
    }

    /// <remarks/>
    public string SASysTrace
    {
        get
        {
            return this.sASysTraceField;
        }
        set
        {
            this.sASysTraceField = value;
        }
    }

    /// <remarks/>
    public string SARRN
    {
        get
        {
            return this.sARRNField;
        }
        set
        {
            this.sARRNField = value;
        }
    }

    /// <remarks/>
    public SAACIType SAACI
    {
        get
        {
            return this.sAACIField;
        }
        set
        {
            this.sAACIField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SAACISpecified
    {
        get
        {
            return this.sAACIFieldSpecified;
        }
        set
        {
            this.sAACIFieldSpecified = value;
        }
    }

    /// <remarks/>
    public SAMrktSpecificDataIndType SAMrktSpecificDataInd
    {
        get
        {
            return this.sAMrktSpecificDataIndField;
        }
        set
        {
            this.sAMrktSpecificDataIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SAMrktSpecificDataIndSpecified
    {
        get
        {
            return this.sAMrktSpecificDataIndFieldSpecified;
        }
        set
        {
            this.sAMrktSpecificDataIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class MTRequestDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private TknGrp tknGrpField;

    private EcommGrp ecommGrpField;

    private SecrTxnGrp secrTxnGrpField;

    private object itemField;

    private CustInfoGrp custInfoGrpField;

    private OrigAuthGrp origAuthGrpField;

    private MnySndGrp mnySndGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public SecrTxnGrp SecrTxnGrp
    {
        get
        {
            return this.secrTxnGrpField;
        }
        set
        {
            this.secrTxnGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public MnySndGrp MnySndGrp
    {
        get
        {
            return this.mnySndGrpField;
        }
        set
        {
            this.mnySndGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AltCNPRequestDetails
{

    private CommonGrp commonGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CardGrp cardGrpField;

    private TAGrp tAGrpField;

    private EcommGrp ecommGrpField;

    private CustInfoGrp custInfoGrpField;

    private ShipGrp shipGrpField;

    private FraudMitGrp fraudMitGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public ShipGrp ShipGrp
    {
        get
        {
            return this.shipGrpField;
        }
        set
        {
            this.shipGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitGrp FraudMitGrp
    {
        get
        {
            return this.fraudMitGrpField;
        }
        set
        {
            this.fraudMitGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AltMerchNameAndAddrGrp
{

    private string merchNameField;

    private string merchAddrField;

    private string merchCityField;

    private string merchStateField;

    private string merchCntyField;

    private string merchPostalCodeField;

    private string merchCtryField;

    private string merchEmailField;

    /// <remarks/>
    public string MerchName
    {
        get
        {
            return this.merchNameField;
        }
        set
        {
            this.merchNameField = value;
        }
    }

    /// <remarks/>
    public string MerchAddr
    {
        get
        {
            return this.merchAddrField;
        }
        set
        {
            this.merchAddrField = value;
        }
    }

    /// <remarks/>
    public string MerchCity
    {
        get
        {
            return this.merchCityField;
        }
        set
        {
            this.merchCityField = value;
        }
    }

    /// <remarks/>
    public string MerchState
    {
        get
        {
            return this.merchStateField;
        }
        set
        {
            this.merchStateField = value;
        }
    }

    /// <remarks/>
    public string MerchCnty
    {
        get
        {
            return this.merchCntyField;
        }
        set
        {
            this.merchCntyField = value;
        }
    }

    /// <remarks/>
    public string MerchPostalCode
    {
        get
        {
            return this.merchPostalCodeField;
        }
        set
        {
            this.merchPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string MerchCtry
    {
        get
        {
            return this.merchCtryField;
        }
        set
        {
            this.merchCtryField = value;
        }
    }

    /// <remarks/>
    public string MerchEmail
    {
        get
        {
            return this.merchEmailField;
        }
        set
        {
            this.merchEmailField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class ShipGrp
{

    private string shipToFirstNmField;

    private string shipToLastNmField;

    private string shipToAddr1Field;

    private string shipToAddr2Field;

    private string shipToCityField;

    private string shipToStateField;

    private string shipToCtryField;

    private string shipToPostalCodeField;

    private string shipToTelNumField;

    private ShipToTelTypeType shipToTelTypeField;

    private bool shipToTelTypeFieldSpecified;

    private DelivTypeType delivTypeField;

    private bool delivTypeFieldSpecified;

    private ShippingCarrierType shippingCarrierField;

    private bool shippingCarrierFieldSpecified;

    private ShippingMthdType shippingMthdField;

    private bool shippingMthdFieldSpecified;

    /// <remarks/>
    public string ShipToFirstNm
    {
        get
        {
            return this.shipToFirstNmField;
        }
        set
        {
            this.shipToFirstNmField = value;
        }
    }

    /// <remarks/>
    public string ShipToLastNm
    {
        get
        {
            return this.shipToLastNmField;
        }
        set
        {
            this.shipToLastNmField = value;
        }
    }

    /// <remarks/>
    public string ShipToAddr1
    {
        get
        {
            return this.shipToAddr1Field;
        }
        set
        {
            this.shipToAddr1Field = value;
        }
    }

    /// <remarks/>
    public string ShipToAddr2
    {
        get
        {
            return this.shipToAddr2Field;
        }
        set
        {
            this.shipToAddr2Field = value;
        }
    }

    /// <remarks/>
    public string ShipToCity
    {
        get
        {
            return this.shipToCityField;
        }
        set
        {
            this.shipToCityField = value;
        }
    }

    /// <remarks/>
    public string ShipToState
    {
        get
        {
            return this.shipToStateField;
        }
        set
        {
            this.shipToStateField = value;
        }
    }

    /// <remarks/>
    public string ShipToCtry
    {
        get
        {
            return this.shipToCtryField;
        }
        set
        {
            this.shipToCtryField = value;
        }
    }

    /// <remarks/>
    public string ShipToPostalCode
    {
        get
        {
            return this.shipToPostalCodeField;
        }
        set
        {
            this.shipToPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string ShipToTelNum
    {
        get
        {
            return this.shipToTelNumField;
        }
        set
        {
            this.shipToTelNumField = value;
        }
    }

    /// <remarks/>
    public ShipToTelTypeType ShipToTelType
    {
        get
        {
            return this.shipToTelTypeField;
        }
        set
        {
            this.shipToTelTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ShipToTelTypeSpecified
    {
        get
        {
            return this.shipToTelTypeFieldSpecified;
        }
        set
        {
            this.shipToTelTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public DelivTypeType DelivType
    {
        get
        {
            return this.delivTypeField;
        }
        set
        {
            this.delivTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DelivTypeSpecified
    {
        get
        {
            return this.delivTypeFieldSpecified;
        }
        set
        {
            this.delivTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public ShippingCarrierType ShippingCarrier
    {
        get
        {
            return this.shippingCarrierField;
        }
        set
        {
            this.shippingCarrierField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ShippingCarrierSpecified
    {
        get
        {
            return this.shippingCarrierFieldSpecified;
        }
        set
        {
            this.shippingCarrierFieldSpecified = value;
        }
    }

    /// <remarks/>
    public ShippingMthdType ShippingMthd
    {
        get
        {
            return this.shippingMthdField;
        }
        set
        {
            this.shippingMthdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ShippingMthdSpecified
    {
        get
        {
            return this.shippingMthdFieldSpecified;
        }
        set
        {
            this.shippingMthdFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class FraudMitGrp
{

    private CustMatIndType custMatIndField;

    private bool custMatIndFieldSpecified;

    private AddChgIndType addChgIndField;

    private bool addChgIndFieldSpecified;

    private FrdScoreIndType frdScoreIndField;

    private bool frdScoreIndFieldSpecified;

    private string numAttemptsField;

    private MembIndType membIndField;

    private bool membIndFieldSpecified;

    private string custIDField;

    private string custStartDateField;

    private string loyIDField;

    private string loyStartDateField;

    private string frdTxtField;

    private string frdPacketField;

    /// <remarks/>
    public CustMatIndType CustMatInd
    {
        get
        {
            return this.custMatIndField;
        }
        set
        {
            this.custMatIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CustMatIndSpecified
    {
        get
        {
            return this.custMatIndFieldSpecified;
        }
        set
        {
            this.custMatIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public AddChgIndType AddChgInd
    {
        get
        {
            return this.addChgIndField;
        }
        set
        {
            this.addChgIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AddChgIndSpecified
    {
        get
        {
            return this.addChgIndFieldSpecified;
        }
        set
        {
            this.addChgIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public FrdScoreIndType FrdScoreInd
    {
        get
        {
            return this.frdScoreIndField;
        }
        set
        {
            this.frdScoreIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FrdScoreIndSpecified
    {
        get
        {
            return this.frdScoreIndFieldSpecified;
        }
        set
        {
            this.frdScoreIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "integer")]
    public string NumAttempts
    {
        get
        {
            return this.numAttemptsField;
        }
        set
        {
            this.numAttemptsField = value;
        }
    }

    /// <remarks/>
    public MembIndType MembInd
    {
        get
        {
            return this.membIndField;
        }
        set
        {
            this.membIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MembIndSpecified
    {
        get
        {
            return this.membIndFieldSpecified;
        }
        set
        {
            this.membIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string CustID
    {
        get
        {
            return this.custIDField;
        }
        set
        {
            this.custIDField = value;
        }
    }

    /// <remarks/>
    public string CustStartDate
    {
        get
        {
            return this.custStartDateField;
        }
        set
        {
            this.custStartDateField = value;
        }
    }

    /// <remarks/>
    public string LoyID
    {
        get
        {
            return this.loyIDField;
        }
        set
        {
            this.loyIDField = value;
        }
    }

    /// <remarks/>
    public string LoyStartDate
    {
        get
        {
            return this.loyStartDateField;
        }
        set
        {
            this.loyStartDateField = value;
        }
    }

    /// <remarks/>
    public string FrdTxt
    {
        get
        {
            return this.frdTxtField;
        }
        set
        {
            this.frdTxtField = value;
        }
    }

    /// <remarks/>
    public string FrdPacket
    {
        get
        {
            return this.frdPacketField;
        }
        set
        {
            this.frdPacketField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class BatchRequestDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private TknGrp tknGrpField;

    private EcommGrp ecommGrpField;

    private SecrTxnGrp secrTxnGrpField;

    private object itemField;

    private EbtGrp ebtGrpField;

    private PurchCardlvl2Grp purchCardlvl2GrpField;

    private PurchCardlvl3Grp[] purchCardlvl3GrpField;

    private CustInfoGrp custInfoGrpField;

    private OrderGrp orderGrpField;

    private SAGrp sAGrpField;

    private OrigAuthGrp origAuthGrpField;

    private DCCGrp dCCGrpField;

    private LodgingGrp lodgingGrpField;

    private AutoRentalGrp autoRentalGrpField;

    private BatchGrp batchGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public SecrTxnGrp SecrTxnGrp
    {
        get
        {
            return this.secrTxnGrpField;
        }
        set
        {
            this.secrTxnGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AmexGrp", typeof(AmexGrp))]
    [System.Xml.Serialization.XmlElementAttribute("DSGrp", typeof(DSGrp))]
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public EbtGrp EbtGrp
    {
        get
        {
            return this.ebtGrpField;
        }
        set
        {
            this.ebtGrpField = value;
        }
    }

    /// <remarks/>
    public PurchCardlvl2Grp PurchCardlvl2Grp
    {
        get
        {
            return this.purchCardlvl2GrpField;
        }
        set
        {
            this.purchCardlvl2GrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("PurchCardlvl3Grp")]
    public PurchCardlvl3Grp[] PurchCardlvl3Grp
    {
        get
        {
            return this.purchCardlvl3GrpField;
        }
        set
        {
            this.purchCardlvl3GrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrderGrp OrderGrp
    {
        get
        {
            return this.orderGrpField;
        }
        set
        {
            this.orderGrpField = value;
        }
    }

    /// <remarks/>
    public SAGrp SAGrp
    {
        get
        {
            return this.sAGrpField;
        }
        set
        {
            this.sAGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public DCCGrp DCCGrp
    {
        get
        {
            return this.dCCGrpField;
        }
        set
        {
            this.dCCGrpField = value;
        }
    }

    /// <remarks/>
    public LodgingGrp LodgingGrp
    {
        get
        {
            return this.lodgingGrpField;
        }
        set
        {
            this.lodgingGrpField = value;
        }
    }

    /// <remarks/>
    public AutoRentalGrp AutoRentalGrp
    {
        get
        {
            return this.autoRentalGrpField;
        }
        set
        {
            this.autoRentalGrpField = value;
        }
    }

    /// <remarks/>
    public BatchGrp BatchGrp
    {
        get
        {
            return this.batchGrpField;
        }
        set
        {
            this.batchGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PurchCardlvl2Grp
{

    private string taxAmtField;

    private TaxIndType taxIndField;

    private bool taxIndFieldSpecified;

    private string vATTaxAmtField;

    private string vATTaxRtField;

    private string purchIdfrField;

    private string pCOrderNumField;

    private string discntAmtField;

    private string frghtAmtField;

    private string dutyAmtField;

    private string destPostalCodeField;

    private string shipFromPostalCodeField;

    private string destCtryCodeField;

    private string merchTaxIDField;

    private string[] prodDescField;

    private string pC3AddField;

    private string merchRefNumField;

    private string merchTypeField;

    private string destStCodeField;

    private ComCardTypeType comCardTypeField;

    private bool comCardTypeFieldSpecified;

    /// <remarks/>
    public string TaxAmt
    {
        get
        {
            return this.taxAmtField;
        }
        set
        {
            this.taxAmtField = value;
        }
    }

    /// <remarks/>
    public TaxIndType TaxInd
    {
        get
        {
            return this.taxIndField;
        }
        set
        {
            this.taxIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TaxIndSpecified
    {
        get
        {
            return this.taxIndFieldSpecified;
        }
        set
        {
            this.taxIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string VATTaxAmt
    {
        get
        {
            return this.vATTaxAmtField;
        }
        set
        {
            this.vATTaxAmtField = value;
        }
    }

    /// <remarks/>
    public string VATTaxRt
    {
        get
        {
            return this.vATTaxRtField;
        }
        set
        {
            this.vATTaxRtField = value;
        }
    }

    /// <remarks/>
    public string PurchIdfr
    {
        get
        {
            return this.purchIdfrField;
        }
        set
        {
            this.purchIdfrField = value;
        }
    }

    /// <remarks/>
    public string PCOrderNum
    {
        get
        {
            return this.pCOrderNumField;
        }
        set
        {
            this.pCOrderNumField = value;
        }
    }

    /// <remarks/>
    public string DiscntAmt
    {
        get
        {
            return this.discntAmtField;
        }
        set
        {
            this.discntAmtField = value;
        }
    }

    /// <remarks/>
    public string FrghtAmt
    {
        get
        {
            return this.frghtAmtField;
        }
        set
        {
            this.frghtAmtField = value;
        }
    }

    /// <remarks/>
    public string DutyAmt
    {
        get
        {
            return this.dutyAmtField;
        }
        set
        {
            this.dutyAmtField = value;
        }
    }

    /// <remarks/>
    public string DestPostalCode
    {
        get
        {
            return this.destPostalCodeField;
        }
        set
        {
            this.destPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string ShipFromPostalCode
    {
        get
        {
            return this.shipFromPostalCodeField;
        }
        set
        {
            this.shipFromPostalCodeField = value;
        }
    }

    /// <remarks/>
    public string DestCtryCode
    {
        get
        {
            return this.destCtryCodeField;
        }
        set
        {
            this.destCtryCodeField = value;
        }
    }

    /// <remarks/>
    public string MerchTaxID
    {
        get
        {
            return this.merchTaxIDField;
        }
        set
        {
            this.merchTaxIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdDesc")]
    public string[] ProdDesc
    {
        get
        {
            return this.prodDescField;
        }
        set
        {
            this.prodDescField = value;
        }
    }

    /// <remarks/>
    public string PC3Add
    {
        get
        {
            return this.pC3AddField;
        }
        set
        {
            this.pC3AddField = value;
        }
    }

    /// <remarks/>
    public string MerchRefNum
    {
        get
        {
            return this.merchRefNumField;
        }
        set
        {
            this.merchRefNumField = value;
        }
    }

    /// <remarks/>
    public string MerchType
    {
        get
        {
            return this.merchTypeField;
        }
        set
        {
            this.merchTypeField = value;
        }
    }

    /// <remarks/>
    public string DestStCode
    {
        get
        {
            return this.destStCodeField;
        }
        set
        {
            this.destStCodeField = value;
        }
    }

    /// <remarks/>
    public ComCardTypeType ComCardType
    {
        get
        {
            return this.comCardTypeField;
        }
        set
        {
            this.comCardTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ComCardTypeSpecified
    {
        get
        {
            return this.comCardTypeFieldSpecified;
        }
        set
        {
            this.comCardTypeFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PurchCardlvl3Grp
{

    private string l3ItemSeqNumField;

    private string l3ItemCodeField;

    private string l3ItemDescField;

    private string l3QtyField;

    private L3ItemQtyExpType l3ItemQtyExpField;

    private bool l3ItemQtyExpFieldSpecified;

    private string l3UnitOfMsureField;

    private string l3UnitCostField;

    private string l3UnitCostExpField;

    private string l3ItemTotField;

    private string l3DiscntAmtField;

    private string l3TaxAmtField;

    private string l3TaxRtField;

    private string l3ProdCodeField;

    private string l3ExtItemAmtField;

    private L3TaxIndType l3TaxIndField;

    private bool l3TaxIndFieldSpecified;

    private string l3TaxRateField;

    private L3TaxRtExpType l3TaxRtExpField;

    private bool l3TaxRtExpFieldSpecified;

    private string l3NatTaxField;

    private string l3NatTaxRtField;

    private string l3MerchVATRegField;

    private string l3OtherTaxAmtField;

    private string l3TaxTypeAppField;

    private string l3TaxAmountField;

    private L3DbCrIndType l3DbCrIndField;

    private bool l3DbCrIndFieldSpecified;

    private L3LineItmDtlIndType l3LineItmDtlIndField;

    private bool l3LineItmDtlIndFieldSpecified;

    private string l3SumComCdField;

    private string l3ProdDescField;

    private L3TaxTypeIdType l3TaxTypeIdField;

    private bool l3TaxTypeIdFieldSpecified;

    /// <remarks/>
    public string L3ItemSeqNum
    {
        get
        {
            return this.l3ItemSeqNumField;
        }
        set
        {
            this.l3ItemSeqNumField = value;
        }
    }

    /// <remarks/>
    public string L3ItemCode
    {
        get
        {
            return this.l3ItemCodeField;
        }
        set
        {
            this.l3ItemCodeField = value;
        }
    }

    /// <remarks/>
    public string L3ItemDesc
    {
        get
        {
            return this.l3ItemDescField;
        }
        set
        {
            this.l3ItemDescField = value;
        }
    }

    /// <remarks/>
    public string L3Qty
    {
        get
        {
            return this.l3QtyField;
        }
        set
        {
            this.l3QtyField = value;
        }
    }

    /// <remarks/>
    public L3ItemQtyExpType L3ItemQtyExp
    {
        get
        {
            return this.l3ItemQtyExpField;
        }
        set
        {
            this.l3ItemQtyExpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool L3ItemQtyExpSpecified
    {
        get
        {
            return this.l3ItemQtyExpFieldSpecified;
        }
        set
        {
            this.l3ItemQtyExpFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string L3UnitOfMsure
    {
        get
        {
            return this.l3UnitOfMsureField;
        }
        set
        {
            this.l3UnitOfMsureField = value;
        }
    }

    /// <remarks/>
    public string L3UnitCost
    {
        get
        {
            return this.l3UnitCostField;
        }
        set
        {
            this.l3UnitCostField = value;
        }
    }

    /// <remarks/>
    public string L3UnitCostExp
    {
        get
        {
            return this.l3UnitCostExpField;
        }
        set
        {
            this.l3UnitCostExpField = value;
        }
    }

    /// <remarks/>
    public string L3ItemTot
    {
        get
        {
            return this.l3ItemTotField;
        }
        set
        {
            this.l3ItemTotField = value;
        }
    }

    /// <remarks/>
    public string L3DiscntAmt
    {
        get
        {
            return this.l3DiscntAmtField;
        }
        set
        {
            this.l3DiscntAmtField = value;
        }
    }

    /// <remarks/>
    public string L3TaxAmt
    {
        get
        {
            return this.l3TaxAmtField;
        }
        set
        {
            this.l3TaxAmtField = value;
        }
    }

    /// <remarks/>
    public string L3TaxRt
    {
        get
        {
            return this.l3TaxRtField;
        }
        set
        {
            this.l3TaxRtField = value;
        }
    }

    /// <remarks/>
    public string L3ProdCode
    {
        get
        {
            return this.l3ProdCodeField;
        }
        set
        {
            this.l3ProdCodeField = value;
        }
    }

    /// <remarks/>
    public string L3ExtItemAmt
    {
        get
        {
            return this.l3ExtItemAmtField;
        }
        set
        {
            this.l3ExtItemAmtField = value;
        }
    }

    /// <remarks/>
    public L3TaxIndType L3TaxInd
    {
        get
        {
            return this.l3TaxIndField;
        }
        set
        {
            this.l3TaxIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool L3TaxIndSpecified
    {
        get
        {
            return this.l3TaxIndFieldSpecified;
        }
        set
        {
            this.l3TaxIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string L3TaxRate
    {
        get
        {
            return this.l3TaxRateField;
        }
        set
        {
            this.l3TaxRateField = value;
        }
    }

    /// <remarks/>
    public L3TaxRtExpType L3TaxRtExp
    {
        get
        {
            return this.l3TaxRtExpField;
        }
        set
        {
            this.l3TaxRtExpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool L3TaxRtExpSpecified
    {
        get
        {
            return this.l3TaxRtExpFieldSpecified;
        }
        set
        {
            this.l3TaxRtExpFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string L3NatTax
    {
        get
        {
            return this.l3NatTaxField;
        }
        set
        {
            this.l3NatTaxField = value;
        }
    }

    /// <remarks/>
    public string L3NatTaxRt
    {
        get
        {
            return this.l3NatTaxRtField;
        }
        set
        {
            this.l3NatTaxRtField = value;
        }
    }

    /// <remarks/>
    public string L3MerchVATReg
    {
        get
        {
            return this.l3MerchVATRegField;
        }
        set
        {
            this.l3MerchVATRegField = value;
        }
    }

    /// <remarks/>
    public string L3OtherTaxAmt
    {
        get
        {
            return this.l3OtherTaxAmtField;
        }
        set
        {
            this.l3OtherTaxAmtField = value;
        }
    }

    /// <remarks/>
    public string L3TaxTypeApp
    {
        get
        {
            return this.l3TaxTypeAppField;
        }
        set
        {
            this.l3TaxTypeAppField = value;
        }
    }

    /// <remarks/>
    public string L3TaxAmount
    {
        get
        {
            return this.l3TaxAmountField;
        }
        set
        {
            this.l3TaxAmountField = value;
        }
    }

    /// <remarks/>
    public L3DbCrIndType L3DbCrInd
    {
        get
        {
            return this.l3DbCrIndField;
        }
        set
        {
            this.l3DbCrIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool L3DbCrIndSpecified
    {
        get
        {
            return this.l3DbCrIndFieldSpecified;
        }
        set
        {
            this.l3DbCrIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public L3LineItmDtlIndType L3LineItmDtlInd
    {
        get
        {
            return this.l3LineItmDtlIndField;
        }
        set
        {
            this.l3LineItmDtlIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool L3LineItmDtlIndSpecified
    {
        get
        {
            return this.l3LineItmDtlIndFieldSpecified;
        }
        set
        {
            this.l3LineItmDtlIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string L3SumComCd
    {
        get
        {
            return this.l3SumComCdField;
        }
        set
        {
            this.l3SumComCdField = value;
        }
    }

    /// <remarks/>
    public string L3ProdDesc
    {
        get
        {
            return this.l3ProdDescField;
        }
        set
        {
            this.l3ProdDescField = value;
        }
    }

    /// <remarks/>
    public L3TaxTypeIdType L3TaxTypeId
    {
        get
        {
            return this.l3TaxTypeIdField;
        }
        set
        {
            this.l3TaxTypeIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool L3TaxTypeIdSpecified
    {
        get
        {
            return this.l3TaxTypeIdFieldSpecified;
        }
        set
        {
            this.l3TaxTypeIdFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class OrderGrp
{

    private string orderDateField;

    private string orderTimeField;

    private string billerRefNumField;

    private string sKUField;

    /// <remarks/>
    public string OrderDate
    {
        get
        {
            return this.orderDateField;
        }
        set
        {
            this.orderDateField = value;
        }
    }

    /// <remarks/>
    public string OrderTime
    {
        get
        {
            return this.orderTimeField;
        }
        set
        {
            this.orderTimeField = value;
        }
    }

    /// <remarks/>
    public string BillerRefNum
    {
        get
        {
            return this.billerRefNumField;
        }
        set
        {
            this.billerRefNumField = value;
        }
    }

    /// <remarks/>
    public string SKU
    {
        get
        {
            return this.sKUField;
        }
        set
        {
            this.sKUField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class DCCGrp
{

    private DCCIndType dCCIndField;

    private bool dCCIndFieldSpecified;

    private string dCCTimeZnField;

    private string dCCAmtField;

    private string dCCRateField;

    private string dCCCrncyField;

    /// <remarks/>
    public DCCIndType DCCInd
    {
        get
        {
            return this.dCCIndField;
        }
        set
        {
            this.dCCIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DCCIndSpecified
    {
        get
        {
            return this.dCCIndFieldSpecified;
        }
        set
        {
            this.dCCIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string DCCTimeZn
    {
        get
        {
            return this.dCCTimeZnField;
        }
        set
        {
            this.dCCTimeZnField = value;
        }
    }

    /// <remarks/>
    public string DCCAmt
    {
        get
        {
            return this.dCCAmtField;
        }
        set
        {
            this.dCCAmtField = value;
        }
    }

    /// <remarks/>
    public string DCCRate
    {
        get
        {
            return this.dCCRateField;
        }
        set
        {
            this.dCCRateField = value;
        }
    }

    /// <remarks/>
    public string DCCCrncy
    {
        get
        {
            return this.dCCCrncyField;
        }
        set
        {
            this.dCCCrncyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class LodgingGrp
{

    private string folioNumField;

    private string roomNumField;

    private string lodRefNumField;

    private string roomRtField;

    private string rmTaxField;

    private ProgramIndType programIndField;

    private bool programIndFieldSpecified;

    private string arvDateField;

    private string depDateField;

    private string arvTimeField;

    private string depTimeField;

    private string durationField;

    private HotelNoShowType hotelNoShowField;

    private bool hotelNoShowFieldSpecified;

    private string lodPhnNumField;

    private string cHNameField;

    private LodChargeTypeType lodChargeTypeField;

    private bool lodChargeTypeFieldSpecified;

    private string extraChrgsField;

    /// <remarks/>
    public string FolioNum
    {
        get
        {
            return this.folioNumField;
        }
        set
        {
            this.folioNumField = value;
        }
    }

    /// <remarks/>
    public string RoomNum
    {
        get
        {
            return this.roomNumField;
        }
        set
        {
            this.roomNumField = value;
        }
    }

    /// <remarks/>
    public string LodRefNum
    {
        get
        {
            return this.lodRefNumField;
        }
        set
        {
            this.lodRefNumField = value;
        }
    }

    /// <remarks/>
    public string RoomRt
    {
        get
        {
            return this.roomRtField;
        }
        set
        {
            this.roomRtField = value;
        }
    }

    /// <remarks/>
    public string RmTax
    {
        get
        {
            return this.rmTaxField;
        }
        set
        {
            this.rmTaxField = value;
        }
    }

    /// <remarks/>
    public ProgramIndType ProgramInd
    {
        get
        {
            return this.programIndField;
        }
        set
        {
            this.programIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ProgramIndSpecified
    {
        get
        {
            return this.programIndFieldSpecified;
        }
        set
        {
            this.programIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string ArvDate
    {
        get
        {
            return this.arvDateField;
        }
        set
        {
            this.arvDateField = value;
        }
    }

    /// <remarks/>
    public string DepDate
    {
        get
        {
            return this.depDateField;
        }
        set
        {
            this.depDateField = value;
        }
    }

    /// <remarks/>
    public string ArvTime
    {
        get
        {
            return this.arvTimeField;
        }
        set
        {
            this.arvTimeField = value;
        }
    }

    /// <remarks/>
    public string DepTime
    {
        get
        {
            return this.depTimeField;
        }
        set
        {
            this.depTimeField = value;
        }
    }

    /// <remarks/>
    public string Duration
    {
        get
        {
            return this.durationField;
        }
        set
        {
            this.durationField = value;
        }
    }

    /// <remarks/>
    public HotelNoShowType HotelNoShow
    {
        get
        {
            return this.hotelNoShowField;
        }
        set
        {
            this.hotelNoShowField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool HotelNoShowSpecified
    {
        get
        {
            return this.hotelNoShowFieldSpecified;
        }
        set
        {
            this.hotelNoShowFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string LodPhnNum
    {
        get
        {
            return this.lodPhnNumField;
        }
        set
        {
            this.lodPhnNumField = value;
        }
    }

    /// <remarks/>
    public string CHName
    {
        get
        {
            return this.cHNameField;
        }
        set
        {
            this.cHNameField = value;
        }
    }

    /// <remarks/>
    public LodChargeTypeType LodChargeType
    {
        get
        {
            return this.lodChargeTypeField;
        }
        set
        {
            this.lodChargeTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LodChargeTypeSpecified
    {
        get
        {
            return this.lodChargeTypeFieldSpecified;
        }
        set
        {
            this.lodChargeTypeFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string ExtraChrgs
    {
        get
        {
            return this.extraChrgsField;
        }
        set
        {
            this.extraChrgsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class AutoRentalGrp
{

    private string rentalCityField;

    private string rentalStateField;

    private string rentalCtryField;

    private string rentalDateField;

    private string rentalTimeField;

    private string returnCityField;

    private string returnStateField;

    private string returnCtryField;

    private string returnDateField;

    private string returnTimeField;

    private string amtExtraChrgsField;

    private string renterNameField;

    private string autoAgreeNumField;

    private string rentalDurationField;

    private string rentalExtraChrgsField;

    private AutoNoShowType autoNoShowField;

    private bool autoNoShowFieldSpecified;

    private string rentalClsIDField;

    private RentalTaxIndType rentalTaxIndField;

    private bool rentalTaxIndFieldSpecified;

    private DelChrgIndType delChrgIndField;

    private bool delChrgIndFieldSpecified;

    /// <remarks/>
    public string RentalCity
    {
        get
        {
            return this.rentalCityField;
        }
        set
        {
            this.rentalCityField = value;
        }
    }

    /// <remarks/>
    public string RentalState
    {
        get
        {
            return this.rentalStateField;
        }
        set
        {
            this.rentalStateField = value;
        }
    }

    /// <remarks/>
    public string RentalCtry
    {
        get
        {
            return this.rentalCtryField;
        }
        set
        {
            this.rentalCtryField = value;
        }
    }

    /// <remarks/>
    public string RentalDate
    {
        get
        {
            return this.rentalDateField;
        }
        set
        {
            this.rentalDateField = value;
        }
    }

    /// <remarks/>
    public string RentalTime
    {
        get
        {
            return this.rentalTimeField;
        }
        set
        {
            this.rentalTimeField = value;
        }
    }

    /// <remarks/>
    public string ReturnCity
    {
        get
        {
            return this.returnCityField;
        }
        set
        {
            this.returnCityField = value;
        }
    }

    /// <remarks/>
    public string ReturnState
    {
        get
        {
            return this.returnStateField;
        }
        set
        {
            this.returnStateField = value;
        }
    }

    /// <remarks/>
    public string ReturnCtry
    {
        get
        {
            return this.returnCtryField;
        }
        set
        {
            this.returnCtryField = value;
        }
    }

    /// <remarks/>
    public string ReturnDate
    {
        get
        {
            return this.returnDateField;
        }
        set
        {
            this.returnDateField = value;
        }
    }

    /// <remarks/>
    public string ReturnTime
    {
        get
        {
            return this.returnTimeField;
        }
        set
        {
            this.returnTimeField = value;
        }
    }

    /// <remarks/>
    public string AmtExtraChrgs
    {
        get
        {
            return this.amtExtraChrgsField;
        }
        set
        {
            this.amtExtraChrgsField = value;
        }
    }

    /// <remarks/>
    public string RenterName
    {
        get
        {
            return this.renterNameField;
        }
        set
        {
            this.renterNameField = value;
        }
    }

    /// <remarks/>
    public string AutoAgreeNum
    {
        get
        {
            return this.autoAgreeNumField;
        }
        set
        {
            this.autoAgreeNumField = value;
        }
    }

    /// <remarks/>
    public string RentalDuration
    {
        get
        {
            return this.rentalDurationField;
        }
        set
        {
            this.rentalDurationField = value;
        }
    }

    /// <remarks/>
    public string RentalExtraChrgs
    {
        get
        {
            return this.rentalExtraChrgsField;
        }
        set
        {
            this.rentalExtraChrgsField = value;
        }
    }

    /// <remarks/>
    public AutoNoShowType AutoNoShow
    {
        get
        {
            return this.autoNoShowField;
        }
        set
        {
            this.autoNoShowField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AutoNoShowSpecified
    {
        get
        {
            return this.autoNoShowFieldSpecified;
        }
        set
        {
            this.autoNoShowFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string RentalClsID
    {
        get
        {
            return this.rentalClsIDField;
        }
        set
        {
            this.rentalClsIDField = value;
        }
    }

    /// <remarks/>
    public RentalTaxIndType RentalTaxInd
    {
        get
        {
            return this.rentalTaxIndField;
        }
        set
        {
            this.rentalTaxIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RentalTaxIndSpecified
    {
        get
        {
            return this.rentalTaxIndFieldSpecified;
        }
        set
        {
            this.rentalTaxIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public DelChrgIndType DelChrgInd
    {
        get
        {
            return this.delChrgIndField;
        }
        set
        {
            this.delChrgIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DelChrgIndSpecified
    {
        get
        {
            return this.delChrgIndFieldSpecified;
        }
        set
        {
            this.delChrgIndFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class VoidTOReversalRequestDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private PFGrp pFGrpField;

    private CardGrp cardGrpField;

    private CheckGrp checkGrpField;

    private TeleCheckECAGrp teleCheckECAGrpField;

    private TCNFTFGrp tCNFTFGrpField;

    private PINGrp pINGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private TknGrp tknGrpField;

    private OfferGrp offerGrpField;

    private EcommGrp ecommGrpField;

    private SecrTxnGrp secrTxnGrpField;

    private object itemField;

    private PrvLblGrp prvLblGrpField;

    private EbtGrp ebtGrpField;

    private EWICDetGrp[] eWICDetGrpField;

    private DebitGrp debitGrpField;

    private CanDebitGrp canDebitGrpField;

    private PurchCardlvl2Grp purchCardlvl2GrpField;

    private PurchCardlvl3Grp[] purchCardlvl3GrpField;

    private CustInfoGrp custInfoGrpField;

    private OrderGrp orderGrpField;

    private StoredValueGrp storedValueGrpField;

    private SAGrp sAGrpField;

    private OrigAuthGrp origAuthGrpField;

    private DCCGrp dCCGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    private FltGrp fltGrpField;

    private LodgingGrp lodgingGrpField;

    private AutoRentalGrp autoRentalGrpField;

    private MnySndGrp mnySndGrpField;

    private RestGrp restGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public PFGrp PFGrp
    {
        get
        {
            return this.pFGrpField;
        }
        set
        {
            this.pFGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public CheckGrp CheckGrp
    {
        get
        {
            return this.checkGrpField;
        }
        set
        {
            this.checkGrpField = value;
        }
    }

    /// <remarks/>
    public TeleCheckECAGrp TeleCheckECAGrp
    {
        get
        {
            return this.teleCheckECAGrpField;
        }
        set
        {
            this.teleCheckECAGrpField = value;
        }
    }

    /// <remarks/>
    public TCNFTFGrp TCNFTFGrp
    {
        get
        {
            return this.tCNFTFGrpField;
        }
        set
        {
            this.tCNFTFGrpField = value;
        }
    }

    /// <remarks/>
    public PINGrp PINGrp
    {
        get
        {
            return this.pINGrpField;
        }
        set
        {
            this.pINGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public OfferGrp OfferGrp
    {
        get
        {
            return this.offerGrpField;
        }
        set
        {
            this.offerGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public SecrTxnGrp SecrTxnGrp
    {
        get
        {
            return this.secrTxnGrpField;
        }
        set
        {
            this.secrTxnGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AmexGrp", typeof(AmexGrp))]
    [System.Xml.Serialization.XmlElementAttribute("DSGrp", typeof(DSGrp))]
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public PrvLblGrp PrvLblGrp
    {
        get
        {
            return this.prvLblGrpField;
        }
        set
        {
            this.prvLblGrpField = value;
        }
    }

    /// <remarks/>
    public EbtGrp EbtGrp
    {
        get
        {
            return this.ebtGrpField;
        }
        set
        {
            this.ebtGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("EWICDetGrp")]
    public EWICDetGrp[] EWICDetGrp
    {
        get
        {
            return this.eWICDetGrpField;
        }
        set
        {
            this.eWICDetGrpField = value;
        }
    }

    /// <remarks/>
    public DebitGrp DebitGrp
    {
        get
        {
            return this.debitGrpField;
        }
        set
        {
            this.debitGrpField = value;
        }
    }

    /// <remarks/>
    public CanDebitGrp CanDebitGrp
    {
        get
        {
            return this.canDebitGrpField;
        }
        set
        {
            this.canDebitGrpField = value;
        }
    }

    /// <remarks/>
    public PurchCardlvl2Grp PurchCardlvl2Grp
    {
        get
        {
            return this.purchCardlvl2GrpField;
        }
        set
        {
            this.purchCardlvl2GrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("PurchCardlvl3Grp")]
    public PurchCardlvl3Grp[] PurchCardlvl3Grp
    {
        get
        {
            return this.purchCardlvl3GrpField;
        }
        set
        {
            this.purchCardlvl3GrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrderGrp OrderGrp
    {
        get
        {
            return this.orderGrpField;
        }
        set
        {
            this.orderGrpField = value;
        }
    }

    /// <remarks/>
    public StoredValueGrp StoredValueGrp
    {
        get
        {
            return this.storedValueGrpField;
        }
        set
        {
            this.storedValueGrpField = value;
        }
    }

    /// <remarks/>
    public SAGrp SAGrp
    {
        get
        {
            return this.sAGrpField;
        }
        set
        {
            this.sAGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public DCCGrp DCCGrp
    {
        get
        {
            return this.dCCGrpField;
        }
        set
        {
            this.dCCGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }

    /// <remarks/>
    public FltGrp FltGrp
    {
        get
        {
            return this.fltGrpField;
        }
        set
        {
            this.fltGrpField = value;
        }
    }

    /// <remarks/>
    public LodgingGrp LodgingGrp
    {
        get
        {
            return this.lodgingGrpField;
        }
        set
        {
            this.lodgingGrpField = value;
        }
    }

    /// <remarks/>
    public AutoRentalGrp AutoRentalGrp
    {
        get
        {
            return this.autoRentalGrpField;
        }
        set
        {
            this.autoRentalGrpField = value;
        }
    }

    /// <remarks/>
    public MnySndGrp MnySndGrp
    {
        get
        {
            return this.mnySndGrpField;
        }
        set
        {
            this.mnySndGrpField = value;
        }
    }

    /// <remarks/>
    public RestGrp RestGrp
    {
        get
        {
            return this.restGrpField;
        }
        set
        {
            this.restGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PFGrp
{

    private PFIndType pFIndField;

    private bool pFIndFieldSpecified;

    private string sellerIDField;

    private string subMerchIDField;

    private string pFPhoneNumberField;

    /// <remarks/>
    public PFIndType PFInd
    {
        get
        {
            return this.pFIndField;
        }
        set
        {
            this.pFIndField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PFIndSpecified
    {
        get
        {
            return this.pFIndFieldSpecified;
        }
        set
        {
            this.pFIndFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string SellerID
    {
        get
        {
            return this.sellerIDField;
        }
        set
        {
            this.sellerIDField = value;
        }
    }

    /// <remarks/>
    public string SubMerchID
    {
        get
        {
            return this.subMerchIDField;
        }
        set
        {
            this.subMerchIDField = value;
        }
    }

    /// <remarks/>
    public string PFPhoneNumber
    {
        get
        {
            return this.pFPhoneNumberField;
        }
        set
        {
            this.pFPhoneNumberField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CheckGrp
{

    private string mICRField;

    private string drvLicField;

    private string stateCodeField;

    private string dLDateOfBirthField;

    private ChkSvcPvdrType chkSvcPvdrField;

    private bool chkSvcPvdrFieldSpecified;

    private ChkEntryMethodType chkEntryMethodField;

    private bool chkEntryMethodFieldSpecified;

    /// <remarks/>
    public string MICR
    {
        get
        {
            return this.mICRField;
        }
        set
        {
            this.mICRField = value;
        }
    }

    /// <remarks/>
    public string DrvLic
    {
        get
        {
            return this.drvLicField;
        }
        set
        {
            this.drvLicField = value;
        }
    }

    /// <remarks/>
    public string StateCode
    {
        get
        {
            return this.stateCodeField;
        }
        set
        {
            this.stateCodeField = value;
        }
    }

    /// <remarks/>
    public string DLDateOfBirth
    {
        get
        {
            return this.dLDateOfBirthField;
        }
        set
        {
            this.dLDateOfBirthField = value;
        }
    }

    /// <remarks/>
    public ChkSvcPvdrType ChkSvcPvdr
    {
        get
        {
            return this.chkSvcPvdrField;
        }
        set
        {
            this.chkSvcPvdrField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ChkSvcPvdrSpecified
    {
        get
        {
            return this.chkSvcPvdrFieldSpecified;
        }
        set
        {
            this.chkSvcPvdrFieldSpecified = value;
        }
    }

    /// <remarks/>
    public ChkEntryMethodType ChkEntryMethod
    {
        get
        {
            return this.chkEntryMethodField;
        }
        set
        {
            this.chkEntryMethodField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ChkEntryMethodSpecified
    {
        get
        {
            return this.chkEntryMethodFieldSpecified;
        }
        set
        {
            this.chkEntryMethodFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PINGrp
{

    private string pINDataField;

    private string keySerialNumDataField;

    /// <remarks/>
    public string PINData
    {
        get
        {
            return this.pINDataField;
        }
        set
        {
            this.pINDataField = value;
        }
    }

    /// <remarks/>
    public string KeySerialNumData
    {
        get
        {
            return this.keySerialNumDataField;
        }
        set
        {
            this.keySerialNumDataField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class ProdCodeGrp
{

    private ServLvlType servLvlField;

    private bool servLvlFieldSpecified;

    private NumOfProdsType numOfProdsField;

    private bool numOfProdsFieldSpecified;

    /// <remarks/>
    public ServLvlType ServLvl
    {
        get
        {
            return this.servLvlField;
        }
        set
        {
            this.servLvlField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ServLvlSpecified
    {
        get
        {
            return this.servLvlFieldSpecified;
        }
        set
        {
            this.servLvlFieldSpecified = value;
        }
    }

    /// <remarks/>
    public NumOfProdsType NumOfProds
    {
        get
        {
            return this.numOfProdsField;
        }
        set
        {
            this.numOfProdsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NumOfProdsSpecified
    {
        get
        {
            return this.numOfProdsFieldSpecified;
        }
        set
        {
            this.numOfProdsFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class ProdCodeDetGrp
{

    private string nACSProdCodeField;

    private UnitOfMsureType unitOfMsureField;

    private bool unitOfMsureFieldSpecified;

    private string qntyField;

    private string unitPriceField;

    private string prodAmtField;

    /// <remarks/>
    public string NACSProdCode
    {
        get
        {
            return this.nACSProdCodeField;
        }
        set
        {
            this.nACSProdCodeField = value;
        }
    }

    /// <remarks/>
    public UnitOfMsureType UnitOfMsure
    {
        get
        {
            return this.unitOfMsureField;
        }
        set
        {
            this.unitOfMsureField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool UnitOfMsureSpecified
    {
        get
        {
            return this.unitOfMsureFieldSpecified;
        }
        set
        {
            this.unitOfMsureFieldSpecified = value;
        }
    }

    /// <remarks/>
    public string Qnty
    {
        get
        {
            return this.qntyField;
        }
        set
        {
            this.qntyField = value;
        }
    }

    /// <remarks/>
    public string UnitPrice
    {
        get
        {
            return this.unitPriceField;
        }
        set
        {
            this.unitPriceField = value;
        }
    }

    /// <remarks/>
    public string ProdAmt
    {
        get
        {
            return this.prodAmtField;
        }
        set
        {
            this.prodAmtField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class RestGrp
{

    private string foodAmtField;

    private string bevgAmtField;

    private string tipAmtField;

    private string restTaxAmtField;

    private string serverIDField;

    /// <remarks/>
    public string FoodAmt
    {
        get
        {
            return this.foodAmtField;
        }
        set
        {
            this.foodAmtField = value;
        }
    }

    /// <remarks/>
    public string BevgAmt
    {
        get
        {
            return this.bevgAmtField;
        }
        set
        {
            this.bevgAmtField = value;
        }
    }

    /// <remarks/>
    public string TipAmt
    {
        get
        {
            return this.tipAmtField;
        }
        set
        {
            this.tipAmtField = value;
        }
    }

    /// <remarks/>
    public string RestTaxAmt
    {
        get
        {
            return this.restTaxAmtField;
        }
        set
        {
            this.restTaxAmtField = value;
        }
    }

    /// <remarks/>
    public string ServerID
    {
        get
        {
            return this.serverIDField;
        }
        set
        {
            this.serverIDField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class TARequestDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private TAGrp tAGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class FleetRequestDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private object itemField;

    private PrvLblGrp prvLblGrpField;

    private CustInfoGrp custInfoGrpField;

    private OrigAuthGrp origAuthGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    private FltGrp fltGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public PrvLblGrp PrvLblGrp
    {
        get
        {
            return this.prvLblGrpField;
        }
        set
        {
            this.prvLblGrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }

    /// <remarks/>
    public FltGrp FltGrp
    {
        get
        {
            return this.fltGrpField;
        }
        set
        {
            this.fltGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PrivateLabelRequestDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private TknGrp tknGrpField;

    private EcommGrp ecommGrpField;

    private PrvLblGrp prvLblGrpField;

    private CustInfoGrp custInfoGrpField;

    private OrigAuthGrp origAuthGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    private FltGrp fltGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public PrvLblGrp PrvLblGrp
    {
        get
        {
            return this.prvLblGrpField;
        }
        set
        {
            this.prvLblGrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }

    /// <remarks/>
    public FltGrp FltGrp
    {
        get
        {
            return this.fltGrpField;
        }
        set
        {
            this.fltGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class GenPrepaidRequestDetails
{

    private CommonGrp commonGrpField;

    private CardGrp cardGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private CustInfoGrp custInfoGrpField;

    private OrigAuthGrp origAuthGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PrepaidRequestDetails
{

    private CommonGrp commonGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CardGrp cardGrpField;

    private CustInfoGrp custInfoGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private EcommGrp ecommGrpField;

    private ShipGrp shipGrpField;

    private StoredValueGrp storedValueGrpField;

    private OrigAuthGrp origAuthGrpField;

    private FraudMitGrp fraudMitGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public ShipGrp ShipGrp
    {
        get
        {
            return this.shipGrpField;
        }
        set
        {
            this.shipGrpField = value;
        }
    }

    /// <remarks/>
    public StoredValueGrp StoredValueGrp
    {
        get
        {
            return this.storedValueGrpField;
        }
        set
        {
            this.storedValueGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitGrp FraudMitGrp
    {
        get
        {
            return this.fraudMitGrpField;
        }
        set
        {
            this.fraudMitGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class EBTRequestDetails
{

    private CommonGrp commonGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CardGrp cardGrpField;

    private PINGrp pINGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private TknGrp tknGrpField;

    private EbtGrp ebtGrpField;

    private EWICDetGrp[] eWICDetGrpField;

    private OrigAuthGrp origAuthGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public PINGrp PINGrp
    {
        get
        {
            return this.pINGrpField;
        }
        set
        {
            this.pINGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public EbtGrp EbtGrp
    {
        get
        {
            return this.ebtGrpField;
        }
        set
        {
            this.ebtGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("EWICDetGrp")]
    public EWICDetGrp[] EWICDetGrp
    {
        get
        {
            return this.eWICDetGrpField;
        }
        set
        {
            this.eWICDetGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CheckRequestDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CheckGrp checkGrpField;

    private TeleCheckECAGrp teleCheckECAGrpField;

    private TCNFTFGrp tCNFTFGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private TAGrp tAGrpField;

    private EcommGrp ecommGrpField;

    private ShipGrp shipGrpField;

    private OrigAuthGrp origAuthGrpField;

    private CustInfoGrp custInfoGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    private FraudMitGrp fraudMitGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CheckGrp CheckGrp
    {
        get
        {
            return this.checkGrpField;
        }
        set
        {
            this.checkGrpField = value;
        }
    }

    /// <remarks/>
    public TeleCheckECAGrp TeleCheckECAGrp
    {
        get
        {
            return this.teleCheckECAGrpField;
        }
        set
        {
            this.teleCheckECAGrpField = value;
        }
    }

    /// <remarks/>
    public TCNFTFGrp TCNFTFGrp
    {
        get
        {
            return this.tCNFTFGrpField;
        }
        set
        {
            this.tCNFTFGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public ShipGrp ShipGrp
    {
        get
        {
            return this.shipGrpField;
        }
        set
        {
            this.shipGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitGrp FraudMitGrp
    {
        get
        {
            return this.fraudMitGrpField;
        }
        set
        {
            this.fraudMitGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class PinlessDebitRequestDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CardGrp cardGrpField;

    private TAGrp tAGrpField;

    private EcommGrp ecommGrpField;

    private DebitGrp debitGrpField;

    private CustInfoGrp custInfoGrpField;

    private OrderGrp orderGrpField;

    private FraudMitGrp fraudMitGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public DebitGrp DebitGrp
    {
        get
        {
            return this.debitGrpField;
        }
        set
        {
            this.debitGrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrderGrp OrderGrp
    {
        get
        {
            return this.orderGrpField;
        }
        set
        {
            this.orderGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitGrp FraudMitGrp
    {
        get
        {
            return this.fraudMitGrpField;
        }
        set
        {
            this.fraudMitGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class DebitRequestDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private CardGrp cardGrpField;

    private PINGrp pINGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private DebitGrp debitGrpField;

    private CanDebitGrp canDebitGrpField;

    private TknGrp tknGrpField;

    private OfferGrp offerGrpField;

    private OrigAuthGrp origAuthGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    private RestGrp restGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public PINGrp PINGrp
    {
        get
        {
            return this.pINGrpField;
        }
        set
        {
            this.pINGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public DebitGrp DebitGrp
    {
        get
        {
            return this.debitGrpField;
        }
        set
        {
            this.debitGrpField = value;
        }
    }

    /// <remarks/>
    public CanDebitGrp CanDebitGrp
    {
        get
        {
            return this.canDebitGrpField;
        }
        set
        {
            this.canDebitGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public OfferGrp OfferGrp
    {
        get
        {
            return this.offerGrpField;
        }
        set
        {
            this.offerGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }

    /// <remarks/>
    public RestGrp RestGrp
    {
        get
        {
            return this.restGrpField;
        }
        set
        {
            this.restGrpField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "com/firstdata/Merchant/gmfV6.10")]
public partial class CreditRequestDetails
{

    private CommonGrp commonGrpField;

    private BillPayGrp billPayGrpField;

    private AltMerchNameAndAddrGrp altMerchNameAndAddrGrpField;

    private PFGrp pFGrpField;

    private CardGrp cardGrpField;

    private PINGrp pINGrpField;

    private AddtlAmtGrp[] addtlAmtGrpField;

    private EMVGrp eMVGrpField;

    private TAGrp tAGrpField;

    private TknGrp tknGrpField;

    private OfferGrp offerGrpField;

    private EcommGrp ecommGrpField;

    private SecrTxnGrp secrTxnGrpField;

    private object itemField;

    private PrvLblGrp prvLblGrpField;

    private CanDebitGrp canDebitGrpField;

    private PurchCardlvl2Grp purchCardlvl2GrpField;

    private PurchCardlvl3Grp[] purchCardlvl3GrpField;

    private CustInfoGrp custInfoGrpField;

    private OrderGrp orderGrpField;

    private ShipGrp shipGrpField;

    private SAGrp sAGrpField;

    private OrigAuthGrp origAuthGrpField;

    private DCCGrp dCCGrpField;

    private ProdCodeGrp prodCodeGrpField;

    private ProdCodeDetGrp[] prodCodeDetGrpField;

    private LodgingGrp lodgingGrpField;

    private AutoRentalGrp autoRentalGrpField;

    private FraudMitGrp fraudMitGrpField;

    private RestGrp restGrpField;

    /// <remarks/>
    public CommonGrp CommonGrp
    {
        get
        {
            return this.commonGrpField;
        }
        set
        {
            this.commonGrpField = value;
        }
    }

    /// <remarks/>
    public BillPayGrp BillPayGrp
    {
        get
        {
            return this.billPayGrpField;
        }
        set
        {
            this.billPayGrpField = value;
        }
    }

    /// <remarks/>
    public AltMerchNameAndAddrGrp AltMerchNameAndAddrGrp
    {
        get
        {
            return this.altMerchNameAndAddrGrpField;
        }
        set
        {
            this.altMerchNameAndAddrGrpField = value;
        }
    }

    /// <remarks/>
    public PFGrp PFGrp
    {
        get
        {
            return this.pFGrpField;
        }
        set
        {
            this.pFGrpField = value;
        }
    }

    /// <remarks/>
    public CardGrp CardGrp
    {
        get
        {
            return this.cardGrpField;
        }
        set
        {
            this.cardGrpField = value;
        }
    }

    /// <remarks/>
    public PINGrp PINGrp
    {
        get
        {
            return this.pINGrpField;
        }
        set
        {
            this.pINGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AddtlAmtGrp")]
    public AddtlAmtGrp[] AddtlAmtGrp
    {
        get
        {
            return this.addtlAmtGrpField;
        }
        set
        {
            this.addtlAmtGrpField = value;
        }
    }

    /// <remarks/>
    public EMVGrp EMVGrp
    {
        get
        {
            return this.eMVGrpField;
        }
        set
        {
            this.eMVGrpField = value;
        }
    }

    /// <remarks/>
    public TAGrp TAGrp
    {
        get
        {
            return this.tAGrpField;
        }
        set
        {
            this.tAGrpField = value;
        }
    }

    /// <remarks/>
    public TknGrp TknGrp
    {
        get
        {
            return this.tknGrpField;
        }
        set
        {
            this.tknGrpField = value;
        }
    }

    /// <remarks/>
    public OfferGrp OfferGrp
    {
        get
        {
            return this.offerGrpField;
        }
        set
        {
            this.offerGrpField = value;
        }
    }

    /// <remarks/>
    public EcommGrp EcommGrp
    {
        get
        {
            return this.ecommGrpField;
        }
        set
        {
            this.ecommGrpField = value;
        }
    }

    /// <remarks/>
    public SecrTxnGrp SecrTxnGrp
    {
        get
        {
            return this.secrTxnGrpField;
        }
        set
        {
            this.secrTxnGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("AmexGrp", typeof(AmexGrp))]
    [System.Xml.Serialization.XmlElementAttribute("DSGrp", typeof(DSGrp))]
    [System.Xml.Serialization.XmlElementAttribute("MCGrp", typeof(MCGrp))]
    [System.Xml.Serialization.XmlElementAttribute("VisaGrp", typeof(VisaGrp))]
    public object Item
    {
        get
        {
            return this.itemField;
        }
        set
        {
            this.itemField = value;
        }
    }

    /// <remarks/>
    public PrvLblGrp PrvLblGrp
    {
        get
        {
            return this.prvLblGrpField;
        }
        set
        {
            this.prvLblGrpField = value;
        }
    }

    /// <remarks/>
    public CanDebitGrp CanDebitGrp
    {
        get
        {
            return this.canDebitGrpField;
        }
        set
        {
            this.canDebitGrpField = value;
        }
    }

    /// <remarks/>
    public PurchCardlvl2Grp PurchCardlvl2Grp
    {
        get
        {
            return this.purchCardlvl2GrpField;
        }
        set
        {
            this.purchCardlvl2GrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("PurchCardlvl3Grp")]
    public PurchCardlvl3Grp[] PurchCardlvl3Grp
    {
        get
        {
            return this.purchCardlvl3GrpField;
        }
        set
        {
            this.purchCardlvl3GrpField = value;
        }
    }

    /// <remarks/>
    public CustInfoGrp CustInfoGrp
    {
        get
        {
            return this.custInfoGrpField;
        }
        set
        {
            this.custInfoGrpField = value;
        }
    }

    /// <remarks/>
    public OrderGrp OrderGrp
    {
        get
        {
            return this.orderGrpField;
        }
        set
        {
            this.orderGrpField = value;
        }
    }

    /// <remarks/>
    public ShipGrp ShipGrp
    {
        get
        {
            return this.shipGrpField;
        }
        set
        {
            this.shipGrpField = value;
        }
    }

    /// <remarks/>
    public SAGrp SAGrp
    {
        get
        {
            return this.sAGrpField;
        }
        set
        {
            this.sAGrpField = value;
        }
    }

    /// <remarks/>
    public OrigAuthGrp OrigAuthGrp
    {
        get
        {
            return this.origAuthGrpField;
        }
        set
        {
            this.origAuthGrpField = value;
        }
    }

    /// <remarks/>
    public DCCGrp DCCGrp
    {
        get
        {
            return this.dCCGrpField;
        }
        set
        {
            this.dCCGrpField = value;
        }
    }

    /// <remarks/>
    public ProdCodeGrp ProdCodeGrp
    {
        get
        {
            return this.prodCodeGrpField;
        }
        set
        {
            this.prodCodeGrpField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("ProdCodeDetGrp")]
    public ProdCodeDetGrp[] ProdCodeDetGrp
    {
        get
        {
            return this.prodCodeDetGrpField;
        }
        set
        {
            this.prodCodeDetGrpField = value;
        }
    }

    /// <remarks/>
    public LodgingGrp LodgingGrp
    {
        get
        {
            return this.lodgingGrpField;
        }
        set
        {
            this.lodgingGrpField = value;
        }
    }

    /// <remarks/>
    public AutoRentalGrp AutoRentalGrp
    {
        get
        {
            return this.autoRentalGrpField;
        }
        set
        {
            this.autoRentalGrpField = value;
        }
    }

    /// <remarks/>
    public FraudMitGrp FraudMitGrp
    {
        get
        {
            return this.fraudMitGrpField;
        }
        set
        {
            this.fraudMitGrpField = value;
        }
    }

    /// <remarks/>
    public RestGrp RestGrp
    {
        get
        {
            return this.restGrpField;
        }
        set
        {
            this.restGrpField = value;
        }
    }
}
