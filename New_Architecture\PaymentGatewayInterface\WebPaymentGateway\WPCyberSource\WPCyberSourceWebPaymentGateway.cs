﻿using Newtonsoft.Json;
using Semnox.Parafait.PaymentGatewayInterface.WPCyberSource;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Semnox.Parafait.PaymentGatewayInterface
{
  public  class WPCyberSourceWebPaymentGateway : PaymentGateway, IPaymentGateway
    {
        private readonly Semnox.Parafait.logging.Logger log;


        private Dictionary<string, string> responseTextCollection = new Dictionary<string, string>();

        private string siteId;
        private string PARTNER_SOLUTION_ID;
        private string ACCESS_KEY;
        private string PROFILE_ID;
        private string CHECKOUT_URL;
        private string SECRET_KEY;
        private string HOST_URL;
        private string REST_SECRET_KEY;
        private string PUBLIC_KEY;
        private string MERCHANT_ID;
        private string CURRENCY_CODE;
        private string IGNORE_AVS;
        private string SUCCESS_RESPONSE_API_URL;
        private string FAILURE_RESPONSE_API_URL;
        private string BUSINESS_DAY_START_TIME;

        private readonly WPCyberSourceWebCommandHandler _wPCyberSourceWebCommandHandler;

        private enum TxResponse
        {
            SUCCESS = 100,
            INVALID_REQUEST = 102,
            PARTIAL_AMOUNT_APPROVED = 110,
            DUPLICATE_TRANSACTION = 104,
            GENERAL_SYSTEM_FAILURE = 150,
            SERVER_TIMEOUT = 151,
            SERVICE_TIMEOUT = 152,
            ISSUING_BANK_UNAVAILABLE = 207,
            PROCESSOR_FAILURE = 236,
        };

        public override bool IsRefundSupported
        {
            get
            {
                return true;
            }
        }
        public override bool IsStatusCheckSupported
        {
            get
            {
                return true;
            }
        }
        public override bool CanCreateMultipleInstances
        {
            get
            {
                return true;
            }
        }


        public WPCyberSourceWebPaymentGateway(PaymentConfiguration paymentConfiguration, PaymentMessages paymentMessages, Semnox.Parafait.logging.Logger logger)
           : base(paymentConfiguration, paymentMessages)
        {
            log = logger;
            log.LogMethodEntry();

            BUSINESS_DAY_START_TIME = paymentConfiguration.GetConfiguration("BUSINESS_DAY_START_TIME");
            CURRENCY_CODE = paymentConfiguration.GetConfiguration("CURRENCY_CODE");
            siteId = paymentConfiguration.GetConfiguration("SITE_ID");

            _wPCyberSourceWebCommandHandler = new WPCyberSourceWebCommandHandler(paymentConfiguration, log);
            
            log.LogMethodExit(null);
        }

        public override void ValidateConfiguration()
        {
            log.LogMethodEntry("START - Configuration Validation");
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PARTNER_ID")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_PARTNER_ID" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_SECRET_KEY")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_SECRET_KEY" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_API_URL" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_BASE_URL")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "HOSTED_PAYMENT_GATEWAY_BASE_URL" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("WPCyberSource secret key")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "WPCyberSource secret key" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CURRENCY_CODE")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CURRENCY_CODE" + " SiteId: " + siteId);
            }
            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("BUSINESS_DAY_START_TIME")))
            {
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "BUSINESS_DAY_START_TIME" + " SiteId: " + siteId);
            }
            //if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ENABLE_ADDRESS_VALIDATION")))
            //{
            //    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ENABLE_ADDRESS_VALIDATION" + " SiteId: " + siteId);
            //}
            log.LogMethodExit("END - completed validation");
        }

        public async override Task<PaymentSessionDTO> CreatePaymentSessionDTO(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);

            PaymentSessionDTO paymentSessionDTO = new PaymentSessionDTO
            {
                RequestId = paymentRequestDTO.RequestIdentifier,
                IntRequestId = paymentRequestDTO.IntRequestIdentifier,
                PaymentGatewayName = PaymentGateways.WPCyberSourceCallbackHostedPayment.ToString()
            };

            log.Debug("Creating HostedPaymentSessionForm");
            paymentSessionDTO.HostedPaymentSessionForm = _wPCyberSourceWebCommandHandler.prepareForm(paymentRequestDTO);

            log.LogMethodExit(paymentSessionDTO);
            return await Task.FromResult(paymentSessionDTO);
        }

  
        private WorldPayWebResponseDTO GetResposeObj(string gatewayResponse)
        {
            //log.LogMethodEntry(gatewayResponse);
            WorldPayWebResponseDTO responseObj = null;
            //utf8=%E2%9C%93&auth_cv_result=2&req_locale=en-gb&req_payer_authentication_indicator=01&req_card_type_selection_indicator=1&payer_authentication_enroll_veres_enrolled=U&req_bill_to_surname=Pai&req_card_expiry_date=12-2023&card_type_name=Visa&auth_amount=15.00&auth_response=0&bill_trans_ref_no=155728&req_payment_method=card&req_payer_authentication_merchant_name=Test+Company&auth_time=2023-01-16T125802Z&payer_authentication_enroll_e_commerce_indicator=internet&transaction_id=6738738818486230204002&req_card_type=001&payer_authentication_transaction_id=mntJCHI2jJQJTY2PRUH0&req_payer_authentication_transaction_mode=S&req_merchant_defined_data1=155728&req_merchant_defined_data2=636&auth_avs_code=U&auth_code=15&payer_authentication_specification_version=2.1.0&req_bill_to_address_country=IN&auth_cv_result_raw=3&req_profile_id=8F564CA8-C495-40DC-8DE6-6B478F5073EF&req_partner_solution_id=RVA8YQK8&signed_date_time=2023-01-16T12%3A58%3A02Z&req_bill_to_address_line1=mangalore&req_card_number=xxxxxxxxxxxx3705&signature=AO10gF6Friey8H3a5pcmpkzvIYHAJ%2BAI4l39oFnvxNk%3D&req_bill_to_address_city=Mangalore&req_bill_to_address_postal_code=575001&reason_code=100&req_bill_to_forename=Nitin&req_payer_authentication_acs_window_size=03&request_token=Axj%2F%2FwSTbU7JlAt3Rj5iABIMxatW7JwojlScBjsBURypOAx3nQu7heRDJpJl6MWZ7cQYOJNtTsmUC3dGPmIAzw0a&req_amount=15&req_bill_to_email=nitin.pai%40semnox.com&payer_authentication_reason_code=100&auth_avs_code_raw=00&req_payer_authentication_challenge_code=04&req_currency=GBP&decision=ACCEPT&message=Request+was+processed+successfully.&signed_field_names=transaction_id%2Cdecision%2Creq_access_key%2Creq_profile_id%2Creq_transaction_uuid%2Creq_transaction_type%2Creq_reference_number%2Creq_amount%2Creq_currency%2Creq_locale%2Creq_payment_method%2Creq_bill_to_forename%2Creq_bill_to_surname%2Creq_bill_to_email%2Creq_bill_to_address_line1%2Creq_bill_to_address_city%2Creq_bill_to_address_country%2Creq_bill_to_address_postal_code%2Creq_card_number%2Creq_card_type%2Creq_card_type_selection_indicator%2Creq_card_expiry_date%2Ccard_type_name%2Creq_merchant_defined_data1%2Creq_merchant_defined_data2%2Creq_partner_solution_id%2Creq_payer_authentication_acs_window_size%2Creq_payer_authentication_indicator%2Creq_payer_authentication_challenge_code%2Creq_payer_authentication_transaction_mode%2Creq_payer_authentication_merchant_name%2Cmessage%2Creason_code%2Cauth_avs_code%2Cauth_avs_code_raw%2Cauth_response%2Cauth_amount%2Cauth_code%2Cauth_cv_result%2Cauth_cv_result_raw%2Cauth_time%2Crequest_token%2Cbill_trans_ref_no%2Cpayer_authentication_reason_code%2Cpayer_authentication_enroll_e_commerce_indicator%2Cpayer_authentication_specification_version%2Cpayer_authentication_transaction_id%2Cpayer_authentication_enroll_veres_enrolled%2Csigned_field_names%2Csigned_date_time&req_transaction_uuid=7c904494-2e0f-43b1-9ec2-34dfd8bfb3f1&req_transaction_type=sale&req_access_key=248d9649918d38c2b1deaeca0253c7e1&req_reference_number=155728

            try
            {
                string jsonString = ConvertQueryStringToJson(gatewayResponse);
                responseObj = JsonConvert.DeserializeObject<WorldPayWebResponseDTO>(jsonString.ToString());
            }
            catch (Exception ex)
            {
                log.Error("Error parsing response JSON. JSON: ", ex);
                throw new ResponseParsingFailedException(paymentMessages.GetMessage(6144), ex); //Failed to convert gatewayResponse string to response object
            }

            log.LogMethodExit(responseObj);
            return responseObj;
        }

        private string ConvertQueryStringToJson(string gatewayResponse)
        {
            log.LogMethodEntry();
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(gatewayResponse);

            Dictionary<string, string> responseDictionary = new Dictionary<string, string>();

            foreach (var key in responseCollection.AllKeys)
            {
                responseDictionary.Add(key, responseCollection[key]);
            }

            string responseJson = JsonConvert.SerializeObject(responseDictionary);

            log.LogMethodExit(responseJson);
            return responseJson;
        }

        public async override Task<string> GetPaymentIdentifier(string paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);
            String paymentGatewayIdentifier = string.Empty;

            try
            {
                WorldPayWebResponseDTO responseObj = GetResposeObj(paymentResponse);
                log.Debug(responseObj.req_reference_number + ":" + responseObj.transaction_id);
                paymentGatewayIdentifier = responseObj.req_reference_number;
            }
            catch (Exception ex)
            {
                log.Error("Error while extracting PaymentIdentifier.", ex);
                throw new ResponseParsingFailedException(paymentMessages.GetMessage(6087), ex);
            }

            log.LogMethodExit(paymentGatewayIdentifier);
            return await Task.FromResult(paymentGatewayIdentifier);
        }



        public async override Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO)
        {
            string paymentResponse = paymentGatewayResponseDTO.GatewayResponse;
            log.LogMethodEntry(paymentResponse);
            
            string invoiceNo = "";
            string recordNo = "";
            string refNo = "";
            string authCode = "";
            decimal amount = 0;
            string authorize = "";
            string tranCode = "";
            string status = "";
            string textResponse = "";
            string dSIXReturnCode = "";
            string acctNo = "";
            string creditCardName = "";
            string nameOnCreditCard = "";
            string creditCardExpiry = "";
            string cardType = "";
            string purchase = "";

            try
            {
                loadResponseText();
                // proceed with processing
                WorldPayWebResponseDTO responseObj = GetResposeObj(paymentResponse);
                if (responseObj != null)
                {
                    log.Debug("Payment Identifier:" + responseObj.req_reference_number);

                    // verify signature
                    string serializedResponse = ConvertQueryStringToJson(paymentResponse);
                    bool result = _wPCyberSourceWebCommandHandler.verifySignature(serializedResponse);
                    if (!result)
                    {
                        log.Info("Payment signature verification failed!");
                        throw new SignatureValidationFailureException(paymentMessages.GetMessage(6145)); //Payment signature verification failed!
                    }
                    if (Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.SUCCESS)
                    {
                        String reasonCode = String.IsNullOrEmpty(responseObj.reason_code) ? "" : responseObj.reason_code;
                        log.Info("Reason code:" + reasonCode);
                        dSIXReturnCode = reasonCode;
                        authCode = responseObj.auth_code ?? "";
                        authorize = responseObj.auth_amount ?? "";
                        purchase = responseObj.req_amount ?? "";
                        Decimal approvedAmount = 0.0M;
                        Decimal.TryParse(authorize, out approvedAmount);
                        amount = approvedAmount;
                        refNo = responseObj.transaction_id ?? ""; // Gateway reference
                        invoiceNo = responseObj.req_reference_number ?? ""; // payment guid
                        textResponse = "SUCCESS " + responseObj.message ?? "";
                        nameOnCreditCard = responseObj.req_bill_to_forename + " " + responseObj.req_bill_to_surname;
                        acctNo = responseObj.req_card_number ?? "XXXXXXXXXXXXXXXX";
                        cardType = responseObj.req_card_type ?? "";
                        creditCardExpiry = responseObj.req_card_expiry_date ?? "";
                        tranCode = PaymentGatewayTransactionType.SALE.ToString();
                        status = PaymentTransactionStatuses.SUCCESS.ToString();
                    }
                    else
                    {
                        log.Debug("Failed Reason Code is " + responseObj.reason_code);
                        // something wrong with the Tx
                        if (Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.PARTIAL_AMOUNT_APPROVED ||
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.DUPLICATE_TRANSACTION ||
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.GENERAL_SYSTEM_FAILURE ||
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.SERVER_TIMEOUT ||
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.SERVICE_TIMEOUT ||
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.PROCESSOR_FAILURE ||
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.ISSUING_BANK_UNAVAILABLE)
                        {
                            log.Info("In list of handled Reason code:" + responseObj.reason_code.ToString());
                            // check Tx details; If captured, then VOID
                            // build Tx Search requestDTO
                            TxSearchRequestDTO searchRequestDTO = new TxSearchRequestDTO
                            {
                                query = "clientReferenceInformation.code:" + responseObj.req_reference_number,
                                sort = "id:desc",
                            };
                            TxSearchResponseDTO txSearchResponse = _wPCyberSourceWebCommandHandler.CreateTxSearch(searchRequestDTO);
                            log.Info("txSearchResponse:" + txSearchResponse.ToString());
                            TxStatusDTO txStatus = _wPCyberSourceWebCommandHandler.GetTxStatusFromSearchResponse(txSearchResponse);
                            log.Info("txStatus:" + txStatus.ToString());

                            // if any Tx has been applied at PG; then first update cCTransactionsPGWDTO
                            if (txStatus.TxType == "SALE")
                            {
                                log.Debug("Partial payment has been approved");
                                log.Info("Reason code:" + txStatus.reasonCode.ToString());
                                dSIXReturnCode = txStatus.reasonCode.ToString();
                                authCode = txStatus.AuthCode;
                                authorize = txStatus.Authorize;
                                purchase = txStatus.Purchase;
                                Decimal approvedAmount = 0.0M;
                                Decimal.TryParse(txStatus.Authorize, out approvedAmount);
                                amount = approvedAmount;
                                refNo = txStatus.RefNo; // Gateway reference
                                invoiceNo = txStatus.InvoiceNo; // payment guid
                                textResponse = "SUCCESS " + txStatus.TextResponse;

                                acctNo = !String.IsNullOrEmpty(txStatus.AcctNo) ? txStatus.AcctNo : String.Empty;

                                tranCode = PaymentGatewayTransactionType.SALE.ToString();
                                status = PaymentTransactionStatuses.SUCCESS.ToString();
                            }
                            else if (txStatus.TxType == "NA")
                            {
                                log.Debug("No Payment has been found");
                                dSIXReturnCode = txStatus.reasonCode.ToString();
                                authCode = txStatus.AuthCode;
                                refNo = txStatus.RefNo; // Gateway reference
                                invoiceNo = txStatus.InvoiceNo; // payment guid
                                textResponse = "FAILED " + txStatus.TextResponse;
                                acctNo = string.Empty;
                                tranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                                status = PaymentTransactionStatuses.FAILED.ToString();
                            }
                        }
                        else
                        {
                            log.Debug("In list of unhandled reason codes");
                            dSIXReturnCode = String.IsNullOrEmpty(responseObj.reason_code ?? "") ? "" : responseObj.reason_code;
                            authCode = responseObj.auth_code ?? "";
                            authorize = "0";
                            purchase = "0";
                            amount = 0.0M;
                            refNo = responseObj.transaction_id ?? ""; // Gateway reference
                            invoiceNo = responseObj.req_reference_number ?? ""; // payment guid
                            textResponse = "FAILED " + responseObj.message ?? "";
                            acctNo = string.Empty;
                            tranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                            status = PaymentTransactionStatuses.FAILED.ToString();
                        }
                    }
                }
                else
                {
                    log.Debug("responseObj is null");
                    status = PaymentTransactionStatuses.FAILED.ToString();
                    purchase = "0";
                    authorize = "0";
                    amount = 0.0M;
                    dSIXReturnCode = "Invalid response received.";
                    textResponse = "Invalid response received.";
                }
            }
            catch (PaymentResponseProcessingException ex)
            {
                log.Error("Payment Gateway Response DTO: " + paymentGatewayResponseDTO.ToString());
                log.Error("Exception occured while processing payment response. Returning exception " + ex);
                recordNo = "C";
                status = PaymentTransactionStatuses.FAILED.ToString();
                purchase = "0";
                authorize = "0";
                amount = 0.0M;
                dSIXReturnCode = ex.Message;
                textResponse = ex.Message;
            }
            catch (Exception ex)
            {
                log.Error("Payment Gateway Response DTO: " + paymentGatewayResponseDTO.ToString());
                log.Error("Exception in process gateway response", ex);
                throw new PaymentResponseProcessingException(paymentMessages.GetMessage(6148), ex); //Exception in process gateway response
            }

            PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, cardType, tranCode, refNo, purchase, authorize, DateTime.MinValue, authCode, null, null, null, null, null, null, null, null, null, status, creditCardName, null, null, amount);

            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }


        /// <summary>
        /// Reverts the payment.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public async override Task<PaymentResponseDTO> Refund(RefundRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)

        {
            log.LogMethodEntry(paymentRequestDTO);

            PaymentResponseDTO paymentResponseDTO;

            string refundTrxId = paymentRequestDTO.RequestIdentifier;
            string invoiceNo = "";
            string recordNo = "";
            string refNo = "";
            string authCode = "";
            decimal amount = 0;
            string authorize = "";
            string tranCode = "";
            DateTime transactionDatetime = paymentRequestDTO.RequestDate;
            string status = "";
            string textResponse = "";
            string dSIXReturnCode = "";
            string acqRefData = "";
            string acctNo = "";
            string purchase = "";

   
            try
            {
                DateTime originalPaymentDate = paymentRequestDTO.PaymentResponses.TransactionDatetime;

                // Define Business Start and End Time
                DateTime bussStartTime = paymentRequestDTO.RequestDate.Date.AddHours(Convert.ToInt32(BUSINESS_DAY_START_TIME));
                DateTime bussEndTime = bussStartTime.AddDays(1);
                if (paymentRequestDTO.RequestDate < bussStartTime)
                {
                    bussStartTime = bussStartTime.AddDays(-1);
                    bussEndTime = bussStartTime.AddDays(1);
                }

                // Decide Void vs Refund basis the Date
                if ((originalPaymentDate >= bussStartTime) && (originalPaymentDate <= bussEndTime))
                {
                    // same day: VOID
                    log.Info("Same day: Void");
                    WorldPayWebRequestDTO worldPayRequestDTO = _wPCyberSourceWebCommandHandler.getRequestDTO(paymentRequestDTO.PaymentResponses.RefNo);
                    log.Debug("getRequestDTO- worldPayRequestDTO: " + worldPayRequestDTO);
                    VoidRequestDTO voidRequestDTO = null;
                    voidRequestDTO = new VoidRequestDTO
                    {
                        clientReferenceInformation = new Clientreferenceinformation
                        {
                            code = refundTrxId, // ccRequestId
                        },
                    };
                    VoidResponseDTO voidResponseDTO;
                    voidResponseDTO = _wPCyberSourceWebCommandHandler.CreateVoid(worldPayRequestDTO, voidRequestDTO);
                    log.Debug("voidResponseDTO: " + voidResponseDTO);

                    if (voidResponseDTO != null && voidResponseDTO.status == "VOIDED")
                    {
                        invoiceNo = paymentRequestDTO.RequestIdentifier;
                        tranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        acctNo = paymentRequestDTO.PaymentResponses.AcctNo;
                        authorize = paymentRequestDTO.Amount.ToString();
                        amount = paymentRequestDTO.Amount;
                        recordNo = paymentRequestDTO.IntRequestIdentifier.ToString(); //parafait TrxId
                        refNo = paymentRequestDTO.PaymentResponses.RefNo; // Gateway reference
                        purchase = paymentRequestDTO.PaymentResponses.Purchase;
                        textResponse = "SUCCESS";
                        dSIXReturnCode = voidResponseDTO.status;
                        status = PaymentTransactionStatuses.SUCCESS.ToString();

                        log.Debug("Void Success");
                        //responseOrigin = paymentRequestDTO.PaymentResponses.ResponseOrigin;
                    }
                    else
                    {
                        log.Debug("Void Failed " + (voidResponseDTO != null ? voidResponseDTO.status : "void object not found"));
                        tranCode = PaymentGatewayTransactionType.VOID.ToString();
                        status = PaymentTransactionStatuses.FAILED.ToString();
                    }
                }
                else
                {
                    // Next Day: Refund
                    log.Info("Next Day: Refund");
                    WorldPayWebRequestDTO worldPayRequestDTO = _wPCyberSourceWebCommandHandler.getRequestDTO(paymentRequestDTO.PaymentResponses.RefNo);
                    log.Debug("getRequestDTO- worldPayRequestDTO: " + worldPayRequestDTO);
                    RefundResponseDTO refundResponseDTO = null;
                    WPRefundRequestDTO refundRequestDTO = null;
                    refundRequestDTO = new WPRefundRequestDTO
                    {
                        clientReferenceInformation = new Clientreferenceinformation
                        {
                            code = refundTrxId, // ccRequestId
                        },
                        orderInformation = new Orderinformation
                        {
                            amountDetails = new Amountdetails
                            {
                                // get the amount to be refunded from the refundTransactionPaymentsDTO
                                totalAmount = Convert.ToString(paymentRequestDTO.PaymentResponses.Amount),
                                //currency = CURRENCY_CODE,
                            }
                        },
                    };
                    refundResponseDTO = _wPCyberSourceWebCommandHandler.CreateRefund(worldPayRequestDTO, refundRequestDTO);
                    log.Debug("refundResponseDTO: " + refundResponseDTO);

                    if (refundResponseDTO != null && refundResponseDTO.status == "PENDING")
                    {
                        log.Debug("Refund Success");
                        invoiceNo = paymentRequestDTO.RequestIdentifier;
                        tranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        acctNo = paymentRequestDTO.PaymentResponses.AcctNo;
                        authorize = paymentRequestDTO.Amount.ToString();
                        amount = paymentRequestDTO.Amount;
                        recordNo = paymentRequestDTO.IntRequestIdentifier.ToString(); //parafait TrxId
                        refNo = paymentRequestDTO.PaymentResponses.RefNo; // Gateway reference
                        purchase = paymentRequestDTO.PaymentResponses.Purchase;
                        textResponse = "SUCCESS";
                        dSIXReturnCode = refundResponseDTO.status;
                        status = PaymentTransactionStatuses.SUCCESS.ToString();

                    }
                    else
                    {
                        log.Debug("Refund Failed " + (refundResponseDTO != null ? refundResponseDTO.status : "refund object not found"));
                        tranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        status = PaymentTransactionStatuses.FAILED.ToString();
                    }
                }

                paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, null, tranCode, refNo, authorize, authorize, transactionDatetime, authCode, null, null, null, null, acqRefData, null, null, null, null, status, null, null, null, amount);
            }
            catch (Exception ex)
            {
                log.Error("Refund request DTO: " + paymentRequestDTO.ToString());
                log.Error("Refund failed: " + ex.Message, ex);
                throw new RefundFailedException(paymentMessages.GetMessage(5721), ex); // Refund Failed 
            }

            log.LogMethodExit(paymentResponseDTO);
            return paymentResponseDTO;
        }

        public async override Task<PaymentResponseDTO> StatusCheck(StatusCheckRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken,string errorMsg)
        {
            log.LogMethodEntry(paymentRequestDTO);
            PaymentResponseDTO paymentResponseDTO;

            try
            {
                if (string.IsNullOrWhiteSpace(paymentRequestDTO.RequestIdentifier))
                {
                    log.Error("No Transaction id passed");
                    throw new TransactionIdNullException(paymentMessages.GetMessage(5944));
                }

                string statusMessage = "";
                string failureMessage = "";
                string invoiceNo = "";
                string recordNo = "";
                string refNo = "";
                string authCode = "";
                decimal amount = 0;
                string authorize = "";
                string purchase = "";
                string currency = "";
                string tranCode = "";
                DateTime transactionDatetime;
                string status = "";
                string textResponse = "";
                string dSIXReturnCode = "";
                string acqRefData = "";
                string responseOrigin = "";
                string acctNo = "";
                string cardType = "";

                // build Tx Search requestDTO
                TxSearchRequestDTO searchRequestDTO = _wPCyberSourceWebCommandHandler.GetTxSearchRequestDTO(paymentRequestDTO.RequestIdentifier);
                log.Debug("GetTxSearchRequestDTO- searchRequestDTO: " + searchRequestDTO);
                TxSearchResponseDTO txSearchResponseDTO = _wPCyberSourceWebCommandHandler.CreateTxSearch(searchRequestDTO);
                log.Debug("CreateTxSearch- txSearchResponseDTO: " + txSearchResponseDTO);

                invoiceNo = paymentRequestDTO.RequestIdentifier;
                tranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                if (txSearchResponseDTO != null && txSearchResponseDTO.totalCount > 0)
                {
                    log.Info("Total count of txSearchResponse: " + txSearchResponseDTO.totalCount.ToString());
                    TxStatusDTO txStatus = _wPCyberSourceWebCommandHandler.GetTxStatusFromSearchResponse(txSearchResponseDTO);
                    log.Debug("GetTxStatusFromSearchResponse- txStatus: " + txStatus);

                    log.Info("Tx Status reasonCode: " + txStatus.reasonCode.ToString());

                    // check if sale/void/refund Tx present
                    // if yes then proceed
                    if (txStatus.TxType == "SALE")
                    {
                        if (Convert.ToInt32(txStatus.reasonCode) == (int)TxResponse.SUCCESS)
                        {
                            log.Info("CC Transactions found with reasonCode 100");
                            authCode = txStatus.AuthCode;
                            authorize = txStatus.Authorize;
                            purchase = txStatus.Purchase;
                            amount = Convert.ToDecimal(!String.IsNullOrEmpty(txStatus.Authorize) ? txStatus.Authorize : "0.0");
                            transactionDatetime = txStatus.TransactionDatetime;
                            refNo = txStatus.RefNo; // Gateway reference
                            acctNo = txStatus.AcctNo;
                            textResponse = txStatus.TextResponse;
                            dSIXReturnCode = txStatus.reasonCode.ToString();
                            status = PaymentTransactionStatuses.SUCCESS.ToString();
                        }
                        else
                        {
                            log.Info("CC Transactions found with reasonCode other than 100");
                            authorize = !String.IsNullOrEmpty(txStatus.Authorize) ? txStatus.Authorize : String.Empty;
                            purchase = txStatus.Purchase;
                            amount = Convert.ToDecimal(!String.IsNullOrEmpty(txStatus.Authorize) ? txStatus.Authorize : "0.0");
                            transactionDatetime = paymentRequestDTO.RequestDate;
                            refNo = txStatus.RefNo; // Gateway reference
                            acctNo = !String.IsNullOrEmpty(txStatus.AcctNo) ? txStatus.AcctNo : String.Empty;
                            textResponse = txStatus.TextResponse;
                            dSIXReturnCode = txStatus.reasonCode.ToString();
                            status = PaymentTransactionStatuses.FAILED.ToString();
                        }
                        paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, cardType, tranCode, refNo, purchase, authorize, transactionDatetime, authCode, null, responseOrigin, null, null, acqRefData, null, null, null, null, status, null, null, null, amount);
                    }
                    else
                    {
                        log.Error("No SALE trx found");
                        throw new StatusCheckResponseNullException(paymentMessages.GetMessage(6146)); //No SALE transaction found while StatusCheck
                    }
                }
                else
                {
                    log.Error("Response count is 0");
                    throw new StatusCheckResponseNullException(paymentMessages.GetMessage(6147)); //No payment found while StatusCheck
                }
            }
            catch (Exception ex)
            {
                log.Error("Status Check request DTO: " + paymentRequestDTO.ToString());
                log.Error("Error performing GetPaymentStatus. Error message: " + ex);
                
                paymentResponseDTO = new PaymentResponseDTO
                {
                    InvoiceNo = paymentRequestDTO.RequestIdentifier,
                    Purchase = "0",
                    Authorize = "0",
                    Amount = 0.0M,
                    TransactionDatetime = paymentRequestDTO.RequestDate,
                    TextResponse = "Error performing GetPaymentStatus!",
                    DSIXReturnCode = ex.Message,
                    Status = PaymentTransactionStatuses.ERROR.ToString(),
                    TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString()
                };
            }

            log.LogMethodExit(paymentResponseDTO);
            return paymentResponseDTO;
        }


        private void loadResponseText()
        {
            try
            {
                responseTextCollection.Add("100", "Successful transaction.");
                responseTextCollection.Add("102", "One or more fields in the request contain invalid data.");
                responseTextCollection.Add("104", "The access_key and transaction_uuid fields for this authorization request match the access_key and transaction_uuid fields of another authorization request that you sent within the past 15 minutes.");
                responseTextCollection.Add("110", "Only a partial amount was approved.");
                responseTextCollection.Add("150", "General system failure.");
                responseTextCollection.Add("151", "The request was received but a server timeout occurred.");
                responseTextCollection.Add("152", "The request was received, but a service timeout occurred.");
                responseTextCollection.Add("200", "The authorization request was approved by the issuing bank but declined because it did not pass the Address Verification System (AVS) check.");
                responseTextCollection.Add("201", "The issuing bank has questions about the request.");
                responseTextCollection.Add("202", "Expired card.");
                responseTextCollection.Add("203", "General decline of the card.");
                responseTextCollection.Add("204", "Insufficient funds in the account.");
                responseTextCollection.Add("205", "Stolen or lost card.");
                responseTextCollection.Add("207", "Issuing bank unavailable.");
                responseTextCollection.Add("208", "Inactive card or card not authorized for card-not-present transactions.");
                responseTextCollection.Add("210", "The card has reached the credit limit.");
                responseTextCollection.Add("211", "Invalid CVN.");
                responseTextCollection.Add("221", "The customer matched an entry on the processor’s negative file.");
                responseTextCollection.Add("222", "Account frozen.");
                responseTextCollection.Add("230", "The authorization request was approved by the issuing bank but declined because it did not pass the CVN check.");
                responseTextCollection.Add("231", "Invalid account number.");
                responseTextCollection.Add("232", "The card type is not accepted by the payment processor.");
                responseTextCollection.Add("233", "General decline by the processor.");
                responseTextCollection.Add("234", "There is a problem with the information in your account.");
                responseTextCollection.Add("236", "Processor failure.");
                responseTextCollection.Add("240", "The card type sent is invalid or does not correlate with the payment card number.");
                responseTextCollection.Add("475", "The cardholder is enrolled for payer authentication.");
                responseTextCollection.Add("476", "Payer authentication could not be authenticated.");
                responseTextCollection.Add("481", "Transaction declined based on your payment settings for the profile.");
                responseTextCollection.Add("520", "The authorization request was approved by the issuing bank but declined based on your legacy Smart Authorization settings.");
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }


    }
}
    