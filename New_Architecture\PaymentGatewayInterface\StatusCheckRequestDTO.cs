﻿
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
   public  class StatusCheckRequestDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        private decimal amount;
        private string requestIdentifier;
        private int intRequestIdentifier;
        private decimal tipAmount;
        private List<PaymentResponseDTO> paymentResponseHistory;
        private PaymentResponseDTO paymentResponses;
        private string paymentStatus;
        private DateTime requestDate;




        /// <summary>
        /// Constructor with all the data fields
        /// </summary>
        public StatusCheckRequestDTO(decimal amount, string requestIdentifier, int intRequestIdentifier, List<PaymentResponseDTO> paymentResponseHistory, string paymentStatus, DateTime requestDate)

        {
            log.LogMethodEntry(amount, requestIdentifier);
            this.amount = amount;
            this.requestDate = requestDate;
            this.requestIdentifier = requestIdentifier;
            this.intRequestIdentifier = intRequestIdentifier;
            this.paymentResponseHistory = paymentResponseHistory;
            this.paymentStatus = paymentStatus;
            log.LogMethodExit();
        }

        /// <summary>
        /// Get/Set method of the Amount field
        /// </summary>
        public decimal Amount
        {
            get
            {
                return amount;
            }

            set
            {
                //IsChanged = true;
                amount = value;
            }
        }

        public string PaymentStatus
        {
            get
            {
                return paymentStatus;
            }

            set
            {
                //IsChanged = true;
                paymentStatus = value;
            }
        }
        /// <summary>
        /// Get method of the RequestDate field
        /// </summary>
        public DateTime RequestDate
        {
            get
            {
                return requestDate;
            }
            set
            {
                // this.IsChanged = true;
                requestDate = value;
            }
        }
        public decimal TipAmount
        {
            get
            {
                return tipAmount;
            }

            set
            {
                //IsChanged = true;
                tipAmount = value;
            }
        }
        public string RequestIdentifier
        {
            get
            {
                return requestIdentifier;
            }

            set
            {
                //IsChanged = true;
                requestIdentifier = value;
            }
        }

        public int IntRequestIdentifier
        {
            get
            {
                return intRequestIdentifier;
            }

            set
            {
                intRequestIdentifier = value;
            }
        }

        public List<PaymentResponseDTO> PaymentResponseHistory
        {
            get { return paymentResponseHistory; }
            set { paymentResponseHistory = value; }
        }
        public PaymentResponseDTO PaymentResponses
        {
            get { return paymentResponses; }
            set { paymentResponses = value; }
        }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
