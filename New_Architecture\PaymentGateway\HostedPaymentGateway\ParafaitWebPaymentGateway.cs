﻿using Semnox.Core.Utilities;
using Semnox.Parafait.Customer;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Site;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Semnox.Parafait.PaymentGateway
{
    public class ParafaitWebPaymentGateway : PaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        IPaymentGateway paymentGateway = null;
        PaymentModeContainerDTO paymentModeContainerDTO = null;

        public ParafaitWebPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken, IPaymentGateway paymentGateway, PaymentModeContainerDTO paymentModeContainerDTO)
            : base(executionContext, isUnattended, cancellationToken)
        {
            log.LogMethodEntry(executionContext, isUnattended, writeToLogDelegate, paymentGateway);

            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel   
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12; // comparable to modern browsers

            this.paymentGateway = paymentGateway;
            this.ExecutionContext = executionContext;
            this.paymentModeContainerDTO = paymentModeContainerDTO;
        }

        public override void ValidateConfiguration()
        {
            log.LogMethodEntry();
            paymentGateway.ValidateConfiguration();
            log.LogMethodExit();
        }

        public async Task<PaymentSessionDTO> CreatePaymentSessionDTO(PaymentRequestDTO paymentRequestDTO, TransactionPaymentDTO transactionPaymentDTO, UnitOfWork unitOfWork)
        {
            log.LogMethodEntry(paymentRequestDTO);
            
            if (paymentGateway.IsProductsInfoRequired || paymentGateway.IsCustomerInfoRequired)
            {
                log.Debug("Building the Transaction for productList.");
                TransactionBL transactionBL = new TransactionBL(ExecutionContext, transactionPaymentDTO.TransactionId, unitOfWork);

                Dictionary<string, PaymentGatewayProductDTO> productIdPaymentGatewayProductDTOMap = new Dictionary<string, PaymentGatewayProductDTO>();
                foreach (TransactionLineBL transactionLineBL in transactionBL.TransactionLineCollection.ActiveTransactionLineBLSequenctialList)
                {
                    PaymentGatewayProductDTO paymentGatewayProductDTO;
                    string id = $"Product:{transactionLineBL.ProductId}Amount:{transactionLineBL.NetAmount.ToString("0.00")}";
                    if (productIdPaymentGatewayProductDTOMap.ContainsKey(id) == false)
                    {
                        paymentGatewayProductDTO = new PaymentGatewayProductDTO();
                        paymentGatewayProductDTO.ProductName = transactionLineBL.ProductName;
                        paymentGatewayProductDTO.ProductAmount = transactionLineBL.NetAmount;
                        paymentGatewayProductDTO.ProductQuantity = (int)transactionLineBL.Quantity;
                        productIdPaymentGatewayProductDTOMap.Add(id, paymentGatewayProductDTO);
                    }
                    else
                    {
                        paymentGatewayProductDTO = productIdPaymentGatewayProductDTOMap[id];
                        paymentGatewayProductDTO.ProductQuantity += (int)transactionLineBL.Quantity;
                    }
                }
                List<PaymentGatewayProductDTO> paymentGatewayProductDTOList = productIdPaymentGatewayProductDTOMap.Values.ToList();
                paymentRequestDTO.PaymentGatewayProductDTOList = paymentGatewayProductDTOList;
                log.Debug("Got the productList");

                if (paymentGateway.IsCustomerInfoRequired)
                {
                    PaymentGatewayCustomerDTO paymentGatewayCustomerDTO = new PaymentGatewayCustomerDTO();
                    if (transactionBL.CustomerId > -1)
                    {
                        log.Debug("Building Customer details");
                        CustomerBL customerBL = new CustomerBL(ExecutionContext, transactionBL.CustomerId);
                        CustomerDTO customerDTO = customerBL.CustomerDTO;
                        paymentGatewayCustomerDTO.CustomerIdentifier = customerDTO.ProfileId.ToString();
                        paymentGatewayCustomerDTO.PGIdentifier = customerDTO.Guid;
                        paymentGatewayCustomerDTO.CustomerName =
                            (!String.IsNullOrWhiteSpace(customerDTO.FirstName) ? customerDTO.FirstName + " " : "") +
                            (!String.IsNullOrWhiteSpace(customerDTO.MiddleName) ? customerDTO.MiddleName + " " : "") +
                            (!String.IsNullOrWhiteSpace(customerDTO.LastName) ? customerDTO.LastName + " " : "");
                        paymentGatewayCustomerDTO.CustomerEmail = customerDTO.Email;
                        paymentGatewayCustomerDTO.CustomerPhone = customerDTO.PhoneNumber;
                        if (customerDTO.LatestAddressDTO != null)
                        {
                            log.Debug("Adding address");
                            paymentGatewayCustomerDTO.AddressLine1 = customerDTO.LatestAddressDTO.Line1;
                            paymentGatewayCustomerDTO.AddressLine2 = customerDTO.LatestAddressDTO.Line2;
                            paymentGatewayCustomerDTO.AddressLine3 = customerDTO.LatestAddressDTO.Line3;
                            paymentGatewayCustomerDTO.City = customerDTO.LatestAddressDTO.City;
                            paymentGatewayCustomerDTO.State = customerDTO.LatestAddressDTO.StateCode;
                            paymentGatewayCustomerDTO.Country = customerDTO.LatestAddressDTO.CountryName;
                            paymentGatewayCustomerDTO.PinCode = customerDTO.LatestAddressDTO.PostalCode;
                        }
                    }
                    else
                    {
                        if (!String.IsNullOrWhiteSpace(transactionBL.GuestName))
                        {
                            log.Debug("Adding guest name ");
                            paymentGatewayCustomerDTO.CustomerName = transactionBL.GuestName;
                        }

                        if (!String.IsNullOrWhiteSpace(transactionBL.GuestContactEmail))
                        {
                            log.Debug("Adding guest email");
                            paymentGatewayCustomerDTO.CustomerEmail = transactionBL.GuestContactEmail;
                        }

                        if (!String.IsNullOrWhiteSpace(transactionBL.GuestContactNumber))
                        {
                            log.Debug("Adding guest phone number");
                            paymentGatewayCustomerDTO.CustomerPhone = transactionBL.GuestContactNumber;
                        }

                        if (String.IsNullOrWhiteSpace(paymentGatewayCustomerDTO.CustomerName))
                        {
                            if (!String.IsNullOrWhiteSpace(transactionBL.GuestContactEmail))
                            {
                                log.Debug("Adding email as guest name ");
                                paymentGatewayCustomerDTO.CustomerName = transactionBL.GuestContactEmail;
                            }
                            else if (!String.IsNullOrWhiteSpace(transactionBL.GuestContactNumber))
                            {
                                log.Debug("Adding contact number as guest name ");
                                paymentGatewayCustomerDTO.CustomerName = transactionBL.GuestContactNumber;
                            }
                        }

                    }
                    paymentRequestDTO.PaymentGatewayCustomerDTO = paymentGatewayCustomerDTO;
                }
            }
            PaymentSessionDTO paymentSessionDTO = await paymentGateway.CreatePaymentSessionDTO(paymentRequestDTO);

            log.LogMethodExit(paymentSessionDTO);
            return paymentSessionDTO;
        }

        public async Task<string> GetPaymentIdentifier(string paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);
            string paymentIdentifier = await paymentGateway.GetPaymentIdentifier(paymentResponse);
            log.LogMethodExit(paymentIdentifier);
            return paymentIdentifier;
        }

        public async Task<PaymentTransactionDTO> ProcessPaymentResponse(string paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);
            DateTime siteDateTime = SiteContainerList.CurrentDateTime(ExecutionContext);
            PaymentGatewayResponseDTO paymentGatewayResponseDTO = new PaymentGatewayResponseDTO(paymentResponse, siteDateTime);
            PaymentResponseDTO paymentResponseDTO = await paymentGateway.ProcessPaymentResponse(paymentGatewayResponseDTO);
            paymentResponseDTO.TransactionDatetime = siteDateTime;
            PaymentTransactionDTO paymentTransactionDTO = ConvertToPaymentTransactionResponseDTO(paymentResponseDTO);
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        public bool RedirectResponseToWebsite()
        {
            log.LogMethodEntry();
            log.LogMethodExit(paymentGateway.RedirectResponseToWebsite);
            return paymentGateway.RedirectResponseToWebsite;
        }

        public string CallbackResponseMessage()
        {
            log.LogMethodEntry();
            log.LogMethodExit(paymentGateway.CallbackResponseMessage);
            return paymentGateway.CallbackResponseMessage;
        }

        public override async Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO refundTransactionPaymentsDTO,
                                                            PaymentTransactionDTO originalPaymentTransactionDTO,
                                                            List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                            IProgress<PaymentProgressReport> progress,
                                                            CancellationToken cancellationToken)
        {
            log.LogMethodEntry(refundTransactionPaymentsDTO, originalPaymentTransactionDTO, paymentTransactionDTOList, progress, cancellationToken);

            List<PaymentResponseDTO> paymentResponseList = ConvertToPaymentResponseDTO(paymentTransactionDTOList);
            decimal amount = Math.Abs(refundTransactionPaymentsDTO.Amount);
            decimal tipAmount = 0;
            string requestIdentifier = refundTransactionPaymentsDTO.Guid.ToString();
            PaymentResponseDTO originalPaymentResponseDTO = ConvertToPaymentResponseDTO(originalPaymentTransactionDTO);
            DateTime bussStartTime;
            DateTime bussEndTime;

            BusinessDate businessDate = new BusinessDate(ExecutionContext);
            bussStartTime = businessDate;
            bussEndTime = bussStartTime.AddDays(1);

            RefundRequestDTO refundRequestDTO = new RefundRequestDTO(amount, requestIdentifier, refundTransactionPaymentsDTO.PaymentId, tipAmount, originalPaymentResponseDTO, paymentResponseList, refundTransactionPaymentsDTO.PaymentDate, bussStartTime,bussEndTime);
            PaymentResponseDTO paymentResponseDTO = await paymentGateway.Refund(refundRequestDTO, progress, cancellationToken);

            PaymentTransactionDTO paymentTransactionDTO = ConvertToPaymentTransactionResponseDTO(paymentResponseDTO);
            //paymentTransactionDTO.InvoiceNo = requestIdentifier;
            paymentTransactionDTO.TransactionId = refundTransactionPaymentsDTO.TransactionId;

            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        public async Task<PaymentTransactionDTO> GetPaymentStatus(TransactionPaymentDTO StatusCheckTransactionPaymentDTO)
        {
            log.LogMethodEntry(StatusCheckTransactionPaymentDTO);
            //PaymentResponseDTO statusCheckPaymentResponseDTO = ConvertToPaymentResponseDTO(StatusCheckPaymentTransactionDTO);

            StatusCheckRequestDTO statusCheckRequestDTO = new StatusCheckRequestDTO(0, StatusCheckTransactionPaymentDTO.Guid, StatusCheckTransactionPaymentDTO.PaymentId, null, null, SiteContainerList.CurrentDateTime(ExecutionContext));

            PaymentResponseDTO paymentResponseDTO = await paymentGateway.StatusCheck(statusCheckRequestDTO, null, cancellationToken,string.Empty);
            PaymentTransactionDTO paymentTransactionDTO = ConvertToPaymentTransactionResponseDTO(paymentResponseDTO);

            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        private static PaymentTransactionDTO ConvertToPaymentTransactionResponseDTO(PaymentResponseDTO paymentResponseDTO)
        {
            // Parse AdditionalResponseDTO from ProcessData if available
            object additionalResponseDTO = null;
            if (!string.IsNullOrWhiteSpace(paymentResponseDTO.ProcessData))
            {
                try
                {
                    // Try to deserialize ProcessData as action data and convert to AdditionalResponseDTO
                    var actionData = JsonConvert.DeserializeObject<dynamic>(paymentResponseDTO.ProcessData);

                    if (actionData?.type != null)
                    {
                        // Create AdditionalResponseDTO from action data
                        additionalResponseDTO = new AdditionalResponseDTO
                        {
                            Type = actionData.type,
                            Url = actionData.url,
                            Method = actionData.method,
                            PaymentData = actionData.paymentData,
                            Data = new Dictionary<string, object>
                            {
                                ["action"] = actionData.action
                            }
                        };
                        log.Debug("Successfully created AdditionalResponseDTO from ProcessData");
                    }
                }
                catch (Exception ex)
                {
                    // If parsing fails, keep ProcessData as string for backward compatibility
                    log.Warning($"Could not parse ProcessData as action data: {ex.Message}");
                    additionalResponseDTO = null;
                }
            }

            return new PaymentTransactionDTO(-1, -1, paymentResponseDTO.InvoiceNo, paymentResponseDTO.TokenID,
                paymentResponseDTO.RecordNo, paymentResponseDTO.DSIXReturnCode, -1, paymentResponseDTO.TextResponse, paymentResponseDTO.AcctNo,
                paymentResponseDTO.CardType, paymentResponseDTO.TranCode, paymentResponseDTO.RefNo, paymentResponseDTO.Purchase,
                paymentResponseDTO.Authorize, paymentResponseDTO.TransactionDatetime, paymentResponseDTO.AuthCode,
                paymentResponseDTO.ProcessData, paymentResponseDTO.ResponseOrigin, paymentResponseDTO.UserTraceData, paymentResponseDTO.CaptureStatus, paymentResponseDTO.AcqRefData,
                paymentResponseDTO.TipAmount, paymentResponseDTO.CustomerCopy, paymentResponseDTO.MerchantCopy, paymentResponseDTO.CustomerCardProfileId,
                null, true, paymentResponseDTO.Status, paymentResponseDTO.CreditCardName, paymentResponseDTO.NameOnCreditCard,
                paymentResponseDTO.CreditCardExpiry, paymentResponseDTO.Amount, null, additionalResponseDTO);
        }

        private static List<PaymentResponseDTO> ConvertToPaymentResponseDTO(List<PaymentTransactionDTO> paymentTransactionDTOList)
        {
            log.LogMethodEntry(paymentTransactionDTOList);
            List<PaymentResponseDTO> result = new List<PaymentResponseDTO>();
            if (paymentTransactionDTOList == null)
            {
                log.LogMethodExit(result, "paymentTransactionDTOList == null");
                return result;
            }
            foreach (PaymentTransactionDTO transaction in paymentTransactionDTOList)
            {
                PaymentResponseDTO paymentResponseDTO = ConvertToPaymentResponseDTO(transaction);
                result.Add(paymentResponseDTO);
            }
            log.LogMethodExit(result);
            return result;
        }

        private static PaymentResponseDTO ConvertToPaymentResponseDTO(PaymentTransactionDTO paymentTransactionDTO)
        {
            return new PaymentResponseDTO(paymentTransactionDTO.InvoiceNo, paymentTransactionDTO.TokenID,
                                          paymentTransactionDTO.RecordNo, paymentTransactionDTO.DSIXReturnCode, paymentTransactionDTO.TextResponse, paymentTransactionDTO.AcctNo,
                                          paymentTransactionDTO.CardType, paymentTransactionDTO.TranCode, paymentTransactionDTO.RefNo, paymentTransactionDTO.Purchase, paymentTransactionDTO.Authorize,
                                          paymentTransactionDTO.TransactionDatetime, paymentTransactionDTO.AuthCode, paymentTransactionDTO.ProcessData, paymentTransactionDTO.ResponseOrigin, paymentTransactionDTO.UserTraceData,
                                          paymentTransactionDTO.CaptureStatus, paymentTransactionDTO.AcqRefData, paymentTransactionDTO.TipAmount, paymentTransactionDTO.CustomerCopy, paymentTransactionDTO.MerchantCopy,
                                          paymentTransactionDTO.CustomerCardProfileId, paymentTransactionDTO.Status,
                                          paymentTransactionDTO.CreditCardName, paymentTransactionDTO.NameOnCreditCard, paymentTransactionDTO.CreditCardExpiry, paymentTransactionDTO.Amount);
        }
    }
}
