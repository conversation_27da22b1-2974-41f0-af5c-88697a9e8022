﻿/********************************************************************************************
* Project Name - Web Payments
 * Description - DTO object received from UI to complete the web payment
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using System;
using Semnox.Parafait.Transaction.V2;

namespace Semnox.Parafait.WebPayments
{
    public class CompleteWebPaymentDTO
    {
        /// <summary>
        /// Get/Set method of PaymentTransactionDTO
        /// </summary>
        public PaymentTransactionDTO PaymentTransactionDTO { get; set; }
        /// <summary>
        /// Get/Set method of OTPId
        /// </summary>
        public string OTPId { get; set; }
        /// <summary>
        /// Get/Set method of OTP
        /// </summary>
        public string OTP { get; set; }
        /// <summary>
        /// Get/Set method of GatewayResponse
        /// </summary>
        public String GatewayResponse { get; set; }
    }
}
