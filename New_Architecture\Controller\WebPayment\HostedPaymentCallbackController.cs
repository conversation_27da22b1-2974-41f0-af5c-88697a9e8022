﻿/********************************************************************************************
 * Project Name - CommnonAPI
 * Description  - Transaction use case controller to apply Web Payment for transaction.
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/

using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Semnox.Core.Utilities;
using Semnox.Core.GenericUtilities;
using System.Threading.Tasks;
using Semnox.Parafait.WebPayments;
using Semnox.Parafait.Site;
using System.IO;
using System.Web;
using Semnox.Parafait.PaymentGateway;

namespace Semnox.CommonAPI.Controllers.WebPayment
{
    public class HostedPaymentCallbackController : ApiController
    {
        private Semnox.Parafait.logging.Logger log;

        /// <summary>
        /// Start Hosted Payment
        /// </summary>
        [HttpPost]
        [Route("api/WebPayment/HostedPaymentCallback/{PaymentGateway}")]
        [AllowAnonymous]
        public async Task<HttpResponseMessage> Post([FromUri] String PaymentGateway, [FromBody] dynamic gatewayResponse)
        {
            ExecutionContext executionContext = null;
            HostedPaymentResponseDTO hostedPaymentResponseDTO = null;
            try
            {
                //System User Execution Builder
                log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                int siteId = SiteContainerList.GetMasterSiteId();
                if (PaymentGateway.Contains("-"))
                {
                    log.Debug("Decrypting payment gateway " + PaymentGateway);
                    string[] tempArray = PaymentGateway.Split(new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries);
                    if (tempArray != null && tempArray.Length == 2)
                    {
                        log.Debug("Site is part of the response. PaymentGateway: "+ PaymentGateway);
                        int.TryParse(tempArray[tempArray.Length - 1], out siteId);
                        //Don't overwrite the gateway, it will fail in processing
                        //PaymentGateway = tempArray[0];
                        log.Debug("Site is part of the response " + siteId + ":" + PaymentGateway);
                    }
                }

                log.Debug("SiteId: " + siteId + ", PaymentGateway: " + PaymentGateway);

                executionContext = new ExecutionContext("", siteId, -1, -1, SiteContainerList.IsCorporate(), -1, -1);
                log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                log.LogMethodEntry(PaymentGateway, gatewayResponse);

                string requestBody = "";
                HttpRequestMessage request = this.Request;
                log.Debug("Request URL " + request.RequestUri.ToString());
                Stream receiveStream = await request.Content.ReadAsStreamAsync();
                StreamReader readStream = new StreamReader(HttpContext.Current.Request.InputStream);
                readStream.BaseStream.Position = 0;
                requestBody = readStream.ReadToEnd();
                log.Debug("Request Body " + requestBody);

                String requestIdentifier = Guid.NewGuid().ToString();
                IWebPaymentsUseCases webPaymentUseCases = WebPaymentsUseCaseFactory.GetWebPaymentsUseCases(executionContext, requestIdentifier);
                hostedPaymentResponseDTO = await webPaymentUseCases.ProcessGatewayResponse(PaymentGateway, requestBody, "CALLBACK");
                log.LogMethodExit(hostedPaymentResponseDTO);
                return Request.CreateResponse(HttpStatusCode.OK, hostedPaymentResponseDTO.CallbackResponseMessage);
            }
            catch (Exception ex)
            {
                string customException = GenericExceptionMessage.GetValidCustomExeptionMessage(ex, executionContext);
                log.Error(customException);
                return Request.CreateResponse(HttpStatusCode.InternalServerError, string.Empty);
            }
        }

    }
}