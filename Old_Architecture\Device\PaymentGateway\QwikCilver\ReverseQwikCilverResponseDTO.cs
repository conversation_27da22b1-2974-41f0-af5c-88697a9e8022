﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
    class ReverseQwikCilverResponseDTO
    {
        private RedeemQwikCilverRequestDTO redeembalancedto;

        public ReverseQwikCilverResponseDTO(RedeemQwikCilverRequestDTO redeembalancedto)
        {
            this.redeembalancedto = redeembalancedto;
        }

        public long TransactionId { get; set; }
        public int CurrentBatchNumber { get; set; }
        public int TransactionTypeId { get; set; }
        public double TotalAmount { get; set; }
        public string Notes { get; set; }
        public string ApprovalCode { get; set; }
        public int ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorDescription { get; set; }
        public char InputType { get; set; }
        public int TotalCards { get; set; }
        public int NumberOfCards { get; set; }
        public List<CardsResponseDTO> Cards { get; set; }
        public string BusinessReferenceNumber { get; set; }
        public string IdempotencyKey { get; set; }
        public string GeneralLedger { get; set; }
        public string CostCentre { get; set; }
        public int ExecutionMode { get; set; }
        public object NetworkTransactionInfo { get; set; }
    }
}
