﻿/********************************************************************************************
 * Project Name - Hosted Payment
 * Description  - Base class for web payments
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/
using System;
using utility = Semnox.Core.Utilities;
using System.Threading;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;

namespace Semnox.Parafait.PaymentGateway
{
    public class WebPaymentGatewayConfiguration
    {
        public string SuccessURL { get; set; }
        public string FailedURL { get; set; }
        public string CancelURL { get; set; }
        public string CallbackURL { get; set; }
        public string PaymentSucceededURL { get; set; }
        public string PaymentFailedURL { get; set; }
        public string PaymentCancelledURL { get; set; }
        public string WebsitePaymentPageURL { get; set; }
        public string WebsitePaymentPageLink { get; set; }
        public string GatewayAPIURL { get; set; }
        public string MerchantId { get; set; }
        public string PartnerId { get; set; }
        public string MerchantKey { get; set; }
        public string GatewayUserNamePubKey { get; set; }
        public string GatewayPasswordSecretKey { get; set; }
        public string GatewayAPIUserNamePubKey { get; set; }
        public string GatewayAPIPasswordSecretKey { get; set; }
        public bool IsCaptchEnabled { get; set; }
        public string CaptchaIdentifier { get; set; }
        public string CaptchaURL { get; set; }
        public bool IsPinCodeValidationRequired { get; set; }
        public int TransactionTimeLimit { get; set; }

        public WebPaymentGatewayConfiguration()
        {
            SuccessURL = "";
            FailedURL = "";
            CancelURL = "";
            CallbackURL = "";
            PaymentSucceededURL = "";
            PaymentFailedURL = "";
            PaymentCancelledURL = "";
            WebsitePaymentPageURL = "";
            WebsitePaymentPageLink = "";
            GatewayAPIURL = "";
            MerchantId = "";
            PartnerId = "";
            GatewayUserNamePubKey = "";
            GatewayPasswordSecretKey = "";
            GatewayAPIUserNamePubKey = "";
            GatewayAPIPasswordSecretKey = "";
            IsCaptchEnabled = false;
            CaptchaIdentifier = "";
            CaptchaURL = "";
            IsPinCodeValidationRequired = false;
        }
    }

    
    public class WebHostedPaymentGateway : PaymentGateway
    {
        /// <summary>
        /// Delegate that invokes to write the Log to a File
        /// </summary>
        public delegate void WriteToLogDelegate(int KioskTrxId, string Activity, int TrxId, int Value, string Message, int POSMachineId, string POSMachine);

        private readonly Semnox.Parafait.logging.Logger log;

        internal WebPaymentGatewayConfiguration WebPaymentGatewayConfiguration { get; set; }
        private HostedPaymentRequestDTO HostedPaymentRequestDTO { get; set; }
        private HostedPaymentResponseDTO HostedPaymentResponseDTO { get; set; }

        /// <summary>
        /// Constructor of hosted payment gateway class.
        /// </summary>
        /// <param name="executionContext">Environment Execution Context.</param>
        /// <param name="isUnattended">Whether the payment process is supervised by an attendant.</param>
        /// <param name="showMessageDelegate"> Delegate instance to display message.</param>
        /// <param name="writeToLogDelegate">Delegate instance for writing the Log to File</param>
        public WebHostedPaymentGateway(utility.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken) 
            : base(executionContext, isUnattended, cancellationToken)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, isUnattended);
            this.isHostedPayment = true;
            log.LogMethodExit(null);
        }

        public virtual Boolean RedirectResponseToWebsite()
        {
            log.LogMethodEntry();
            log.LogMethodExit(true);
            return true;
        }

        public virtual HostedPaymentRequestDTO CreateGatewayPaymentRequest(CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO)
        {
            log.LogMethodEntry(createHostedPaymentRequestDTO);
            log.LogMethodExit(HostedPaymentRequestDTO);
            return HostedPaymentRequestDTO;
        }

        protected HostedPaymentRequestDTO GetHostedPaymentRequestDTO(CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO, string paymentGatewayName)
        {
            log.LogMethodEntry(createHostedPaymentRequestDTO, paymentGatewayName, this.WebPaymentGatewayConfiguration);
            if (HostedPaymentRequestDTO == null)
            {
                HostedPaymentRequestDTO = new HostedPaymentRequestDTO();
                HostedPaymentRequestDTO.TransactionId = createHostedPaymentRequestDTO.TransactionId;
                HostedPaymentRequestDTO.TransactionPaymentId = createHostedPaymentRequestDTO.TransactionPaymentId;
                HostedPaymentRequestDTO.TransactionPaymentGuid = createHostedPaymentRequestDTO.TransactionPaymentGuid;
                HostedPaymentRequestDTO.TransactionGuid = createHostedPaymentRequestDTO.TransactionGuid;
                HostedPaymentRequestDTO.SiteId = createHostedPaymentRequestDTO.SiteId;
                HostedPaymentRequestDTO.POSMachine = createHostedPaymentRequestDTO.POSMachine;
                HostedPaymentRequestDTO.PaymentModeId = createHostedPaymentRequestDTO.PaymentModeId;
                HostedPaymentRequestDTO.PaymentMode = createHostedPaymentRequestDTO.PaymentMode;
                HostedPaymentRequestDTO.CurrencyCode = createHostedPaymentRequestDTO.CurrencyCode;
                HostedPaymentRequestDTO.Amount = createHostedPaymentRequestDTO.Amount;
                HostedPaymentRequestDTO.Products = createHostedPaymentRequestDTO.Products;
                HostedPaymentRequestDTO.PaymentGatewayCustomer = createHostedPaymentRequestDTO.PaymentGatewayCustomer;

                HostedPaymentRequestDTO.GatewayAPIURL = this.WebPaymentGatewayConfiguration.GatewayAPIURL;
                HostedPaymentRequestDTO.SuccessURL = this.WebPaymentGatewayConfiguration.SuccessURL.Replace(paymentGatewayName, paymentGatewayName + "-" + createHostedPaymentRequestDTO.SiteId).Trim();
                HostedPaymentRequestDTO.FailedURL = this.WebPaymentGatewayConfiguration.FailedURL.Replace(paymentGatewayName, paymentGatewayName + "-" + createHostedPaymentRequestDTO.SiteId).Trim();
                HostedPaymentRequestDTO.CancelURL = this.WebPaymentGatewayConfiguration.CancelURL.Replace(paymentGatewayName, paymentGatewayName + "-" + createHostedPaymentRequestDTO.SiteId).Trim();
                HostedPaymentRequestDTO.CallbackURL = this.WebPaymentGatewayConfiguration.CallbackURL.Replace(paymentGatewayName, paymentGatewayName + "-" + createHostedPaymentRequestDTO.SiteId).Trim();
                HostedPaymentRequestDTO.PaymentSucceededURL = this.WebPaymentGatewayConfiguration.PaymentSucceededURL;
                HostedPaymentRequestDTO.PaymentFailedURL = this.WebPaymentGatewayConfiguration.PaymentFailedURL;
                HostedPaymentRequestDTO.PaymentCancelledURL = this.WebPaymentGatewayConfiguration.PaymentCancelledURL.Replace("@trxGuid", HostedPaymentRequestDTO.TransactionGuid.ToString())
                                                                                                                     .Replace("@transactionId", HostedPaymentRequestDTO.TransactionGuid.ToString());

                HostedPaymentRequestDTO.IsCaptchEnabled = this.WebPaymentGatewayConfiguration.IsCaptchEnabled;
                HostedPaymentRequestDTO.CaptchaIdentifier = this.WebPaymentGatewayConfiguration.CaptchaIdentifier;
                HostedPaymentRequestDTO.CaptchaURL = this.WebPaymentGatewayConfiguration.CaptchaURL;
                HostedPaymentRequestDTO.IsPinCodeValidationRequired = this.WebPaymentGatewayConfiguration.IsPinCodeValidationRequired;
            }
            log.LogMethodExit(HostedPaymentRequestDTO);
            return HostedPaymentRequestDTO;
        }

        public virtual HostedPaymentResponseDTO ProcessGatewayResponse(String paymentGatewayResponse)
        {
            log.LogMethodEntry(paymentGatewayResponse);
            HostedPaymentResponseDTO = new HostedPaymentResponseDTO();
            log.LogMethodExit(HostedPaymentResponseDTO);
            return HostedPaymentResponseDTO;
        }

        protected HostedPaymentResponseDTO GetHostedPaymentResponseDTO()
        {
            log.LogMethodEntry(HostedPaymentResponseDTO);
            if (HostedPaymentResponseDTO == null)
            {
                HostedPaymentResponseDTO = new HostedPaymentResponseDTO();
            }
            log.LogMethodExit(HostedPaymentRequestDTO);
            return HostedPaymentResponseDTO;
        }

        public virtual string GetPaymentIdentifier(String paymentGatewayResponse)
        {
            log.LogMethodEntry(paymentGatewayResponse);
            String paymentGatewayIdentifier = string.Empty;
            log.LogMethodExit(paymentGatewayIdentifier);
            return paymentGatewayIdentifier;
        }

        public virtual HostedPaymentResponseDTO GetPaymentStatus(string transactionPaymentGuid)
        {
            log.LogMethodEntry(transactionPaymentGuid);
            HostedPaymentResponseDTO statusDTO = new HostedPaymentResponseDTO();
            log.LogMethodExit(statusDTO);
            return statusDTO;
        }

        internal virtual PaymentTransactionStatuses MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus);
            PaymentTransactionStatuses paymentStatusType = PaymentTransactionStatuses.FAILED;
            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }
    
        internal string GetCardTypeHelper(string startingTwoDigitOfCC)
        {
            log.LogMethodEntry();
            string cardType = "";

            if (string.IsNullOrWhiteSpace(startingTwoDigitOfCC))
            {
                log.Error("No card token provided. Unable to identify card type");
                return "";
            }

            log.Debug("Card first two digits: " + startingTwoDigitOfCC);

            if (startingTwoDigitOfCC.StartsWith("4"))
            {
                cardType = "Visa";
            }
            else if (startingTwoDigitOfCC.StartsWith("2") || startingTwoDigitOfCC.StartsWith("5"))
            {
                cardType = "Mastercard";
            }
            else if (startingTwoDigitOfCC.StartsWith("34") || startingTwoDigitOfCC.StartsWith("37"))
            {
                cardType = "AMEX";
            }
            else if (startingTwoDigitOfCC.StartsWith("6"))
            {
                cardType = "Discover";
            }
            else if (startingTwoDigitOfCC.StartsWith("36"))
            {
                cardType = "Diners Club";
            }
            else if (startingTwoDigitOfCC.StartsWith("35"))
            {
                cardType = "JCB";
            }
            else
            {
                cardType = "";
            }

            log.Debug("Card type: " + cardType);

            log.LogMethodExit(cardType);
            return cardType;
        }
    }

    
}
