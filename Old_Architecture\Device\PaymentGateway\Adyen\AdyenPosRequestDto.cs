﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{

    internal class AdyenPosRequestDto
    {
    }

    #region [COMMON]
    public class RequestDto
    {
        public Saletopoirequest SaleToPOIRequest { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
            //return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore});
        }
    }

    public class Saletopoirequest
    {
        public Messageheader MessageHeader { get; set; }
        public Paymentrequest PaymentRequest { get; set; }
        public Transactionstatusrequest TransactionStatusRequest { get; set; }
        public Messagereference MessageReference { get; set; }
        public ReversalRequestDto ReversalRequest { get; set; }

        public EventNotification EventNotification { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
        }
    }

    #endregion

    #region [PAYMENTS]

    public class Messageheader
    {
        public string ProtocolVersion { get; set; }
        public string MessageClass { get; set; }
        public string MessageCategory { get; set; }
        public string MessageType { get; set; }
        public string SaleID { get; set; }
        public string ServiceID { get; set; }
        public string POIID { get; set; }
    }

    public class Paymentrequest
    {
        public Saledata SaleData { get; set; }
        public Paymenttransaction PaymentTransaction { get; set; }

        public Paymentdata PaymentData { get; set; }
    }

    public class Paymentdata
    {
        public string PaymentType { get; set; }
    }

    public class Saledata
    {
        public Saletransactionid SaleTransactionID { get; set; }
        public string SaleToAcquirerData { get; set; }
        public string TokenRequestedType { get; set; }
        public string SaleReferenceID { get; set; }
    }

    public class Saletransactionid
    {
        public string TransactionID { get; set; }
        public string TimeStamp { get; set; }
    }

    public class Paymenttransaction
    {
        public Amountsreq AmountsReq { get; set; }
        public Transactionconditions TransactionConditions { get; set; }
    }

    public class Amountsreq
    {
        public string Currency { get; set; }
        public double RequestedAmount { get; set; }
        public double TipAmount { get; set; }
    }

    #endregion

    #region [GET TRANSACTION STATUS]

    public class GetTransactionStatusRequestDto
    {
        public Saletopoirequest SaleToPOIRequest { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
        }
    }


    public class Transactionstatusrequest
    {
        public bool ReceiptReprintFlag { get; set; }
        public string[] DocumentQualifier { get; set; }
        public Messagereference MessageReference { get; set; }
    }

    public class Messagereference
    {
        public string SaleID { get; set; }
        public string ServiceID { get; set; }
        public string MessageCategory { get; set; }
    }

    #endregion

    #region [ABORT TRANSACTION]
    public class AbortRequest
    {
        public string AbortReason { get; set; }
        public Messagereference messagereference { get; set; }
    }

    #endregion

    #region [REFUND]
    //public class RefundRequestDto
    //{
    //    public Saletopoirequest SaleToPOIRequest { get; set; }
    //    public ReversalRequestDto reversalRequestDto { get; set; }
    //    public override string ToString()
    //    {
    //        return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
    //    }

    //}
    public class ReversalRequestDto
    {
        public OriginalPOITransaction OriginalPOITransaction { get; set; }
        public string ReversalReason { get; set; }
        public double ReversedAmount { get; set; }
        public Saledata SaleData { get; set; }
    }

    public class OriginalPOITransaction
    {
        public POITransactionID POITransactionID { get; set; }
    }

    public class POITransactionID
    {
        public string TransactionID { get; set; }
        public string TimeStamp { get; set; }
    }

    #endregion

    #region [ManualKeyEntry]
    public class ManualKeyEntryRequestDto
    {
        public Saletopoirequest SaleToPOIRequest { get; set; }
    }

    public class Transactionconditions
    {
        public string[] ForceEntryMode { get; set; }
        public string[] AllowedPaymentBrand { get; set; }
    }
    #endregion

    #region [Capture]
    public class CapturePaymentRequestDto
    {
        public string reference { get; set; }
        public Amount amount { get; set; }
        public string merchantAccount { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
        }
    }

    public class Amount
    {
        public int value { get; set; }
        public string currency { get; set; }
    }
    #endregion

    #region [Cancel Auth]
    public class CancelAuthRequestDto
    {
        public string reference { get; set; }
        public string merchantAccount { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
        }
    }
    #endregion

    #region [WEB REFUND]
    public class WebRefundRequestDto
    {
        public string reference { get; set; }
        public string merchantAccount { get; set; }
    }
    #endregion


    public class MakeSubscriptionPaymentRequestDto
    {
        public Amount amount { get; set; }
        public string reference { get; set; }
        public Paymentmethod paymentMethod { get; set; }
        public string shopperReference { get; set; }
        public string returnUrl { get; set; }
        public string merchantAccount { get; set; }
        public string shopperInteraction { get; set; }
        public string recurringProcessingModel { get; set; }
        public string store { get; set; }
        public ApplicationInfo applicationInfo { get; set; }
        public Additionaldata additionalData { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
        }
    }

    public class ApplicationInfo
    {
        public ExternalPlatform externalPlatform { get; set; }
        public MerchantApplication merchantApplication { get; set; }
        public MerchantDevice merchantDevice { get; set; }
    }

    public class ExternalPlatform
    {
        public string integrator { get; set; }
        public string name { get; set; }
        public string version { get; set; }
        
    }

    public class MerchantApplication
    {
        public string name { get; set; }
        public string version { get; set; }
    }

    public class MerchantDevice
    {
        public string os { get; set; }
        public string osVersion { get; set; }
        //public string reference { get; set; }
        
    }

    //public class Amount
    //{
    //    public string currency { get; set; }
    //    public int value { get; set; }
    //}

    public class Paymentmethod
    {
        public string type { get; set; }
        public string storedPaymentMethodId { get; set; }
        public string brand { get; set; }
    }

    public class AdjustAuthorizationRequestDto
    {
        public string merchantAccount { get; set; }
        public string originalReference { get; set; }
        public Amount modificationAmount { get; set; }
        //public string reason { get; set; }
        public string reference { get; set; }
         public string allowPartialAuth { get; set; }

        public Additionaldata additionalData { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
        }
    }


}
