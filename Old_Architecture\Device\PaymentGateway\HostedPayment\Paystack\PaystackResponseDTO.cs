﻿using Newtonsoft.Json;


namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Paystack
{
    public class PayStackResponseDTO
    {
        public bool status { get; set; }

        public string message { get; set; }

        public PayStackTransactionResponseDTO data { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }
    public class PayStackTransactionResponseDTO
    {
        public string authorization_url { get; set; }

        public string access_code { get; set; }

        public string reference { get; set; }

    }

    public class LogDTO
    {
        public int start_time { get; set; }

        public int time_spent { get; set; }

        public int attempts { get; set; }

        public int errors { get; set; }

        public bool success { get; set; }

        public bool mobile { get; set; }

        public string[] input { get; set; }

        public HistoryDTO[] history { get; set; }


    }
    public class HistoryDTO
    {
        public string type { get; set; }

        public string message { get; set; }

        public string time { get; set; }
    }
    public class AuthorizationDTO
    {
        public string authorization_code { get; set; }

        public string bin { get; set; }

        public string last4 { get; set; }

        public string exp_month { get; set; }

        public string exp_year { get; set; }

        public string channel { get; set; }

        public string card_type { get; set; }

        public string bank { get; set; }
        public string country_code { get; set; }

        public string brand { get; set; }

        public bool reusable { get; set; }

        public string signature { get; set; }

        public string account_name { get; set; }


    }

    public class CustomerDTO
    {
        public int id { get; set; }

        public string first_name { get; set; }

        public string last_name { get; set; }

        public string email { get; set; }

        public string customer_code { get; set; }

        public string phone { get; set; }

        public string metadata { get; set; }

        public string risk_action { get; set; }

        public string international_format_phone { get; set; }



    }
    public class MetaDataDTO
    {
        public CustomFieldsDTO[] custom_fields { get; set; }

        public string referrer { get; set; }

    }

    public class CustomFieldsDTO
    {
        public string display_name { get; set; }

        public string variable_name { get; set; }

        public string value { get; set; }


    }

    public class RefundCustomerDTO
    {
        public int id { get; set; }
        public string first_name { get; set; }
        public string last_name { get; set; }
        public string email { get; set; }
        public string customer_code { get; set; }
        public string phone { get; set; }
        public object metadata { get; set; }
        public string risk_action { get; set; }
        public string international_format_phone { get; set; }
    }

    public class RefundDetailsDto
    {
        public int integration { get; set; }
        public TransactionQueryDTO transaction { get; set; }
        public object dispute { get; set; }
        public object settlement { get; set; }
        public int id { get; set; }
        public string domain { get; set; }
        public string currency { get; set; }
        public int amount { get; set; }
        public string status { get; set; }
        public object refunded_at { get; set; }
        public string refunded_by { get; set; }
        public string customer_note { get; set; }
        public string merchant_note { get; set; }
        public int deducted_amount { get; set; }
        public bool fully_deducted { get; set; }
        public string created_at { get; set; }
        public string updatedAt { get; set; }
        public string expected_at { get; set; }
        public object bank_reference { get; set; }
        public string transaction_reference { get; set; }
        public string reason { get; set; }
        public RefundCustomerDTO customer { get; set; }
        public string refund_type { get; set; }
        public int transaction_amount { get; set; }
        public string initiated_by { get; set; }
        public string channel { get; set; }
        public object session_id { get; set; }
        public bool collect_account_number { get; set; }
    }

    public class TransactionQueryDTO
    {
        public double id { get; set; }
        public string domain { get; set; }
        public string status { get; set; }
        public string reference { get; set; }
        public object receiptNumber { get; set; }
        public int amount { get; set; }
        public object message { get; set; }
        public string gatewayResponse { get; set; }
        public object helpdeskLink { get; set; }
        public string paidAt { get; set; }
        public string createdAt { get; set; }
        public string channel { get; set; }
        public string currency { get; set; }
        public string ipAddress { get; set; }
        public MetadataDTO metadata { get; set; }
        public LogDTO log { get; set; }
        public int? fees { get; set; }
        public object feesSplit { get; set; }
        public AuthorizationDTO authorization { get; set; }
        public CustomerDTO customer { get; set; }
        public object plan { get; set; }
        public object subaccount { get; set; }
        public object split { get; set; }
        public object orderId { get; set; }
        public string paidAtProperty { get; set; }
        public string createdAtProperty { get; set; }
        public int? requestedAmount { get; set; }
        public object posTransactionData { get; set; }
        public SourceDTO source { get; set; }
        public object feesBreakdown { get; set; }
    }

    public class MetadataDTO
    {
        public int paymentMode_id { get; set; }
        public string order_id { get; set; }
        public string customer_phonenumber { get; set; }
        public string customer_email { get; set; }
        public string customer_name { get; set; }
        public string cancel_action { get; set; }
        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }

    public class SourceDTO
    {
        public string type { get; set; }
        public string sourceProperty { get; set; }
        public object identifier { get; set; }
    }

    public class PaystackCallbackResponse
    {
        public string trxref { get; set; }
        public string reference { get; set; }
        public Data data { get; set; }
    }

    public class Data
    {
        public string id { get; set; }
        public string reference { get; set; }
    }

    public class PayStackWebhookResponseDTO
    {
        public string event_name { get; set; }
        public string message { get; set; }

        public bool status { get; set; }


        public TransactionQueryDTO data { get; set; }

    }
    public class PayStackVerifyTransactionResponseDTO
    {
        public bool status { get; set; }

        public string message { get; set; }

        public TransactionQueryDTO data { get; set; }

    }
    public class PayStackRefundDetailsDTO
    {
        public bool status { get; set; }

        public string message { get; set; }

        public RefundDetailsDto data { get; set; }

        public RefundMetaDTO meta { get; set; }

        public string type { get; set; }

        public string code { get; set; }

    }

    public class RefundMetaDTO
    {
        public string nextStep { get; set; }

    }
}
