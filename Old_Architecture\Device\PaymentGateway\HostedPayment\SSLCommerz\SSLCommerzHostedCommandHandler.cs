﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;




namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.SSLCommerz
{
    public class SSLCommerzHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private readonly string STORE_ID;
        private readonly string STORE_PASSWD;
        private readonly string BASE_URL;

        private readonly string CHECKOUT_URL;
        private readonly string TRX_STATUS_URL;
        private readonly string REFUND_URL;
        public SSLCommerzHostedCommandHandler(string STORE_ID, string STORE_PASSWD, string BASE_URL, string CHECKOUT_URL, string TRX_STATUS_URL, string REFUND_URL)
        {

            this.STORE_ID = STORE_ID;
            this.STORE_PASSWD = STORE_PASSWD;
            this.BASE_URL = BASE_URL;
            this.CHECKOUT_URL = CHECKOUT_URL;
            this.TRX_STATUS_URL = TRX_STATUS_URL;
            this.REFUND_URL = REFUND_URL;
        }

        /// <summary>
        /// Creates a checkout session by sending a request to the PayStack API with the provided transaction details.
        /// </summary>
        /// <param name="sslCommerzRequestDTO">The PayStackTransactionRequestDTO containing details of the checkout request.</param>
        /// <returns>
        /// Returns a PayStackResponseDTO representing the response received from the PayStack API.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error creating the payment request or processing the response.</exception>
        public SSLCommerzResponseDTO CreateCheckout(SSLCommerzRequestDTO sslCommerzRequestDTO)
        {
            log.LogMethodEntry("Create checkout request :", sslCommerzRequestDTO);

            SSLCommerzResponseDTO checkoutResponseDto = null;
            try
            {
                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("store_id", sslCommerzRequestDTO.store_id));

                formData.Add(new KeyValuePair<string, string>("store_passwd", sslCommerzRequestDTO.store_passwd));

                formData.Add(new KeyValuePair<string, string>("tran_id", sslCommerzRequestDTO.tran_id));

                formData.Add(new KeyValuePair<string, string>("currency", sslCommerzRequestDTO.currency));

                formData.Add(new KeyValuePair<string, string>("total_amount", sslCommerzRequestDTO.total_amount.ToString()));

                formData.Add(new KeyValuePair<string, string>("success_url", sslCommerzRequestDTO.success_url));

                formData.Add(new KeyValuePair<string, string>("cancel_url", sslCommerzRequestDTO.cancel_url));

                formData.Add(new KeyValuePair<string, string>("fail_url", sslCommerzRequestDTO.fail_url));

                formData.Add(new KeyValuePair<string, string>("product_name", sslCommerzRequestDTO.product_name));

                formData.Add(new KeyValuePair<string, string>("product_category", sslCommerzRequestDTO.product_category));

                formData.Add(new KeyValuePair<string, string>("product_profile", sslCommerzRequestDTO.product_profile));

                formData.Add(new KeyValuePair<string, string>("cus_name", sslCommerzRequestDTO.cus_name));

                formData.Add(new KeyValuePair<string, string>("cus_add1", sslCommerzRequestDTO.cus_add1));

                formData.Add(new KeyValuePair<string, string>("cus_city", sslCommerzRequestDTO.cus_city));

                formData.Add(new KeyValuePair<string, string>("cus_email", sslCommerzRequestDTO.cus_email));

                formData.Add(new KeyValuePair<string, string>("cus_country", sslCommerzRequestDTO.cus_country));

                formData.Add(new KeyValuePair<string, string>("ipn_url", sslCommerzRequestDTO.ipn_url));

                formData.Add(new KeyValuePair<string, string>("cus_phone", sslCommerzRequestDTO.cus_phone));

                formData.Add(new KeyValuePair<string, string>("shipping_method", sslCommerzRequestDTO.shipping_method));

                formData.Add(new KeyValuePair<string, string>("value_a", sslCommerzRequestDTO.value_a));

                log.Debug($"CreateGatewayPaymentRequest(): Make Payment RequestDTO : {formData.ToString()}");

                if (sslCommerzRequestDTO == null)
                {
                    throw new Exception("Error creating payment request");
                }
                // Send the API request to initiate the transaction
                WebRequestClient webRequestClient = new WebRequestClient(CHECKOUT_URL, HttpVerb.POST, formData);
                webRequestClient.ContentType = "application/x-www-form-urlencoded";
                webRequestClient.IsBasicAuthentication = false;
                log.Debug("CHECKOUT_URL" + CHECKOUT_URL);

                string responseFromServer = webRequestClient.MakeRequest();
                log.Debug("responseFromServer" + responseFromServer);

                // Deserialize the response from the server
                checkoutResponseDto = JsonConvert.DeserializeObject<SSLCommerzResponseDTO>(responseFromServer);
            }
            catch (Exception ex)
            {
                throw new Exception("Error creating checkout", ex);
            }
            log.LogMethodExit("Chechout Response: " + checkoutResponseDto);
            return checkoutResponseDto;
        }
        /// <summary>
        /// Prepares a gateway request string as an HTML page that automatically redirects the user to the specified checkout URL.
        /// </summary>
        /// <param name="checkoutURL">The URL to redirect the user to for the checkout process.</param>
        /// <returns>
        /// Returns a string representing the HTML page with JavaScript to perform the redirection.
        /// </returns>
        //public string PrepareGatewayRequestString(string checkoutURL, string formName)
        //{
        //    log.LogMethodEntry("PrepareGatewayRequestString()" + checkoutURL);
        //    StringBuilder strBuilder = new StringBuilder();

        //    strBuilder.Append("<html>");
        //    strBuilder.Append("<body onload=\"loadPayment()\">");
        //    strBuilder.Append("<script>function loadPayment() { ");
        //    strBuilder.Append($"window.location.replace(\"{checkoutURL}\");");
        //    strBuilder.Append("}</script>");

        //    strBuilder.Append("</body></html>");
        //    log.LogMethodExit();
        //    return strBuilder.ToString();

        //}
        public string PrepareGatewayRequestString(string checkoutURL, string formName)
        {
            log.LogMethodEntry("PrepareGatewayRequestString()" + checkoutURL);
            StringBuilder strBuilder = new StringBuilder();

            strBuilder.Append("<html>");
            strBuilder.Append($"<body onload=\"loadPayment()\">");
            strBuilder.Append($"<form name=\"{formName}\" onsubmit=\"return loadPayment()\">");
            strBuilder.Append("<script>");
            strBuilder.Append("function loadPayment() { ");
            strBuilder.Append($"window.location.replace(\"{checkoutURL}\");");
            strBuilder.Append("return false; }"); // Prevent form submission
            strBuilder.Append("</script>");
            strBuilder.Append("</form>");
            strBuilder.Append("</body></html>");

            log.LogMethodExit();
            return strBuilder.ToString();
        }


        public string ErrorForm(Dictionary<string, string> errorParams)
        {
            try
            {
                StringBuilder builder = new StringBuilder();
                builder.Append("<html><head>");
                builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
                builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
                builder.Append("</head><body onload=\"document.PaymentFailureForm.submit()\">");
                builder.Append("<form name=\"PaymentFailureForm\" method=\"GET\" action=\"/account/checkouterror\">");

                foreach (KeyValuePair<string, string> param in errorParams)
                {
                    builder.Append(string.Format("<input type=\"hidden\" name=\"{0}\" value=\"{1}\">", param.Key, param.Value));
                }

                builder.Append("</form>");
                builder.Append("</body></html>");

                log.LogMethodExit();
                return builder.ToString();
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public SSLCommerzTrxStatusElementDTO CreateTxSearch(string trxId)
        {
            log.LogMethodEntry(trxId);

            SSLCommerzTrxStatusResponseDTO jsonResponse = null;

            try
            {
                string API_URL = TRX_STATUS_URL + $"?tran_id={trxId}&store_id={STORE_ID}&store_passwd={STORE_PASSWD}";
                string responseFromServer;

                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.GET);
                webRequestClient.ContentType = "application/json";

                responseFromServer = webRequestClient.GetResponse();
                jsonResponse = JsonConvert.DeserializeObject<SSLCommerzTrxStatusResponseDTO>(responseFromServer);

                log.Debug("Transaction result: " + jsonResponse);

                if (jsonResponse != null && jsonResponse.element != null && jsonResponse.element.Any())
                {
                    // Sort elements by transaction date in descending order
                    List<SSLCommerzTrxStatusElementDTO> sortedElements = jsonResponse.element.OrderByDescending(e => DateTime.Parse(e.tran_date)).ToList();

                    // Return the first element (latest transaction)
                    log.LogMethodExit("Filtered Transaction Details: " + sortedElements[0]);
                    return sortedElements[0];
                }
                else
                {
                    log.Error("No transaction elements found in the response.");
                    log.LogMethodExit("Filtered Transaction Details: null");
                    return null;
                }
            }
            catch (Exception ex)
            {
                // Log and rethrow generic exception
                log.Error("An error occurred: " + ex.Message);
                throw;
            }
        }

        public SSLCommerzRefundResponseDTO CreateRefund(string bank_tran_id, double refund_amount, string refund_remark = "Refund")
        {
            log.LogMethodEntry("Create Refund for trxId: " + bank_tran_id);
            SSLCommerzRefundResponseDTO jsonResponse = null; // Initialize jsonResponse as null

            try
            {
                string API_URL = REFUND_URL + $"?bank_tran_id={bank_tran_id}&store_id={STORE_ID}&store_passwd={STORE_PASSWD}&refund_amount={refund_amount}&refund_remarks={refund_remark}";
                string responseFromServer;

                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.GET);
                webRequestClient.ContentType = "application/json";


                responseFromServer = webRequestClient.GetResponse();
                jsonResponse = JsonConvert.DeserializeObject<SSLCommerzRefundResponseDTO>(responseFromServer);


            }
            catch (Exception ex)
            {
                log.Error("An error occurred: " + ex.Message);
                throw;
            }
            log.LogMethodExit("Refund Response: " + jsonResponse);
            return jsonResponse;
        }

        public string GetStatusMessage(string status)
        {
            log.LogMethodEntry(status);
            switch (status)
            {
                case "VALID":
                    return "Payment Completed Successfully.";
                case "VALIDATED":
                    return "Payment Completed Successfully.";
                case "FAILED":
                    return "Payment Failed.";
                case "PENDING":
                    return "Payment Processing.";
                default:
                    return "Payment Declined.";
            }

        }
        public string GetRefundStatusMessage(string status)
        {
            log.LogMethodEntry(status);
            switch (status)
            {
                case "success":
                    return "Refund Initiated Successfully.";
                case "processing":
                    return "Refund Processing.";
                case "failed":
                    return "Refund Failed.";

                default:
                    return "Unknown status.";
            }

        }

        public string GetMaskedCardNumber(string rawCreditCardNumber)
        {
            log.LogMethodEntry(rawCreditCardNumber);

            if (string.IsNullOrEmpty(rawCreditCardNumber))
            {
                log.Error("No Card number present");
                return "";
            }

            string lastFourDigit = rawCreditCardNumber.Substring(rawCreditCardNumber.Length - 4, 4);
            log.Debug("lastFourDigit: " + lastFourDigit);

            string maskedCardNumber = string.Empty;
            try
            {
                lastFourDigit = lastFourDigit.Replace("*", "");
                if (string.IsNullOrWhiteSpace(lastFourDigit))
                {
                    log.Error("Card number was empty");
                    log.LogMethodExit(maskedCardNumber);
                    return maskedCardNumber;
                }

                maskedCardNumber = "**** **** **** " + lastFourDigit;
                log.LogMethodExit(maskedCardNumber);
                return maskedCardNumber;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }


    }
}
