//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by wsdl, Version=4.6.81.0.
// 
namespace Cis {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="FiskalizacijaService", Namespace="http://www.apis-it.hr/fin/2012/services/FiskalizacijaService")]
    public partial class FiskalizacijaService : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback racuniOperationCompleted;
        
        private System.Threading.SendOrPostCallback echoOperationCompleted;
        
        private System.Threading.SendOrPostCallback provjeraOperationCompleted;

        public FiskalizacijaService()
        {
            this.Url = "";
        }

        /// <remarks/>
        public FiskalizacijaService(string url) {
			this.Url = url;
        }
        
        /// <remarks/>
        public event racuniCompletedEventHandler racuniCompleted;
        
        /// <remarks/>
        public event echoCompletedEventHandler echoCompleted;
        
        /// <remarks/>
        public event provjeraCompletedEventHandler provjeraCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://e-porezna.porezna-uprava.hr/fiskalizacija/2012/services/FiskalizacijaServi" +
            "ce/racuni", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Bare)]
        [return: System.Xml.Serialization.XmlElementAttribute("RacunOdgovor", Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
        public RacunOdgovor racuni([System.Xml.Serialization.XmlElementAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")] RacunZahtjev RacunZahtjev) {
            object[] results = this.Invoke("racuni", new object[] {
                        RacunZahtjev});
            return ((RacunOdgovor)(results[0]));
        }
        
        /// <remarks/>
        public System.IAsyncResult Beginracuni(RacunZahtjev RacunZahtjev, System.AsyncCallback callback, object asyncState) {
            return this.BeginInvoke("racuni", new object[] {
                        RacunZahtjev}, callback, asyncState);
        }
        
        /// <remarks/>
        public RacunOdgovor Endracuni(System.IAsyncResult asyncResult) {
            object[] results = this.EndInvoke(asyncResult);
            return ((RacunOdgovor)(results[0]));
        }
        
        /// <remarks/>
        public void racuniAsync(RacunZahtjev RacunZahtjev) {
            this.racuniAsync(RacunZahtjev, null);
        }
        
        /// <remarks/>
        public void racuniAsync(RacunZahtjev RacunZahtjev, object userState) {
            if ((this.racuniOperationCompleted == null)) {
                this.racuniOperationCompleted = new System.Threading.SendOrPostCallback(this.OnracuniOperationCompleted);
            }
            this.InvokeAsync("racuni", new object[] {
                        RacunZahtjev}, this.racuniOperationCompleted, userState);
        }
        
        private void OnracuniOperationCompleted(object arg) {
            if ((this.racuniCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.racuniCompleted(this, new racuniCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://e-porezna.porezna-uprava.hr/fiskalizacija/2012/services/FiskalizacijaServi" +
            "ce/echo", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Bare)]
        [return: System.Xml.Serialization.XmlElementAttribute("EchoResponse", Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
        public string echo([System.Xml.Serialization.XmlElementAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")] string EchoRequest) {
            object[] results = this.Invoke("echo", new object[] {
                        EchoRequest});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public System.IAsyncResult Beginecho(string EchoRequest, System.AsyncCallback callback, object asyncState) {
            return this.BeginInvoke("echo", new object[] {
                        EchoRequest}, callback, asyncState);
        }
        
        /// <remarks/>
        public string Endecho(System.IAsyncResult asyncResult) {
            object[] results = this.EndInvoke(asyncResult);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void echoAsync(string EchoRequest) {
            this.echoAsync(EchoRequest, null);
        }
        
        /// <remarks/>
        public void echoAsync(string EchoRequest, object userState) {
            if ((this.echoOperationCompleted == null)) {
                this.echoOperationCompleted = new System.Threading.SendOrPostCallback(this.OnechoOperationCompleted);
            }
            this.InvokeAsync("echo", new object[] {
                        EchoRequest}, this.echoOperationCompleted, userState);
        }
        
        private void OnechoOperationCompleted(object arg) {
            if ((this.echoCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.echoCompleted(this, new echoCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://e-porezna.porezna-uprava.hr/fiskalizacija/2012/services/FiskalizacijaServi" +
            "ce/provjera", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Bare)]
        [return: System.Xml.Serialization.XmlElementAttribute("ProvjeraOdgovor", Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
        public ProvjeraOdgovor provjera([System.Xml.Serialization.XmlElementAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")] ProvjeraZahtjev ProvjeraZahtjev) {
            object[] results = this.Invoke("provjera", new object[] {
                        ProvjeraZahtjev});
            return ((ProvjeraOdgovor)(results[0]));
        }
        
        /// <remarks/>
        public System.IAsyncResult Beginprovjera(ProvjeraZahtjev ProvjeraZahtjev, System.AsyncCallback callback, object asyncState) {
            return this.BeginInvoke("provjera", new object[] {
                        ProvjeraZahtjev}, callback, asyncState);
        }
        
        /// <remarks/>
        public ProvjeraOdgovor Endprovjera(System.IAsyncResult asyncResult) {
            object[] results = this.EndInvoke(asyncResult);
            return ((ProvjeraOdgovor)(results[0]));
        }
        
        /// <remarks/>
        public void provjeraAsync(ProvjeraZahtjev ProvjeraZahtjev) {
            this.provjeraAsync(ProvjeraZahtjev, null);
        }
        
        /// <remarks/>
        public void provjeraAsync(ProvjeraZahtjev ProvjeraZahtjev, object userState) {
            if ((this.provjeraOperationCompleted == null)) {
                this.provjeraOperationCompleted = new System.Threading.SendOrPostCallback(this.OnprovjeraOperationCompleted);
            }
            this.InvokeAsync("provjera", new object[] {
                        ProvjeraZahtjev}, this.provjeraOperationCompleted, userState);
        }
        
        private void OnprovjeraOperationCompleted(object arg) {
            if ((this.provjeraCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.provjeraCompleted(this, new provjeraCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class RacunZahtjev {
        
        private ZaglavljeType zaglavljeField;
        
        private RacunType racunField;
        
        private SignatureType signatureField;
        
        private string idField;
        
        /// <remarks/>
        public ZaglavljeType Zaglavlje {
            get {
                return this.zaglavljeField;
            }
            set {
                this.zaglavljeField = value;
            }
        }
        
        /// <remarks/>
        public RacunType Racun {
            get {
                return this.racunField;
            }
            set {
                this.racunField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature {
            get {
                return this.signatureField;
            }
            set {
                this.signatureField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class ZaglavljeType {
        
        private string idPorukeField;
        
        private string datumVrijemeField;
        
        /// <remarks/>
        public string IdPoruke {
            get {
                return this.idPorukeField;
            }
            set {
                this.idPorukeField = value;
            }
        }
        
        /// <remarks/>
        public string DatumVrijeme {
            get {
                return this.datumVrijemeField;
            }
            set {
                this.datumVrijemeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class GreskaType {
        
        private string sifraGreskeField;
        
        private string porukaGreskeField;
        
        /// <remarks/>
        public string SifraGreske {
            get {
                return this.sifraGreskeField;
            }
            set {
                this.sifraGreskeField = value;
            }
        }
        
        /// <remarks/>
        public string PorukaGreske {
            get {
                return this.porukaGreskeField;
            }
            set {
                this.porukaGreskeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class ZaglavljeOdgovorType {
        
        private string idPorukeField;
        
        private string datumVrijemeField;
        
        /// <remarks/>
        public string IdPoruke {
            get {
                return this.idPorukeField;
            }
            set {
                this.idPorukeField = value;
            }
        }
        
        /// <remarks/>
        public string DatumVrijeme {
            get {
                return this.datumVrijemeField;
            }
            set {
                this.datumVrijemeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class ObjectType {
        
        private System.Xml.XmlNode[] anyField;
        
        private string idField;
        
        private string mimeTypeField;
        
        private string encodingField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any {
            get {
                return this.anyField;
            }
            set {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string MimeType {
            get {
                return this.mimeTypeField;
            }
            set {
                this.mimeTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string Encoding {
            get {
                return this.encodingField;
            }
            set {
                this.encodingField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class SPKIDataType {
        
        private byte[][] sPKISexpField;
        
        private System.Xml.XmlElement anyField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SPKISexp", DataType="base64Binary")]
        public byte[][] SPKISexp {
            get {
                return this.sPKISexpField;
            }
            set {
                this.sPKISexpField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlElement Any {
            get {
                return this.anyField;
            }
            set {
                this.anyField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class PGPDataType {
        
        private object[] itemsField;
        
        private ItemsChoiceType1[] itemsElementNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("PGPKeyID", typeof(byte[]), DataType="base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("PGPKeyPacket", typeof(byte[]), DataType="base64Binary")]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items {
            get {
                return this.itemsField;
            }
            set {
                this.itemsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType1[] ItemsElementName {
            get {
                return this.itemsElementNameField;
            }
            set {
                this.itemsElementNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#", IncludeInSchema=false)]
    public enum ItemsChoiceType1 {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("##any:")]
        Item,
        
        /// <remarks/>
        PGPKeyID,
        
        /// <remarks/>
        PGPKeyPacket,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class X509IssuerSerialType {
        
        private string x509IssuerNameField;
        
        private string x509SerialNumberField;
        
        /// <remarks/>
        public string X509IssuerName {
            get {
                return this.x509IssuerNameField;
            }
            set {
                this.x509IssuerNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer")]
        public string X509SerialNumber {
            get {
                return this.x509SerialNumberField;
            }
            set {
                this.x509SerialNumberField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class X509DataType {
        
        private object[] itemsField;
        
        private ItemsChoiceType[] itemsElementNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("X509CRL", typeof(byte[]), DataType="base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("X509Certificate", typeof(byte[]), DataType="base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("X509IssuerSerial", typeof(X509IssuerSerialType))]
        [System.Xml.Serialization.XmlElementAttribute("X509SKI", typeof(byte[]), DataType="base64Binary")]
        [System.Xml.Serialization.XmlElementAttribute("X509SubjectName", typeof(string))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items {
            get {
                return this.itemsField;
            }
            set {
                this.itemsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType[] ItemsElementName {
            get {
                return this.itemsElementNameField;
            }
            set {
                this.itemsElementNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#", IncludeInSchema=false)]
    public enum ItemsChoiceType {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("##any:")]
        Item,
        
        /// <remarks/>
        X509CRL,
        
        /// <remarks/>
        X509Certificate,
        
        /// <remarks/>
        X509IssuerSerial,
        
        /// <remarks/>
        X509SKI,
        
        /// <remarks/>
        X509SubjectName,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class RetrievalMethodType {
        
        private TransformType[] transformsField;
        
        private string uRIField;
        
        private string typeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Transform", IsNullable=false)]
        public TransformType[] Transforms {
            get {
                return this.transformsField;
            }
            set {
                this.transformsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string URI {
            get {
                return this.uRIField;
            }
            set {
                this.uRIField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class TransformType {
        
        private object[] itemsField;
        
        private string[] textField;
        
        private string algorithmField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("XPath", typeof(string))]
        public object[] Items {
            get {
                return this.itemsField;
            }
            set {
                this.itemsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string Algorithm {
            get {
                return this.algorithmField;
            }
            set {
                this.algorithmField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class RSAKeyValueType {
        
        private byte[] modulusField;
        
        private byte[] exponentField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Modulus {
            get {
                return this.modulusField;
            }
            set {
                this.modulusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Exponent {
            get {
                return this.exponentField;
            }
            set {
                this.exponentField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class DSAKeyValueType {
        
        private byte[] pField;
        
        private byte[] qField;
        
        private byte[] gField;
        
        private byte[] yField;
        
        private byte[] jField;
        
        private byte[] seedField;
        
        private byte[] pgenCounterField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] P {
            get {
                return this.pField;
            }
            set {
                this.pField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Q {
            get {
                return this.qField;
            }
            set {
                this.qField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] G {
            get {
                return this.gField;
            }
            set {
                this.gField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Y {
            get {
                return this.yField;
            }
            set {
                this.yField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] J {
            get {
                return this.jField;
            }
            set {
                this.jField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Seed {
            get {
                return this.seedField;
            }
            set {
                this.seedField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] PgenCounter {
            get {
                return this.pgenCounterField;
            }
            set {
                this.pgenCounterField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class KeyValueType {
        
        private object itemField;
        
        private string[] textField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("DSAKeyValue", typeof(DSAKeyValueType))]
        [System.Xml.Serialization.XmlElementAttribute("RSAKeyValue", typeof(RSAKeyValueType))]
        public object Item {
            get {
                return this.itemField;
            }
            set {
                this.itemField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class KeyInfoType {
        
        private object[] itemsField;
        
        private ItemsChoiceType2[] itemsElementNameField;
        
        private string[] textField;
        
        private string idField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        [System.Xml.Serialization.XmlElementAttribute("KeyName", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("KeyValue", typeof(KeyValueType))]
        [System.Xml.Serialization.XmlElementAttribute("MgmtData", typeof(string))]
        [System.Xml.Serialization.XmlElementAttribute("PGPData", typeof(PGPDataType))]
        [System.Xml.Serialization.XmlElementAttribute("RetrievalMethod", typeof(RetrievalMethodType))]
        [System.Xml.Serialization.XmlElementAttribute("SPKIData", typeof(SPKIDataType))]
        [System.Xml.Serialization.XmlElementAttribute("X509Data", typeof(X509DataType))]
        [System.Xml.Serialization.XmlChoiceIdentifierAttribute("ItemsElementName")]
        public object[] Items {
            get {
                return this.itemsField;
            }
            set {
                this.itemsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("ItemsElementName")]
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public ItemsChoiceType2[] ItemsElementName {
            get {
                return this.itemsElementNameField;
            }
            set {
                this.itemsElementNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        public string[] Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#", IncludeInSchema=false)]
    public enum ItemsChoiceType2 {
        
        /// <remarks/>
        [System.Xml.Serialization.XmlEnumAttribute("##any:")]
        Item,
        
        /// <remarks/>
        KeyName,
        
        /// <remarks/>
        KeyValue,
        
        /// <remarks/>
        MgmtData,
        
        /// <remarks/>
        PGPData,
        
        /// <remarks/>
        RetrievalMethod,
        
        /// <remarks/>
        SPKIData,
        
        /// <remarks/>
        X509Data,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class SignatureValueType {
        
        private string idField;
        
        private byte[] valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute(DataType="base64Binary")]
        public byte[] Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class DigestMethodType {
        
        private System.Xml.XmlNode[] anyField;
        
        private string algorithmField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any {
            get {
                return this.anyField;
            }
            set {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string Algorithm {
            get {
                return this.algorithmField;
            }
            set {
                this.algorithmField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class ReferenceType {
        
        private TransformType[] transformsField;
        
        private DigestMethodType digestMethodField;
        
        private byte[] digestValueField;
        
        private string idField;
        
        private string uRIField;
        
        private string typeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Transform", IsNullable=false)]
        public TransformType[] Transforms {
            get {
                return this.transformsField;
            }
            set {
                this.transformsField = value;
            }
        }
        
        /// <remarks/>
        public DigestMethodType DigestMethod {
            get {
                return this.digestMethodField;
            }
            set {
                this.digestMethodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] DigestValue {
            get {
                return this.digestValueField;
            }
            set {
                this.digestValueField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string URI {
            get {
                return this.uRIField;
            }
            set {
                this.uRIField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class SignatureMethodType {
        
        private string hMACOutputLengthField;
        
        private System.Xml.XmlNode[] anyField;
        
        private string algorithmField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="integer")]
        public string HMACOutputLength {
            get {
                return this.hMACOutputLengthField;
            }
            set {
                this.hMACOutputLengthField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any {
            get {
                return this.anyField;
            }
            set {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string Algorithm {
            get {
                return this.algorithmField;
            }
            set {
                this.algorithmField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class CanonicalizationMethodType {
        
        private System.Xml.XmlNode[] anyField;
        
        private string algorithmField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlTextAttribute()]
        [System.Xml.Serialization.XmlAnyElementAttribute()]
        public System.Xml.XmlNode[] Any {
            get {
                return this.anyField;
            }
            set {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
        public string Algorithm {
            get {
                return this.algorithmField;
            }
            set {
                this.algorithmField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class SignedInfoType {
        
        private CanonicalizationMethodType canonicalizationMethodField;
        
        private SignatureMethodType signatureMethodField;
        
        private ReferenceType[] referenceField;
        
        private string idField;
        
        /// <remarks/>
        public CanonicalizationMethodType CanonicalizationMethod {
            get {
                return this.canonicalizationMethodField;
            }
            set {
                this.canonicalizationMethodField = value;
            }
        }
        
        /// <remarks/>
        public SignatureMethodType SignatureMethod {
            get {
                return this.signatureMethodField;
            }
            set {
                this.signatureMethodField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Reference")]
        public ReferenceType[] Reference {
            get {
                return this.referenceField;
            }
            set {
                this.referenceField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
    public partial class SignatureType {
        
        private SignedInfoType signedInfoField;
        
        private SignatureValueType signatureValueField;
        
        private KeyInfoType keyInfoField;
        
        private ObjectType[] objectField;
        
        private string idField;
        
        /// <remarks/>
        public SignedInfoType SignedInfo {
            get {
                return this.signedInfoField;
            }
            set {
                this.signedInfoField = value;
            }
        }
        
        /// <remarks/>
        public SignatureValueType SignatureValue {
            get {
                return this.signatureValueField;
            }
            set {
                this.signatureValueField = value;
            }
        }
        
        /// <remarks/>
        public KeyInfoType KeyInfo {
            get {
                return this.keyInfoField;
            }
            set {
                this.keyInfoField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Object")]
        public ObjectType[] Object {
            get {
                return this.objectField;
            }
            set {
                this.objectField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(DataType="ID")]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class NaknadaType {
        
        private string nazivNField;
        
        private string iznosNField;
        
        /// <remarks/>
        public string NazivN {
            get {
                return this.nazivNField;
            }
            set {
                this.nazivNField = value;
            }
        }
        
        /// <remarks/>
        public string IznosN {
            get {
                return this.iznosNField;
            }
            set {
                this.iznosNField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class PorezOstaloType {
        
        private string nazivField;
        
        private string stopaField;
        
        private string osnovicaField;
        
        private string iznosField;
        
        /// <remarks/>
        public string Naziv {
            get {
                return this.nazivField;
            }
            set {
                this.nazivField = value;
            }
        }
        
        /// <remarks/>
        public string Stopa {
            get {
                return this.stopaField;
            }
            set {
                this.stopaField = value;
            }
        }
        
        /// <remarks/>
        public string Osnovica {
            get {
                return this.osnovicaField;
            }
            set {
                this.osnovicaField = value;
            }
        }
        
        /// <remarks/>
        public string Iznos {
            get {
                return this.iznosField;
            }
            set {
                this.iznosField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class PorezType {
        
        private string stopaField;
        
        private string osnovicaField;
        
        private string iznosField;
        
        /// <remarks/>
        public string Stopa {
            get {
                return this.stopaField;
            }
            set {
                this.stopaField = value;
            }
        }
        
        /// <remarks/>
        public string Osnovica {
            get {
                return this.osnovicaField;
            }
            set {
                this.osnovicaField = value;
            }
        }
        
        /// <remarks/>
        public string Iznos {
            get {
                return this.iznosField;
            }
            set {
                this.iznosField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class BrojRacunaType {
        
        private string brOznRacField;
        
        private string oznPosPrField;
        
        private string oznNapUrField;
        
        /// <remarks/>
        public string BrOznRac {
            get {
                return this.brOznRacField;
            }
            set {
                this.brOznRacField = value;
            }
        }
        
        /// <remarks/>
        public string OznPosPr {
            get {
                return this.oznPosPrField;
            }
            set {
                this.oznPosPrField = value;
            }
        }
        
        /// <remarks/>
        public string OznNapUr {
            get {
                return this.oznNapUrField;
            }
            set {
                this.oznNapUrField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class RacunType {
        
        private string oibField;
        
        private bool uSustPdvField;
        
        private string datVrijemeField;
        
        private OznakaSlijednostiType oznSlijedField;
        
        private BrojRacunaType brRacField;
        
        private PorezType[] pdvField;
        
        private PorezType[] pnpField;
        
        private PorezOstaloType[] ostaliPorField;
        
        private string iznosOslobPdvField;
        
        private string iznosMarzaField;
        
        private string iznosNePodlOporField;
        
        private NaknadaType[] naknadeField;
        
        private string iznosUkupnoField;
        
        private NacinPlacanjaType nacinPlacField;
        
        private string oibOperField;
        
        private string zastKodField;
        
        private bool nakDostField;
        
        private string paragonBrRacField;
        
        private string specNamjField;
        
        /// <remarks/>
        public string Oib {
            get {
                return this.oibField;
            }
            set {
                this.oibField = value;
            }
        }
        
        /// <remarks/>
        public bool USustPdv {
            get {
                return this.uSustPdvField;
            }
            set {
                this.uSustPdvField = value;
            }
        }
        
        /// <remarks/>
        public string DatVrijeme {
            get {
                return this.datVrijemeField;
            }
            set {
                this.datVrijemeField = value;
            }
        }
        
        /// <remarks/>
        public OznakaSlijednostiType OznSlijed {
            get {
                return this.oznSlijedField;
            }
            set {
                this.oznSlijedField = value;
            }
        }
        
        /// <remarks/>
        public BrojRacunaType BrRac {
            get {
                return this.brRacField;
            }
            set {
                this.brRacField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Porez", IsNullable=false)]
        public PorezType[] Pdv {
            get {
                return this.pdvField;
            }
            set {
                this.pdvField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Porez", IsNullable=false)]
        public PorezType[] Pnp {
            get {
                return this.pnpField;
            }
            set {
                this.pnpField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Porez", IsNullable=false)]
        public PorezOstaloType[] OstaliPor {
            get {
                return this.ostaliPorField;
            }
            set {
                this.ostaliPorField = value;
            }
        }
        
        /// <remarks/>
        public string IznosOslobPdv {
            get {
                return this.iznosOslobPdvField;
            }
            set {
                this.iznosOslobPdvField = value;
            }
        }
        
        /// <remarks/>
        public string IznosMarza {
            get {
                return this.iznosMarzaField;
            }
            set {
                this.iznosMarzaField = value;
            }
        }
        
        /// <remarks/>
        public string IznosNePodlOpor {
            get {
                return this.iznosNePodlOporField;
            }
            set {
                this.iznosNePodlOporField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Naknada", IsNullable=false)]
        public NaknadaType[] Naknade {
            get {
                return this.naknadeField;
            }
            set {
                this.naknadeField = value;
            }
        }
        
        /// <remarks/>
        public string IznosUkupno {
            get {
                return this.iznosUkupnoField;
            }
            set {
                this.iznosUkupnoField = value;
            }
        }
        
        /// <remarks/>
        public NacinPlacanjaType NacinPlac {
            get {
                return this.nacinPlacField;
            }
            set {
                this.nacinPlacField = value;
            }
        }
        
        /// <remarks/>
        public string OibOper {
            get {
                return this.oibOperField;
            }
            set {
                this.oibOperField = value;
            }
        }
        
        /// <remarks/>
        public string ZastKod {
            get {
                return this.zastKodField;
            }
            set {
                this.zastKodField = value;
            }
        }
        
        /// <remarks/>
        public bool NakDost {
            get {
                return this.nakDostField;
            }
            set {
                this.nakDostField = value;
            }
        }
        
        /// <remarks/>
        public string ParagonBrRac {
            get {
                return this.paragonBrRacField;
            }
            set {
                this.paragonBrRacField = value;
            }
        }
        
        /// <remarks/>
        public string SpecNamj {
            get {
                return this.specNamjField;
            }
            set {
                this.specNamjField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public enum OznakaSlijednostiType {
        
        /// <remarks/>
        N,
        
        /// <remarks/>
        P,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public enum NacinPlacanjaType {
        
        /// <remarks/>
        G,
        
        /// <remarks/>
        K,
        
        /// <remarks/>
        C,
        
        /// <remarks/>
        T,
        
        /// <remarks/>
        O,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class RacunOdgovor {
        
        private ZaglavljeOdgovorType zaglavljeField;
        
        private string jirField;
        
        private GreskaType[] greskeField;
        
        private SignatureType signatureField;
        
        private string idField;
        
        /// <remarks/>
        public ZaglavljeOdgovorType Zaglavlje {
            get {
                return this.zaglavljeField;
            }
            set {
                this.zaglavljeField = value;
            }
        }
        
        /// <remarks/>
        public string Jir {
            get {
                return this.jirField;
            }
            set {
                this.jirField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Greska", IsNullable=false)]
        public GreskaType[] Greske {
            get {
                return this.greskeField;
            }
            set {
                this.greskeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature {
            get {
                return this.signatureField;
            }
            set {
                this.signatureField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class ProvjeraZahtjev {
        
        private ZaglavljeType zaglavljeField;
        
        private RacunType racunField;
        
        private SignatureType signatureField;
        
        private string idField;
        
        /// <remarks/>
        public ZaglavljeType Zaglavlje {
            get {
                return this.zaglavljeField;
            }
            set {
                this.zaglavljeField = value;
            }
        }
        
        /// <remarks/>
        public RacunType Racun {
            get {
                return this.racunField;
            }
            set {
                this.racunField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature {
            get {
                return this.signatureField;
            }
            set {
                this.signatureField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.apis-it.hr/fin/2012/types/f73")]
    public partial class ProvjeraOdgovor {
        
        private ZaglavljeOdgovorType zaglavljeField;
        
        private RacunType racunField;
        
        private GreskaType[] greskeField;
        
        private SignatureType signatureField;
        
        private string idField;
        
        /// <remarks/>
        public ZaglavljeOdgovorType Zaglavlje {
            get {
                return this.zaglavljeField;
            }
            set {
                this.zaglavljeField = value;
            }
        }
        
        /// <remarks/>
        public RacunType Racun {
            get {
                return this.racunField;
            }
            set {
                this.racunField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayItemAttribute("Greska", IsNullable=false)]
        public GreskaType[] Greske {
            get {
                return this.greskeField;
            }
            set {
                this.greskeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://www.w3.org/2000/09/xmldsig#")]
        public SignatureType Signature {
            get {
                return this.signatureField;
            }
            set {
                this.signatureField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    public delegate void racuniCompletedEventHandler(object sender, racuniCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class racuniCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal racuniCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public RacunOdgovor Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((RacunOdgovor)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    public delegate void echoCompletedEventHandler(object sender, echoCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class echoCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal echoCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    public delegate void provjeraCompletedEventHandler(object sender, provjeraCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.6.81.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class provjeraCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal provjeraCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ProvjeraOdgovor Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ProvjeraOdgovor)(this.results[0]));
            }
        }
    }
}
