﻿using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    class WPCyberSourceWebPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public WPCyberSourceWebPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_PARTNER_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_PARTNER_ID"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_BASE_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_API_URL"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY"));

            SetConfiguration("WPCyberSource secret key", SystemOptionViewContainerList.GetSystemOption(executionContext, "Hosted Payment keys", "WPCyberSource secret key"));

            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
            SetConfiguration("SITE_ID", Convert.ToString(executionContext.GetSiteId()));
            SetConfiguration("BUSINESS_DAY_START_TIME", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "BUSINESS_DAY_START_TIME", "6"));
            //SetConfiguration("ENABLE_ADDRESS_VALIDATION", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "ENABLE_ADDRESS_VALIDATION"));

        }
    }
}
