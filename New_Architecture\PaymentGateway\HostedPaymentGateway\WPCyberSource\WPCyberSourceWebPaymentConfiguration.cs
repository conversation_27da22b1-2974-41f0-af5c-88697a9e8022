﻿using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    class WPCyberSourceWebPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public WPCyberSourceWebPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            log.LogMethodEntry();
            log.Debug("Initializing WPCyberSourceWeb configuration");

            PaymentModeAttributesContainerDTO paymentModeAttributes = paymentModeContainerDTO.PaymentModeAttributesContainerDTO;

            if (paymentModeAttributes == null)
            {
                log.Error($"PaymentModeAttributesContainerDTO is null for PaymentModeId {paymentModeContainerDTO.PaymentModeId}, site_id: {Convert.ToString(executionContext.GetSiteId())}. Cannot initialize WPCyberSourceWebPaymentConfiguration.");
                log.Error("PaymentModeDTO: " + paymentModeContainerDTO.ToString());
                throw new PaymentModeAttributesNotFoundException("PaymentMode attributes not found.");
            }

            SetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayMerchantId));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayMerchantKey));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayPublicKey));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewaySecretKey));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayPublishableKey));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_PARTNER_ID", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayPartnerId));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_BASE_URL", paymentModeAttributes.PaymentGatewayBaseURL);
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL", paymentModeAttributes.PaymentGatewayAPIURL);
            SetConfiguration("WPCyberSource secret key", Encryption.Decrypt(paymentModeAttributes.EncryptedEncryptionSecretKey));

            SetConfiguration("CURRENCY_CODE", paymentModeAttributes.CurrencyCode);
            SetConfiguration("SITE_ID", Convert.ToString(executionContext.GetSiteId()));
            SetConfiguration("BUSINESS_DAY_START_TIME", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "BUSINESS_DAY_START_TIME", "6"));
            //SetConfiguration("ENABLE_ADDRESS_VALIDATION", paymentModeAttributes.EnableAddressValidation);

            log.LogMethodExit();
        }
    }
}
