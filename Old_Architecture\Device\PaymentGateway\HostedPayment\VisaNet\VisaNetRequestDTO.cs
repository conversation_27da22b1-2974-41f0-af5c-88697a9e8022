﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.VisaNet
{
    #region VisaNetFormSessionRequestDTO
    /// <summary>
    /// VisaNet Form session request
    /// </summary>
    public class VisaNetFormSessionRequestDTO
    {
        public class VisaNetFormSessionRequest
        {
            public double amount { get; set; }
            public string channel { get; set; }
            public bool countable { get; set; }
            public Antifraud antifraud { get; set; }

            public override string ToString()
            {
                return JsonConvert.SerializeObject(this);
            }
        }
        public class Antifraud
        {
            public string clientIp { get; set; }
            public Dictionary<string, string> merchantDefineData { get; set; }

        }
    }
    #endregion VisaNetFormSessionRequestDTO

    #region AuthorizeTransactionRequestDTO
    /// <summary>
    /// VisaNet AuthorizeTransactionRequestDTO
    /// </summary>
    public class AuthorizeTransactionRequestDTO
    {
        public class AuthorizeTransactionRequest
        {
            public string channel { get; set; }
            public string captureType { get; set; }
            public bool countable { get; set; }
            public Order order { get; set; }

            public override string ToString()
            {
                return JsonConvert.SerializeObject(this);
            }
        }
        public class Order
        {
            public string tokenId { get; set; }
            public string purchaseNumber { get; set; }
            public double amount { get; set; }
            public string currency { get; set; }
        }
    }
    #endregion AuthorizeTransactionRequestDTO

    #region ReverseRequestDTO
    /// <summary>
    /// VisaNet Refund DTO
    /// </summary>
    public class ReverseRequestDTO
    {
        public class ReverseRequest
        {
            public string channel { get; set; }
            public Order order { get; set; }

            public override string ToString()
            {
                return JsonConvert.SerializeObject(this);
            }
        }
        public class Order
        {
            public string purchaseNumber { get; set; }
            public string transactionDate { get; set; }
        }
    }
    #endregion ReverseRequestDTO
}
