﻿/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Payment Print Attributes
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.190.0     24-Sep-2024         Amrutha      Created
 ********************************************************************************************************/


using Semnox.Core.Utilities;
using Semnox.Parafait.Site;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.User;
using Semnox.Parafait.ViewContainer;
using System.Collections.Generic;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    class ParafaitPaymentPrintAttributes : PaymentPrintAttribute
    {
        
        public ParafaitPaymentPrintAttributes(ExecutionContext executionContext, TransactionDTO transactionDTO)
        {
            string siteAddress=string.Empty;
            string tipPercentage=string.Empty;
            string tipText;
            decimal? transactionNetAmount;
            SiteContainerDTO siteContainerDTO = SiteViewContainerList.GetCurrentSiteContainerDTO(executionContext);
            string[] addressArray = siteContainerDTO != null ? siteContainerDTO.SiteAddress.Split(',') : new string[] { };
            for (int i = 0; i < addressArray.Length; i++)
            {
                siteAddress = addressArray[i] + ((i != addressArray.Length - 1) ? "," : "");
            }
            SetAttribute("SITE_NAME", siteContainerDTO.SiteName);
            SetAttribute("SITE_ADDRESS", siteAddress);

            UserContainerDTO userContainerDTO = UserViewContainerList.GetUserContainerDTO(executionContext.SiteId, executionContext.UserPKId);
            string username = userContainerDTO.UserName;

            SetAttribute("USER_NAME", username);

            LookupsContainerDTO lookupList = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "ADDITIONAL_PRINT_FIELDS");
            if (lookupList.LookupValuesContainerDTOList != null && lookupList.LookupValuesContainerDTOList.Count > 0)
            {
                LookupValuesContainerDTO lookupValuesContainerDTO = lookupList.LookupValuesContainerDTOList.Find(x => x.LookupValue.Equals("@SuggestiveTipText"));

                if (lookupValuesContainerDTO != null && !string.IsNullOrWhiteSpace(lookupValuesContainerDTO.Description))
                {
                    tipText = lookupValuesContainerDTO.Description;
                    SetAttribute("TIP_TEXT", tipText);
                }
                lookupValuesContainerDTO = lookupList.LookupValuesContainerDTOList.Find(x => x.LookupValue.Equals("@SuggestiveTipValues"));
                if (lookupValuesContainerDTO != null && !string.IsNullOrWhiteSpace(lookupValuesContainerDTO.Description))
                {
                    tipPercentage = lookupValuesContainerDTO.Description;
                }
            }

            transactionNetAmount = transactionDTO.TransactionNetAmount;

            string currencyCode=string.Empty;
            foreach(TransactionPaymentDTO trx in transactionDTO.TransactionPaymentDTOList)
            {
                currencyCode = trx.CurrencyCode;
                break;
            }
            decimal tipPercent;
            if (!string.IsNullOrEmpty(tipPercentage))
            {
                string[] tipArray = tipPercentage.Split('|');

                int index = 1;
                foreach (string s in tipArray)
                {
                    if (!string.IsNullOrWhiteSpace(s) && decimal.TryParse(s, out tipPercent))
                    {
                        decimal calculatedTip = transactionNetAmount.Value * (tipPercent / 100);
                        string text = $"{s}% is {currencyCode} {calculatedTip:F4}";
                        SetAttribute($"TIP_TEXT_{index}", text);
                        index++;
                    }
                }
            }

        }
    }
}
