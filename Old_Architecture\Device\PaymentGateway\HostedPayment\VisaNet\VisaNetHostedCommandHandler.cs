﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.VisaNet
{
    class VisaNetHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string _userName;
        private string _password;
        private string _base_url;
        private string _merchant_id;
        public VisaNetHostedCommandHandler(string userName, string password, string BASE_URL, string MERCHANT_ID)
        {
            log.LogMethodEntry();
            this._userName = userName;
            this._password = password;
            this._base_url = BASE_URL;
            this._merchant_id = MERCHANT_ID;
            log.LogMethodExit();
        }

        public string GenerateSecurityToken()
        {
            log.LogMethodEntry();
            string securityToken = "";
            try
            {
                string securityTokenURL = _base_url + "/api.security/v1/security";
                log.Debug("securityTokenURL: " + securityTokenURL);

                WebRequestClient securityTokenWebRequestClient = new WebRequestClient
                {
                    EndPoint = securityTokenURL,
                    Method = HttpVerb.GET,
                    Username = _userName,
                    Password = _password,
                    IsBasicAuthentication = true,
                    ContentType = "text/plain"
                };

                securityToken = securityTokenWebRequestClient.GetResponse();
                log.Debug("Get security token response: " + securityToken);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(securityToken);
            return securityToken;
        }

        public VisaNetResponseDTO GetOrderStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            VisaNetResponseDTO visaNetTxSearchResponseDTO = null;
            try
            {
                string securityToken = GenerateSecurityToken();

                if (string.IsNullOrEmpty(securityToken))
                {
                    log.Error("Empty securityToken");
                    throw new Exception("Failed to make Transaction Search");
                }

                string endPoint = _base_url + "/api.authorization/v3/retrieve/purchase/" + _merchant_id + "/" + trxId;
                log.Debug("Query API endPoint: " + endPoint);

                WebRequestClient webRequestClient = new WebRequestClient(endPoint, HttpVerb.GET);
                webRequestClient.Password = securityToken;
                webRequestClient.IsBasicAuthentication = false;
                string response = webRequestClient.GetResponse();
                log.Debug("Transaction status response: " + response);

                visaNetTxSearchResponseDTO = JsonConvert.DeserializeObject<VisaNetResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(visaNetTxSearchResponseDTO);
            return visaNetTxSearchResponseDTO;
        }

        public dynamic CreateCheckout(VisaNetFormSessionRequestDTO.VisaNetFormSessionRequest visaNetFormSessionRequest)
        {
            log.LogMethodEntry(visaNetFormSessionRequest);
            dynamic visaNetFormSessionResponse = null;

            string securityToken = GenerateSecurityToken();

            if (!string.IsNullOrEmpty(securityToken))
            {
                string formSessionEndpoint = _base_url + "/api.ecommerce/v2/ecommerce/token/session/" + _merchant_id;

                string formSessionPostData = JsonConvert.SerializeObject(visaNetFormSessionRequest, Formatting.Indented);
                log.Debug("Create Checkout Request: " + visaNetFormSessionRequest);

                WebRequestClient formSessionWebRequestClient = new WebRequestClient(formSessionEndpoint, HttpVerb.POST, formSessionPostData);
                formSessionWebRequestClient.Password = securityToken;
                formSessionWebRequestClient.IsBasicAuthentication = false;

                string formSessionResponse = formSessionWebRequestClient.MakeRequest();
                log.Debug("Response for get form session API:" + formSessionResponse);

                visaNetFormSessionResponse = JsonConvert.DeserializeObject(formSessionResponse);
            }
            log.LogMethodExit(visaNetFormSessionResponse);
            return visaNetFormSessionResponse;
        }

        public void AuthorizeTransaction(AuthorizeTransactionRequestDTO.AuthorizeTransactionRequest authorizeTransactionRequest)
        {
            log.LogMethodEntry(authorizeTransactionRequest);
            string authorizationTransactionendPoint = _base_url + "/api.authorization/v3/authorization/ecommerce/" + _merchant_id;

            string securityToken = GenerateSecurityToken();

            string authorizeTransactionRequestData = JsonConvert.SerializeObject(authorizeTransactionRequest, Formatting.Indented);
            log.Debug("Authorize Transaction Request: " + authorizeTransactionRequest);
            
            WebRequestClient authorizeTransactionWebRequestClient = new WebRequestClient(authorizationTransactionendPoint, HttpVerb.POST, authorizeTransactionRequestData);
            authorizeTransactionWebRequestClient.Password = securityToken;
            authorizeTransactionWebRequestClient.IsBasicAuthentication = false;

            string authorizeTransactionResponse = authorizeTransactionWebRequestClient.MakeRequest();
            log.Debug("Authorize Transaction response: " + authorizeTransactionResponse);

            log.LogMethodExit();
        }

        public VisaNetResponseDTO CreateRefund(ReverseRequestDTO.ReverseRequest reverseRequest)
        {
            log.LogMethodEntry(reverseRequest);
            VisaNetResponseDTO visaNetRefundResponseDTO = null;

            string endPoint = _base_url + "/api.authorization/v3/reverse/ecommerce/" + _merchant_id;

            string securityToken = GenerateSecurityToken();

            string postData = JsonConvert.SerializeObject(reverseRequest, Formatting.Indented);
            log.Debug("Refund request data: " + reverseRequest);

            if (!string.IsNullOrEmpty(securityToken))
            {
                WebRequestClient webRequestClient = new WebRequestClient(endPoint, HttpVerb.POST, postData);
                webRequestClient.Password = securityToken;
                webRequestClient.IsBasicAuthentication = false;

                string response = webRequestClient.MakeRequest();
                log.Debug("Refund Response: " + response);

                visaNetRefundResponseDTO = JsonConvert.DeserializeObject<VisaNetResponseDTO>(response);
            }
            log.LogMethodExit(visaNetRefundResponseDTO);
            return visaNetRefundResponseDTO;
        }
    }
}
