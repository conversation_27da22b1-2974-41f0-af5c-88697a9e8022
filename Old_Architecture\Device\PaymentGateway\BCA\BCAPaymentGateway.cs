﻿using BCAPayments;
using Semnox.Core.Utilities;
using Semnox.Parafait.Languages;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Semnox.Parafait.Device.PaymentGateway.BCA
{
    internal class BCAPaymentGateway : PaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        string portName;
        int baudRate = 115200;
        //int minPreAuth = 1;
        int dataBits = 8;
        IDisplayStatusUI statusDisplayUi;
        bool isManual;
        bool isAuthEnabled;
        bool enableAutoAuthorization;
        bool isCustomerAllowedToDecideEntryMode;
        bool isQREnabled;
        string DynamicCurrencyConverter;
        enum TransactionType
        {
            SALE,
            REFUND,
            AUTHORIZATION,
            VOID,
            PREAUTH,
            TATokenRequest
        }
        private static readonly Dictionary<string, string> ResponseDescriptions = new Dictionary<string, string>
{
    {"00", "Approve" },
    { "54", "Decline: Expired Card" },
    { "55", "Decline: Incorrect PIN" },
    { "P2", "Read Card Error" },
    { "P3", "User pressed Cancel on EDC" },
    { "Z3", "EMV Card Decline" },
    { "CE", "Connection Error/Line Busy" },
    { "TO", "Connection Timeout" },
    { "PT", "EDC Problem" },
    { "S2", "TRANSAKSI GAGAL ULANGI TRANSAKSI DI EDC" },
    { "S3", "TXN BLM DIPROSES MINTA SCAN QR" },
    { "S4", "TXN EXPIRED ULANGI TRANSAKSI" },
    { "TN", "Topup Tunai Not Ready" }
};
        public BCAPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
  : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {

            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel   
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12; // comparable to modern browsers
            isAuthEnabled = utilities.getParafaitDefaults("ALLOW_CREDIT_CARD_AUTHORIZATION").Equals("Y");
            string value = utilities.getParafaitDefaults("CREDIT_CARD_TERMINAL_PORT_NO");
            isCustomerAllowedToDecideEntryMode = utilities.getParafaitDefaults("ALLOW_CUSTOMER_TO_DECIDE_ENTRY_MODE").Equals("Y");
            DynamicCurrencyConverter = utilities.getParafaitDefaults("CURRENCY_CONVERSION_FACTOR");
            portName = "COM" + value;
        }

        //public override void Initialize()
        //{
        //    log.LogMethodEntry();
        //    CheckLastTransactionStatus();
        //    log.LogMethodExit();
        //}

        public override TransactionPaymentsDTO MakePayment(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);

            statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(1839, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "BCA Payment Gateway");
            statusDisplayUi.EnableCancelButton(false);
            isManual = false;
            Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
            TransactionType trxType = TransactionType.SALE;
            string paymentId = string.Empty;
            CCTransactionsPGWDTO cCOrgTransactionsPGWDTO = null;
            double amount = (transactionPaymentsDTO.Amount) * 100;

            try
            {
                if (transactionPaymentsDTO.Amount >= 0)
                {

                    if (!isUnattended)
                    {
                        cCOrgTransactionsPGWDTO = GetPreAuthorizationCCTransactionsPGWDTO(transactionPaymentsDTO);

                        if (cCOrgTransactionsPGWDTO == null && isCustomerAllowedToDecideEntryMode)
                        {
                            // Manual Key Entry Mode
                            frmEntryMode entryMode = new frmEntryMode();
                            utilities.setLanguage(entryMode);
                            if (entryMode.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                            {
                                if (entryMode.IsManual)
                                {
                                    isManual = entryMode.IsManual;
                                    log.Debug($"isManual={isManual}");
                                }
                            }
                            else
                            {
                                log.Error("Operation cancelled from frmEntryMode.");
                                throw new Exception(utilities.MessageUtils.getMessage("Operation cancelled."));
                            }
                        }

                        if (isAuthEnabled && enableAutoAuthorization)
                        {
                            log.Debug("Creditcard auto authorization is enabled");
                            trxType = TransactionType.AUTHORIZATION;
                        }
                        else
                        {
                            if (isAuthEnabled)
                            {
                                frmTransactionTypeUI frmTranType = new frmTransactionTypeUI(utilities, (cCOrgTransactionsPGWDTO == null) ? "TATokenRequest" : "Sale", transactionPaymentsDTO.Amount, showMessageDelegate);
                                if (frmTranType.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                                {
                                    if (frmTranType.TransactionType.Equals("Authorization") || frmTranType.TransactionType.Equals("Sale"))
                                    {
                                        if (frmTranType.TransactionType.Equals("Authorization"))
                                        {
                                            trxType = TransactionType.AUTHORIZATION;
                                        }
                                        else
                                        {
                                            trxType = TransactionType.SALE;
                                        }
                                    }
                                    else if (frmTranType.TransactionType.Equals("TATokenRequest"))
                                    {
                                        trxType = TransactionType.TATokenRequest;
                                        //transactionPaymentsDTO.Amount = Convert.ToDouble(minPreAuth);
                                    }
                                }
                                else
                                {
                                    log.Error("Operation cancelled.");
                                    throw new Exception(utilities.MessageUtils.getMessage("Operation cancelled."));
                                }
                            }
                        }
                    }
                }
                thr.Start();
                if (transactionPaymentsDTO != null)
                {
                    string isTransactionSucceeded = string.Empty;

                    BCAResponseDTO responseObject = null;

                    CCRequestPGWDTO ccRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, trxType.ToString());
                    log.LogVariableState("ccRequestPGWDTO", ccRequestPGWDTO);
                    if (transactionPaymentsDTO.Amount >= 0)
                    {

                        //if (trxType == TransactionType.TATokenRequest)
                        //{
                        //    statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                        //    //amount = transactionPaymentsDTO.Amount;
                        //    if (amount < 1 || Convert.ToDouble(minPreAuth) < 1)
                        //    {
                        //        log.Error("Pre-Auth amount value should be minimum 1");
                        //        throw new Exception("Pre-Auth amount value should be minimum 1");
                        //    }
                        //    var requestDTO = new BCARequestDTO()
                        //    {
                        //        TransactionType = "05", //pre-auth
                        //        TransactionAmount = ((int)(amount)).ToString("d12"), // Format amount to 12 digits
                        //    };
                        //    log.LogVariableState("Authorization RequestDTO Value", requestDTO);
                        //    responseObject = MakeTransactionRequest(requestDTO);
                        //    log.LogVariableState("Item Auth responseObject", responseObject);

                        //    if (responseObject != null)
                        //    {
                        //        if (string.IsNullOrWhiteSpace(responseObject.ResponseCode))
                        //        {
                        //            log.Error($"responseObject.ResponseCode was null");
                        //            throw new Exception($"Payment failed");
                        //        }
                        //        log.Debug("Sale response received");
                        //        double resAmount = Convert.ToDouble(responseObject.TransactionAmount);
                        //        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        //        cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                        //        cCTransactionsPGWDTO.AcctNo = responseObject.PAN;
                        //        cCTransactionsPGWDTO.AuthCode = responseObject.ApprovalCode;
                        //        //cCTransactionsPGWDTO.CardType = responseObject.SchemeId;
                        //        cCTransactionsPGWDTO.RefNo = responseObject.RRN;
                        //        cCTransactionsPGWDTO.RecordNo = responseObject.InvoiceNumber;
                        //        //cCTransactionsPGWDTO.TextResponse = responseObject.ResponseText;
                        //        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.TATokenRequest.ToString();
                        //        cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        //        cCTransactionsPGWDTO.Authorize = resAmount.ToString();
                        //        cCTransactionsPGWDTO.AcqRefData = GetMaskedMid(Convert.ToString(responseObject.MerchantId));
                        //        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        //        ccTransactionsPGWBL.Save();
                        //        log.Debug("Sale response saved to cCTransactionsPGW");
                        //        log.Debug("Sale response - Validating against success codes");
                        //        if (responseObject.ResponseCode != "00")
                        //        {
                        //            // Tx failed
                        //            string failureDescp = FailureDescriptions.ContainsKey(responseObject.ResponseCode) ? FailureDescriptions[responseObject.ResponseCode] : "Payment failed";
                        //            log.Error($"Payment failed: {Convert.ToString(failureDescp)}");
                        //            throw new Exception($"Payment failed: {Convert.ToString(failureDescp)}");

                        //        }
                        //        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        //        //transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        //        transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        //        transactionPaymentsDTO.Amount = resAmount;
                        //        //transactionPaymentsDTO.CreditCardAuthorization = responseObject.ApprovalCode;
                        //        transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                        //        log.LogVariableState("Sale cCTransactionsPGWDTO", cCTransactionsPGWDTO);
                        //        log.LogVariableState("Sale transactionPaymentsDTO", transactionPaymentsDTO);

                        //    }
                        //    else
                        //    {
                        //        log.Fatal("Response was null");
                        //        throw new Exception("Error in processing Payment");

                        //    }
                        //}

                        //if (trxType == TransactionType.AUTHORIZATION)
                        //{
                        //    log.Debug("Entered into Authorization selection with Trx type " + trxType.ToString());
                        //    statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                        //    log.LogVariableState("1", "Entered into requestDTO creation");


                        //    statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                        //    // check if it is subscription


                        //    // check if it is preauth adjust
                        //    if (cCOrgTransactionsPGWDTO != null)
                        //    {
                        //        // go for praauth adjust
                        //        log.Debug("Pre-auth adjust initiated");



                        //        var requestDTO = new BCARequestDTO()
                        //        {
                        //            TransactionType = "07", //offline
                        //            TransactionAmount = ((int)(amount)).ToString("d12"), // Format amount to 12 digits
                        //            AuthCode = cCOrgTransactionsPGWDTO.AuthCode
                        //        };
                        //        log.LogVariableState("Authorization RequestDTO Value", requestDTO);
                        //        responseObject = MakeTransactionRequest(requestDTO);
                        //        log.LogVariableState("Item Auth responseObject", responseObject);

                        //        if (responseObject != null)
                        //        {
                        //            if (string.IsNullOrWhiteSpace(responseObject.ResponseCode))
                        //            {
                        //                log.Error($"responseObject.ResponseCode was null");
                        //                throw new Exception($"Payment failed");
                        //            }
                        //            log.Debug("Sale response received");
                        //            double resAmount = Convert.ToDouble(responseObject.TransactionAmount);
                        //            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        //            cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                        //            cCTransactionsPGWDTO.AcctNo = responseObject.PAN;
                        //            cCTransactionsPGWDTO.AuthCode = responseObject.ApprovalCode;
                        //            //cCTransactionsPGWDTO.CardType = responseObject.SchemeId;
                        //            cCTransactionsPGWDTO.RefNo = responseObject.RRN;
                        //            cCTransactionsPGWDTO.RecordNo = responseObject.InvoiceNumber;
                        //            //cCTransactionsPGWDTO.TextResponse = responseObject.ResponseText;
                        //            cCTransactionsPGWDTO.TranCode = TransactionType.AUTHORIZATION.ToString();
                        //            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        //            cCTransactionsPGWDTO.Authorize = resAmount.ToString();
                        //            cCTransactionsPGWDTO.AcqRefData = GetMaskedMid(Convert.ToString(responseObject.MerchantId));
                        //            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        //            ccTransactionsPGWBL.Save();
                        //            log.Debug("Sale response saved to cCTransactionsPGW");
                        //            log.Debug("Sale response - Validating against success codes");
                        //            if (responseObject.ResponseCode != "00")
                        //            {
                        //                // Tx failed
                        //                string failureDescp = FailureDescriptions.ContainsKey(responseObject.ResponseCode) ? FailureDescriptions[responseObject.ResponseCode] : "Payment failed";
                        //                log.Error($"Payment failed: {Convert.ToString(failureDescp)}");
                        //                throw new Exception($"Payment failed: {Convert.ToString(failureDescp)}");

                        //            }
                        //            log.Debug("Sale response - Validation successfull");
                        //            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        //            //transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        //            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        //            transactionPaymentsDTO.Amount = resAmount;
                        //            transactionPaymentsDTO.CreditCardAuthorization = responseObject.ApprovalCode;
                        //            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                        //            log.LogVariableState("Sale cCTransactionsPGWDTO", cCTransactionsPGWDTO);
                        //            log.LogVariableState("Sale transactionPaymentsDTO", transactionPaymentsDTO);


                        //        }
                        //        else
                        //        {
                        //            log.Fatal("Response was null");
                        //            throw new Exception("Error in processing Payment");

                        //        }
                        //    }
                        //}
                        if (trxType == TransactionType.SALE)
                        {
                            log.Debug("Entered into Sale with Tx type " + trxType.ToString());

                            var requestDTO = new BCARequestDTO() { };

                            statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                            log.Debug("Entered into Sale");

                            if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardNumber))
                            {
                                log.Info("Credit Card Mode Started");

                                //if (isManual)
                                //{
                                //    requestDTO = new BCARequestDTO
                                //    {
                                //        TransactionType = "13", // contactless
                                //        TransactionAmount = ((int)(amount)).ToString("d12"), // Format amount to 12 digits
                                //        InstallmentFlag = "N", // Default for Sale
                                //        RedeemFlag = "N",    // Default for Sale
                                //        DCCFlag = string.IsNullOrWhiteSpace(DynamicCurrencyConverter) ? "N" : DynamicCurrencyConverter
                                //    };
                                //}
                                //else
                                //{
                                requestDTO = new BCARequestDTO
                                {
                                    TransactionType = "01", // Sale
                                    TransactionAmount = ((int)(amount)).ToString("d12"), // Format amount to 12 digits
                                    InstallmentFlag = "N", // Default for Sale
                                    RedeemFlag = "N",    // Default for Sale
                                    DCCFlag = string.IsNullOrWhiteSpace(DynamicCurrencyConverter) ? "N" : DynamicCurrencyConverter
                                };
                                //}


                                log.LogVariableState("sale requestDTO", requestDTO);

                                responseObject = MakeTransactionRequest(requestDTO);
                            }
                            else
                            {
                                log.Info("UPI Mode Started");
                                requestDTO = new BCARequestDTO
                                {
                                    TransactionType = "31", // QR
                                    TransactionAmount = ((int)(amount)).ToString("d12"), // Format amount to 12 digits
                                    DCCFlag = string.IsNullOrWhiteSpace(DynamicCurrencyConverter) ? "N" : DynamicCurrencyConverter
                                };
                                log.LogVariableState("sale requestDTO", requestDTO);

                                responseObject = MakeTransactionRequest(requestDTO);

                            }

                            log.LogVariableState("sale responseObject", responseObject);
                            if (responseObject != null)
                            {
                                if (
                                    (string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardNumber) && responseObject.ResponseCode != "00") ||
                                    (!string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardNumber) && !responseObject.IsValidApprovalCode())
                                    )
                                {
                                    // Tx failed
                                    string failureDescp = ResponseDescriptions.ContainsKey(responseObject.ResponseCode) ? ResponseDescriptions[responseObject.ResponseCode] : "Payment failed";
                                    log.Error($"Sale failed: {Convert.ToString(failureDescp)}");
                                    throw new Exception($"Sale failed: {Convert.ToString(failureDescp)}");
                                }
                                log.Debug("Sale response received");
                                double resAmount = Convert.ToDouble(responseObject.TransactionAmount);
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.AcctNo = responseObject.PAN;
                                cCTransactionsPGWDTO.AuthCode = responseObject.ApprovalCode;
                                //cCTransactionsPGWDTO.CardType = responseObject.SchemeId;
                                cCTransactionsPGWDTO.RefNo = responseObject.RRN;
                                cCTransactionsPGWDTO.RecordNo = responseObject.InvoiceNumber;
                                cCTransactionsPGWDTO.TextResponse = ResponseDescriptions.ContainsKey(responseObject.ResponseCode) ? ResponseDescriptions[responseObject.ResponseCode] : "";
                                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.Authorize = resAmount.ToString();
                                cCTransactionsPGWDTO.AcqRefData = GetMaskedMid(Convert.ToString(responseObject.MerchantId));

                                SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);


                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                ccTransactionsPGWBL.Save();
                                log.Debug("Sale response saved to cCTransactionsPGW");
                                log.Debug("Sale response - Validating against success codes");
                                if (responseObject.ResponseCode != "00")
                                {
                                    // Tx failed
                                    string failureDescp = ResponseDescriptions.ContainsKey(responseObject.ResponseCode) ? ResponseDescriptions[responseObject.ResponseCode] : "Payment failed";
                                    log.Error($"Payment failed: {Convert.ToString(failureDescp)}");
                                    throw new Exception($"Payment failed: {Convert.ToString(failureDescp)}");

                                }
                                log.Debug("Sale response - Validation successfull");
                                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                //transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.Amount = resAmount;
                                transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                log.LogVariableState("Sale cCTransactionsPGWDTO", cCTransactionsPGWDTO);
                                log.LogVariableState("Sale transactionPaymentsDTO", transactionPaymentsDTO);


                            }
                            else
                            {
                                log.Fatal("Response was null");
                                throw new Exception("Error in processing Payment");

                            }


                        }
                    }

                }
                else
                {
                    log.Error("Item  Response waas empty");
                    throw new Exception("Item  failed");

                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw new Exception(ex.Message, ex);
            }
            finally
            {
                log.Debug("Reached Finally");
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                    statusDisplayUi = null;
                }
            }
            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;

        }


        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            try
            {
                if (transactionPaymentsDTO != null)
                {
                    if (transactionPaymentsDTO.Amount < 0)
                    {
                        log.Error("Variable Refund Not Supported");
                        throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, "Variable Refund Not Supported"));
                    }

                    statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(4202, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "BCA Payment Gateway");
                    statusDisplayUi.EnableCancelButton(false);
                    statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                    Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
                    thr.Start();

                    BCAResponseDTO BCAResponse;
                    CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.REFUND.ToString());
                    CCTransactionsPGWBL ccOrigTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = ccOrigTransactionsPGWBL.CCTransactionsPGWDTO;
                    double amount = (transactionPaymentsDTO.Amount + transactionPaymentsDTO.TipAmount);
                    double refundAmount;

                    DateTime originalPaymentDate = ccOrigTransactionsPGWDTO.TransactionDatetime;
                    TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL(utilities.ExecutionContext);
                    List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.TRANSACTION_ID, transactionPaymentsDTO.TransactionId.ToString()));
                    List<TransactionPaymentsDTO> transactionPaymentsDTOList = transactionPaymentsListBL.GetTransactionPaymentsDTOList(searchParameters);
                    if (transactionPaymentsDTOList != null && transactionPaymentsDTOList.Any())
                    {
                        originalPaymentDate = transactionPaymentsDTOList[0].PaymentDate;
                    }
                    log.LogVariableState("originalPaymentDate", originalPaymentDate);

                    DateTime bussStartTime = utilities.getServerTime().Date.AddHours(Convert.ToInt32(ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "BUSINESS_DAY_START_TIME")));
                    DateTime bussEndTime = bussStartTime.AddDays(1);
                    if (utilities.getServerTime() < bussStartTime)
                    {
                        bussStartTime = bussStartTime.AddDays(-1);
                        bussEndTime = bussStartTime.AddDays(1);
                    }

                    // check business date
                    if ((originalPaymentDate >= bussStartTime) && (originalPaymentDate <= bussEndTime))
                    {
                        if ((!string.IsNullOrEmpty(ccOrigTransactionsPGWDTO.Authorize) && Convert.ToDecimal(ccOrigTransactionsPGWDTO.Authorize) != Convert.ToDecimal(amount)))//This is the valid condition if we check refundable first. Because once it enters to this section it will do full reversal
                        {
                            log.Error("Partial Void is not possible. Please wait for the batch to settle.");//Batch is not settled
                            statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage("Partial Void is not possible. Please wait for the batch to settle."));
                            throw new Exception(utilities.MessageUtils.getMessage("Partial Void is not possible. Please wait for the batch to settle."));
                        }
                        else
                        {
                            var requestDTO = new BCARequestDTO()
                            {
                                TransactionType = "08", //void
                                InvoiceNumber = ccOrigTransactionsPGWDTO.RecordNo,

                            };
                            log.LogVariableState("VOID requestDTO", requestDTO);

                            BCAResponse = MakeTransactionRequest(requestDTO);

                            log.LogVariableState(" VOID BCAResponse", BCAResponse);

                            if (BCAResponse.ResponseCode != "00")
                            {
                                // Tx failed
                                string failureDescp = ResponseDescriptions.ContainsKey(BCAResponse.ResponseCode) ? ResponseDescriptions[BCAResponse.ResponseCode] : "Payment failed";
                                log.Error($"Refund failed: {Convert.ToString(failureDescp)}");
                                throw new Exception($"Refund failed: {Convert.ToString(failureDescp)}");

                            }
                            else
                            {
                                // VOID Succeeded
                                log.LogVariableState("responseObject VOID", BCAResponse);
                                refundAmount = Convert.ToDouble(BCAResponse.TransactionAmount);
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.AcctNo = BCAResponse.PAN;
                                cCTransactionsPGWDTO.AuthCode = BCAResponse.ApprovalCode;
                                cCTransactionsPGWDTO.RefNo = BCAResponse.RRN;
                                cCTransactionsPGWDTO.RecordNo = BCAResponse.InvoiceNumber;
                                cCTransactionsPGWDTO.TextResponse = ResponseDescriptions.ContainsKey(BCAResponse.ResponseCode) ? ResponseDescriptions[BCAResponse.ResponseCode] : "";
                                cCTransactionsPGWDTO.TranCode = TransactionType.VOID.ToString();
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.Authorize = refundAmount.ToString();

                                SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);


                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                ccTransactionsPGWBL.Save();

                                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.CreditCardAuthorization = BCAResponse.ApprovalCode;
                                transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                                transactionPaymentsDTO.TipAmount = ccTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount == null ? 0 : Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                                transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWDTO.AcctNo;

                            }

                        }

                    }
                    else
                    {
                        if ((!string.IsNullOrEmpty(ccOrigTransactionsPGWDTO.Authorize) && Convert.ToDecimal(ccOrigTransactionsPGWDTO.Authorize) != Convert.ToDecimal(amount)))//This is the valid condition if we check refundable first. Because once it enters to this section it will do full reversal
                        {
                            var requestDTO = new BCARequestDTO()
                            {
                                TransactionType = "03", //refund
                                TransactionAmount = ((int)(amount * 100)).ToString("d12"),
                            };

                            log.LogVariableState("requestDTO", requestDTO);

                            BCAResponse = MakeTransactionRequest(requestDTO);
                            log.LogVariableState("Item Refund responseObject", BCAResponse);

                            log.LogVariableState("BCAResponse", BCAResponse);
                            if (BCAResponse != null)
                            {
                                if (string.IsNullOrWhiteSpace(BCAResponse.ResponseCode))
                                {
                                    log.Error($"responseObject.ResponseCode was null");
                                    throw new Exception($"Payment failed");
                                }
                                log.Debug("partial Refund response received");
                                refundAmount = Convert.ToDouble(BCAResponse.TransactionAmount);
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.AcctNo = BCAResponse.PAN;
                                cCTransactionsPGWDTO.AuthCode = BCAResponse.ApprovalCode;
                                //cCTransactionsPGWDTO.CardType = responseObject.SchemeId;
                                cCTransactionsPGWDTO.RefNo = BCAResponse.InvoiceNumber;
                                cCTransactionsPGWDTO.RecordNo = BCAResponse.RRN;
                                cCTransactionsPGWDTO.TextResponse = ResponseDescriptions.ContainsKey(BCAResponse.ResponseCode) ? ResponseDescriptions[BCAResponse.ResponseCode] : "";
                                cCTransactionsPGWDTO.TranCode = TransactionType.REFUND.ToString();
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.Authorize = refundAmount.ToString();
                                cCTransactionsPGWDTO.AcqRefData = GetMaskedMid(Convert.ToString(BCAResponse.MerchantId));
                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                ccTransactionsPGWBL.Save();
                                log.Debug("Partial Refund response saved to cCTransactionsPGW");
                                log.LogVariableState("Partial Refund cCTransactionsPGWDTO", cCTransactionsPGWDTO);
                                log.Debug("Partial Refund response - Validating against success codes");
                                if (BCAResponse.ResponseCode != "00")
                                {
                                    // Tx failed
                                    string failureDescp = ResponseDescriptions.ContainsKey(BCAResponse.ResponseCode) ? ResponseDescriptions[BCAResponse.ResponseCode] : "Payment failed";
                                    log.Error($"Partial Refund failed: {Convert.ToString(failureDescp)}");
                                    throw new Exception($"Partial Refund failed: {Convert.ToString(failureDescp)}");

                                }
                                log.Debug("Partial Refund  response - Validation successfull");


                                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                //transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize) * -1;
                                transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                log.LogVariableState("Partial Refund transactionPaymentsDTO", transactionPaymentsDTO);


                            }
                            else
                            {
                                log.Error("Item Refund Response waas empty");
                                throw new Exception("Item Refund failed");
                            }

                        }
                        else if ((!string.IsNullOrEmpty(ccOrigTransactionsPGWDTO.Authorize) && Convert.ToDecimal(ccOrigTransactionsPGWDTO.Authorize) == Convert.ToDecimal(amount)))
                        {
                            var requestDTO = new BCARequestDTO()
                            {
                                TransactionType = "03", //refund
                                TransactionAmount = ((int)(amount * 100)).ToString("d12"),
                            };

                            log.LogVariableState("requestDTO", requestDTO);

                            BCAResponse = MakeTransactionRequest(requestDTO);
                            log.LogVariableState("Full Refund responseObject", BCAResponse);

                            log.LogVariableState("BCAResponse", BCAResponse);
                            if (BCAResponse != null)
                            {
                                if (string.IsNullOrWhiteSpace(BCAResponse.ResponseCode))
                                {
                                    log.Error($"responseObject.ResponseCode was null");
                                    throw new Exception($"Payment failed");
                                }
                                log.Debug("Full Refund response received");
                                refundAmount = Convert.ToDouble(BCAResponse.TransactionAmount);
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.AcctNo = BCAResponse.PAN;
                                cCTransactionsPGWDTO.AuthCode = BCAResponse.ApprovalCode;
                                //cCTransactionsPGWDTO.CardType = responseObject.SchemeId;
                                cCTransactionsPGWDTO.RefNo = BCAResponse.InvoiceNumber;
                                cCTransactionsPGWDTO.RecordNo = BCAResponse.RRN;
                                cCTransactionsPGWDTO.TextResponse = ResponseDescriptions.ContainsKey(BCAResponse.ResponseCode) ? ResponseDescriptions[BCAResponse.ResponseCode] : "";
                                cCTransactionsPGWDTO.TranCode = TransactionType.REFUND.ToString();
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.Authorize = refundAmount.ToString();
                                cCTransactionsPGWDTO.AcqRefData = GetMaskedMid(Convert.ToString(BCAResponse.MerchantId));
                                //SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);
                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                ccTransactionsPGWBL.Save();
                                log.Debug("Full Refund response saved to cCTransactionsPGW");
                                log.LogVariableState("Full Refund cCTransactionsPGWDTO", cCTransactionsPGWDTO);
                                log.Debug("Full Refund response - Validating against success codes");

                                if (BCAResponse.ResponseCode != "00")
                                {
                                    // Tx failed
                                    string failureDescp = ResponseDescriptions.ContainsKey(BCAResponse.ResponseCode) ? ResponseDescriptions[BCAResponse.ResponseCode] : "Payment failed";
                                    log.Error($"Full Refund failed: {Convert.ToString(failureDescp)}");
                                    throw new Exception($"Full Refund failed: {Convert.ToString(failureDescp)}");

                                }
                                log.Debug("Full Refund response - Validation successfull");


                                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                //transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize) * -1;
                                transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                log.LogVariableState("Full Refund transactionPaymentsDTO", transactionPaymentsDTO);

                            }
                            else
                            {
                                log.Error("Full Refund Response waas empty");
                                throw new Exception("Full Refund failed");
                            }
                        }

                    }
                }
                log.LogMethodExit(transactionPaymentsDTO);
                return transactionPaymentsDTO;
            }
            catch (Exception ex)
            {
                statusDisplayUi.DisplayText("Error occured while Refunding the Amount");
                log.Error("Error occured while Refunding the Amount", ex);
                log.Fatal("Ends -RefundAmount(transactionPaymentsDTO) method " + ex.ToString());
                log.LogMethodExit(null, "throwing Exception");
                throw new Exception("Refund failed exception :" + ex.Message);
            }
            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                }
            }

        }

        private BCAResponseDTO MakeTransactionRequest(BCARequestDTO BCARequestDTO)
        {
            log.LogMethodEntry(BCARequestDTO);

            BCAResponseDTO bCAResponse = null;
            BCACommandHandler bCACommandHandler = new BCACommandHandler(statusDisplayUi, utilities.ExecutionContext, BCARequestDTO);
            // Initialize CommunicationPort
            var communicationPort = new CommunicationPort(portName, baudRate, dataBits);
            try
            {
                // Open communication port
                communicationPort.Open();
                bCAResponse = bCACommandHandler.Execute(communicationPort);
                communicationPort.Close();
                log.LogVariableState("BCA Response :", bCAResponse);
            }
            catch (Exception ex)
            {
                log.Error($"Error: {ex.ToString()}");
                log.Error(ex);
                throw ex;
            }
            log.LogMethodExit(bCAResponse);
            return bCAResponse;
        }
        public override bool IsSettlementPending(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            bool returnValue = false;
            if (isAuthEnabled)
            {
                if (transactionPaymentsDTO != null && transactionPaymentsDTO.CCResponseId != -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));
                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                    if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Any())
                    {
                        if (cCTransactionsPGWDTOList[0].TranCode == PaymentGatewayTransactionType.AUTHORIZATION.ToString())
                        {
                            if (cCTransactionsPGWDTOList[0].ParentResponseId == null || cCTransactionsPGWDTOList[0].ParentResponseId <= -1)
                            {
                                returnValue = true;
                            }
                        }
                        if (cCTransactionsPGWDTOList[0].TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString())
                        {
                            searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));
                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, PaymentGatewayTransactionType.AUTHORIZATION.ToString()));
                            List<CCTransactionsPGWDTO> childCCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                            if (childCCTransactionsPGWDTOList != null && childCCTransactionsPGWDTOList.Any())
                            {
                                returnValue = true;
                            }

                        }
                    }
                }
            }
            log.LogMethodExit(returnValue);
            return returnValue;
        }
        public override TransactionPaymentsDTO PerformSettlement(TransactionPaymentsDTO transactionPaymentsDTO, bool IsForcedSettlement = false)
        {
            log.LogMethodEntry(transactionPaymentsDTO, IsForcedSettlement);
            BCAResponseDTO responseObject;
            try
            {
                string isTransactionSucceeded = string.Empty;
                if (transactionPaymentsDTO != null)
                {
                    double baseAmount = transactionPaymentsDTO.Amount * 100;

                    statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(1839, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "BCA Payment Gateway");

                    statusDisplayUi.EnableCancelButton(false);
                    Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
                    thr.Start();
                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = ccTransactionsPGWBL.CCTransactionsPGWDTO;

                    CCRequestPGWDTO ccRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_PAYMENT);
                    log.LogVariableState("ccRequestPGWDTO", ccRequestPGWDTO);


                    if (!IsForcedSettlement)
                    {
                        log.Debug("Starts Capture");
                        var requestDTO = new BCARequestDTO
                        {
                            TransactionType = "07",
                            TransactionAmount = ((int)(baseAmount)).ToString("d12"),
                            AuthCode = ccOrigTransactionsPGWDTO.AuthCode
                        };


                        log.Debug("Initiate Capture Request");
                        log.LogVariableState("PerformSettlement Capture requestDTO", requestDTO);
                        responseObject = MakeTransactionRequest(requestDTO);
                        log.LogVariableState("PerformSettlement Capture responseObjectCapture", responseObject);
                        if (responseObject != null)
                        {
                            statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));


                            if (string.IsNullOrWhiteSpace(responseObject.ResponseCode))
                            {
                                log.Error($"responseObject.ResponseCode was null");
                                throw new Exception($"Settlement failed");
                            }
                            log.Debug("Received Capture Response");

                            double resamount = Convert.ToDouble(responseObject.TransactionAmount);
                            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                            cCTransactionsPGWDTO.AcctNo = responseObject.PAN;
                            cCTransactionsPGWDTO.AuthCode = responseObject.ApprovalCode;
                            cCTransactionsPGWDTO.CardType = responseObject.IssuerId;
                            cCTransactionsPGWDTO.RefNo = responseObject.InvoiceNumber;
                            cCTransactionsPGWDTO.RecordNo = responseObject.RRN;
                            //cCTransactionsPGWDTO.TextResponse = responseObject.ResponseText;
                            cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            cCTransactionsPGWDTO.Authorize = resamount.ToString();
                            cCTransactionsPGWDTO.AcqRefData = GetMaskedMid(Convert.ToString(responseObject.MerchantId));
                            //SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);
                            ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                            ccTransactionsPGWBL.Save();

                            log.Debug("Sale response saved to cCTransactionsPGW");
                            log.Debug("Sale response - Validating against success codes");
                            if (responseObject.ResponseCode != "00")
                            {
                                // Tx failed
                                string failureDescp = ResponseDescriptions.ContainsKey(responseObject.ResponseCode) ? ResponseDescriptions[responseObject.ResponseCode] : "Payment failed";
                                log.Error($"Payment failed: {Convert.ToString(failureDescp)}");
                                throw new Exception($"Payment failed: {Convert.ToString(failureDescp)}");

                            }
                            log.Debug("settlement response - Validation successfull");
                            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                            //transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                            transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Amount);
                            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                            log.LogVariableState("Sale transactionPaymentsDTO", transactionPaymentsDTO);
                            log.Debug("Capture Response saved to CCTransactionPGW");
                            log.LogVariableState("PerformSettlement Capture ccTransactionsPGWBL", ccTransactionsPGWBL);
                            log.Debug("Validating Capture Response against the success response codes");

                            log.Debug("Capture Response - Validation successfull");

                            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                            transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                            transactionPaymentsDTO.Amount = resamount;
                            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                            log.LogVariableState("PerformSettlement Capture transactionPaymentsDTO", transactionPaymentsDTO);

                        }
                        else
                        {
                            log.LogMethodExit(transactionPaymentsDTO);
                            log.Error("Capture response was null");
                            throw new Exception(utilities.MessageUtils.getMessage("CANCELLED"));
                        }
                    }
                }
                else
                {
                    statusDisplayUi.DisplayText("Invalid payment data.");
                    log.Error("Capture transactionPaymentsDTO was null");
                    throw new Exception(utilities.MessageUtils.getMessage("Invalid payment data."));
                }
            }
            catch (Exception ex)
            {
                if (statusDisplayUi != null)
                    statusDisplayUi.DisplayText("Error occured while performing settlement");
                log.Error("Error occured while performing settlement", ex);
                log.LogMethodExit(null, "Throwing Exception " + ex);
                throw (ex);
            }
            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                    statusDisplayUi = null;
                }
            }
            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;

        }
        private string GetMaskedMid(string mid)
        {
            log.LogMethodEntry();
            string maskedMid = string.Empty;
            if (!string.IsNullOrWhiteSpace(mid))
            {
                maskedMid = mid.Length > 4 ? new string('X', mid.Length - 4) + mid.Substring(mid.Length - 4) : mid;
            }
            log.LogMethodExit(maskedMid);
            return maskedMid;
        }
        private void SendPrintReceiptRequest(TransactionPaymentsDTO transactionPaymentsDTO, CCTransactionsPGWDTO cCTransactionsPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCTransactionsPGWDTO);
            if (ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "PRINT_CUSTOMER_RECEIPT") == "Y")
            {
                transactionPaymentsDTO.Memo = GetReceiptText(transactionPaymentsDTO, cCTransactionsPGWDTO, false);
            }
            if (ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "PRINT_MERCHANT_RECEIPT") == "Y" && !isUnattended)
            {
                transactionPaymentsDTO.Memo += GetReceiptText(transactionPaymentsDTO, cCTransactionsPGWDTO, true);
            }
            log.LogMethodExit();
        }
        private string GetReceiptText(TransactionPaymentsDTO trxPaymentsDTO, CCTransactionsPGWDTO ccTransactionsPGWDTO, bool IsMerchantCopy)
        {
            log.LogMethodEntry(trxPaymentsDTO, ccTransactionsPGWDTO, IsMerchantCopy);
            try
            {
                string[] addressArray = utilities.ParafaitEnv.SiteAddress.Split(',');
                string receiptText = "";

                receiptText += AllignText(utilities.ParafaitEnv.SiteName, Alignment.Center);
                if (addressArray != null && addressArray.Length > 0)
                {
                    for (int i = 0; i < addressArray.Length; i++)
                    {
                        receiptText += Environment.NewLine + AllignText(addressArray[i] + ((i != addressArray.Length - 1) ? "," : ""), Alignment.Center);
                    }
                }
                receiptText += Environment.NewLine;
                if (ccTransactionsPGWDTO.AcqRefData != null && !string.IsNullOrWhiteSpace(ccTransactionsPGWDTO.AcqRefData))
                {
                    string maskedMerchantId = (new String('X', 8) + ((ccTransactionsPGWDTO.AcqRefData.Length > 4) ? ccTransactionsPGWDTO.AcqRefData.Substring(ccTransactionsPGWDTO.AcqRefData.Length - 4)
                                                                                             : ccTransactionsPGWDTO.AcqRefData));
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Merchant ID") + "     : ".PadLeft(12) + maskedMerchantId, Alignment.Left);
                }
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Date") + ": ".PadLeft(4) + ccTransactionsPGWDTO.TransactionDatetime.ToString("MMM dd yyyy HH:mm"), Alignment.Left);
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Type") + ": ".PadLeft(4) + ccTransactionsPGWDTO.TranCode, Alignment.Left);
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Invoice Number") + "  : ".PadLeft(6) + ccTransactionsPGWDTO.InvoiceNo, Alignment.Left);
                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.AuthCode))
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Authorization") + "   : ".PadLeft(10) + ccTransactionsPGWDTO.AuthCode, Alignment.Left);
                //if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.CardType))
                //    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Card Type") + "       : ".PadLeft(15) + ccTransactionsPGWDTO.CardType, Alignment.Left);
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Cardholder Name") + ": ".PadLeft(3) + trxPaymentsDTO.NameOnCreditCard, Alignment.Left);
                string maskedPAN = ((string.IsNullOrEmpty(ccTransactionsPGWDTO.AcctNo) ? ccTransactionsPGWDTO.AcctNo
                                                                             : (new String('X', 12) + ((ccTransactionsPGWDTO.AcctNo.Length > 4)
                                                                                                     ? ccTransactionsPGWDTO.AcctNo.Substring(ccTransactionsPGWDTO.AcctNo.Length - 4)
                                                                                                     : ccTransactionsPGWDTO.AcctNo))));
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("PAN") + ": ".PadLeft(24) + maskedPAN, Alignment.Left);
                //receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Entry Mode") + ": ".PadLeft(13) + ccTransactionsPGWDTO.CaptureStatus, Alignment.Left);

                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage(ccTransactionsPGWDTO.TextResponse.ToUpper()), Alignment.Center);
                receiptText += Environment.NewLine;
                //if (ccTransactionsPGWDTO.TranCode.Equals(TransactionType.CAPTURE.ToString()) || ccTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
                //{
                //    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Amount") + "  : " + Convert.ToDouble(trxPaymentsDTO.Amount).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                //}
                if (ccTransactionsPGWDTO.TranCode.Equals(TransactionType.AUTHORIZATION.ToString()))
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Amount") + " : " + ((Convert.ToDouble(ccTransactionsPGWDTO.Authorize) == 0) ? (trxPaymentsDTO.Amount + trxPaymentsDTO.TipAmount) : Convert.ToDouble(ccTransactionsPGWDTO.Authorize)).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Total") + "                           : " + ((Convert.ToDouble(ccTransactionsPGWDTO.Authorize) == 0) ? (trxPaymentsDTO.Amount + trxPaymentsDTO.TipAmount) : Convert.ToDouble(ccTransactionsPGWDTO.Authorize)).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                    //receiptText += Environment.NewLine;
                }
                else
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Total") + "                           : " + ((Convert.ToDouble(ccTransactionsPGWDTO.Authorize) == 0) ? (trxPaymentsDTO.Amount + trxPaymentsDTO.TipAmount) : Convert.ToDouble(ccTransactionsPGWDTO.Authorize)).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                    receiptText += Environment.NewLine;
                }
                if ((!string.IsNullOrEmpty(ccTransactionsPGWDTO.TextResponse) && ccTransactionsPGWDTO.TextResponse.ToUpper().Equals("APPROVED")) && ccTransactionsPGWDTO.TranCode.ToUpper().Equals(TransactionType.AUTHORIZATION.ToString()))
                {
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Total") + "                          : " + "_____________", Alignment.Left);
                }
                receiptText += Environment.NewLine;
                if (IsMerchantCopy)
                {
                    if ((!string.IsNullOrEmpty(ccTransactionsPGWDTO.TextResponse) && ccTransactionsPGWDTO.TextResponse.Equals("APPROVED")))
                    {
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText("_______________________", Alignment.Center);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Signature"), Alignment.Center);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage(1180), Alignment.Center);
                        //}
                    }
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + AllignText("**" + utilities.MessageUtils.getMessage("Merchant Copy") + "**", Alignment.Center);
                }
                else
                {
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("IMPORTANT— retain this copy for your records"), Alignment.Center);
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + AllignText("**" + utilities.MessageUtils.getMessage("Cardholder Copy") + " **", Alignment.Center);
                }

                receiptText += Environment.NewLine;
                receiptText += AllignText(" " + utilities.MessageUtils.getMessage("Thank You"), Alignment.Center);
                if ((!ccTransactionsPGWDTO.TranCode.Equals("CAPTURE") || (ccTransactionsPGWDTO.TranCode.Equals("CAPTURE") && IsMerchantCopy && PrintReceipt)))
                {
                    if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.TextResponse) && ccTransactionsPGWDTO.TextResponse.Equals("APPROVED"))
                    {
                        if (IsMerchantCopy)
                        {
                            ccTransactionsPGWDTO.MerchantCopy = receiptText;
                        }
                        else
                        {
                            ccTransactionsPGWDTO.CustomerCopy = receiptText;
                        }

                    }
                    else
                    {
                        receiptText = receiptText.Replace("@invoiceNo", "");
                        Print(receiptText);
                    }
                }
                log.LogMethodExit(receiptText);
                return receiptText;
            }
            catch (Exception ex)
            {
                log.Fatal("GetReceiptText() failed to print receipt exception:" + ex.ToString());
                return null;
            }
        }
        public static string AllignText(string text, Alignment align)
        {
            log.LogMethodEntry(text, align);

            int pageWidth = 70;
            string res;
            if (align.Equals(Alignment.Right))
            {
                string returnValueNew = text.PadLeft(pageWidth, ' ');
                log.LogMethodExit(returnValueNew);
                return returnValueNew;
            }
            else if (align.Equals(Alignment.Center))
            {
                int len = (pageWidth - text.Length);
                int len2 = len / 2;
                len2 = len2 + text.Length;
                res = text.PadLeft(len2);
                if (res.Length > pageWidth && res.Length > text.Length)
                {
                    res = res.Substring(res.Length - pageWidth);
                }

                log.LogMethodExit(res);
                return res;
            }
            else
            {
                log.LogMethodExit(text);
                return text;
            }
        }
        public void Print(string printText)
        {
            log.LogMethodEntry(printText);
            try
            {
                using (System.Drawing.Printing.PrintDocument pd = new System.Drawing.Printing.PrintDocument())
                {
                    pd.DefaultPageSettings.PaperSize = new System.Drawing.Printing.PaperSize("Custom", 300, 700);

                    pd.PrintPage += (sender, e) =>
                    {
                        Font f = new Font("Arial", 9);
                        e.Graphics.DrawString(printText, f, new SolidBrush(Color.Black), new RectangleF(0, 0, pd.DefaultPageSettings.PrintableArea.Width, pd.DefaultPageSettings.PrintableArea.Height));
                    };
                    SetReceiptPrinter(pd);
                    pd.Print();
                }
            }
            catch (Exception ex)
            {
                utilities.EventLog.logEvent("PaymentGateway", 'I', "Receipt print failed.", printText, this.GetType().Name, 2, "", "", utilities.ParafaitEnv.LoginID, utilities.ParafaitEnv.POSMachine, null);
                log.Error("Error in printing cc receipt" + printText, ex);
            }
            log.LogMethodExit(null);
        }
        private BCAResponseDTO GetInfo()
        {
            var requestDto = new BCARequestDTO()
            {
                TransactionType = "23",
            };
            BCAResponseDTO responseObject = null;

            responseObject = MakeTransactionRequest(requestDto);
            if (responseObject != null)
            {
                if (string.IsNullOrWhiteSpace(responseObject.ResponseCode))
                {
                    log.Error($"responseObject.ResponseCode was null");
                    throw new Exception($"Payment failed");
                }
            }
            return responseObject;
        }
        private CCTransactionsPGWDTO GetPreAuthorizationCCTransactionsPGWDTO(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);

            CCTransactionsPGWDTO preAuthorizationCCTransactionsPGWDTO = null;
            if (utilities.getParafaitDefaults("ALLOW_CREDIT_CARD_AUTHORIZATION").Equals("Y"))
            {
                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRANSACTION_ID, transactionPaymentsDTO.TransactionId.ToString()));
                if (transactionPaymentsDTO.SplitId != -1)
                {
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SPLIT_ID, transactionPaymentsDTO.SplitId.ToString()));
                }
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, PaymentGatewayTransactionType.TATokenRequest.ToString()));
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
                {
                    preAuthorizationCCTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                }
            }

            log.LogMethodExit(preAuthorizationCCTransactionsPGWDTO);
            return preAuthorizationCCTransactionsPGWDTO;
        }

        //public override void SendLastTransactionStatusCheckRequest(CCRequestPGWDTO cCRequestPGWDTO, CCTransactionsPGWDTO cCTransactionsPGWDTO)
        //{
        //    log.LogMethodEntry(cCRequestPGWDTO, cCTransactionsPGWDTO);
        //    BCAResponseDTO responseObject = null;
        //    CCTransactionsPGWDTO ccTransactionsPGWDTOResponse = null;
        //    Thread statusThread = null;

        //    try
        //    {
        //        // Initialize business logic objects
        //        TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL(utilities.ExecutionContext);
        //        CCTransactionsPGWListBL ccTransactionsPGWListBL = new CCTransactionsPGWListBL();

        //        // Initialize UI for status display (changed gateway name to BCA Payment Gateway)
        //        string statusMessage = "Checking the transaction status" +
        //            ((cCRequestPGWDTO != null) ? " of TrxId:" + cCRequestPGWDTO.InvoiceNo + " Amount:" + cCRequestPGWDTO.POSAmount : ".");
        //        statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended,
        //            utilities.MessageUtils.getMessage(statusMessage), "BCA Payment Gateway");
        //        statusDisplayUi.EnableCancelButton(false);
        //        statusThread = new Thread(statusDisplayUi.ShowStatusWindow);

        //        // Process existing transaction check
        //        if (cCTransactionsPGWDTO != null)
        //        {
        //            log.Debug("cCTransactionsPGWDTO is not null");

        //            // Skip processing for refund and void transactions or those without response
        //            if (string.IsNullOrEmpty(cCTransactionsPGWDTO.TextResponse) ||
        //                cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.REFUND.ToString()) ||
        //                cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.VOID.ToString()))
        //            {
        //                log.Debug("credit card transaction done from this POS is not approved.");
        //                log.LogMethodExit(true);
        //                return;
        //            }

        //            // Start status display
        //            statusThread.Start();
        //            statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage("Last Transaction Status Check is Processing..."));

        //            // Check for existing transactions based on type
        //            if (cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.AUTHORIZATION.ToString()))
        //            {
        //                // Check if authorized transaction is already captured
        //                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> ccTransactionsSearchParams = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>
        //        {
        //            new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, cCTransactionsPGWDTO.ResponseID.ToString()),
        //            new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString())
        //        };

        //                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = ccTransactionsPGWListBL.GetCCTransactionsPGWDTOList(ccTransactionsSearchParams);

        //                if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
        //                {
        //                    // Handle captured transaction
        //                    if (cCTransactionsPGWDTOList[0].TranCode.Equals(PaymentGatewayTransactionType.CAPTURE.ToString()))
        //                    {
        //                        var capturedList = CheckFollowUpTransactions(ccTransactionsPGWListBL, cCTransactionsPGWDTOList[0]);
        //                        if (capturedList != null && capturedList.Count > 0)
        //                        {
        //                            log.Debug("The authorized transaction is captured.");
        //                            return;
        //                        }
        //                    }
        //                    // Handle tip adjustment
        //                    else if (cCTransactionsPGWDTOList[0].TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
        //                    {
        //                        var tipAdjustedList = CheckFollowUpTransactions(ccTransactionsPGWListBL, cCTransactionsPGWDTOList[0]);
        //                        if (tipAdjustedList != null && tipAdjustedList.Count > 0)
        //                        {
        //                            log.Debug("The authorized transaction is adjusted for tip.");
        //                            return;
        //                        }
        //                    }

        //                    // Check payment transactions
        //                    if (!CheckExistingPaymentTransaction(transactionPaymentsListBL, cCTransactionsPGWDTOList[0]))
        //                    {
        //                        cCTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
        //                    }
        //                    else
        //                    {
        //                        log.Debug("The capture/tip adjusted transaction exists for the authorization request with requestId:" +
        //                            cCTransactionsPGWDTO.InvoiceNo + " and its upto date");
        //                        return;
        //                    }
        //                }
        //            }
        //            else if (cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.CAPTURE.ToString()))
        //            {
        //                // Check if captured transaction is tip adjusted
        //                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> ccTransactionsSearchParams = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>
        //        {
        //            new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, cCTransactionsPGWDTO.ResponseID.ToString()),
        //            new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString())
        //        };

        //                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = ccTransactionsPGWListBL.GetCCTransactionsPGWDTOList(ccTransactionsSearchParams);

        //                if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
        //                {
        //                    // Handle tip adjustment
        //                    if (cCTransactionsPGWDTOList[0].TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
        //                    {
        //                        var tipAdjustedList = CheckFollowUpTransactions(ccTransactionsPGWListBL, cCTransactionsPGWDTOList[0]);
        //                        if (tipAdjustedList != null && tipAdjustedList.Count > 0)
        //                        {
        //                            log.Debug("The captured transaction is adjusted for tip.");
        //                            return;
        //                        }
        //                    }

        //                    // Check payment transactions
        //                    if (!CheckExistingPaymentTransaction(transactionPaymentsListBL, cCTransactionsPGWDTOList[0]))
        //                    {
        //                        cCTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
        //                    }
        //                    else
        //                    {
        //                        log.Debug("The tip adjusted transaction exists for the capture request with requestId:" +
        //                            cCTransactionsPGWDTO.InvoiceNo + " and its upto date");
        //                        return;
        //                    }
        //                }
        //            }
        //            else if (cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
        //            {
        //                log.Debug("credit card transaction is tip adjustment.");
        //                log.LogMethodExit(true);
        //                return;
        //            }

        //            log.Debug("Starts Last Transaction check");
        //            // Use the BCACommandHandler to get the last transaction instead of the Geidea-specific call.
        //            string rRN = (cCTransactionsPGWDTO != null) ? cCTransactionsPGWDTO.RefNo : string.Empty;
        //            var requestDTO = new BCARequestDTO
        //            {
        //                TransactionType = "32", // QR
        //                // TransactionAmount = ((int)(amount)).ToString("d12"), // Format amount to 12 digits
        //                DCCFlag = string.IsNullOrWhiteSpace(DynamicCurrencyConverter) ? "N" : DynamicCurrencyConverter
        //            };
        //            // responseObject = mcbCommandHandler.GetLastTransaction(string.Empty);
        //            responseObject = MakeTransactionRequest(requestDTO);
        //            log.LogVariableState("Last Trx Check responsePaymentObject", responseObject);

        //            // Validate response
        //            if (!ValidateTransactionResponse(responseObject, cCTransactionsPGWDTO))
        //            {
        //                return;
        //            }

        //            // Create response DTO
        //            ccTransactionsPGWDTOResponse = CreateTransactionResponseDTO(responseObject, cCRequestPGWDTO);
        //            log.LogVariableState("Last Trx Check ccTransactionsPGWDTOResponse", ccTransactionsPGWDTOResponse);

        //            // Check transaction success
        //            if (!responseObject.IsValidApprovalCode()) // CheckTransactionSuccess(responseObject)
        //            {
        //                // Save failed transaction
        //                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTOResponse);
        //                ccTransactionsPGWBL.Save();
        //                log.Debug("Last Trx Check saved to cCTransactionsPGW");
        //                return;
        //            }

        //            log.Debug("Last trx check response - Validation successful");
        //        }
        //        else if (cCRequestPGWDTO != null)
        //        {
        //            log.Debug("cCRequestPGWDTO is not null");
        //            statusThread.Start();
        //            statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage("Last Transaction Status Check is Processing..."));

        //            log.Debug("Starts Last Transaction Check");
        //            var requestDTO = new BCARequestDTO
        //            {
        //                TransactionType = "32", // QR
        //                // TransactionAmount = ((int)(amount)).ToString("d12"), // Format amount to 12 digits
        //                DCCFlag = string.IsNullOrWhiteSpace(DynamicCurrencyConverter) ? "N" : DynamicCurrencyConverter
        //            };
        //            // responseObject = mcbCommandHandler.GetLastTransaction(string.Empty);
        //            responseObject = MakeTransactionRequest(requestDTO);

        //            // responseObject = mcbCommandHandler.GetLastTransaction(string.Empty); // cCRequestPGWDTO.ReceiptNo
        //            log.LogVariableState("Last Trx Check responsePaymentObject", responseObject);

        //            // Validate response
        //            if (!ValidateTransactionResponse(responseObject, cCTransactionsPGWDTO))
        //            {
        //                return;
        //            }

        //            // Create response DTO
        //            ccTransactionsPGWDTOResponse = CreateTransactionResponseDTO(responseObject, cCRequestPGWDTO);
        //            log.LogVariableState("Last Trx Check ccTransactionsPGWDTOResponse", ccTransactionsPGWDTOResponse);

        //            log.Debug("Last trx check response - Validating against success codes");
        //            // Check transaction success
        //            if (!responseObject.IsValidApprovalCode()) //CheckTransactionSuccess(responseObject)
        //            {
        //                // Save failed transaction
        //                log.Debug("Saving ccTransactionsPGWDTOResponse.");
        //                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTOResponse);
        //                ccTransactionsPGWBL.Save();
        //                log.Debug("Last Trx Check saved to cCTransactionsPGW");
        //                return;
        //            }

        //            log.Debug("Last trx check response - Validation successfull");
        //        }

        //        // Process transaction response
        //        if (ccTransactionsPGWDTOResponse == null)
        //        {
        //            log.Debug("ccTransactionsPGWDTOResponse is null");
        //            log.Error("Last transaction status is not available." +
        //                ((cCRequestPGWDTO == null) ? "" : " RequestId:" + cCRequestPGWDTO.RequestID + ", Amount:" + cCRequestPGWDTO.POSAmount));
        //            return;
        //        }

        //        log.Debug("ccTransactionsPGWDTOResponse is not null");
        //        try
        //        {
        //            log.LogVariableState("ccTransactionsPGWDTOResponse", ccTransactionsPGWDTOResponse);

        //            // Save transaction if needed
        //            if (cCTransactionsPGWDTO == null)
        //            {
        //                log.Debug("Saving ccTransactionsPGWDTOResponse.");
        //                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTOResponse);
        //                ccTransactionsPGWBL.Save();
        //            }

        //            log.LogVariableState("ccTransactionsPGWDTOResponse", ccTransactionsPGWDTOResponse);

        //            // Process refund for sale or authorization
        //            if (!string.IsNullOrEmpty(ccTransactionsPGWDTOResponse.TextResponse) &&
        //                (cCRequestPGWDTO.TransactionType == TransactionType.SALE.ToString() ||
        //                 cCRequestPGWDTO.TransactionType == TransactionType.AUTHORIZATION.ToString()))
        //            {
        //                TransactionPaymentsDTO transactionPaymentsDTO = new TransactionPaymentsDTO();
        //                try
        //                {
        //                    transactionPaymentsDTO.TransactionId = Convert.ToInt32(cCRequestPGWDTO.InvoiceNo);
        //                }
        //                catch
        //                {
        //                    log.Debug("Transaction id conversion is failed");
        //                }

        //                transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWDTOResponse.Authorize);
        //                transactionPaymentsDTO.CCResponseId = (cCTransactionsPGWDTO == null) ?
        //                    ccTransactionsPGWDTOResponse.ResponseID : cCTransactionsPGWDTO.ResponseID;

        //                log.LogVariableState("transactionPaymentsDTO", transactionPaymentsDTO);
        //                log.Debug("Calling RefundAmount()");

        //                if (statusDisplayUi != null)
        //                {
        //                    statusDisplayUi.CloseStatusWindow();
        //                    statusDisplayUi = null;
        //                }

        //                // transactionPaymentsDTO = RefundAmount(transactionPaymentsDTO);
        //            }
        //        }
        //        catch (Exception ex)
        //        {
        //            log.Debug("Exception one");
        //            if (!isUnattended && showMessageDelegate != null)
        //            {
        //                // Display error message if needed
        //                // showMessageDelegate(utilities.MessageUtils.getMessage("Last transaction status check is failed. :" + ((cCRequestPGWDTO != null) ? " TransactionID:" + cCRequestPGWDTO.InvoiceNo + " Amount:" + cCRequestPGWDTO.POSAmount : ".")), "Last Transaction Status Check", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //            }
        //            log.Error("Last transaction check failed", ex);
        //            throw;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Debug("Exception two");
        //        log.Error(ex);
        //        throw;
        //    }
        //    finally
        //    {
        //        log.Debug("Reached finally.");
        //        if (statusDisplayUi != null)
        //        {
        //            statusDisplayUi.CloseStatusWindow();
        //            statusDisplayUi = null;
        //        }
        //    }

        //    log.LogMethodExit();
        //}
        //private List<CCTransactionsPGWDTO> CheckFollowUpTransactions(CCTransactionsPGWListBL listBL, CCTransactionsPGWDTO transaction)
        //{
        //    var searchParams = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>
        //        {
        //            new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, transaction.ResponseID.ToString()),
        //            new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString())
        //        };

        //    return listBL.GetCCTransactionsPGWDTOList(searchParams);
        //}

        //private bool CheckExistingPaymentTransaction(TransactionPaymentsListBL listBL, CCTransactionsPGWDTO transaction)
        //{
        //    var searchParams = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>
        //        {
        //            new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, transaction.ResponseID.ToString()),
        //            new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString())
        //        };
        //    List<TransactionPaymentsDTO> transactions = listBL.GetTransactionPaymentsDTOList(searchParams);
        //    return transactions != null && transactions.Count > 0;
        //}

        //private bool ValidateTransactionResponse(BCAResponseDTO responseObject, CCTransactionsPGWDTO cTransactionsPGWDTO)
        //{
        //    if (responseObject == null)
        //    {
        //        log.Error($"responseObject was null");
        //        return false;
        //    }

        //    if (responseObject.ApprovalCode == null)
        //    {
        //        log.Error($"responseObject.ApprovalCode was null. Last Transaction Check failed");
        //        return false;
        //    }

        //    if (!responseObject.IsValidApprovalCode())
        //    {
        //        log.Error($"responseObject.ApprovalCode is not successful");
        //        return false;
        //    }

        //    //if (!responseObject.RRN.Equals(cTransactionsPGWDTO.RefNo))
        //    //{
        //    //    log.Error($"responseObject.RRN != cTransactionsPGWDTO.RefNo. Last Transaction Check failed.");
        //    //    return false;
        //    //}

        //    return true;
        //}

        //private CCTransactionsPGWDTO CreateTransactionResponseDTO(BCAResponseDTO responseObject, CCRequestPGWDTO requestDTO)
        //{
        //    double resamount = Convert.ToDouble(responseObject.TransactionAmount);
        //    CCTransactionsPGWDTO responseDTO = new CCTransactionsPGWDTO
        //    {
        //        InvoiceNo = requestDTO.RequestID.ToString(),
        //        AcctNo = responseObject.PAN,
        //        AuthCode = responseObject.ApprovalCode,
        //        // CardType = responseObject.SchemeId,
        //        RefNo = responseObject.RRN,
        //        RecordNo = "",//responseObject.TrxRrn,
        //        TextResponse = ResponseDescriptions.ContainsKey(responseObject.ResponseCode) ? ResponseDescriptions[responseObject.ResponseCode] : "",
        //        TranCode = requestDTO.TransactionType,
        //        TransactionDatetime = utilities.getServerTime(),
        //        Authorize = resamount.ToString()
        //};

        //    return responseDTO;
        //}
    }
}
