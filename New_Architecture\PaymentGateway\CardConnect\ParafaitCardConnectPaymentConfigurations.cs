﻿/******************************************************************************************************
 * Project Name - Payment Gateway
 * Description  - Parafait Adyen Payment Configurations
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By    Remarks          
 ******************************************************************************************************
 *2.190.0     26-Feb-2025         Amrutha      Created
 ********************************************************************************************************/

using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    class ParafaitCardConnectPaymentConfigurations : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        public ParafaitCardConnectPaymentConfigurations(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO, bool isUnattended)
        {
            string posId = executionContext.POSMachineName;

            SetConfiguration("CREDIT_CARD_TERMINAL_PORT_NO", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_PORT_NO"));
            SetConfiguration("CREDIT_CARD_STORE_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_STORE_ID"));
            SetConfiguration("CREDIT_CARD_HOST_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_HOST_URL"));
            SetConfiguration("CREDIT_CARD_DEVICE_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_DEVICE_URL"));
            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
            SetConfiguration("ENABLE_AUTO_CREDIT_CARD_AUTHORIZATION", paymentModeContainerDTO.EnableAutoCreditcardAuthorization ? "Y" : "N");
            SetConfiguration("MIN_PREAUTH", Convert.ToString(paymentModeContainerDTO.PreAuthAmount));
            SetConfiguration("CREDIT_CARD_HOST_USERNAME", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_HOST_USERNAME"));
            SetConfiguration("CREDIT_CARD_TOKEN_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TOKEN_ID"));
            SetConfiguration("CREDIT_CARD_TERMINAL_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_TERMINAL_ID"));
            SetConfiguration("CREDIT_CARD_HOST_PASSWORD", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CREDIT_CARD_HOST_PASSWORD"));  ///5tBc6bfn81FKzR4p8k6Hw==
            SetConfiguration("ENABLE_CREDIT_CARD_DEVICE_BEEP_SOUND", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "ENABLE_CREDIT_CARD_DEVICE_BEEP_SOUND"));
            SetConfiguration("ALLOW_CREDIT_CARD_AUTHORIZATION", paymentModeContainerDTO.AllowCreditCardAuthorization ? "Y" : "N");
            SetConfiguration("ENABLE_SIGNATURE_VERIFICATION", paymentModeContainerDTO.EnableSignatureVerification? "Y" : "N");
            SetConfiguration("ENABLE_ADDRESS_VALIDATION", paymentModeContainerDTO.EnableAddressValidation ? "Y" : "N");
            SetConfiguration("CURRENCY_SYMBOL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_SYMBOL"));
            SetConfiguration("ENABLE_DEBIT_PIN_PAYMENT", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "ENABLE_DEBIT_PIN_PAYMENT"));
            SetConfiguration("PosId", posId);
            SetConfiguration("isUnattended", isUnattended? "Y" : "N");
        }



       
    }
}
