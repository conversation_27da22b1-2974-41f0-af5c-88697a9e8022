﻿using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.Printer.FiscalPrinter.fiskaltrust
{
    public class FiscalTrustDataHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private DataAccessHandler dataAccessHandler;
        protected SqlTransaction sqlTransaction;
        public FiscalTrustDataHandler(SqlTransaction sqlTransaction = null)
        {
            log.LogMethodEntry(sqlTransaction);
            this.sqlTransaction = sqlTransaction;
            dataAccessHandler = new DataAccessHandler();
            log.LogMethodExit();
        }
        internal List<FiscalTrustDTO> GetFiskalTrustDailyOperationList(int requestId, int programId, int siteId)
        {
            log.LogMethodEntry(requestId, programId, siteId);
            List<FiscalTrustDTO> fiscalTrustDTOList = new List<FiscalTrustDTO>();
            string selectQuery = @"SELECT 
                                    crd.ConcurrentRequestId,
                                    pm.POSMachineId,
                                    pm.POSName,
                                    pm.Computer_Name,
                                    crd.Status,
                                    crd.Data,
                                    crd.Remarks
                                FROM 
                                    ConcurrentRequestDetails crd
                                LEFT JOIN 
                                    POSMachines pm 
                                ON 
                                    crd.ParafaitObjectId = pm.POSMachineId
                                WHERE 
                                    crd.ConcurrentRequestId = @requestId
                                    AND crd.ConcurrentProgramId = @programId
                                    AND (crd.site_id = @siteId OR @siteId = -1);
                                ";
            SqlParameter[] parameters = new SqlParameter[]
            {
                    new SqlParameter("@requestId", requestId.ToString()),
                    new SqlParameter("@programId", programId.ToString()),
                    new SqlParameter("@siteId", siteId.ToString())
            };
            log.LogVariableState("selectQuery", selectQuery);
            DataTable dataTable = dataAccessHandler.executeSelectQuery(selectQuery, parameters, sqlTransaction);
            if (dataTable.Rows.Count > 0)
            {
                foreach (DataRow dataRow in dataTable.Rows)
                {
                    FiscalTrustDTO fiscalTrustDTO = GetFiscalTrustDTO(dataRow);
                    fiscalTrustDTOList.Add(fiscalTrustDTO);
                }
            }
            log.LogMethodExit(fiscalTrustDTOList);
            return fiscalTrustDTOList;
        } 
        private FiscalTrustDTO GetFiscalTrustDTO(DataRow dataRow)
        {
            log.LogMethodEntry(dataRow);
            FiscalTrustDTO fiscalTrustDTO = new FiscalTrustDTO
            (
                dataRow["ConcurrentRequestId"] == DBNull.Value ? -1 : Convert.ToInt32(dataRow["ConcurrentRequestId"]),
                dataRow["POSMachineId"] == DBNull.Value ? -1 : Convert.ToInt32(dataRow["POSMachineId"]),
                dataRow["POSName"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["POSName"]),
                dataRow["Computer_Name"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["Computer_Name"]),
                dataRow["Status"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["Status"]),
                dataRow["Data"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["Data"]),
                dataRow["Remarks"] == DBNull.Value ? string.Empty : Convert.ToString(dataRow["Remarks"])
            );
            log.LogMethodExit(fiscalTrustDTO);
            return fiscalTrustDTO;
        }
         
    }
}
