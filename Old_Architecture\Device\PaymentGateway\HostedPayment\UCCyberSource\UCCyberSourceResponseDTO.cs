﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.UCCyberSource
{

    public class UCCyberSourceResponseDTO
    {
        public Data data { get; set; }
        public string type { get; set; }
    }

    public class AllowedPaymentType
    {
        public int page { get; set; }
        public string type { get; set; }
    }

    public class AmountDetails
    {
        public string totalAmount { get; set; }
        public string currency { get; set; }
    }

    public class CaptureMandate
    {
        public string billingType { get; set; }
        public bool requestEmail { get; set; }
        public bool requestPhone { get; set; }
        public bool requestShipping { get; set; }
        public List<string> shipToCountries { get; set; }
        public bool showAcceptedNetworkIcons { get; set; }
    }

    public class Data
    {
        public List<AllowedPaymentType> allowedPaymentTypes { get; set; }
        public PaymentConfigurations paymentConfigurations { get; set; }
        public CaptureMandate captureMandate { get; set; }
        public OrderInformation orderInformation { get; set; }
        public List<string> targetOrigins { get; set; }
        public Iframes iframes { get; set; }
        public string clientVersion { get; set; }
        public string country { get; set; }
        public string locale { get; set; }
        public List<string> allowedCardNetworks { get; set; }
        public string cr { get; set; }
        public string serviceOrigin { get; set; }
        public string clientLibrary { get; set; }
        public string loggingPath { get; set; }
        public string assetsPath { get; set; }
        public string clientLibraryIntegrity { get; set; }
    }

    public class DpaData
    {
        public string dpaName { get; set; }
        public string dpaLogoUri { get; set; }
        public string dpaPresentationName { get; set; }
        public string dpaUri { get; set; }
    }

    public class DpaTransactionOptions
    {
        public string dpaLocale { get; set; }
        public string payloadTypeIndicator { get; set; }
        public string reviewAction { get; set; }
        public List<object> dpaAcceptedBillingCountries { get; set; }
        public List<string> dpaAcceptedShippingCountries { get; set; }
        public string dpaBillingPreference { get; set; }
        public string dpaShippingPreference { get; set; }
        public bool consumerNameRequested { get; set; }
        public bool consumerEmailAddressRequested { get; set; }
        public bool consumerPhoneNumberRequested { get; set; }
        public TransactionAmount transactionAmount { get; set; }
        public PaymentOptions paymentOptions { get; set; }
        public string transactionType { get; set; }
        public string threeDsPreference { get; set; }
    }

    public class Iframes
    {
        public string mce { get; set; }
        public string buttons { get; set; }
        public string src { get; set; }
        public string c2p { get; set; }
        public string googlepay { get; set; }
    }

    public class OrderInformation
    {
        public AmountDetails amountDetails { get; set; }
    }

    public class PanEncryptionKey
    {
        public string kty { get; set; }
        public string e { get; set; }
        public string use { get; set; }
        public string kid { get; set; }
        public string n { get; set; }
        public List<string> key_ops { get; set; }
        public string alg { get; set; }
    }

    public class Parameters
    {
        public string srcInitiatorId { get; set; }
        public string srciDpaId { get; set; }
        public string srciTransactionId { get; set; }
        public DpaTransactionOptions dpaTransactionOptions { get; set; }
        public DpaData dpaData { get; set; }
    }

    public class PaymentConfigurations
    {
        public SRCVISA SRCVISA { get; set; }
        public SRCMASTERCARD SRCMASTERCARD { get; set; }
        public SRCAMEX SRCAMEX { get; set; }
    }

    public class PaymentOptions
    {
        public bool dpaPanRequested { get; set; }
    }


    public class SRCAMEX
    {
        public string origin { get; set; }
        public string path { get; set; }
        public PanEncryptionKey panEncryptionKey { get; set; }
        public Parameters parameters { get; set; }
    }

    public class SRCMASTERCARD
    {
        public string origin { get; set; }
        public string path { get; set; }
        public PanEncryptionKey panEncryptionKey { get; set; }
        public Parameters parameters { get; set; }
    }

    public class SRCVISA
    {
        public string origin { get; set; }
        public string path { get; set; }
        public PanEncryptionKey panEncryptionKey { get; set; }
        public Parameters parameters { get; set; }
    }

    public class TransactionAmount
    {
        public string transactionAmount { get; set; }
        public string transactionCurrencyCode { get; set; }
    }


    public class TxSearchResponseDTO
    {
        public _Links _links { get; set; }
        public string searchId { get; set; }
        public bool save { get; set; }
        public string query { get; set; }
        public int count { get; set; }
        public int totalCount { get; set; }
        public int limit { get; set; }
        public int offset { get; set; }
        public string sort { get; set; }
        public string timezone { get; set; }
        public DateTime submitTimeUtc { get; set; }
        public _Embedded _embedded { get; set; }
    }

    public class _Links
    {
        public Authreversal authReversal { get; set; }
        public Self self { get; set; }
        public Capture capture { get; set; }
        public Void _void { get; set; }
        public Transactiondetail transactionDetail { get; set; }

    }

    public class Authreversal
    {
        public string method { get; set; }
        public string href { get; set; }
    }

    public class Self
    {
        public string method { get; set; }
        public string href { get; set; }
    }

    public class Void
    {
        public string method { get; set; }
        public string href { get; set; }
    }

    public class Capture
    {
        public string method { get; set; }
        public string href { get; set; }
    }

    public class Transactiondetail
    {
        public string href { get; set; }
        public string method { get; set; }
    }


    public class _Embedded
    {
        public List<Transactionsummary> transactionSummaries { get; set; }
    }

    public class Transactionsummary
    {
        public string id { get; set; }
        public DateTime submitTimeUtc { get; set; }
        public string merchantId { get; set; }
        public Applicationinformation applicationInformation { get; set; }
        public Buyerinformation buyerInformation { get; set; }
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public Consumerauthenticationinformation consumerAuthenticationInformation { get; set; }
        public Deviceinformation deviceInformation { get; set; }
        public Fraudmarkinginformation fraudMarkingInformation { get; set; }
        public Merchantinformation merchantInformation { get; set; }
        public Orderinformation orderInformation { get; set; }
        public Paymentinformation paymentInformation { get; set; }
        public Processinginformation processingInformation { get; set; }
        public Processorinformation processorInformation { get; set; }
        public Pointofsaleinformation pointOfSaleInformation { get; set; }
        public Riskinformation riskInformation { get; set; }
        public _Links _links { get; set; }
        public Installmentinformation installmentInformation { get; set; }
    }


    public class Applicationinformation
    {
        public int reasonCode { get; set; }
        public List<Application> applications { get; set; }
        public string rCode { get; set; }
        public string rFlag { get; set; }
    }

    public class Application
    {
        public string name { get; set; }
        public string reasonCode { get; set; }
        public string rCode { get; set; }
        public string rFlag { get; set; }
        public string reconciliationId { get; set; }
        public string rMessage { get; set; }
        public int returnCode { get; set; }
    }

    public class Buyerinformation
    {
    }

    public class Clientreferenceinformation
    {
        public string code { get; set; }
        public string applicationName { get; set; }
        public string applicationVersion { get; set; }
        public Partner partner { get; set; }
    }

    public class Partner
    {
    }

    public class Consumerauthenticationinformation
    {
        public Strongauthentication strongAuthentication { get; set; }
        public string eciRaw { get; set; }
    }

    public class Strongauthentication
    {
    }

    public class Deviceinformation
    {
        public string ipAddress { get; set; }
    }

    public class Installmentinformation
    {
    }

    public class Fraudmarkinginformation
    {
    }

    public class Merchantinformation
    {
        public Merchantdescriptor merchantDescriptor { get; set; }
        public string resellerId { get; set; }
    }

    public class Merchantdescriptor
    {
        public string name { get; set; }
    }


    public class billTo
    {
        public string address1 { get; set; }
        public string address2 { get; set; }
        public string country { get; set; }
        public string administrativeArea { get; set; }
        public string locality { get; set; }
        public string district { get; set; }
        public string postalCode { get; set; }
        public string buildingNumber { get; set; }
        public string email { get; set; }
        public string firstName { get; set; }
        public string lastName { get; set; }
        public string phoneNumber { get; set; }


        public string state { get; set; }
        public string city { get; set; }

    }

    public class Shipto
    {
    }

    public class Paymentinformation
    {
        public Tokenizedcard tokenizedCard { get; set; }
        //public Card1 card { get; set; }

        public Customer customer { get; set; }
        public Paymentinstrument paymentInstrument { get; set; }
        public Instrumentidentifier instrumentIdentifier { get; set; }
        public Shippingaddress shippingAddress { get; set; }
        public Paymenttype paymentType { get; set; }
        public Card card { get; set; }
        public Invoice invoice { get; set; }
        public Accountfeatures accountFeatures { get; set; }

    }


    public class Tokenizedcard
    {
        public string type { get; set; }
    }

    public class Customer
    {
    }

    public class Paymentinstrument
    {
    }

    public class Instrumentidentifier
    {
    }

    public class Shippingaddress
    {
    }

    public class Paymenttype
    {
        public string name { get; set; }
        public string type { get; set; }
        public string method { get; set; }
    }



    public class Invoice
    {
    }

    public class Accountfeatures
    {
    }
    public class Card
    {
        public string type { get; set; }
        public string expirationMonth { get; set; }
        public string expirationYear { get; set; }

        public string suffix { get; set; }
        public string prefix { get; set; }
        public string number { get; set; }
    }

    public class Processinginformation
    {
        public string paymentSolution { get; set; }
        public string commerceIndicator { get; set; }
        public Authorizationoptions authorizationOptions { get; set; }
        public Banktransferoptions bankTransferOptions { get; set; }
        public Japanpaymentoptions japanPaymentOptions { get; set; }
        public Fundingoptions fundingOptions { get; set; }
        public bool capture { get; set; }
    }

    public class Authorizationoptions
    {
        public string authType { get; set; }
        public Initiator initiator { get; set; }
    }

    public class Initiator
    {
        public Merchantinitiatedtransaction merchantInitiatedTransaction { get; set; }
    }

    public class Merchantinitiatedtransaction
    {
    }

    public class Banktransferoptions
    {
    }

    public class Japanpaymentoptions
    {
    }

    public class Fundingoptions
    {
        public bool firstRecurringPayment { get; set; }
    }

    public class Processorinformation
    {
        public string transactionId { get; set; }
        public Processor processor { get; set; }
        public string networkTransactionId { get; set; }
        public string approvalCode { get; set; }
        public string responseCode { get; set; }
        public Avs avs { get; set; }
        public Cardverification cardVerification { get; set; }
        public Achverification achVerification { get; set; }
        public Electronicverificationresults electronicVerificationResults { get; set; }
        public string eventStatus { get; set; }
    }


    public class Processor
    {
        public string name { get; set; }
    }
    public class Avs
    {
        public string code { get; set; }
        public string codeRaw { get; set; }
    }

    public class Cardverification
    {
    }

    public class Achverification
    {
        public string resultCodeRaw { get; set; }
    }

    public class Electronicverificationresults
    {
    }


    public class Pointofsaleinformation
    {
        public Partner partner { get; set; }
        public string terminalId { get; set; }
    }

    public class Riskinformation
    {
        public Score score { get; set; }
        public Providers providers { get; set; }
    }

    public class Score
    {
    }

    public class Providers
    {
        public Fingerprint fingerPrint { get; set; }
    }

    public class Fingerprint
    {
        public string trueIPAddress { get; set; }
        public string hash { get; set; }
        public string smartId { get; set; }
    }

    public class TxStatusDTO
    {
        public int reasonCode { get; set; }
        public string status { get; set; }
        public string paymentId { get; set; }
        public string TxType { get; set; }
        public string InvoiceNo { get; set; }
        public string AuthCode { get; set; }
        public string Authorize { get; set; }
        public string Purchase { get; set; }
        public DateTime TransactionDatetime { get; set; }
        public string AcctNo { get; set; }
        public string RecordNo { get; set; }
        public string RefNo { get; set; }
        public string TextResponse { get; set; }

    }

    public class VoidResponseDTO
    {
        public _Links _links { get; set; }
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public string id { get; set; }
        public Orderinformation orderInformation { get; set; }
        public string status { get; set; }
        public DateTime submitTimeUtc { get; set; }
        public Voidamountdetails voidAmountDetails { get; set; }
    }

    public class Voidamountdetails
    {
        public string currency { get; set; }
        public string voidAmount { get; set; }
    }

    public class RefundResponseDTO
    {
        public _Links _links { get; set; }
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public string id { get; set; }
        public Orderinformation orderInformation { get; set; }
        public Processorinformation processorInformation { get; set; }
        public string reconciliationId { get; set; }
        public Refundamountdetails refundAmountDetails { get; set; }
        public string status { get; set; }
        public DateTime submitTimeUtc { get; set; }
    }
    public class Refundamountdetails
    {
        public string currency { get; set; }
        public string refundAmount { get; set; }
    }
}
