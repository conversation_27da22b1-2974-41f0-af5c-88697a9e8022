﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class SettlementRequestDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        private decimal amount;
        private string requestIdentifier;
        private decimal tipAmount;
        private List<PaymentResponseDTO> paymentResponseHistory;
        private PaymentResponseDTO paymentResponseDTO;
        private int intRequestIdentifier;
        private DateTime requestDate;


        /// <summary>
        /// Constructor with all the data fields
        /// </summary>
        public SettlementRequestDTO(decimal amount, string requestIdentifier, int intRequestIdentifier, decimal tipAmount,PaymentResponseDTO paymentResponseDTO, List<PaymentResponseDTO> paymentResponseHistory, DateTime requestDate)

        {
            log.LogMethodEntry(amount, requestIdentifier);
            this.amount = amount;
            this.tipAmount = tipAmount;
            this.requestDate = requestDate;
            this.paymentResponseDTO = paymentResponseDTO;
            this.requestIdentifier = requestIdentifier;
            this.intRequestIdentifier = intRequestIdentifier;
            this.paymentResponseHistory = paymentResponseHistory;
            log.LogMethodExit();
        }

        /// <summary>
        /// Get/Set method of the Amount field
        /// </summary>
        public decimal Amount
        {
            get
            {
                return amount;
            }

            set
            {
                //IsChanged = true;
                amount = value;
            }
        }
        public decimal TipAmount
        {
            get
            {
                return tipAmount;
            }

            set
            {
                //IsChanged = true;
                tipAmount = value;
            }
        }
        public string RequestIdentifier
        {
            get
            {
                return requestIdentifier;
            }

            set
            {
                //IsChanged = true;
                requestIdentifier = value;
            }
        }
        public int IntRequestIdentifier
        {
            get
            {
                return intRequestIdentifier;
            }

            set
            {
                //IsChanged = true;
                intRequestIdentifier = value;
            }
        }
        /// <summary>
        /// Get method of the requestDate field
        /// </summary>
        public DateTime RequestDate
        {
            get
            {
                return requestDate;
            }
            set
            {
                // this.IsChanged = true;
                requestDate = value;
            }
        }

        public List<PaymentResponseDTO> PaymentResponseHistory
        {
            get { return paymentResponseHistory; }
            set { paymentResponseHistory = value; }
        }
        public PaymentResponseDTO PaymentResponseDTO
        {
            get { return paymentResponseDTO; }
            set { paymentResponseDTO = value; }
        }

        public override string ToString()
        {
            return Newtonsoft.Json.JsonConvert.ToString(this);
        }
    }
}
