﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Midtrans
{
    public class MidtransCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private string environment;
        private string serverKey;
        private string clientKey;
        private string transactionToken;
        private string snapUrl;
        private string gatewayBaseUrl;
        private string gatewayPostUrl;
        private string post_url;
        private string paymentPageLink;

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";


        private HostedGatewayDTO hostedGatewayDTO;
        IDictionary<string, string> paymentIds = new Dictionary<string, string>();
        MidtransHostedCommandHandler midtransHostedCommandHandler;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
                            { "settlement", PaymentStatusType.SUCCESS },
                            { "capture", PaymentStatusType.SUCCESS },
                            { "deny", PaymentStatusType.FAILED },
                            { "cancel", PaymentStatusType.FAILED },
                            { "expire", PaymentStatusType.FAILED },
                            { "refund", PaymentStatusType.FAILED },
                            { "partial_refund", PaymentStatusType.FAILED },
                            { "failure", PaymentStatusType.FAILED },
                            { "pending", PaymentStatusType.PENDING },
                            { "authorize", PaymentStatusType.PENDING }
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundVoidStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
                            { "200", PaymentStatusType.SUCCESS },
                            { "201", PaymentStatusType.PENDING},
                            { "202", PaymentStatusType.FAILED },
                            { "400", PaymentStatusType.FAILED },
                            { "401", PaymentStatusType.FAILED },
                            { "402", PaymentStatusType.FAILED },
                            { "403", PaymentStatusType.FAILED },
                            { "404", PaymentStatusType.FAILED },
                            { "405", PaymentStatusType.FAILED },
                            { "406", PaymentStatusType.FAILED },
                            { "407", PaymentStatusType.FAILED },
                            { "408", PaymentStatusType.FAILED },
                            { "409", PaymentStatusType.FAILED },
                            { "410", PaymentStatusType.FAILED },
                            { "411", PaymentStatusType.FAILED },
                            { "412", PaymentStatusType.FAILED },
                            { "413", PaymentStatusType.FAILED },
                            { "500", PaymentStatusType.FAILED },
                            { "501", PaymentStatusType.FAILED },
                            { "502", PaymentStatusType.FAILED },
                            { "503", PaymentStatusType.FAILED },
                            { "504", PaymentStatusType.FAILED },
        };

        public MidtransCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            BuildTransactions = false;
            Initialize();
            log.LogMethodExit(null);
        }


        public override void Initialize()
        {
            hostedGatewayDTO.GatewayRequestStringContentType = RequestContentType.FORM.ToString();
            serverKey = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            clientKey = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            gatewayPostUrl = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_SESSION_URL");
            gatewayBaseUrl = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_BASE_URL");
            snapUrl = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_API_URL");

            post_url = "/account/Midtrans";

            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", serverKey);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY", clientKey);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_SESSION_URL", gatewayPostUrl);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_BASE_URL", gatewayBaseUrl);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_API_URL", snapUrl);
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";

            if (string.IsNullOrWhiteSpace(serverKey))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(clientKey))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            }
            if (string.IsNullOrWhiteSpace(gatewayPostUrl))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SESSION_URL");
            }
            if (string.IsNullOrWhiteSpace(gatewayBaseUrl))
            {
                errMsg += String.Format(errMsgFormat, "CREDIT_CARD_HOST_URL");
            }
            if (string.IsNullOrWhiteSpace(snapUrl))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }

            midtransHostedCommandHandler = new MidtransHostedCommandHandler(serverKey, gatewayPostUrl);

            String apiSite = "";
            String webSite = "";

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API") != null)
            {
                apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;

            }
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB") != null)
            {
                webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;

            }

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    var queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.MidtransCallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.MidtransCallbackHostedPayment.ToString());

            }

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            if (string.IsNullOrWhiteSpace(this.hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(this.hostedGatewayDTO.CancelURL) || string.IsNullOrWhiteSpace(this.hostedGatewayDTO.FailureURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SUCCESS_REDIRECT_URL/FAILURE_REDIRECT_URL/CANCEL_REDIRECT_URL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;

            log.LogMethodExit();
        }

        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentToken);
            try
            {
                if (transactionPaymentsDTO.Amount <= 0)
                {
                    log.Error($"Order amount must be greater than zero. Order Amount was {transactionPaymentsDTO.Amount}");
                    throw new Exception("Order amount must be greater than zero");
                }
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;

                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
                requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
                requestParamsDict.Add("paymentToken", paymentToken);

                hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
                hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(hostedGatewayDTO.GatewayRequestString, paymentPageLink);
                //hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(requestParamsDict, paymentPageLink, "authForm");


                log.Debug("Request string:" + hostedGatewayDTO.GatewayRequestString);
                log.Debug("Direct request link:" + hostedGatewayDTO.PaymentRequestLink);
                //log.Debug("GatewayRequestFormString:" + hostedGatewayDTO.GatewayRequestFormString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }


        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            try
            {
                //transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;
                //CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());
                //Below code is to get transaction token from the Midtrans
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, transactionPaymentsDTO.Guid);//here, transactionPaymentsDTO.guid contains guid of ccrequestpgw
                CCRequestPGWDTO cCRequestPGWDTO = cCRequestPGWBL.CCRequestPGWDTO;
                string endPoint = gatewayBaseUrl + "/v1/transactions";

                MidtransGetTokenRequestDTO.GetTokenRequest getTokenRequest = new MidtransGetTokenRequestDTO.GetTokenRequest
                {
                    transaction_details = new MidtransGetTokenRequestDTO.TransactionDetails
                    {
                        gross_amount = Convert.ToInt32(transactionPaymentsDTO.Amount), // decimal not supported for currency IDR,
                        order_id = transactionPaymentsDTO.TransactionId.ToString()
                    },
                    credit_card = new MidtransGetTokenRequestDTO.CreditCard
                    {
                        secure = true
                    },
                    customer_details = new MidtransGetTokenRequestDTO.CustomerDetails
                    {
                        first_name = transactionPaymentsDTO.CreditCardName.ToString(),
                        last_name = transactionPaymentsDTO.Memo.ToString(),
                        email = transactionPaymentsDTO.NameOnCreditCard.ToString(),
                        phone = transactionPaymentsDTO.Reference
                    },
                };

                string response = midtransHostedCommandHandler.MakeRequest(getTokenRequest, endPoint);
                log.Debug("Response for get token API:" + response);
                dynamic midtransGetTokenResponse = JsonConvert.DeserializeObject(response);
                if (!string.IsNullOrEmpty(midtransGetTokenResponse["token"].ToString()))
                {
                    transactionToken = midtransGetTokenResponse["token"];
                    log.Debug("Transaction Token: " + transactionToken);
                }

                hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO));
                hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO), post_url, "MidtransWebPaymentsForm");
                log.Debug(hostedGatewayDTO.GatewayRequestString);

            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        /// <summary>
        /// SetPostParameters
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns></returns>
        private IDictionary<string, string> SetPostParameters(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCRequestPGWDTO);
            IDictionary<string, string> postparamslist = new Dictionary<string, string>();
            postparamslist.Clear();
            postparamslist.Add("transactionToken", transactionToken);
            postparamslist.Add("clientKey", clientKey);
            postparamslist.Add("snapUrl", snapUrl);
            postparamslist.Add("paymentModeId", transactionPaymentsDTO.PaymentModeId.ToString());
            postparamslist.Add("successURL", successResponseAPIURL);
            postparamslist.Add("failureURL", failureResponseAPIURL);
            postparamslist.Add("cancelURL", hostedGatewayDTO.CancelURL);
            log.LogMethodExit(postparamslist);
            return postparamslist;
        }

        /// <summary>
        /// GetSubmitFormKeyValueList
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append(string.Format("</head><body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" id=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");

            return builder.ToString();
        }

        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            if (this.hostedGatewayDTO == null)
            {
                this.hostedGatewayDTO = new HostedGatewayDTO();
            }

            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            log.Debug("DeserializeObject Initiated");

            MidTransTxSearchResponseDTO response = JsonConvert.DeserializeObject<MidTransTxSearchResponseDTO>(gatewayResponse);
            log.Debug("Test response " + response);
            if (response != null && response.order_id != null)
            {

                hostedGatewayDTO.TrxId = Convert.ToInt32(response.order_id);
                hostedGatewayDTO.GatewayReferenceNumber = response.transaction_id;
            }
            else
            {
                log.Error("Response for Sale Transaction doesn't contain TrxId.");
                throw new Exception("Error processing your payment");
            }
            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            MidTransTxSearchResponseDTO response = JsonConvert.DeserializeObject<MidTransTxSearchResponseDTO>(gatewayResponse);
            log.Debug("Transcation response: " + response);
            MidTransTxSearchResponseDTO midTransTxSearchResponseDTO = null;
            try
            {
                hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(response.order_id);

                midTransTxSearchResponseDTO = midtransHostedCommandHandler.GetOrderStatus(response.order_id);

                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(midTransTxSearchResponseDTO.gross_amount);
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = midTransTxSearchResponseDTO.transaction_id;

                log.Debug("Transaction Status:" + response.transaction_status);
                string status = midTransTxSearchResponseDTO.transaction_status;

                PaymentStatusType paymentStatus = MapPaymentStatus(status, PaymentGatewayTransactionType.SALE);

                log.Debug("Transaction Status after Mapping:" + paymentStatus.ToString());

                if (paymentStatus == PaymentStatusType.SUCCESS)
                {
                    if (midTransTxSearchResponseDTO.payment_type == "credit_card")

                    {
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = midTransTxSearchResponseDTO.approval_code;
                        string acctNo = response.masked_card;
                        if (!string.IsNullOrEmpty(acctNo))
                        {
                            acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                        }
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = acctNo;
                    }

                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;
                }
                else if (paymentStatus == PaymentStatusType.PENDING)
                {
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;

                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.PENDING;
                    if (midTransTxSearchResponseDTO.payment_type == "credit_card")
                    {
                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = midTransTxSearchResponseDTO.approval_code;
                    }

                }
                else if (paymentStatus == PaymentStatusType.FAILED)
                {
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;

                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    if (midTransTxSearchResponseDTO.payment_type == "credit_card")
                    {

                        hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = midTransTxSearchResponseDTO.approval_code;
                    }
                }
                else
                {
                    log.Error("Payment status is unknown. Considering status as failed Status: " + paymentStatus.ToString());
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                if (!String.IsNullOrEmpty(cCRequestsPGWDTO.ReferenceNo))
                {
                    string[] resvalues = cCRequestsPGWDTO.ReferenceNo.ToString().Split('|');
                    foreach (string word in resvalues)
                    {
                        if (word.Contains("PaymentModeId") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(word.Split(':')[1]);
                        }
                        else if (word.Contains("CurrencyCode") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = word.Split(':')[1];
                        }
                    }
                }

                if (cCRequestsPGWDTO == null)
                {
                    log.Error($"No cCRequest details found for TrxID {hostedGatewayDTO.TransactionPaymentsDTO.TransactionId}");
                    throw new Exception("Error processing payment");
                }

                TransactionSiteId = cCRequestsPGWDTO.SiteId;

                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);

                if (cCTransactionsPGWDTOList == null)
                {
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", midTransTxSearchResponseDTO.gross_amount);
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", midTransTxSearchResponseDTO.gross_amount);
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.RefNo = midTransTxSearchResponseDTO.transaction_id;
                    cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.AcctNo = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber;
                    cCTransactionsPGWDTO.TextResponse = hostedGatewayDTO.PaymentStatus.ToString();
                    cCTransactionsPGWDTO.DSIXReturnCode = midTransTxSearchResponseDTO.fraud_status;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.AuthCode = midTransTxSearchResponseDTO.fraud_status;
                    cCTransactionsPGWDTO.PaymentStatus = paymentStatus.ToString();

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                }
            }
            catch (Exception ex)
            {
                log.Error("Last transaction check failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        public MidTransTxSearchResponseDTO GetOrderStatus(string trxId)
        {
            log.LogMethodEntry(trxId);

            MidTransTxSearchResponseDTO midTransTxSearchResponseDTO = null;
            try
            {
                if (string.IsNullOrEmpty(trxId))
                {
                    log.Error("No TransactionId passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                string endPoint = gatewayPostUrl + "/v2/" + trxId + "/status";
                WebRequestClient webRequestClient = new WebRequestClient(endPoint, HttpVerb.GET);
                webRequestClient.Username = serverKey;
                webRequestClient.IsBasicAuthentication = true;
                string response = webRequestClient.GetResponse();
                log.Debug("Response for Get transaction status: " + response);
                midTransTxSearchResponseDTO = JsonConvert.DeserializeObject<MidTransTxSearchResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(midTransTxSearchResponseDTO);
            return midTransTxSearchResponseDTO;
        }

        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = null;
            MidTransTxSearchResponseDTO midTransTxSearchResponseDTO = null;
            DateTime paymentDate;
            string textResponse, acctNo;
            PaymentStatusType refundPaymentStatus;
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters);

                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO);

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo; // parafait trxId
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                //Check Trx response status if not settled then call Cancel(Void), Else- Refund transaction
                midTransTxSearchResponseDTO = midtransHostedCommandHandler.GetOrderStatus(refundTrxId);

                acctNo = midTransTxSearchResponseDTO.masked_card;
                if (!string.IsNullOrEmpty(acctNo))
                {
                    acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                }

                if (midTransTxSearchResponseDTO == null)
                {
                    log.Error("Refund payment txStatus resposne was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (midTransTxSearchResponseDTO.payment_type.ToLower() == "gopay" || midTransTxSearchResponseDTO.payment_type.ToLower() == "credit_card")
                {
                    //VOID PAYMENT
                    if (midTransTxSearchResponseDTO.transaction_status.ToLower() == "capture" || midTransTxSearchResponseDTO.transaction_status.ToLower() == "pending" || midTransTxSearchResponseDTO.transaction_status.ToLower() == "authorize")
                    {
                        log.Debug("Starting Void payment");
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);

                        string endPoint = $"{gatewayPostUrl}/v2/{refundTrxId}/cancel";
                        log.Debug("Void(Cancel) API endPoint: " + endPoint);

                        MidTransVoidResponseDTO midTransVoidResponseDTO = midtransHostedCommandHandler.MakeVoid(endPoint);
                        log.Debug("Void Response: " + midTransVoidResponseDTO);

                        paymentDate = !string.IsNullOrEmpty(midTransVoidResponseDTO.transaction_time) ? Convert.ToDateTime(midTransVoidResponseDTO.transaction_time) : utilities.getServerTime();
                        refundPaymentStatus = MapPaymentStatus(midTransVoidResponseDTO.status_code, PaymentGatewayTransactionType.VOID);
                        log.Debug("Value of txSearchPaymentStatus: " + refundPaymentStatus.ToString());
                        if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Void Success");
                            textResponse = PaymentStatusType.SUCCESS.ToString();
                        }
                        else
                        {
                            log.Error("Void Failed");
                            textResponse = PaymentStatusType.FAILED.ToString();
                        }

                        CCTransactionsPGWDTO cCVoidTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestPGWDTO.RequestID.ToString(), null, refundTrxId, midTransVoidResponseDTO.status_message, -1,
                                        textResponse, acctNo, "", PaymentGatewayTransactionType.VOID.ToString(), midTransVoidResponseDTO.transaction_id, string.Format("{0:0.00}", midTransVoidResponseDTO.gross_amount),
                                        string.Format("{0:0.00}", midTransVoidResponseDTO.gross_amount), paymentDate, midTransVoidResponseDTO.status_code, null, null, null, null, null, null, null, null, null);
                        log.Debug("Void- cCTransactionsPGWDTO: " + cCVoidTransactionsPGWDTO.ToString());
                        cCVoidTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCVoidTransactionsPGWDTO);
                        ccTransactionsPGWBL.Save();
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                        if (textResponse != PaymentStatusType.SUCCESS.ToString())
                        {
                            throw new Exception("Refund processing failed");
                        }
                    }
                    //REFUND PAYMENT
                    else if (midTransTxSearchResponseDTO.transaction_status.ToLower() == "settlement")
                    {
                        log.Debug("Starting Refund");
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                        MidtransRefundRequestDTO.MidtransRefundRequest midtransRefundRequest = new MidtransRefundRequestDTO.MidtransRefundRequest
                        {
                            refund_key = transactionPaymentsDTO.Reference,
                            amount = Convert.ToInt32(transactionPaymentsDTO.Amount), // decimal not supported for currency IDR
                        };

                        string endPoint = gatewayPostUrl + "/v2/" + refundTrxId + "/refund";
                        log.Debug("Refund API endPoint: " + endPoint);

                        MidTransRefundResponseDTO midTransRefundResponseDTO = midtransHostedCommandHandler.MakeRefund(midtransRefundRequest, endPoint);
                        log.Debug("Refund Response: " + midTransRefundResponseDTO);
                        paymentDate = !string.IsNullOrEmpty(midTransRefundResponseDTO.transaction_time) ? Convert.ToDateTime(midTransRefundResponseDTO.transaction_time) : utilities.getServerTime();

                        refundPaymentStatus = MapPaymentStatus(midTransRefundResponseDTO.status_code, PaymentGatewayTransactionType.REFUND);
                        log.Debug("Value of txSearchPaymentStatus: " + refundPaymentStatus.ToString());
                        if (refundPaymentStatus == PaymentStatusType.SUCCESS && midTransRefundResponseDTO.transaction_status.ToLower() == "refund")
                        {
                            log.Debug("Refund Success");
                            textResponse = PaymentStatusType.SUCCESS.ToString();
                            //todo, focus on transaction_status for mapping
                        }
                        else
                        {
                            log.Error("Refund Failed");
                            textResponse = PaymentStatusType.FAILED.ToString();
                        }

                        CCTransactionsPGWDTO cCRefundTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestPGWDTO.RequestID.ToString(), null, refundTrxId, midTransRefundResponseDTO.status_message, -1,
                                        textResponse, acctNo, "", PaymentGatewayTransactionType.REFUND.ToString(), midTransRefundResponseDTO.transaction_id, string.Format("{0:0.00}", midTransRefundResponseDTO.gross_amount),
                                        string.Format("{0:0.00}", midTransRefundResponseDTO.gross_amount), paymentDate, midTransRefundResponseDTO.status_code, null, ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null, null, null, null, null, null, null, null);
                        log.Debug("Void- cCTransactionsPGWDTO: " + cCRefundTransactionsPGWDTO.ToString());
                        cCRefundTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();
                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCRefundTransactionsPGWDTO);
                        ccTransactionsPGWBL.Save();
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                        if (textResponse != PaymentStatusType.SUCCESS.ToString())
                        {
                            throw new Exception("Refund processing failed");
                        }
                    }
                    else if (midTransTxSearchResponseDTO.transaction_status.ToLower() == "refund" || midTransTxSearchResponseDTO.transaction_status.ToLower() == "cancel")
                    {
                        //Partial refund transaction not allowed for refund
                        log.Error($"Transaction trxID: {refundTrxId} has been already Voided/Refunded. Payment TrxStatus: {midTransTxSearchResponseDTO.transaction_status}");
                        throw new Exception("Transaction already refunded");
                    }
                    else if (midTransTxSearchResponseDTO.transaction_status.ToLower() == "partial_refund")
                    {
                        //Partial refund transaction not allowed for refund
                        log.Error("Partial refund transaction not allowed for refund");
                        throw new Exception("Partial refund transaction not allowed for refund");
                    }
                    else
                    {
                        log.Error("Payment refund failed");
                        throw new Exception("Error processing Refund");
                    }
                }
                else
                {
                    //Only GoPay and Credit card payments have refund options
                    log.Error("Only GoPay and Credit card payments have refund options");
                    throw new Exception("Only GoPay and Credit card payments have refund options");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            MidTransTxSearchResponseDTO midTransTxSearchResponseDTO = null;
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;
            string acctNo;
            DateTime paymentDate;

            try
            {
                if (string.IsNullOrEmpty(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                midTransTxSearchResponseDTO = GetOrderStatus(trxId);

                if (midTransTxSearchResponseDTO != null)
                {
                    if (!string.IsNullOrEmpty(midTransTxSearchResponseDTO.transaction_status) && (midTransTxSearchResponseDTO.transaction_status.ToLower() == "capture" || midTransTxSearchResponseDTO.transaction_status.ToLower() == "settlement"))
                    {
                        log.Error($"GetTransactionStatus: Payment success for TrxId {trxId}");

                        acctNo = midTransTxSearchResponseDTO.masked_card ?? "";
                        if (!string.IsNullOrEmpty(acctNo))
                        {
                            acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                        }
                        paymentDate = !string.IsNullOrEmpty(midTransTxSearchResponseDTO.transaction_time) ? Convert.ToDateTime(midTransTxSearchResponseDTO.transaction_time) : utilities.getServerTime();

                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestsPGWDTO.RequestID.ToString(), null, trxId, midTransTxSearchResponseDTO.status_message, -1,
                                        midTransTxSearchResponseDTO.transaction_status, acctNo, midTransTxSearchResponseDTO.card_type, PaymentGatewayTransactionType.STATUSCHECK.ToString(), midTransTxSearchResponseDTO.transaction_id, midTransTxSearchResponseDTO.gross_amount,
                                        midTransTxSearchResponseDTO.gross_amount, paymentDate, midTransTxSearchResponseDTO.approval_code, null, null, null, null, null, null, null, null, null);
                        log.Debug("GetTransactionStatus- cCTransactionsPGWDTO: " + cCTransactionsPGWDTO.ToString());

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();

                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", "no payment found");
                        dict.Add("retref", midTransTxSearchResponseDTO.transaction_id ?? "");
                        dict.Add("amount", midTransTxSearchResponseDTO.gross_amount ?? "0");
                        dict.Add("orderId", trxId);
                    }
                }
                else
                {
                    log.Error($"Could not find Payment for trxId: {trxId}.");
                    //cancel the Tx in Parafait DB
                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", trxId);
                }
            }
            catch (Exception ex)
            {
                log.Error("GetTransactionStatus failed - " + ex.Message);
                throw;
            }

            resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

            log.LogMethodExit(resData);
            return resData;
        }

      
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.PENDING;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.VOID:
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundVoidStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }


        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            MidTransTxSearchResponseDTO orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    //throw new Exception("Insufficient Params passed to the request");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = GetOrderStatus(trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error($"Order status for trxId: {trxIdString} failed.");
                    throw new Exception($"Transaction search failed for trxId: {trxIdString}!");
                }

                PaymentStatusType txSearchPaymentStatus = MapPaymentStatus(orderStatusResult.transaction_status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of txSearchPaymentStatus: " + txSearchPaymentStatus.ToString());
                string acctNo = orderStatusResult.masked_card ?? "";
                if (!string.IsNullOrEmpty(acctNo))
                {
                    acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                }

                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                cCTransactionsPGWDTO.RecordNo = trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", orderStatusResult.gross_amount ?? "");
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", orderStatusResult.gross_amount ?? "");
                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                cCTransactionsPGWDTO.RefNo = trxIdString;
                cCTransactionsPGWDTO.AuthCode = orderStatusResult.approval_code;
                cCTransactionsPGWDTO.AcctNo = acctNo;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.transaction_status;
                cCTransactionsPGWDTO.DSIXReturnCode = txSearchPaymentStatus.ToString();
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.AuthCode = orderStatusResult.fraud_status;
                cCTransactionsPGWDTO.PaymentStatus = txSearchPaymentStatus.ToString();

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }

    }
}

