﻿/********************************************************************************************
 * Project Name -  Thawani Hosted Payment Gateway                                                                     
 * Description  -  Class to handle the payment of Thawani Payment Gateway
 *
 **************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 *********************************************************************************************
 *2.140.4      2-Jan-2023    Muaaz Musthafa                  Created for Website
 *2.152.1      8-Jul-2024    Prajwal Shrikanth Hegde         Payment Standardization - Added MapPaymentStatus() and GetPaymentStatusSearch()
 ********************************************************************************************/

using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Semnox.Parafait.Discounts;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Thawani
{

    public class ThawaniPayCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private HostedGatewayDTO hostedGatewayDTO;
        private string PUBLIC_KEY;
        private string SECRET_KEY;
        private string HOST_URL;
        private string CHECKOUT_URL;
        private string currencyCode;
        private string CANCEL_URL;
        private const string REFUND_REASON = "Payment Cancelled";

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";
        private string paymentPageLink;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"successful", PaymentStatusType.SUCCESS}, // Ref link: https://thawani-technologies.stoplight.io/docs/thawani-ecommerce-api/0c6618e26a362-payment-model
            {"inProccess", PaymentStatusType.FAILED},
            {"failed", PaymentStatusType.FAILED},
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"successful", PaymentStatusType.SUCCESS}, // Ref link: https://thawani-technologies.stoplight.io/docs/thawani-ecommerce-api/cc21ee27b164f-create-a-refund
            {"failed", PaymentStatusType.FAILED},
        };

        public ThawaniPayCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            this.hostedGatewayDTO = new HostedGatewayDTO();
            this.Initialize();
            this.BuildTransactions = false;
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();
            SECRET_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            PUBLIC_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            HOST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            CHECKOUT_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SESSION_URL");
            currencyCode = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");

            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";

            if (string.IsNullOrWhiteSpace(SECRET_KEY))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(PUBLIC_KEY))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            }
            if (string.IsNullOrWhiteSpace(HOST_URL))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(CHECKOUT_URL))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SESSION_URL");
            }

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            String apiSite = "";
            String webSite = "";

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API") != null)
            {
                apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;
            }
            //log.Debug("apiSite " + apiSite);

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB") != null)
            {
                webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;
            }
            //log.Debug("webSite " + webSite);

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    var queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.ThawaniPayCallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                //log.Debug("successResponseAPIURL " + successResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                //log.Debug("failureResponseAPIURL " + failureResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                //log.Debug("cancelResponseAPIURL " + cancelResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                //log.Debug("callbackResponseAPIURL " + callbackResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                //log.Debug("successRedirectURL " + this.hostedGatewayDTO.SuccessURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.ThawaniPayCallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;
            
            log.LogMethodExit();
        }

        /// <summary>
        /// Creates a initial gateway request.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="paymentToken"></param>
        /// <returns>HostedGatewayDTO</returns>
        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);

                if (transactionPaymentsDTO.Amount <= 0)
                {
                    log.Error($"Order amount must be greater than zero. Order Amount was {transactionPaymentsDTO.Amount}");
                    throw new Exception("Order amount must be greater than zero");
                }

                CCRequestPGWDTO cCRequestPGWDTO = this.CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
                requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
                requestParamsDict.Add("paymentToken", paymentToken);

                this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
                this.hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(this.hostedGatewayDTO.GatewayRequestString, paymentPageLink);
                this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(requestParamsDict, paymentPageLink, "authForm");

                log.Debug("Request string:" + this.hostedGatewayDTO.GatewayRequestString);
                log.Debug("Direct request link:" + this.hostedGatewayDTO.PaymentRequestLink);
                log.Debug("GatewayRequestFormString:" + this.hostedGatewayDTO.GatewayRequestFormString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + this.hostedGatewayDTO.ToString());
            return this.hostedGatewayDTO;
        }



        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            CheckoutSessionResponseDto response = null;

            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);
                int PaymentModeId = transactionPaymentsDTO.PaymentModeId;
                string orderId = transactionPaymentsDTO.TransactionId.ToString();
                string checkoutUrl = string.Empty;

                string checkout_success_url = successResponseAPIURL + $"?client_reference_id={orderId}&PaymentModeId={PaymentModeId}";
                string checkout_cancel_url = hostedGatewayDTO.CancelURL;

                List<Product> productList = new List<Product>();

                //get product details
                foreach (DiscountCouponsDTO dcDTO in transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList)
                {
                    productList.Add(new Product
                    {
                        name = dcDTO.FromNumber,
                        quantity = dcDTO.Count,
                        unit_amount = Convert.ToInt32(dcDTO.CouponValue * 1000)
                    });
                }
                // build Request Dto
                CreateCheckoutSessionRequestDto checkoutRequestDTO = new CreateCheckoutSessionRequestDto
                {
                    client_reference_id = orderId,
                    mode = "payment",
                    products = productList,
                    success_url = checkout_success_url,
                    cancel_url = checkout_cancel_url,
                    metadata = new Metadata
                    {
                        Customer_name = transactionPaymentsDTO.CreditCardName, // CreditCardName contains customer name
                        Customer_email = transactionPaymentsDTO.NameOnCreditCard, // NameOnCreditCard contains e-mail Id
                        Customer_phonenumber = transactionPaymentsDTO.CardEntitlementType, // CardEntitlementType contains phone number
                        orderid = transactionPaymentsDTO.TransactionId,
                        PaymentModeId = PaymentModeId
                    }
                };

                log.Debug($"Create Checkout RequestDto: {JsonConvert.SerializeObject(checkoutRequestDTO)}");
                ThawaniPayCommandHandler commandHandler = new ThawaniPayCommandHandler(PUBLIC_KEY, SECRET_KEY, HOST_URL, CHECKOUT_URL);
                response = commandHandler.CreateCheckout(checkoutRequestDTO);
                log.Debug($"Create Checkout ResponseDto: {JsonConvert.SerializeObject(response)}");

                if (response == null)
                {
                    log.Error("CreateGatewayPaymentRequest(): Checkout Transaction Response was empty");
                    throw new Exception("Error: could not create payment session");
                }

                if (string.IsNullOrWhiteSpace(response.data.session_id))
                {
                    log.Error("Create_Checkout response.data.session_id was null");
                    throw new Exception("Error processing the payment");
                }

                checkoutUrl = CHECKOUT_URL + response.data.session_id;
                log.Debug($"CreateGatewayPaymentRequest(): Checkout URL: {checkoutUrl}");

                this.hostedGatewayDTO.RequestURL = checkoutUrl;

                this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(SetPostParameters(), checkoutUrl, "fromThawaniForm", "GET");

                log.Info("request url:" + this.hostedGatewayDTO.RequestURL);
                log.Info("request form string:" + this.hostedGatewayDTO.GatewayRequestFormString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + this.hostedGatewayDTO.ToString());
            return this.hostedGatewayDTO;
        }

        /// <summary>
        /// Sets the get parameters required for the Thawani API request.
        /// </summary>
        /// <returns>
        /// Returns a dictionary containing the get parameters for the Thawani API request.
        /// </returns>
        private IDictionary<string, string> SetPostParameters()
        {
            log.LogMethodEntry();
            IDictionary<string, string> postparamslist = new Dictionary<string, string>();

            postparamslist.Clear();
            postparamslist.Add("key", PUBLIC_KEY);

            log.LogMethodExit(postparamslist);
            return postparamslist;
        }

        /// <summary>
        /// GetSubmitFormKeyValueList
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();

            builder.Append("<html>");

            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");
            return builder.ToString();
        }

        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            this.hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            CheckoutSessionResponseDto checkoutSessionResponse = null;
            GetPaymentListResponseDto paymentListResponseDto = null;
            ThawaniPayResponse paymentResponseObj = null;
            string paymentStatus = "";
            Data paymentObj = null;

            try
            {
                if (string.IsNullOrWhiteSpace(gatewayResponse))
                {
                    log.Error("Response for Sale Transaction was empty.");
                    throw new Exception("Error processing your payment");
                }

                log.Debug($"Sale Tx Response: {gatewayResponse}");
                dynamic response = JsonConvert.DeserializeObject(gatewayResponse);

                if (response["client_reference_id"] != null)
                {
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(response["client_reference_id"]);
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }

                if (response["PaymentModeId"] != null) // from browser redirect 
                {
                    hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(response["PaymentModeId"]);
                }
                else if (response["metadata"]["PaymentModeId"] != null) // from webhook
                {
                    hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(response["metadata"]["PaymentModeId"]);
                }


                ThawaniPayCommandHandler commandHandler = new ThawaniPayCommandHandler(PUBLIC_KEY, SECRET_KEY, HOST_URL, CHECKOUT_URL);
                GetCheckoutSessionRequestDto getCheckoutSessionRequestDto = new GetCheckoutSessionRequestDto
                {
                    client_reference_id = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()
                };
                log.Debug($"getCheckoutSessionRequestDto: {getCheckoutSessionRequestDto.client_reference_id.ToString()}");
                paymentResponseObj = commandHandler.GetPaymentByMerchantRequestId(getCheckoutSessionRequestDto);
                log.Debug($"paymentListResponseDto={JsonConvert.SerializeObject(paymentResponseObj)}");

                if (paymentResponseObj == null || paymentResponseObj.paymentListResponseDto == null)
                {
                    log.Error("Payment Response was null");
                    throw new Exception("Error processing your payment");
                }

                checkoutSessionResponse = paymentResponseObj.checkoutSessionResponseDto;
                paymentListResponseDto = paymentResponseObj.paymentListResponseDto;
                //get exact object

                // Search for successful payment
                var responseObj = from resultObj in paymentListResponseDto.data
                                  where resultObj.refunded == false
                                  orderby resultObj.created_at descending
                                  select resultObj;
                log.Debug($"responseObj={JsonConvert.SerializeObject(responseObj)}");

                paymentObj = responseObj.FirstOrDefault();
                if (paymentObj == null)
                {
                    log.Error("ProcessGatewayResponse: Payment object is null");
                    throw new Exception("Error processing your payment");
                }

                log.Debug($"Payment Object: {JsonConvert.SerializeObject(paymentObj)}");

                //found the payment object
                //update db
                string maskedCardNo = string.IsNullOrEmpty(paymentObj.masked_card) && paymentObj.masked_card.Length > 4 ? "" : String.Format("{0}{1}", "XXXXXXXXXXXX", paymentObj.masked_card.Substring(paymentObj.masked_card.Length - 4, 4));
                string cardType = string.IsNullOrEmpty(paymentObj.masked_card) ? "" : GetCardTypeHelper(paymentObj.masked_card.Substring(0, 2)) + " " + paymentObj.card_type;

                PaymentStatusType salePaymentStatus = MapPaymentStatus(paymentObj.status, PaymentGatewayTransactionType.SALE);

                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = currencyCode;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = paymentObj.payment_id == null ? "" : paymentObj.payment_id;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = maskedCardNo;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = cardType;
                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(paymentObj.amount * 0.001);
                hostedGatewayDTO.GatewayReferenceNumber = paymentObj.payment_id == null ? "" : paymentObj.payment_id;
                hostedGatewayDTO.PaymentStatusMessage = paymentObj.reason == null ? "" : paymentObj.reason;

                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    // payment succeeded
                    //hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = responseObj.auth_code;
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                    paymentStatus = "APPROVED_";
                }
                else
                {
                    //payment was failed
                    //hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = responseObj.auth_code;
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    paymentStatus = "FAILED_";
                }
                
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                this.TransactionSiteId = cCRequestsPGWDTO.SiteId;

                log.Debug("Trying to update the CC request to payment processing status");
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);

                int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(PaymentProcessStatusType.PAYMENT_PROCESSING.ToString(), hostedGatewayDTO.PaymentProcessStatus.ToString());

                if (rowsUpdated == 0)
                {
                    log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                }
                else
                {
                    log.Debug("CC request updated to " + hostedGatewayDTO.PaymentProcessStatus.ToString());
                }

                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference));
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);

                if (cCTransactionsPGWDTOList == null)
                {
                    //update the CCTransactionsPGWDTO
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString(); // parafait TrxId         
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    //Thawani does not provide authcode
                    //cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.Authorize = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.Purchase = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.AcctNo = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber;
                    cCTransactionsPGWDTO.CardType = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName;
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference; // paymentId
                    cCTransactionsPGWDTO.TextResponse = string.Concat(paymentStatus, paymentObj.status);
                    cCTransactionsPGWDTO.DSIXReturnCode = hostedGatewayDTO.PaymentStatusMessage;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                    this.hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment Processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="gatewayResponse"></param>
        /// <returns></returns>
        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            if (this.hostedGatewayDTO == null)
            {
                this.hostedGatewayDTO = new HostedGatewayDTO();
            }
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            log.Debug("DeserializeObject Initiated");

            dynamic response = JsonConvert.DeserializeObject(gatewayResponse);

            int tempTrxId = 0;
            if (response == null)
            {
                log.Error("Response for Sale Transaction doesn't contain TrxId.");
                throw new Exception("Error processing your payment");
            }

            if (response["event_type"] != null && Convert.ToString(response["event_type"]).ToLower() != "checkout.completed")
            {
                log.Error("Response for Sale Transaction doesn't contain TrxId.");
                throw new Exception("Error processing your payment");
            }

            if (response["event_type"] != null)
            {
                int.TryParse(Convert.ToString(response["data"]["client_reference_id"]), out tempTrxId);
            }
            else
            {
                int.TryParse(Convert.ToString(response["client_reference_id"]), out tempTrxId);
            }

            if (tempTrxId <= 0)
            {
                log.Error("Response for Sale Transaction doesn't contain TrxId.");
                throw new Exception("Error processing your payment");
            }

            hostedGatewayDTO.TrxId = tempTrxId;

            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefundSuccess = false;
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }

                log.Debug("Refund processing started");
                //ModifiedBy Y
                log.Debug("Entering Request DTO");
                RefundResponseDto refundResponseDTO = null;
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                CreateRefundRequestDto requestDto = new CreateRefundRequestDto
                {
                    payment_id = transactionPaymentsDTO.Reference,
                    reason = REFUND_REASON,
                    // we can send Merchant defined fields here if required
                    metadata = new Metadata
                    {
                        PaymentModeId = transactionPaymentsDTO.PaymentModeId > 0 ? transactionPaymentsDTO.PaymentModeId : -1,
                    },
                };
                log.Debug("Existing Request DTO");

                log.Debug("ThawaniPay Refund RequestDTO: " + requestDto);

                ThawaniPayCommandHandler commandHandler = new ThawaniPayCommandHandler(PUBLIC_KEY, SECRET_KEY, HOST_URL, CHECKOUT_URL);
                refundResponseDTO = commandHandler.CreateRefund(requestDto);
                log.Debug("refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }

                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();

                if (refundResponseDTO.success)
                {
                    PaymentStatusType refundPaymentStatus = MapPaymentStatus(refundResponseDTO.data.status, PaymentGatewayTransactionType.REFUND);

                    ccTransactionsPGWDTO.RefNo = refundResponseDTO.data.refund_id; //paymentId
                    ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", refundResponseDTO.data.amount * 0.001);
                    ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", refundResponseDTO.data.amount * 0.001);
                    ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();

                    if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                    {
                        log.Debug("Refund Success");
                        isRefundSuccess = true;
                        ccTransactionsPGWDTO.TextResponse = refundResponseDTO.data.status == null ? "" : refundResponseDTO.data.status;
                        ccTransactionsPGWDTO.DSIXReturnCode = refundResponseDTO.data.reason == null ? "" : refundResponseDTO.data.reason;
                    }
                    else
                    {
                        //refund failed
                        isRefundSuccess = false;
                        string errorMessage = refundResponseDTO.description;
                        log.Error($"Refund Failed. Error Message received: {errorMessage} | {refundResponseDTO.data.reason}");
                        ccTransactionsPGWDTO.TextResponse = refundResponseDTO.data.status == null ? "" : refundResponseDTO.data.status;
                        ccTransactionsPGWDTO.DSIXReturnCode = refundResponseDTO.data.reason == null ? "" : refundResponseDTO.data.reason;
                    }
                }
                else
                {
                    isRefundSuccess = false;
                    log.Error("Refund failed. Response message: " + refundResponseDTO.description);
                    ccTransactionsPGWDTO.TextResponse = refundResponseDTO.description;
                    ccTransactionsPGWDTO.DSIXReturnCode = refundResponseDTO.code.ToString();
                    ccTransactionsPGWDTO.PaymentStatus = PaymentStatusType.FAILED.ToString();
                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefundSuccess)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType defaultStatus = PaymentStatusType.FAILED; //default status
            PaymentStatusType paymentStatusType = defaultStatus;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to failed.");
                    paymentStatusType = defaultStatus;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentStatusType = defaultStatus;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentGatewayTransactionType);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PaymentStatusType mappedPaymentStatus = PaymentStatusType.NONE;
            string trxIdString = string.Empty;

            GetPaymentListResponseDto paymentObj = null;
            CheckoutSessionResponseDto checkoutSessionResponse = null;
            ThawaniPayResponse paymentResponseObj = null;
            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (Convert.ToInt32(trxIdString) < 0 || string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No Transaction id passed");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("cCRequestsPGWDTO is null. Failed to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                //build Tx Search requestDTO
                GetCheckoutSessionRequestDto requestDto = new GetCheckoutSessionRequestDto
                {
                    client_reference_id = trxIdString,
                };
                log.Debug("GetPaymentByMerchantRequestId- searchRequestDTO: " + JsonConvert.SerializeObject(requestDto));
                ThawaniPayCommandHandler commandHandler = new ThawaniPayCommandHandler(PUBLIC_KEY, SECRET_KEY, HOST_URL, CHECKOUT_URL);
                paymentResponseObj = commandHandler.GetPaymentByMerchantRequestId(requestDto);
                if (paymentResponseObj == null || paymentResponseObj.paymentListResponseDto == null)
                {
                    log.Error("paymentResponseObj was null");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }
                log.Debug("Entire Payment Object: " + paymentResponseObj);

                checkoutSessionResponse = paymentResponseObj.checkoutSessionResponseDto;
                paymentObj = paymentResponseObj.paymentListResponseDto;

                if (paymentObj == null)
                {
                    log.Error("GetPaymentStatusSearch: Could not find the Payment object");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                var responseObj = from resultObj in paymentObj.data
                                  where resultObj.refunded == false
                                  orderby resultObj.created_at descending
                                  select resultObj;
                log.Debug($"responseObj={JsonConvert.SerializeObject(responseObj)}");

                var response = responseObj.FirstOrDefault();
                //found the payment
                //update db
                if (responseObj == null)
                {
                    log.Error("GetPaymentStatusSearch: Could not find the Payment or payment already refunded");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                log.Debug($"Payment found:{JsonConvert.SerializeObject(response)}");

                mappedPaymentStatus = MapPaymentStatus(response.status, PaymentGatewayTransactionType.STATUSCHECK);

                string maskedCardNo = string.IsNullOrEmpty(response.masked_card) ? "" : String.Format("{0}{1}", "XXXXXXXXXXXX", response.masked_card.Substring(response.masked_card.Length - 4, 4));
                string cardType = string.IsNullOrEmpty(response.masked_card) ? "" : GetCardTypeHelper(response.masked_card.Substring(0, 2)) + " " + response.card_type;

                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                cCTransactionsPGWDTO.Authorize = (response.amount * 0.001).ToString();
                cCTransactionsPGWDTO.Purchase = (response.amount * 0.001).ToString();
                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                cCTransactionsPGWDTO.RefNo = response.payment_id; //paymentId
                cCTransactionsPGWDTO.RecordNo = trxIdString; //parafait TrxId
                cCTransactionsPGWDTO.AcctNo = maskedCardNo;
                cCTransactionsPGWDTO.CardType = cardType;
                cCTransactionsPGWDTO.TextResponse = response.status;
                cCTransactionsPGWDTO.DSIXReturnCode = response.reason;
                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                cCTransactionsPGWDTO.PaymentStatus = mappedPaymentStatus.ToString();

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;

        }

        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);

            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;
            GetPaymentListResponseDto paymentObj = null;
            CheckoutSessionResponseDto checkoutSessionResponse = null;
            ThawaniPayResponse paymentResponseObj = null;
            try
            {
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                //build Tx Search requestDTO
                GetCheckoutSessionRequestDto requestDto = new GetCheckoutSessionRequestDto
                {
                    client_reference_id = trxId,
                };
                log.Debug("GetPaymentByMerchantRequestId- searchRequestDTO: " + JsonConvert.SerializeObject(requestDto));
                ThawaniPayCommandHandler commandHandler = new ThawaniPayCommandHandler(PUBLIC_KEY, SECRET_KEY, HOST_URL, CHECKOUT_URL);
                paymentResponseObj = commandHandler.GetPaymentByMerchantRequestId(requestDto);
                if (paymentResponseObj == null)
                {
                    log.Error("paymentResponseObj was null");
                    throw new Exception("Error fetching the payment");
                }
                log.Debug("GetTransactionStatus- Entire Payment Object: " + paymentResponseObj);

                checkoutSessionResponse = paymentResponseObj.checkoutSessionResponseDto;
                paymentObj = paymentResponseObj.paymentListResponseDto;

                if (paymentObj != null)
                {

                    var responseObj = from resultObj in paymentObj.data
                                      where resultObj.status == "successful" && resultObj.refunded == false // Fetch Successful Sale Transaction
                                      select resultObj;
                    log.Debug($"responseObj={JsonConvert.SerializeObject(responseObj)}");

                    if (responseObj.Count() > 0)
                    {
                        var response = responseObj.FirstOrDefault();
                        //found the payment
                        //update db
                        log.Debug($"Payment found:{JsonConvert.SerializeObject(response)}");

                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        cCTransactionsPGWDTO.Authorize = (response.amount * 0.001).ToString();
                        cCTransactionsPGWDTO.Purchase = (response.amount * 0.001).ToString();
                        cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        cCTransactionsPGWDTO.RefNo = response.payment_id == null ? "" : response.payment_id; //paymentId
                        cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                        cCTransactionsPGWDTO.AcctNo = string.IsNullOrEmpty(response.masked_card) && response.masked_card.Length > 4 ? "" : String.Format("{0}{1}", "XXXXXXXXXXXX", response.masked_card.Substring(response.masked_card.Length - 4, 4));
                        cCTransactionsPGWDTO.TextResponse = response.status == null ? "" : response.status;
                        cCTransactionsPGWDTO.DSIXReturnCode = response.reason == null ? "" : response.reason;
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();

                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", "payment failed");
                        dict.Add("orderId", trxId);
                    }

                    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                    log.LogMethodExit(resData);
                    return resData;
                }
                else
                {
                    log.Error("GetTransactionStatus: Could not find the Payment object");
                    //cancel the Tx in Parafait DB
                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", trxId);
                }
                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

        }

    }
}