﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;ExpressSoap&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="ExpressSoap" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;ExpressSoap1&quot; /&gt;" bindingType="basicHttpBinding" name="ExpressSoap1" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://certtransaction.elementexpress.com/express.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;ExpressSoap&quot; contract=&quot;ElementExpress.ExpressSoap&quot; name=&quot;ExpressSoap&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://certtransaction.elementexpress.com/express.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;ExpressSoap&quot; contract=&quot;ElementExpress.ExpressSoap&quot; name=&quot;ExpressSoap&quot; /&gt;" contractName="ElementExpress.ExpressSoap" name="ExpressSoap" />
  </endpoints>
</configurationSnapshot>