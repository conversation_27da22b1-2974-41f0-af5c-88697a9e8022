﻿/********************************************************************************************
 * Project Name -  VisaNet(Niubiz) Hosted Payment Gateway                                                                     
 * Description  -  Class to handle the payment of VisaNet(Niubiz) Hosted Payment Gateway
 *
 **************
 **Version Log
  *Version     Date            Modified By                     Remarks          
 *********************************************************************************************
 *2.130.0     08-Oct-2021      Jinto Thomas           Created for Website 
 *2.130.4     22-Feb-2022      Mathew Ninan           Modified DateTime to ServerDateTime 
 **********   07-Oct-2022      Muaaz Musthafa         Fix - throw exception if the card is not authorized
 *2.140.5     10-Feb-2023      Muaaz Musthafa         Fixed Payment Auth Request by using the Query API for payment details
 *2.140.5     10-Feb-2023      Muaaz Musthafa         Added support for Payment refund and TxSearch API  
 ********************************************************************************************/
using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.VisaNet
{

    public class VisaNetsHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        //private string environment;
        private string userName;
        private string password;
        private string merchantId;
        private string channelName;
        private string visaNetCheckoutJsUrl;
        private string sessionKey;
        private string visaNetApiBaseUrl;
        private string post_url;
        string customerToken;
        private string currencyCode;
        private HostedGatewayDTO hostedGatewayDTO;
        private Dictionary<string, string> responseCodes = new Dictionary<string, string>();
        private string merchantlogo;
        private string successUrl;
        private string failureUrl;
        private string callbackUrl;
        VisaNetHostedCommandHandler commandHandler = null;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Authorized", PaymentStatusType.SUCCESS },
            { "Not Authorized", PaymentStatusType.FAILED },
            { "Confirmed", PaymentStatusType.SUCCESS },
            { "Not Confirmed", PaymentStatusType.FAILED },
            { "Reject", PaymentStatusType.FAILED },
            { "Review", PaymentStatusType.PENDING },
            { "Not Verified", PaymentStatusType.FAILED },
        };
        private static readonly Dictionary<string, PaymentStatusType> StatusCheckStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Authorized", PaymentStatusType.SUCCESS },
            { "Not Authorized", PaymentStatusType.FAILED },
            { "Confirmed", PaymentStatusType.SUCCESS },
            { "Not Confirmed", PaymentStatusType.FAILED },
            { "Verified", PaymentStatusType.SUCCESS },
            { "Not Verified", PaymentStatusType.FAILED },
            { "Voided", PaymentStatusType.FAILED },
            { "Not Voided", PaymentStatusType.FAILED },
            { "Reject", PaymentStatusType.FAILED },
            { "Review", PaymentStatusType.PENDING }
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Authorized", PaymentStatusType.SUCCESS },
            { "Not Authorized", PaymentStatusType.FAILED },
            { "Confirmed", PaymentStatusType.SUCCESS },
            { "Not Confirmed", PaymentStatusType.FAILED },
            { "Verified", PaymentStatusType.SUCCESS },
            { "Not Verified", PaymentStatusType.FAILED },
            { "Voided", PaymentStatusType.SUCCESS },
            { "Not Voided", PaymentStatusType.FAILED },
            { "Reject", PaymentStatusType.FAILED },
            { "Review", PaymentStatusType.PENDING }
        };

        public VisaNetsHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            Initialize();
            log.LogMethodExit(null);
        }


        public override void Initialize()
        {
            hostedGatewayDTO.GatewayRequestStringContentType = RequestContentType.FORM.ToString();
            userName = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            password = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            merchantId = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            visaNetApiBaseUrl = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_BASE_URL");
            channelName = utilities.getParafaitDefaults("PAYMENT_GATEWAY_CHANNEL_NAME");
            visaNetCheckoutJsUrl = utilities.getParafaitDefaults("HOSTED_PAYMENT_GATEWAY_API_URL");
            currencyCode = utilities.getParafaitDefaults("CURRENCY_CODE");
            post_url = "/account/VisaNet";

            if (responseCodes.Count() == 0)
            {
                InitializeResponseCodes();
            }

            log.LogVariableState("userName", userName);
            log.LogVariableState("password", password);
            log.LogVariableState("merchantId", merchantId);
            log.LogVariableState("visaNetApiBaseUrl", visaNetApiBaseUrl);
            log.LogVariableState("visaNetCheckoutJsUrl", visaNetCheckoutJsUrl);
            log.LogVariableState("channelName", channelName);
            log.LogVariableState("currencyCode", currencyCode);
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";
            if (string.IsNullOrWhiteSpace(userName))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(password))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            }
            if (string.IsNullOrWhiteSpace(merchantId))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            }
            if (string.IsNullOrWhiteSpace(visaNetApiBaseUrl))
            {
                errMsg += String.Format(errMsgFormat, "CREDIT_CARD_HOST_URL");
            }
            if (string.IsNullOrWhiteSpace(visaNetCheckoutJsUrl))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(channelName))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_CHANNEL_NAME");
            }

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }

            commandHandler = new VisaNetHostedCommandHandler(userName, password, visaNetApiBaseUrl, merchantId);

            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            searchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
            List<LookupValuesDTO> lookupValuesDTOlist = lookupValuesList.GetAllLookupValues(searchParameters);

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_URL").Count() == 1)
            {
                hostedGatewayDTO.SuccessURL = lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.VisaNetsHostedPayment.ToString());
                successUrl = hostedGatewayDTO.SuccessURL;

                Uri NewUri;
                if (Uri.TryCreate(hostedGatewayDTO.SuccessURL, UriKind.Absolute, out NewUri))
                {
                    post_url = NewUri.GetLeftPart(UriPartial.Authority) + post_url;
                }
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILED_URL").Count() == 1)
            {
                hostedGatewayDTO.FailureURL = lookupValuesDTOlist.Where(x => x.LookupValue == "FAILED_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.VisaNetsHostedPayment.ToString());
                failureUrl = hostedGatewayDTO.FailureURL;
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_URL").Count() == 1)
            {
                hostedGatewayDTO.CallBackURL = lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_URL").First().Description.ToLower().Replace("@gateway", PaymentGateways.VisaNetsHostedPayment.ToString());
                callbackUrl = hostedGatewayDTO.CallBackURL;
            }

            if (string.IsNullOrWhiteSpace(hostedGatewayDTO.SuccessURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.FailureURL) || string.IsNullOrWhiteSpace(hostedGatewayDTO.CallBackURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "HOSTED_PAYMENT_DEFINED_DATA1").Count() == 1)
            {
                merchantlogo = lookupValuesDTOlist.Where(x => x.LookupValue == "HOSTED_PAYMENT_DEFINED_DATA1").First().Description;
            }
        }

        public override HostedGatewayDTO CreateGatewayPaymentRequest(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            customerToken = transactionPaymentsDTO.Reference;

            CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

            Dictionary<string, string> merchantDefineData = new Dictionary<string, string>();
            merchantDefineData.Add("MDD4", transactionPaymentsDTO.NameOnCreditCard);
            merchantDefineData.Add("MDD21", "1");
            merchantDefineData.Add("MDD32", transactionPaymentsDTO.CustomerCardProfileId); // transactionPaymentsDTO.CustomerCardProfileId contains CustomerID detail passed
            merchantDefineData.Add("MDD75", "Registered");
            merchantDefineData.Add("MDD77", (ServerDateTime.Now - transactionPaymentsDTO.CreationDate).Days.ToString()); // transactionPaymentsDTO.CreationDate contains Date of customer registration

            VisaNetFormSessionRequestDTO.Antifraud antifraud = new VisaNetFormSessionRequestDTO.Antifraud();
            antifraud.clientIp = transactionPaymentsDTO.ExternalSourceReference;
            antifraud.merchantDefineData = merchantDefineData;

            VisaNetFormSessionRequestDTO.VisaNetFormSessionRequest visaNetFormSessionRequest = new VisaNetFormSessionRequestDTO.VisaNetFormSessionRequest();
            visaNetFormSessionRequest.amount = transactionPaymentsDTO.Amount;
            visaNetFormSessionRequest.channel = channelName;
            visaNetFormSessionRequest.countable = true;
            visaNetFormSessionRequest.antifraud = antifraud;
            
            dynamic visaNetFormSessionResponse = commandHandler.CreateCheckout(visaNetFormSessionRequest);

            if (!string.IsNullOrEmpty(visaNetFormSessionResponse["sessionKey"].ToString()))
                {
                    sessionKey = visaNetFormSessionResponse["sessionKey"];
                    log.Debug("Form session: " + sessionKey);
                }

            hostedGatewayDTO.GatewayRequestString = GetSubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO), post_url, "frmPayHosted");
            log.Debug(hostedGatewayDTO.GatewayRequestString);

            hostedGatewayDTO.FailureURL = "/account/checkouterror";
            hostedGatewayDTO.SuccessURL = "/account/receipt";
            hostedGatewayDTO.CancelURL = "/account/checkoutstatus";
            LookupsList lookupList = new LookupsList(utilities.ExecutionContext);
            List<KeyValuePair<LookupsDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<LookupsDTO.SearchByParameters, string>>();
            searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
            searchParameters.Add(new KeyValuePair<LookupsDTO.SearchByParameters, string>(LookupsDTO.SearchByParameters.LOOKUP_NAME, "WEB_SITE_CONFIGURATION"));
            List<LookupsDTO> lookups = lookupList.GetAllLookups(searchParameters, true);
            if (lookups != null && lookups.Any())
            {
                List<LookupValuesDTO> lookupValuesDTOList = lookups[0].LookupValuesDTOList;
                LookupValuesDTO temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_FAILURE_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                {
                    hostedGatewayDTO.FailureURL = temp.Description;
                }

                temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_SUCCESS_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                {
                    hostedGatewayDTO.SuccessURL = temp.Description;
                }

                temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_CANCEL_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                {
                    hostedGatewayDTO.CancelURL = temp.Description;
                }

                // pending url
                temp = lookupValuesDTOList.FirstOrDefault(x => x.LookupValue.Equals("HOSTED_PAYMENT_PENDING_URL"));
                if (temp != null && !String.IsNullOrWhiteSpace(temp.Description))
                {
                    hostedGatewayDTO.PendingURL = temp.Description;
                }
            }

            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }



        /// <summary>
        /// SetPostParameters
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns></returns>
        private IDictionary<string, string> SetPostParameters(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCRequestPGWDTO);
            int tokenLifeTime = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "JWT_TOKEN_LIFE_TIME", 0);
            SecurityTokenBL securityTokenBL = new SecurityTokenBL(utilities.ExecutionContext);
            string guid = string.IsNullOrEmpty(customerToken) ? "" : customerToken;
            securityTokenBL.GenerateNewJWTToken("External POS", guid, utilities.ExecutionContext.GetSiteId().ToString(), "-1", "-1", "Customer", "-1", null, tokenLifeTime);

            IDictionary<string, string> postparamslist = new Dictionary<string, string>();
            postparamslist.Clear();
            postparamslist.Add("sessiontoken", sessionKey);
            postparamslist.Add("channel", channelName);
            postparamslist.Add("merchantid", merchantId);
            postparamslist.Add("purchasenumber", transactionPaymentsDTO.TransactionId.ToString());
            postparamslist.Add("amount", transactionPaymentsDTO.Amount.ToString());
            postparamslist.Add("cardholdername", transactionPaymentsDTO.CreditCardName.ToString());
            postparamslist.Add("cardholderlastname", transactionPaymentsDTO.Memo);
            postparamslist.Add("cardholderemail", transactionPaymentsDTO.NameOnCreditCard);
            postparamslist.Add("email", transactionPaymentsDTO.NameOnCreditCard);
            postparamslist.Add("expirationminutes", "20");
            postparamslist.Add("hidexbutton", "true");
            postparamslist.Add("timeouturl", failureUrl + $"?OrderId={transactionPaymentsDTO.TransactionId}&PaymentModeId={transactionPaymentsDTO.PaymentModeId}");
            postparamslist.Add("usertoken", "");
            postparamslist.Add("action", successUrl + $"?OrderId={transactionPaymentsDTO.TransactionId}&PaymentModeId={transactionPaymentsDTO.PaymentModeId}");
            postparamslist.Add("checkoutjsurl", visaNetCheckoutJsUrl);
            postparamslist.Add("customerToken", securityTokenBL.GetSecurityTokenDTO.Token);
            postparamslist.Add("usedId", transactionPaymentsDTO.CustomerCardProfileId);
            postparamslist.Add("merchantlogo", merchantlogo);
            log.LogMethodExit(postparamslist);
            return postparamslist;
        }

        /// <summary>
        /// GetSubmitFormKeyValueList
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append(string.Format("</head><body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" id=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");

            return builder.ToString();
        }


        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            string transactionToken;
            string securityToken;
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            VisaNetResponseDTO visaNetTxSearchResponseDTO = null;
            DateTime paymentDate = new DateTime();
            int transactionId, paymentModeId;
            string ccAuthorization, acctNo, ccCardType, textResponse, dSIXReturnCode, authCode = "";
            bool isStatusUpdated;

            dynamic response = JsonConvert.DeserializeObject(gatewayResponse);
            log.Debug("Transcation response: " + response);

            try
            {
                if (string.IsNullOrEmpty(response["transactionToken"].ToString()) && string.IsNullOrEmpty(response["OrderId"].ToString()) && string.IsNullOrEmpty(response["PaymentModeId"].ToString()))
                {
                    log.Error("No value found for Transaction Token/OrderId/PaymentModeId.");
                    throw new Exception("Error processing your payment");
                }

                transactionToken = response["transactionToken"];
                transactionId = Convert.ToInt32(response["OrderId"]);
                paymentModeId = Convert.ToInt32(response["PaymentModeId"]);

                log.Debug("Transaction Token: " + transactionToken);
                log.Debug("OrderId (transactionId): " + transactionId);
                log.Debug("PaymentModeId: " + paymentModeId);

                hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = transactionId;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = transactionToken;
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = paymentModeId;

                hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PROCESSING;
                isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);

                if (!isStatusUpdated)
                {
                    log.Error("ProcessGatewayResponse():Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, transactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("No cCRequestsPGW details found for TrxId: " + transactionId);
                    throw new Exception("Error processing your payment");
                }
                TransactionSiteId = cCRequestsPGWDTO.SiteId;
                
                AuthorizeTransactionRequestDTO.AuthorizeTransactionRequest authorizeTransactionRequest = new AuthorizeTransactionRequestDTO.AuthorizeTransactionRequest
                {
                    order = new AuthorizeTransactionRequestDTO.Order
                    {
                        purchaseNumber = transactionId.ToString(),
                        tokenId = transactionToken,
                        amount = Convert.ToDouble(cCRequestsPGWDTO.POSAmount),
                        currency = currencyCode,
                    },
                    channel = "web",
                    captureType = "manual",
                    countable = true
                };

                commandHandler.AuthorizeTransaction(authorizeTransactionRequest);

                //Make Order Status
                visaNetTxSearchResponseDTO = commandHandler.GetOrderStatus(transactionId.ToString());

                if (visaNetTxSearchResponseDTO == null)
                {
                    log.Error("No data found from Query API for Trx Id: " + transactionId);
                    throw new Exception("Error processing your payment");
                }

                paymentDate = GetPaymentDate(visaNetTxSearchResponseDTO);

                //Updating TransactionPaymentsDTO details
                if (visaNetTxSearchResponseDTO.dataMap != null)
                {
                    ccAuthorization = visaNetTxSearchResponseDTO.dataMap.AUTHORIZATION_CODE;
                    acctNo = visaNetTxSearchResponseDTO.dataMap.CARD;
                    acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                    ccCardType = visaNetTxSearchResponseDTO.dataMap.BRAND;
                    textResponse = visaNetTxSearchResponseDTO.dataMap.STATUS;
                    authCode = visaNetTxSearchResponseDTO.dataMap.AUTHORIZATION_CODE;
                    if (responseCodes.ContainsKey(visaNetTxSearchResponseDTO.dataMap.ACTION_CODE))
                    {
                        dSIXReturnCode = responseCodes[visaNetTxSearchResponseDTO.dataMap.ACTION_CODE];
                    }
                    else
                    {
                        dSIXReturnCode = "Pago fue rechazado. Por favor, comunícate con tu banco para mayor información.";
                    }
                }
                else
                {
                    ccAuthorization = "";
                    acctNo = "";
                    ccCardType = "";
                    textResponse = "";
                    authCode = "";
                    dSIXReturnCode = "";
                }

                TransactionPaymentsDTO transactionPaymentsDTO = new TransactionPaymentsDTO(-1, transactionId, paymentModeId, visaNetTxSearchResponseDTO.order.amount, acctNo, "", ccCardType, "", ccAuthorization, -1, "", -1, -1, transactionId.ToString(), "", false, TransactionSiteId, -1, "", paymentDate, "", -1, null, 0, -1, "", -1, currencyCode, null,SubscriptionAuthorizationMode.N);
                log.Debug("ProcessGatewayResponse- transactionPaymentsDTO: " + transactionPaymentsDTO.ToString());
                PaymentStatusType paymentStatus = MapPaymentStatus(visaNetTxSearchResponseDTO.order.status, PaymentGatewayTransactionType.SALE);
                log.Debug("PaymentStatus: " + paymentStatus);

                hostedGatewayDTO.TransactionPaymentsDTO = transactionPaymentsDTO;

                if (paymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Payment Authorized");
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                }
                else
                {
                    log.Error("Payment not Authorized. Status: " + visaNetTxSearchResponseDTO.order.status);
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }

                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference));
                cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);

                if (cCTransactionsPGWDTOList == null)
                {
                    log.Debug("No CC Transactions found");

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestsPGWDTO.RequestID.ToString(), null, transactionId.ToString(), dSIXReturnCode, -1,
                                    textResponse, acctNo, ccCardType, PaymentGatewayTransactionType.SALE.ToString(), transactionId.ToString(), string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount),
                                    string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount), paymentDate, authCode, null, null, null, null, null, null, null, null, null);
                    cCTransactionsPGWDTO.PaymentStatus = paymentStatus.ToString();
                    log.Debug("ProcessGatewayResponse- cCTransactionsPGWDTO: " + cCTransactionsPGWDTO.ToString());

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                    isStatusUpdated = UpdatePaymentProcessingStatus(hostedGatewayDTO);
                }
                if (!isStatusUpdated)
                {
                    log.Error("Error updating the payment status");
                    throw new Exception("redirect checkoutmessage");
                }
            }
            catch (Exception ex)
            {
                log.Error("Last transaction check failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        private DateTime GetPaymentDate(VisaNetResponseDTO visaNetTxSearchResponseDTO)
        {
            log.LogMethodEntry(visaNetTxSearchResponseDTO);
            DateTime paymentDate = new DateTime();

            if (visaNetTxSearchResponseDTO.order != null)
            {
                log.Debug("Payment Date from response: " + visaNetTxSearchResponseDTO.order.transactionDate);
                if (DateTime.TryParseExact(visaNetTxSearchResponseDTO.order.transactionDate, "yyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }

        /// <summary>
        /// This method is used to refund the amount
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            VisaNetResponseDTO visaNetTxSearchResponseDTO = null;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            VisaNetResponseDTO visaNetRefundResponseDTO = null;
            string refundTrxId = string.Empty;
            DateTime paymentDate = new DateTime();
            bool isRefund = false;
            PaymentStatusType refundStatus;

            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                visaNetTxSearchResponseDTO = commandHandler.GetOrderStatus(refundTrxId);
                if (visaNetTxSearchResponseDTO == null)
                {
                    log.Error($"Could not find Payment for trxId: {refundTrxId}");
                    throw new Exception("Error processing Refund");
                }

                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                ReverseRequestDTO.ReverseRequest reverseRequest = new ReverseRequestDTO.ReverseRequest
                {
                    channel = channelName,
                    order = new ReverseRequestDTO.Order
                    {
                        purchaseNumber = refundTrxId,
                        transactionDate = visaNetTxSearchResponseDTO.order.transactionDate
                    },
                };

                visaNetRefundResponseDTO = commandHandler.CreateRefund(reverseRequest);

            if (visaNetRefundResponseDTO == null)
                {
                    log.Error($"Failure to perform refund for : {refundTrxId}");
                    throw new Exception("Error processing Refund");
                }

                paymentDate = GetPaymentDate(visaNetTxSearchResponseDTO);

                string cardType, acctNo, textResponse, authCode, dSIXReturnCode;

                if (visaNetRefundResponseDTO.dataMap != null)
                {
                    cardType = visaNetRefundResponseDTO.dataMap.BRAND;
                    acctNo = visaNetRefundResponseDTO.dataMap.CARD;
                    acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                    textResponse = visaNetRefundResponseDTO.dataMap.STATUS;
                    authCode = visaNetTxSearchResponseDTO.dataMap.AUTHORIZATION_CODE;
                    dSIXReturnCode = visaNetTxSearchResponseDTO.dataMap.ACTION_DESCRIPTION;
                    refundStatus = MapPaymentStatus(visaNetRefundResponseDTO.dataMap.STATUS, PaymentGatewayTransactionType.REFUND);
                }
                else
                {
                    cardType = "";
                    acctNo = "";
                    textResponse = "Failed";
                    authCode = "";
                    dSIXReturnCode = "Failed";
                    refundStatus = PaymentStatusType.FAILED;
                }
                log.Debug("Refund status: " + refundStatus);
                log.Debug("response status: " + visaNetRefundResponseDTO.dataMap.STATUS);
                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestPGWDTO.RequestID.ToString(), null, refundTrxId, dSIXReturnCode, -1,
                                textResponse, acctNo, cardType, PaymentGatewayTransactionType.REFUND.ToString(), refundTrxId, string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount),
                                string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount), paymentDate, authCode, null, ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : "", null, null, visaNetRefundResponseDTO.order != null ? visaNetRefundResponseDTO.order.originalTraceNumber : "", null, null, null, null);
                ccTransactionsPGWDTO.PaymentStatus = refundStatus.ToString();

                log.Debug("RefundAmount- cCTransactionsPGWDTO: " + ccTransactionsPGWDTO.ToString());

                if (refundStatus == PaymentStatusType.SUCCESS)
                {
                    isRefund = true;
                    log.Debug("Refund Success");
                }
                else
                {
                    isRefund = false;
                    log.Error("Refund Failed");
                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception(utilities.MessageUtils.getMessage(2203));
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        /// <summary>
        /// This method is used  to get transaction status
        /// </summary>
        /// <param name="trxId">trxId </param>
        /// <returns></returns>
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            VisaNetResponseDTO visaNetTxSearchResponseDTO = null;
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;
            DateTime paymentDate = new DateTime();
            string dSIXReturnCode, textResponse, acctNo, cardType, authCode;

            try
            {
                if (string.IsNullOrEmpty(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                visaNetTxSearchResponseDTO = commandHandler.GetOrderStatus(trxId);

                if (visaNetTxSearchResponseDTO != null)
                {
                    paymentDate = GetPaymentDate(visaNetTxSearchResponseDTO);

                    if (visaNetTxSearchResponseDTO.order.status.ToLower() == "Authorized".ToLower())
                    {
                        if (visaNetTxSearchResponseDTO.dataMap != null)
                        {
                            textResponse = visaNetTxSearchResponseDTO.dataMap.STATUS;
                            acctNo = visaNetTxSearchResponseDTO.dataMap.CARD;
                            acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                            cardType = visaNetTxSearchResponseDTO.dataMap.BRAND;
                            authCode = visaNetTxSearchResponseDTO.dataMap.AUTHORIZATION_CODE;
                            dSIXReturnCode = !string.IsNullOrEmpty(visaNetTxSearchResponseDTO.dataMap.ACTION_DESCRIPTION) ? visaNetTxSearchResponseDTO.dataMap.ACTION_DESCRIPTION : textResponse;
                        }
                        else
                        {
                            dSIXReturnCode = "";
                            textResponse = "";
                            acctNo = "";
                            cardType = "";
                            authCode = "";
                        }

                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestsPGWDTO.RequestID.ToString(), null, trxId, dSIXReturnCode, -1,
                                    textResponse, acctNo, cardType, PaymentGatewayTransactionType.STATUSCHECK.ToString(), trxId, string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount),
                                    string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount), paymentDate, authCode, null, null, null, null, null, null, null, null, null);
                        log.Debug("GetTransactionStatus- cCTransactionsPGWDTO: " + cCTransactionsPGWDTO.ToString());

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();

                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", "no payment found");
                        dict.Add("retref", visaNetTxSearchResponseDTO.order.transactionId);
                        dict.Add("amount", string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount));
                        dict.Add("orderId", trxId);
                    }
                }
                else
                {
                    log.Error($"Could not find Payment for trxId: {trxId}.");
                    //cancel the Tx in Parafait DB
                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", trxId);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }
            resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

            log.LogMethodExit(resData);
            return resData;
        }

        private void InitializeResponseCodes()
        {
            responseCodes.Add("000", "Autorizada");
            responseCodes.Add("101", "La tarjeta ingresada se encuentra vencida. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("102", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("104", "Tu tarjeta no tiene autorización para realizar esta operación. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("106", "Has excedido la cantidad de intentos permitidos para ingresar tu contraseña. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("107", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("108", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("109", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("110", "Tu tarjeta no tiene autorización para realizar esta operación. Por favor, comunícate con tu banco para más información.");
            responseCodes.Add("111", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("112", "No has ingresado tu contraseña. Por favor, intenta nuevamente.");
            responseCodes.Add("113", " ");
            responseCodes.Add("116", "Tu tarjeta tiene fondos insuficientes. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("117", "La contraseña ingresada es incorrecta. Por favor, intenta nuevamente o comunícate con tu banco para mayor información.");
            responseCodes.Add("118", "La tarjeta ingresada es inválida. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("119", "Has excedido la cantidad de intentos permitidos para ingresar tu contraseña. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("121", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("126", "La contraseña ingresada es incorrecta. Por favor, intenta nuevamente o comunícate con el banco para mayor información.");
            responseCodes.Add("129", "La tarjeta ingresada es inválida. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("151", "La tarjeta ingresada se encuentra cancelada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("161", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("180", "La tarjeta ingresada es inválida. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("181", "Tu tarjeta de débito tiene restricciones. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("182", "Tu tarjeta de crédito tiene restricciones. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("183", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento.");
            responseCodes.Add("190", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("191", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("192", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("199", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("201", "La tarjeta ingresada se encuentra vencida. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("202", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("204", "Tu tarjeta no tiene autorización para realizar esta operación. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("206", "Has excedido la cantidad de intentos permitidos para ingresar tu contraseña. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("207", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("208", "Tu tarjeta fue reportada como perdida. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("209", "Tu tarjeta fue reportada como robada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("263", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("264", "Tu banco no se encuentra disponible en estos momentos. Por favor, intenta nuevamente en unos minutos.");
            responseCodes.Add("265", "La clave secreta ingresada es incorrecta. Por favor, intenta nuevamente o comunícate con tu banco para mayor información.");
            responseCodes.Add("266", "La tarjeta ingresada se encuentra vencida. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("280", "La contraseña ingresada es incorrecta. Por favor, intenta nuevamente o comunícate con tu banco para mayor información.");
            responseCodes.Add("282", "Tu tarjeta de crédito tiene restricciones. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("283", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("290", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("300", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("306", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("310", "Tarjeta bloqueada por pérdida. Contactar con emisor.");
            responseCodes.Add("311", "Tarjeta bloqueada por robo. Contactar con emisor.");
            responseCodes.Add("312", "Tarjeta bloqueada por vencimiento. Contactar con emisor.");
            responseCodes.Add("313", "Tarjeta bloqueada por emisor. Contactar con emisor.");
            responseCodes.Add("314", "Tarjeta bloqueada. Contactar con emisor.");
            responseCodes.Add("315", "Tarjeta bloqueada por vencimiento. Contactar con emisor.");
            responseCodes.Add("316", "Tarjeta no existe por emisor.");
            responseCodes.Add("317", "Tarjeta bloqueada por emisor. Contactar con emisor");
            responseCodes.Add("319", "Tarjeta habilitada, pero sin disponible para el cargo.");
            responseCodes.Add("320", "Tarjeta en lista negra.");
            responseCodes.Add("401", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para más información.");
            responseCodes.Add("403", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("404", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("405", "Tu tarjeta ha superado la cantidad de compras máximas permitidas en el día. Por favor, intenta nuevamente con otra tarjeta.");
            responseCodes.Add("406", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("407", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("420", "La tarjeta ingresada no es Visa. Recuerda que solo puedes realizar pagos con tarjeta Visa.");
            responseCodes.Add("421", "Tu tarjeta fue reportada como riesgosa. Por favor, comunícate con tu banco.");
            responseCodes.Add("423", "¡Disculpa! La comunicación ha sido interrumpida y el proceso de pago ha sido cancelado. Por favor, intenta nuevamente.");
            responseCodes.Add("424", "Tu tarjeta fue reportada como riesgosa. Por favor, comunícate con tu banco.");
            responseCodes.Add("426", "La venta no ha podido ser procesada, es posible que el link de pago no esté habilitado. Por favor, comunícate con el vendedor/establecimiento.");
            responseCodes.Add("427", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("428", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("429", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("430", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("431", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("432", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("433", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("434", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("435", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("436", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("437", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("438", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("439", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("440", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("441", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("442", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("443", "Los datos de la tarjeta ingresados son inválidos. Por favor, ingresa el mes y el año de expiración de tu tarjeta de crédito.");
            responseCodes.Add("444", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("445", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("446", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("447", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("448", "El número de cuotas ingresado es inválido. Recuerda que debes ingresar un número entero menor a 36.");
            responseCodes.Add("449", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("450", "El número de tarjeta ingresado es inválido. Por favor, intenta nuevamente con otra tarjeta.");
            responseCodes.Add("451", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("452", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento.");
            responseCodes.Add("453", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("454", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("455", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("456", "El monto de venta no ha sido ingresado. Por favor, intenta nuevamente.");
            responseCodes.Add("457", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("458", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("459", "No has ingresado el número de tarjeta. Por favor, intenta nuevamente con otra tarjeta.");
            responseCodes.Add("460", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("461", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("462", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("463", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("464", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("465", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("466", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("467", "El código “CVC” ingresado es inválido. Recuerda que el código “CVC” es un número entero de 3 o 4 dígitos. Si no logras visualizarlo, comunícate con tu banco.");
            responseCodes.Add("468", "El código “CVC” ingresado es inválido. Recuerda que el código “CVC” es un número entero de 3 o 4 dígitos. Si no logras visualizarlo, comunícate con tu banco.");
            responseCodes.Add("469", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("470", "El monto ingresado es inválido. Recuerda que solo debes ingresar números y punto decimal.");
            responseCodes.Add("471", "El monto ingresado es inválido. Recuerda que solo debes ingresar números y punto decimal.");
            responseCodes.Add("472", "El número de cuotas ingresado es inválido. Recuerda que debes ingresar un número entero menor a 36.");
            responseCodes.Add("473", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("474", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("475", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("476", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("477", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("478", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("479", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("480", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("481", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("482", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("483", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("484", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("485", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("486", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("487", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("488", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("489", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("490", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("491", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("492", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("493", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("494", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("495", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("496", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("497", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("498", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("619", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("666", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("667", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("668", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("670", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("672", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("673", "Por favor, intenta nuevamente en unos minutos.");
            responseCodes.Add("674", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para más información.");
            responseCodes.Add("675", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para más información.");
            responseCodes.Add("678", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para más información.");
            responseCodes.Add("680", "Tu banco ha denegado la transacción, verifica tener la opción de compras por internet activa en tu tarjeta.");
            responseCodes.Add("682", "El proceso de pago ha sido cancelado. Por favor, intenta nuevamente en unos minutos.");
            responseCodes.Add("683", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para más información.");
            responseCodes.Add("684", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para más información.");
            responseCodes.Add("685", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para más información.");
            responseCodes.Add("690", "Operación Denegada. Contactar con el comercio.");
            responseCodes.Add("691", "Operación Denegada. Contactar con el comercio.");
            responseCodes.Add("692", "Operación Denegada. Contactar con el comercio.");
            responseCodes.Add("754", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimient o para mayor información.");
            responseCodes.Add("904", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("909", "La venta no ha podido ser procesada. Por favor, intenta nuevamente o comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("910", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("912", "Tu banco no se encuentra disponible para autenticar la venta. Por favor, intenta nuevamente más tarde.");
            responseCodes.Add("913", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("916", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("928", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("940", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("941", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("942", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("943", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("945", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("946", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("947", "La venta no ha podido ser procesada. Por favor, comunícate con el vendedor/establecimiento para mayor información.");
            responseCodes.Add("948", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("949", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
            responseCodes.Add("965", "La venta no ha podido ser procesada. Por favor, comunícate con tu banco para mayor información.");
        }
        //add reference links
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.PENDING;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = StatusCheckStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            VisaNetResponseDTO visaNetTxSearchResponseDTO = null;
            string trxIdString = string.Empty;
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            DateTime paymentDate = new DateTime();
            string ccAuthorization, acctNo, ccCardType, textResponse, dSIXReturnCode, authCode = "";

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                visaNetTxSearchResponseDTO = commandHandler.GetOrderStatus(trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(visaNetTxSearchResponseDTO));

                if (visaNetTxSearchResponseDTO == null)
                {
                    log.Error($"Order status for trxId: {trxIdString} failed.");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                paymentDate = GetPaymentDate(visaNetTxSearchResponseDTO);

                //Updating TransactionPaymentsDTO details
                if (visaNetTxSearchResponseDTO.dataMap != null)
                {
                    ccAuthorization = visaNetTxSearchResponseDTO.dataMap.AUTHORIZATION_CODE;
                    acctNo = visaNetTxSearchResponseDTO.dataMap.CARD;
                    acctNo = string.Concat("************", acctNo.Substring((acctNo.Length - 4), 4));
                    ccCardType = visaNetTxSearchResponseDTO.dataMap.BRAND;
                    textResponse = visaNetTxSearchResponseDTO.dataMap.STATUS;
                    authCode = visaNetTxSearchResponseDTO.dataMap.AUTHORIZATION_CODE;
                    if (responseCodes.ContainsKey(visaNetTxSearchResponseDTO.dataMap.ACTION_CODE))
                    {
                        dSIXReturnCode = responseCodes[visaNetTxSearchResponseDTO.dataMap.ACTION_CODE];
                    }
                    else
                    {
                        dSIXReturnCode = "Pago fue rechazado. Por favor, comunícate con tu banco para mayor información.";
                    }
                }
                else
                {
                    ccAuthorization = "";
                    acctNo = "";
                    ccCardType = "";
                    textResponse = "";
                    authCode = "";
                    dSIXReturnCode = "";
                }

                PaymentStatusType paymentStatus = MapPaymentStatus(visaNetTxSearchResponseDTO.order.status, PaymentGatewayTransactionType.SALE);
                log.Debug("PaymentStatus: " + paymentStatus);

                hostedGatewayDTO.TransactionPaymentsDTO = transactionPaymentsDTO;

                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestsPGWDTO.RequestID.ToString(), null, trxIdString, dSIXReturnCode, -1,
                  textResponse, acctNo, ccCardType, paymentGatewayTransactionType.ToString(), trxIdString, string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount),
                  string.Format("{0:0.00}", visaNetTxSearchResponseDTO.order.amount), paymentDate, authCode, null, null, null, null, null, null, null, null, null);
                cCTransactionsPGWDTO.PaymentStatus = paymentStatus.ToString();

                log.Debug("ProcessGatewayResponse- cCTransactionsPGWDTO: " + cCTransactionsPGWDTO.ToString());

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();

            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }

    }
}