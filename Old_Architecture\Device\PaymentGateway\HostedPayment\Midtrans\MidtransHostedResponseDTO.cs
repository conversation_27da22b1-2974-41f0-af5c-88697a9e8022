﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Midtrans
{

    #region MidTransTxSearchResponseDTO
    public class MidTransTxSearchResponseDTO
    {
        public string masked_card { get; set; }
        public string approval_code { get; set; }
        public string bank { get; set; }
        public string eci { get; set; }
        public string channel_response_code { get; set; }
        public string channel_response_message { get; set; }
        public string transaction_time { get; set; }
        public string gross_amount { get; set; }
        public string currency { get; set; }
        public string order_id { get; set; }
        public string payment_type { get; set; }
        public string signature_key { get; set; }
        public string status_code { get; set; }
        public string transaction_id { get; set; }
        public string transaction_status { get; set; }
        public string fraud_status { get; set; }
        public string settlement_time { get; set; }
        public string status_message { get; set; }
        public string merchant_id { get; set; }
        public string card_type { get; set; }
        public string three_ds_version { get; set; }
        public bool challenge_completion { get; set; }
    }
    #endregion MidTransTxSearchResponseDTO

    #region MidTransRefundResponseDTO
    public class MidTransRefundResponseDTO
    {
        public string status_code { get; set; }
        public string status_message { get; set; }
        public string transaction_id { get; set; }
        public string order_id { get; set; }
        public string payment_type { get; set; }
        public string transaction_time { get; set; }
        public string transaction_status { get; set; }
        public string gross_amount { get; set; }
        public int refund_chargeback_id { get; set; }
        public string refund_amount { get; set; }
        public string refund_key { get; set; }
    }
    #endregion MidTransRefundResponseDTO

    #region MidTransVoidResponseDTO
    public class MidTransVoidResponseDTO
    {
        public string status_code { get; set; }
        public string status_message { get; set; }
        public string transaction_id { get; set; }
        public string masked_card { get; set; }
        public string order_id { get; set; }
        public string payment_type { get; set; }
        public string transaction_time { get; set; }
        public string transaction_status { get; set; }
        public string fraud_status { get; set; }
        public string bank { get; set; }
        public string gross_amount { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
    #endregion MidTransVoidResponseDTO
}
