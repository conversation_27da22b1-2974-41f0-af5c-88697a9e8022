﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Semnox.Parafait.Languages;
using Semnox.Parafait.Site;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Runtime.ExceptionServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Semnox.Parafait.Device.PaymentGateway
{
    public class AdyenPaymentGateway : PaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        string deviceUrl;

        bool isAuthEnabled;
        bool isDeviceBeepSoundRequired;
        bool isAddressValidationRequired;
        bool isCustomerAllowedToDecideEntryMode;


        bool isSignatureRequired;
        bool enableAutoAuthorization;
        string minPreAuth;
        string posId;
        IDisplayStatusUI statusDisplayUi;
        public delegate void DisplayWindow();
        private bool isPrintReceiptEnabled;

        // By Prasad
        private string deviceIpAddress; //= "*************";
        private string devicePort;
        private string protocolVersion = "3.0";
        private string messageClass = "Service";
        private string donationMessageCategory = "Payment";
        private string donationSaleId = "AdyenGiving";//Enquire
        private string tapiUrl;
        private string webApiKey;
        // staging
        private string webApiHostUrl;//= "https://checkout-test.adyen.com";
        private bool isCancelled = false;

        // production
        // private string WEB_API_HOST_URL = "https://checkout.adyen.com";
        private string webApiVersion;
        private string terminalId;
        private bool isCreditCardDonationEnabled = false;
        private bool isPartialPaymentEnabled = false;

        private bool isTipAllowed = false;
        private bool isCloudMode = true;


        private string shopperCountry;
        private string shopperCurrency;
        private string shopperLocale;
        private int currencyConversionFactor;
        private string preAuthAdjustApiUrl;
        private string storeName;
        private int adyenTransactionTimeout;

        private string merchantAccount;
        private const string PAYMENT_SUCCESS_STATUS = "Success";
        private const string REFUND_SUCCESS_STATUS = "Success";
        private const string PAYMENT_PARTIAL_STATUS = "Partial";
        private const string PAYMENT_FAILURE_STATUS = "Failure";
        private const string REFUND_FAILURE_STATUS = "Failure";
        private List<string> unsupportedCCSchemesforRefRefund = new List<string>();
        private List<string> successfullPaymentResponseList = new List<string>()
        {
            "partial",
            "success",
            "approved",
            "authorised"
        };




        private readonly Dictionary<string, string> LastTrxCheckMessageCategory = new Dictionary<string, string>
        {
            {"TATokenRequest", "Payment" },
            {"AUTHORIZATION", "Payment" },
            {"SALE", "Payment"},
            {"CREDIT CARD PAYMENT", "Payment"},
            {"REFUND", "Reversal"},
            {"VOID", "Reversal" }
        };


        // Application Information
        private Dictionary<string, string> applicationInfo = new Dictionary<string, string>();

        // ESD Data
        private Dictionary<string, string> esdInfo = new Dictionary<string, string>();


        private object locker = new object();
        // end


        public override bool IsTipAdjustmentAllowed
        {
            get { return true; }
        }
        public enum Alignment
        {
            Left,
            Right,
            Center
        }
        enum TransactionType
        {
            TATokenRequest,
            SALE,
            REFUND,
            AUTHORIZATION,
            VOID,
            PREAUTH,
            CAPTURE,
            DONATION // TBC newly added
        }
        public bool IsCancelled
        {
            get
            {
                lock (locker)
                {
                    return isCancelled;
                }
            }

            set
            {
                lock (locker)
                {
                    isCancelled = value;
                }
            }
        }


        public AdyenPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
    : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {

            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel   
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12; // comparable to modern browsers
            merchantAccount = utilities.getParafaitDefaults("CREDIT_CARD_STORE_ID"); //SemnoxSolutions757_PosSetup_TEST
            webApiHostUrl = utilities.getParafaitDefaults("CREDIT_CARD_HOST_URL"); //"https://checkout-test.adyen.com";
            webApiVersion = GetAdyenConfigurationLookupValue("POS_WEB_API_VERSION"); //"v70";// utilities.getParafaitDefaults("CREDIT_CARD_HOST_URL"); // TBC "v70"//map it to seperate default required
            webApiKey = utilities.getParafaitDefaults("CREDIT_CARD_TOKEN_ID");//"AQE0hmfxJo/HbhBLw0m/n3Q5qf3Ve45AA5xSa2pa02yikWhYyZIh6qTWrNG2PfAyNMgbeebiKBDBXVsNvuR83LVYjEgiTGAH-p+BQql2Jvdjm/H63LoA5rHYpOtCTXQH67+R+tbEccXo=-r3bY~xvU3=Xjwk_M";//utilities.getParafaitDefaults("CREDIT_CARD_TOKEN_ID");

            terminalId = utilities.getParafaitDefaults("CREDIT_CARD_TERMINAL_ID"); // terminal id :"P400Plus-*********"
            deviceUrl = utilities.getParafaitDefaults("CREDIT_CARD_DEVICE_URL"); // cloud API URL => https://terminal-api-test.adyen.com/sync
            deviceIpAddress = utilities.getParafaitDefaults("CREDIT_CARD_TERMINAL_IP_ADDRESS"); // TBC Add Terminal IP- "*************" Address
            devicePort = utilities.getParafaitDefaults("CREDIT_CARD_TERMINAL_PORT_NO"); // TBC Add Terminal Port "8443"

            minPreAuth = utilities.getParafaitDefaults("CREDIT_CARD_MIN_PREAUTH"); // TBC It must be greater than 0
            isAuthEnabled = utilities.getParafaitDefaults("ALLOW_CREDIT_CARD_AUTHORIZATION").Equals("Y");

            isCustomerAllowedToDecideEntryMode = utilities.getParafaitDefaults("ALLOW_CUSTOMER_TO_DECIDE_ENTRY_MODE").Equals("Y");

            enableAutoAuthorization = utilities.getParafaitDefaults("ENABLE_AUTO_CREDITCARD_AUTHORIZATION").Equals("Y");

            isCreditCardDonationEnabled = utilities.getParafaitDefaults("ENABLE_CREDIT_CARD_DONATION").Equals("Y");
            isPartialPaymentEnabled = utilities.getParafaitDefaults("ALLOW_PARTIAL_APPROVAL").Equals("Y");

            isTipAllowed = utilities.getParafaitDefaults("SHOW_TIP_AMOUNT_KEYPAD").Equals("Y");

            currencyConversionFactor = Convert.ToInt32(utilities.getParafaitDefaults("CURRENCY_CONVERSION_FACTOR")); // for GBP Set it to 2
            shopperCurrency = utilities.getParafaitDefaults("CURRENCY_CODE");//GBP
            isCloudMode = utilities.getParafaitDefaults("ENABLE_CLOUD_MODE_COMMUNICATION").Equals("Y");
            isSignatureRequired = (isSignatureRequired) ? !isUnattended : false;

            // Printing is handled on POS in Adyen
            PrintReceipt = false;
            if (utilities.getParafaitDefaults("CC_PAYMENT_RECEIPT_PRINT").Equals("N"))//If CC_PAYMENT_RECEIPT_PRINT which comes from POS is set as false then terminal should print, If you need terminal to print the receipt then set CC_PAYMENT_RECEIPT_PRINT value as N
            {
                PrintReceipt = true;
            }
            isPrintReceiptEnabled = PrintReceipt;

            preAuthAdjustApiUrl = GetAdyenConfigurationLookupValue("POS_PRE_AUTH_ADJUST_API_URL"); //"https://pal-test.adyen.com/pal/servlet/Payment/v68/adjustAuthorisation";
            storeName = GetAdyenConfigurationLookupValue("STORE_NAME");
            webApiHostUrl = $"{webApiHostUrl}/{webApiVersion}";

            // TBC get application info from defaults/lookups
            // if all the values are fetched, populate the dictionary

            Semnox.Parafait.Site.Site site = new Site.Site(utilities.ExecutionContext.SiteId);
            SiteDTO siteDTO = site.getSitedTO;
            string externalPlatformVersion = string.Empty;
            string posVersion = string.Empty;
            if (siteDTO != null)
            {
                externalPlatformVersion = siteDTO.Version;
                posVersion = externalPlatformVersion;
                log.Debug($"externalPlatformVersion: {externalPlatformVersion}");
            }

            if (int.TryParse(ConfigurationManager.AppSettings["AdyenTransactionTimeout"], out adyenTransactionTimeout) == false)
            {
                adyenTransactionTimeout = 150000;
            }
            log.Debug($"adyenTransactionTimeout={adyenTransactionTimeout}");


            OperatingSystem os = Environment.OSVersion;
            string osVersion = Convert.ToString(os.Version.Major);
            string operationSystem = os.ToString();
            applicationInfo.Add("applicationInfo.externalPlatform.name", GetAdyenConfigurationLookupValue("POS_SUPPLIER_NAME"));
            applicationInfo.Add("applicationInfo.externalPlatform.version", externalPlatformVersion);
            applicationInfo.Add("applicationInfo.externalPlatform.integrator", GetAdyenConfigurationLookupValue("SERVICE_INTEGRATOR_NAME"));
            applicationInfo.Add("applicationInfo.merchantApplication.name", GetAdyenConfigurationLookupValue("MERCHANT_APPLICATION_NAME"));
            applicationInfo.Add("applicationInfo.merchantApplication.version", posVersion);
            applicationInfo.Add("applicationInfo.merchantDevice.os", GetAdyenConfigurationLookupValue("MERCHANT_OS"));
            applicationInfo.Add("applicationInfo.merchantDevice.osVersion", osVersion);
            log.LogVariableState("applicationInfo", applicationInfo);


            GetUnsupportedCardSchemesForRefRefund();

            // TBC Get the ESD data from lookups
            // perform a null check on them
            // populate the dictionary
            //esdInfo.Add("enhancedSchemeData.orderDate", orderDate);
            //esdInfo.Add("enhancedSchemeData.customerReference", customerGuid);
            //esdInfo.Add("enhancedSchemeData.destinationPostalCode", customerPostalCode);
            log.LogVariableState("enhancedSchemeData", esdInfo);

            posId = utilities.ExecutionContext.POSMachineName;
            log.Debug($"merchantId: {merchantAccount}");
            log.Debug($"isUnattended: {isUnattended}");
            log.Debug($"webApiVersion: {webApiVersion}");
            log.Debug($"webApiHostUrl: {webApiHostUrl}");
            log.Debug($"terminalId: {terminalId}");
            log.Debug($"webApiKey: {webApiKey}");
            log.Debug($"enableAutoAuthorization {enableAutoAuthorization}");
            log.Debug($"posId: {posId}");
            log.Debug($"isCreditCardDonationEnabled: {isCreditCardDonationEnabled}");
            log.Debug($"isPartialPaymentEnabled: {isPartialPaymentEnabled}");

            log.Debug($"currencyConversionFactor: {currencyConversionFactor}");
            log.Debug($"isCloudMode: {isCloudMode}");
            log.Debug($"deviceIpAddress: {deviceIpAddress}");
            log.Debug($"devicePort: {devicePort}");
            log.Debug($"preAuthAdjustApiUrl {preAuthAdjustApiUrl}");
            log.Debug($"storeName {storeName}");
            if (isCloudMode)
            {
                if (string.IsNullOrEmpty(deviceUrl))
                {
                    throw new Exception(utilities.MessageUtils.getMessage("Please enter CREDIT_CARD_DEVICE_URL value in configuration."));
                }
                tapiUrl = deviceUrl; // $"https://terminal-api-test.adyen.com/sync";
            }
            else
            {
                // local mode
                if (string.IsNullOrEmpty(deviceIpAddress))
                {
                    throw new Exception(utilities.MessageUtils.getMessage("Please enter CREDIT_CARD_DEVICE_URL value in configuration."));
                }
                if (string.IsNullOrEmpty(devicePort))
                {
                    throw new Exception(utilities.MessageUtils.getMessage("Please enter CREDIT_CARD_DEVICE_URL value in configuration."));
                }
                tapiUrl = $"https://{deviceIpAddress}:{devicePort}/nexo";
            }
            log.LogVariableState("tapiUrl", tapiUrl);


            if (string.IsNullOrEmpty(terminalId))
            {
                throw new Exception(utilities.MessageUtils.getMessage("Please enter CREDIT_CARD_TERMINAL_ID value in configuration."));
            }
            if (string.IsNullOrEmpty(webApiKey))
            {
                throw new Exception(utilities.MessageUtils.getMessage("Please enter CREDIT_CARD_TOKEN_ID value in configuration for webApiKey."));
            }
            if (string.IsNullOrEmpty(webApiHostUrl))
            {
                throw new Exception(utilities.MessageUtils.getMessage("Please enter CREDIT_CARD_HOST_URL value in configuration."));
            }

            if (string.IsNullOrEmpty(merchantAccount))
            {
                throw new Exception(utilities.MessageUtils.getMessage("Configuration CREDIT_CARD_MERCHANT_ID is not set."));
            }

            log.LogMethodExit(null);

        }

        public override void Initialize()
        {
            log.LogMethodEntry();
            CheckLastTransactionStatus();
            log.LogMethodExit();
        }
        private void GetUnsupportedCardSchemesForRefRefund()
        {
            LookupsContainerDTO lookupsContainerDTO = LookupsContainerList.GetLookupsContainerDTO(utilities.ExecutionContext.SiteId, "ADYEN_UN_SUPPORTED_REF_REFUND", utilities.ExecutionContext);
            if (lookupsContainerDTO != null && lookupsContainerDTO.LookupValuesContainerDTOList != null && lookupsContainerDTO.LookupValuesContainerDTOList.Any())
            {
                unsupportedCCSchemesforRefRefund = new List<string>();
                foreach (LookupValuesContainerDTO lookupValuesContainerDTO in lookupsContainerDTO.LookupValuesContainerDTOList)
                {
                    unsupportedCCSchemesforRefRefund.Add(lookupValuesContainerDTO.LookupValue.ToLower());
                }
            }
        }

        private string GetAdyenConfigurationLookupValue(string lookupValue)
        {
            log.LogMethodEntry(lookupValue);
            string description = string.Empty;
            LookupValuesContainerDTO lookupValuesContainerDTO = LookupsContainerList.GetLookupValuesContainerDTO(utilities.ExecutionContext.SiteId, "ADYEN_PAYMENT_CONFIGURATION", lookupValue);
            if (lookupValuesContainerDTO != null && !string.IsNullOrWhiteSpace(lookupValuesContainerDTO.Description))
            {
                description = lookupValuesContainerDTO.Description;
            }
            else
            {
                log.Error($"Configuration {lookupValue} is not set.");
                throw new Exception(utilities.MessageUtils.getMessage($"Configuration {lookupValue} is not set."));
            }
            log.LogMethodExit(description);
            return description;

        }

        public override TransactionPaymentsDTO MakePayment(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.Info("MakePayment method - Enter");
            VerifyPaymentRequest(transactionPaymentsDTO);
            bool isSubscriptionTokenCreation = false;
            if (transactionPaymentsDTO.SubscriptionAuthorizationMode == SubscriptionAuthorizationMode.I)
            {
                isSubscriptionTokenCreation = true;
            }
            log.Debug($"Subscription token creation is set to={isSubscriptionTokenCreation}");
            bool isManual = false;
            statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(1839, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "Adyen Payment Gateway");
            statusDisplayUi.CancelClicked += StatusDisplayUi_CancelClicked;
            statusDisplayUi.EnableCancelButton(false);
            isManual = false;
            Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
            TransactionType trxType = TransactionType.SALE;
            string paymentId = string.Empty;
            CCTransactionsPGWDTO preAuthCCOrgTransactionsPGWDTO = null;
            decimal authorize;
            decimal tip;
            decimal trxAmount;

            decimal orderAmount = Convert.ToDecimal(transactionPaymentsDTO.Amount);
            log.Info("orderAmount =" + orderAmount);
            try
            {
                if (transactionPaymentsDTO.Amount > 0)
                {
                    preAuthCCOrgTransactionsPGWDTO = GetPreAuthorizationCCTransactionsPGWDTO(transactionPaymentsDTO);
                    if (!isUnattended)
                    {
                        if (preAuthCCOrgTransactionsPGWDTO == null && isCustomerAllowedToDecideEntryMode)
                        {
                            // Manual Key Entry Mode
                            frmEntryMode entryMode = new frmEntryMode();
                            utilities.setLanguage(entryMode);
                            if (entryMode.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                            {
                                if (entryMode.IsManual)
                                {
                                    isManual = entryMode.IsManual;
                                    log.Debug($"isManual={isManual}");
                                }
                            }
                            else
                            {
                                log.Error("Operation cancelled from frmEntryMode.");
                                throw new Exception(utilities.MessageUtils.getMessage("Operation cancelled."));
                            }
                        }
                        if (!isSubscriptionTokenCreation)
                        {
                            if (isAuthEnabled && enableAutoAuthorization)
                            {
                                log.Debug("Creditcard auto authorization is enabled");
                                trxType = TransactionType.AUTHORIZATION;
                            }
                            else
                            {
                                if (isAuthEnabled)
                                {
                                    frmTransactionTypeUI frmTranType = new frmTransactionTypeUI(utilities, (preAuthCCOrgTransactionsPGWDTO == null) ? "TATokenRequest" : "Authorization", transactionPaymentsDTO.Amount, showMessageDelegate);
                                    if (frmTranType.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                                    {
                                        if (frmTranType.TransactionType.Equals("Authorization") || frmTranType.TransactionType.Equals("Sale"))
                                        {
                                            if (frmTranType.TransactionType.Equals("Authorization"))
                                            {
                                                trxType = TransactionType.AUTHORIZATION;
                                            }
                                            else
                                            {
                                                trxType = TransactionType.SALE;
                                            }
                                        }
                                        else if (frmTranType.TransactionType.Equals("TATokenRequest"))
                                        {
                                            trxType = TransactionType.TATokenRequest;
                                            transactionPaymentsDTO.Amount = Convert.ToDouble(minPreAuth);
                                            transactionPaymentsDTO.TipAmount = 0; // TBC
                                        }
                                    }
                                    else
                                    {
                                        log.Error("Operation cancelled from frmTransactionTypeUI.");
                                        throw new Exception(utilities.MessageUtils.getMessage("Operation cancelled."));
                                    }
                                }
                            }
                        }

                    }
                }
                thr.Start();
                string result = null;
                log.Debug($"TrxType={trxType}");
                if (transactionPaymentsDTO != null && transactionPaymentsDTO.Amount > 0)
                {
                    CCRequestPGWDTO ccRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, trxType.ToString());
                    log.LogVariableState("ccRequestPGWDTO", ccRequestPGWDTO);
                    string serviceId = ccRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);

                    AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, trxType == TransactionType.TATokenRequest ? false : isTipAllowed, isManual, isSubscriptionTokenCreation, applicationInfo, adyenTransactionTimeout, storeName);

                    if (trxType == TransactionType.AUTHORIZATION)
                    {
                        statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                        log.Info("Auth transaction initiated");
                        // check if it is subscription


                        // check if it is preauth adjust
                        if (preAuthCCOrgTransactionsPGWDTO != null)
                        {
                            // go for praauth adjust
                            log.Debug("Pre-auth adjust initiated");
                            string preauthPaymentId = commandHandler.GetTransactionId(preAuthCCOrgTransactionsPGWDTO.RecordNo);
                            log.Debug("preauthPaymentId: " + preauthPaymentId);
                            CCTransactionsPGWDTO latestChildCCTransactionsPGWDTO = GetLatestChildCCTransactionPGWDTO(transactionPaymentsDTO.TransactionId, transactionPaymentsDTO.SplitId, preAuthCCOrgTransactionsPGWDTO);

                            if (latestChildCCTransactionsPGWDTO == null)
                            {
                                latestChildCCTransactionsPGWDTO = preAuthCCOrgTransactionsPGWDTO;
                            }
                            decimal latestAuthAdjustedAmount = latestChildCCTransactionsPGWDTO.ResponseID == preAuthCCOrgTransactionsPGWDTO.ResponseID ? 0 : Convert.ToDecimal(latestChildCCTransactionsPGWDTO.Amount);
                            decimal authAdjustuthorizedAmount = Convert.ToDecimal(transactionPaymentsDTO.Amount);
                            log.Info("authAdjustuthorizedAmount + latestAuthAdjustedAmount = " + authAdjustuthorizedAmount + latestAuthAdjustedAmount);
                            log.Info("preauthPaymentId = " + preauthPaymentId);
                            log.Info("latestChildCCTransactionsPGWDTO.ResponseOrigin = " + latestChildCCTransactionsPGWDTO.ResponseOrigin);
                            log.Info("currencyConversionFactor = " + currencyConversionFactor);
                            log.Info("preAuthAdjustApiUrl = " + preAuthAdjustApiUrl);

                            result = commandHandler.DoPreAuthorizationAdjustment(authAdjustuthorizedAmount + latestAuthAdjustedAmount, preauthPaymentId, latestChildCCTransactionsPGWDTO.ResponseOrigin, ccRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, preAuthAdjustApiUrl);
                            log.Debug($"Preauth Adjustment response={JsonConvert.SerializeObject(result)}");

                            if (string.IsNullOrWhiteSpace(result))
                            {
                                log.Error($"MakePayment(): Auth response was null");
                                throw new Exception("Authorization Failed. No response received.");
                            }

                            AdjustAuthResponseDto authAdjustResponseObject = JsonConvert.DeserializeObject<AdjustAuthResponseDto>(result);
                            log.LogVariableState("authAdjustResponseObject", JsonConvert.SerializeObject(authAdjustResponseObject));

                            if (authAdjustResponseObject == null)
                            {
                                log.Error($"MakePayment(): Auth Adjust response deserialization failed");
                                throw new Exception("Authorization Failed.");
                            }

                            if (!authAdjustResponseObject.response.Equals("Authorised"))
                            {
                                log.Error($"MakePayment(): Auth Adjust failed");
                                throw new Exception("Authorization Failed.");
                            }

                            CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                            ccTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(preAuthCCOrgTransactionsPGWDTO.AcctNo);
                            ccTransactionsPGWDTO.AuthCode = authAdjustResponseObject.additionalData.authCode;
                            ccTransactionsPGWDTO.CardType = preAuthCCOrgTransactionsPGWDTO.CardType;
                            ccTransactionsPGWDTO.RefNo = authAdjustResponseObject.additionalData.merchantReference;
                            ccTransactionsPGWDTO.RecordNo = authAdjustResponseObject.pspReference;
                            ccTransactionsPGWDTO.ResponseOrigin = authAdjustResponseObject.additionalData.adjustAuthorisationData;
                            ccTransactionsPGWDTO.TextResponse = authAdjustResponseObject.response;
                            ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.AUTHORIZATION.ToString();
                            ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            ccTransactionsPGWDTO.Authorize = authAdjustuthorizedAmount.ToString();
                            ccTransactionsPGWDTO.Purchase = authAdjustuthorizedAmount.ToString();
                            ccTransactionsPGWDTO.ParentResponseId = preAuthCCOrgTransactionsPGWDTO.ResponseID;
                            ccTransactionsPGWDTO.Amount = Convert.ToDouble(authAdjustuthorizedAmount + latestAuthAdjustedAmount);

                            SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                            CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);

                            try
                            {
                                log.Info("CCTransactionsPGW Save is called");
                                CCTransactionsPGWBLSave(cCTransactionsPGWBL);
                                log.Debug("Sale response saved to cCTransactionsPGW");

                            }
                            catch (Exception e)
                            {
                                log.Error("error saving CCTransactionsPGW");
                                log.Error(e);
                                log.Error("Calling VoidPayment method");
                                bool isSuccess = VoidPayment(ccRequestPGWDTO, transactionPaymentsDTO, ccTransactionsPGWDTO, serviceId);
                                ccTransactionsPGWDTO = null;
                                if (isSuccess)
                                {
                                    throw new Exception(utilities.MessageUtils.getMessage(6180)); //Payment Not complete. Amount has been refunded
                                }
                                else
                                {
                                    throw new Exception(utilities.MessageUtils.getMessage(5500)); //Something went wrong....Transaction Failed
                                }
                            }
                            transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                            transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                            transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                            transactionPaymentsDTO.Amount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                            transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                            transactionPaymentsDTO.CreditCardAuthorization = cCTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;

                        }
                        else
                        {
                            // authorization
                            bool lastTrxCheckrequiredforAuth = false;
                            string lastTrxCheckErrorMsq = string.Empty;
                            string trxErrorMsq = string.Empty;
                            CCTransactionsPGWBL ccTransactionsPGWBL = null;
                            if (isSubscriptionTokenCreation)
                            {
                                // token creation
                                log.Debug("Subscription token creation started");
                                result = commandHandler.DoCreateCardTokenForSubscription(transactionPaymentsDTO.Amount, transactionPaymentsDTO.CustomerCardProfileId, ccRequestPGWDTO.RequestID.ToString());// customer Profile Id -> customer card profile id
                                log.Debug($"subscription payment request with token creation result={result}");
                            }
                            else
                            {
                                // normal Auth
                                log.Debug("Direct Authorization started");

                                try
                                {

                                    result = commandHandler.DoPreAuthorization(orderAmount, ccRequestPGWDTO.RequestID.ToString());
                                    log.Debug($"Direct Auth result={JsonConvert.SerializeObject(result)}");
                                }
                                catch (Exception ex)
                                {
                                    trxErrorMsq = ex.Message;
                                    lastTrxCheckErrorMsq = string.Empty;
                                    lastTrxCheckrequiredforAuth = true;

                                }
                            }
                            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            if (lastTrxCheckrequiredforAuth)
                            {
                                try
                                {
                                    log.Debug("Initiating Last trx check- Direct Auth");
                                    cCTransactionsPGWDTO = PerformLasttrxCheck(ccRequestPGWDTO);

                                    SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);

                                    ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                    //ccTransactionsPGWBL.Save();
                                    try
                                    {
                                        log.Info("CCTransactionsPGW Save is called");
                                        CCTransactionsPGWBLSave(ccTransactionsPGWBL);
                                        log.Debug("Authorization response saved to cCTransactionsPGW");
                                        
                                    }
                                    catch (Exception e)
                                    {
                                        log.Error("error saving CCTransactionsPGW");
                                        log.Error(e);
                                        log.Error("Calling VoidPayment method");
                                        bool isSuccess = VoidPayment(ccRequestPGWDTO, transactionPaymentsDTO, cCTransactionsPGWDTO, serviceId);
                                        cCTransactionsPGWDTO = null;

                                        if (isSuccess)
                                        {
                                            throw new Exception(utilities.MessageUtils.getMessage(6180)); //Payment Not complete. Amount has been refunded
                                        }
                                        else
                                        {
                                            throw new Exception(utilities.MessageUtils.getMessage(5500)); //Something went wrong....Transaction Failed
                                        }
                                    }
                                    if (cCTransactionsPGWDTO.TextResponse.Equals(PAYMENT_FAILURE_STATUS))
                                    {
                                        log.Error($"Last transaction check failed. {cCTransactionsPGWDTO.DSIXReturnCode}");
                                        throw new Exception($"Last transaction check failed. {cCTransactionsPGWDTO.DSIXReturnCode}");
                                    }
                                    if (isCreditCardDonationEnabled && !string.IsNullOrWhiteSpace(cCTransactionsPGWDTO.RefNo))
                                    {
                                        SaveDonationResponse(transactionPaymentsDTO, cCTransactionsPGWDTO.RefNo, Convert.ToString(transactionPaymentsDTO.CCResponseId), isManual);
                                    }
                                }
                                catch (Exception ltxex)
                                {
                                    lastTrxCheckErrorMsq = ltxex.Message;
                                    log.Error(ltxex);
                                    string errorMessage = string.IsNullOrWhiteSpace(lastTrxCheckErrorMsq) ? "" :
                                        $"Error in {ccRequestPGWDTO.TransactionType} trx: {lastTrxCheckErrorMsq}";
                                    errorMessage += string.IsNullOrWhiteSpace(errorMessage) ? "" : " | " + $"Error in Last trx check:{lastTrxCheckErrorMsq}";
                                    throw new Exception(errorMessage);
                                }
                            }
                            else
                            {
                                if (string.IsNullOrWhiteSpace(result))
                                {
                                    log.Error($"MakePayment(): Auth response was null");
                                    throw new Exception("Authorization Failed. No response received.");
                                }
                                PaymentResponseDto responseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
                                log.LogVariableState("responseObject", JsonConvert.SerializeObject(responseObject));
                                if (responseObject == null)
                                {
                                    log.Error($"MakePayment(): Auth response deserialization failed");
                                    throw new Exception("Authorization Failed. Response deserialization failed.");
                                }

                                CheckOtherPaymentErrors(commandHandler, responseObject);

                                Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.PaymentResponse.Response.AdditionalResponse);

                                if (responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_PARTIAL_STATUS))
                                {
                                    log.Debug($"MakePayment(): Auth Succeded for Partial Amount.");
                                }

                                decimal authorizedAmount = 0;
                                decimal tipAmount = 0;
                                cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                                if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult != null)
                                {
                                    if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp != null)
                                    {
                                        authorizedAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                        tipAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.TipAmount);
                                    }

                                    cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                                    cCTransactionsPGWDTO.AuthCode = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                                    cCTransactionsPGWDTO.CardType = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                                    cCTransactionsPGWDTO.CaptureStatus = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode != null ? responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0] : null;
                                }
                                cCTransactionsPGWDTO.RefNo = responseObject.SaleToPOIResponse.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                                cCTransactionsPGWDTO.RecordNo = responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TransactionID;
                                cCTransactionsPGWDTO.ProcessData = getProcessData(responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TimeStamp);
                                cCTransactionsPGWDTO.ResponseOrigin = additionalResponseParams.ContainsKey("adjustAuthorisationData") ? Convert.ToString(additionalResponseParams["adjustAuthorisationData"]) : "";
                                if (isSubscriptionTokenCreation)
                                {
                                    cCTransactionsPGWDTO.TokenID = additionalResponseParams.ContainsKey("recurring.recurringDetailReference") ? Convert.ToString(additionalResponseParams["recurring.recurringDetailReference"]) : "";
                                    cCTransactionsPGWDTO.CustomerCardProfileId = (additionalResponseParams.ContainsKey("recurring.shopperReference") && !string.IsNullOrWhiteSpace(additionalResponseParams["recurring.shopperReference"])) ? Convert.ToString(additionalResponseParams["recurring.shopperReference"]) : transactionPaymentsDTO.CustomerCardProfileId;

                                }

                                if (additionalResponseParams.Any())
                                {
                                    string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                                    acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                                    acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";

                                    cCTransactionsPGWDTO.AcqRefData = acqrefData;
                                }

                                cCTransactionsPGWDTO.TextResponse = responseObject.SaleToPOIResponse.PaymentResponse.Response.Result;
                                cCTransactionsPGWDTO.TranCode = tipAmount > 0 ? PaymentGatewayTransactionType.SALE.ToString() : PaymentGatewayTransactionType.AUTHORIZATION.ToString();
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();
                                cCTransactionsPGWDTO.Purchase = transactionPaymentsDTO.Amount.ToString();
                                cCTransactionsPGWDTO.Amount = Convert.ToDouble(authorizedAmount);
                                cCTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                                SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);
                                if (responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_FAILURE_STATUS))
                                {
                                    string errorMessage = string.IsNullOrWhiteSpace(responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition) ? "" : $"ErrorCondition: {responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition}";
                                    if (additionalResponseParams != null && additionalResponseParams.ContainsKey("message"))
                                    {
                                        errorMessage += $" Message: {additionalResponseParams["message"]}";
                                    }
                                    if (additionalResponseParams != null && additionalResponseParams.ContainsKey("refusalReason"))
                                    {
                                        errorMessage += $" Refusal Reason: {additionalResponseParams["refusalReason"]}";
                                    }
                                    cCTransactionsPGWDTO.DSIXReturnCode = errorMessage;
                                    CCTransactionsPGWBL cCTrxPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                    cCTrxPGWBL.Save();
                                    log.Error($"MakePayment(): Auth failed due to {errorMessage}");
                                    throw new Exception($"Authorization Failed. {errorMessage}");
                                }

                                ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                try
                                {
                                    log.Info("CCTransactionsPGW Save is called");
                                    CCTransactionsPGWBLSave(ccTransactionsPGWBL);
                                    log.Debug("Authorization response saved to cCTransactionsPGW");

                                }
                                catch (Exception e)
                                {
                                    log.Error("error saving CCTransactionsPGW");
                                    log.Error(e);
                                    log.Error("Calling VoidPayment method");
                                    bool isSuccess = VoidPayment(ccRequestPGWDTO, transactionPaymentsDTO, cCTransactionsPGWDTO, serviceId);
                                    cCTransactionsPGWDTO = null;
                                    if (isSuccess)
                                    {
                                        throw new Exception(utilities.MessageUtils.getMessage(6180)); //Payment Not complete. Amount has been refunded
                                    }
                                    else
                                    {
                                        throw new Exception(utilities.MessageUtils.getMessage(5500)); //Something went wrong....Transaction Failed
                                    }
                                }


                                if (isSubscriptionTokenCreation && responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult != null)
                                {
                                    transactionPaymentsDTO.CreditCardExpiry = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.SensitiveCardData.ExpiryDate;
                                }
                                if (isCreditCardDonationEnabled)
                                {

                                    // check if donation params are present in the original payment response
                                    if (additionalResponseParams.ContainsKey("adyen_giving_service_id") && !string.IsNullOrWhiteSpace(additionalResponseParams["adyen_giving_service_id"]))
                                    {
                                        SaveDonationResponse(transactionPaymentsDTO, additionalResponseParams["adyen_giving_service_id"], Convert.ToString(transactionPaymentsDTO.CCResponseId), isManual);
                                    }
                                }
                                
                            }
                            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                            transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                            authorize = Convert.ToDecimal(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                            tip = Convert.ToDecimal(ccTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                            trxAmount = authorize - tip;
                            transactionPaymentsDTO.Amount = Convert.ToDouble(trxAmount);
                            transactionPaymentsDTO.TipAmount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                            transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                        }

                    }
                    else if (trxType == TransactionType.SALE)
                    {
                        statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                        log.Info("SALE initiated");

                        {
                            // DIRECT SALE


                            if (isSubscriptionTokenCreation)
                            {
                                // token creation
                                result = commandHandler.DoCreateCardTokenForSubscription(transactionPaymentsDTO.Amount, transactionPaymentsDTO.CustomerCardProfileId, ccRequestPGWDTO.RequestID.ToString());
                                log.Debug($"subscription payment request with token creation result={JsonConvert.SerializeObject(result)}");
                            }
                            else
                            {

                                try
                                {
                                    result = commandHandler.DoAuthorization(orderAmount, ccRequestPGWDTO.RequestID.ToString());
                                    log.Debug($"SALE  result={JsonConvert.SerializeObject(result)}");
                                }
                                catch (Exception ex)
                                {

                                    string trxErrorMsq = ex.Message;
                                    string lastTrxCheckErrorMsq = string.Empty;
                                    try
                                    {
                                        CCTransactionsPGWDTO lasttrxCCTransactionsPGWDTO = PerformLasttrxCheck(ccRequestPGWDTO);
                                        CCTransactionsPGWBL cctrxPGWBL = new CCTransactionsPGWBL(lasttrxCCTransactionsPGWDTO);
                                        try
                                        {
                                            log.Info("CCTransactionsPGW Save is called");
                                            CCTransactionsPGWBLSave(cctrxPGWBL);
                                            log.Debug("Authorization response saved to cCTransactionsPGW");
                                            
                                        }
                                        catch (Exception e)
                                        {
                                            log.Error("error saving CCTransactionsPGW");
                                            log.Error(e);
                                            log.Error("Calling VoidPayment method");
                                            bool isSuccess = VoidPayment(ccRequestPGWDTO, transactionPaymentsDTO, lasttrxCCTransactionsPGWDTO, serviceId);
                                            lasttrxCCTransactionsPGWDTO = null;
                                            if (isSuccess)
                                            {
                                                throw new Exception(utilities.MessageUtils.getMessage(6180)); //Payment Not complete. Amount has been refunded
                                            }
                                            else
                                            {
                                                throw new Exception(utilities.MessageUtils.getMessage(5500)); //Something went wrong....Transaction Failed
                                            }
                                        }
                                        SendPrintReceiptRequest(transactionPaymentsDTO, lasttrxCCTransactionsPGWDTO);
                                        if (lasttrxCCTransactionsPGWDTO.TextResponse.Equals(PAYMENT_FAILURE_STATUS))
                                        {
                                            log.Error($"Last transaction check failed- sale . {lasttrxCCTransactionsPGWDTO.DSIXReturnCode}");
                                            throw new Exception($"Last transaction check failed. {lasttrxCCTransactionsPGWDTO.DSIXReturnCode}");
                                        }

                                        transactionPaymentsDTO.CCResponseId = lasttrxCCTransactionsPGWDTO.ResponseID;
                                        transactionPaymentsDTO.CreditCardName = lasttrxCCTransactionsPGWDTO.CardType;
                                        transactionPaymentsDTO.Reference = lasttrxCCTransactionsPGWDTO.RefNo;
                                        authorize = Convert.ToDecimal(lasttrxCCTransactionsPGWDTO.Authorize);
                                        tip = Convert.ToDecimal(lasttrxCCTransactionsPGWDTO.TipAmount);
                                        trxAmount = authorize - tip;
                                        transactionPaymentsDTO.Amount = Convert.ToDouble(trxAmount);
                                        transactionPaymentsDTO.TipAmount = Convert.ToDouble(lasttrxCCTransactionsPGWDTO.TipAmount);
                                        transactionPaymentsDTO.CreditCardNumber = lasttrxCCTransactionsPGWDTO.AcctNo;

                                        if (isCreditCardDonationEnabled && !string.IsNullOrWhiteSpace(lasttrxCCTransactionsPGWDTO.RefNo))
                                        {
                                            SaveDonationResponse(transactionPaymentsDTO, lasttrxCCTransactionsPGWDTO.RefNo, Convert.ToString(transactionPaymentsDTO.CCResponseId), isManual);
                                        }

                                        log.LogMethodExit(transactionPaymentsDTO);
                                        return transactionPaymentsDTO;

                                    }
                                    catch (Exception ltxex)
                                    {
                                        lastTrxCheckErrorMsq = ltxex.Message;
                                        log.Error(ex);
                                        string errorMessage = string.IsNullOrWhiteSpace(trxErrorMsq) ? "" :
                                            $"Error in {ccRequestPGWDTO.TransactionType} trx: {trxErrorMsq}";
                                        errorMessage += string.IsNullOrWhiteSpace(errorMessage) ? "" : " | " + $"Error in Last trx check:{lastTrxCheckErrorMsq}";
                                        throw new Exception(errorMessage);
                                    }

                                }
                            }

                            if (string.IsNullOrWhiteSpace(result))
                            {
                                log.Error($"MakePayment(): Payment response was null");
                                throw new Exception("Payment Failed. No response received.");
                            }
                            PaymentResponseDto responseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
                            log.LogVariableState("responseObject", JsonConvert.SerializeObject(responseObject));
                            if (responseObject == null)
                            {
                                log.Error($"MakePayment(): Payment response deserialization failed");
                                throw new Exception("Payment Failed. Response deserialization failed.");
                            }

                            CheckOtherPaymentErrors(commandHandler, responseObject);

                            Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.PaymentResponse.Response.AdditionalResponse);

                            if (responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_PARTIAL_STATUS))
                            {
                                log.Debug($"MakePayment(): sale Payment Succeded for Partial Amount.");
                            }

                            decimal authorizedAmount = 0;
                            decimal tipAmount = 0;
                            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                            if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult != null)
                            {
                                if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp != null)
                                {
                                    authorizedAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                    tipAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.TipAmount);
                                }

                                cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                                cCTransactionsPGWDTO.AuthCode = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                                cCTransactionsPGWDTO.CardType = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                                cCTransactionsPGWDTO.CaptureStatus =
                                    responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode != null ? responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0] : null;
                            }

                            cCTransactionsPGWDTO.RefNo = responseObject.SaleToPOIResponse.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                            cCTransactionsPGWDTO.RecordNo = responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TransactionID;
                            cCTransactionsPGWDTO.ProcessData = getProcessData(responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TimeStamp);

                            if (isSubscriptionTokenCreation)
                            {
                                cCTransactionsPGWDTO.TokenID = additionalResponseParams.ContainsKey("recurring.recurringDetailReference") ? Convert.ToString(additionalResponseParams["recurring.recurringDetailReference"]) : null;
                                cCTransactionsPGWDTO.CustomerCardProfileId = (additionalResponseParams.ContainsKey("recurring.shopperReference") && !string.IsNullOrWhiteSpace(additionalResponseParams["recurring.shopperReference"])) ? Convert.ToString(additionalResponseParams["recurring.shopperReference"]) : transactionPaymentsDTO.CustomerCardProfileId;
                            }

                            if (additionalResponseParams.Any())
                            {
                                string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                                acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                                acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";

                                cCTransactionsPGWDTO.AcqRefData = acqrefData;
                            }

                            cCTransactionsPGWDTO.TextResponse = responseObject.SaleToPOIResponse.PaymentResponse.Response.Result;
                            cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            cCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();
                            cCTransactionsPGWDTO.Purchase = transactionPaymentsDTO.Amount.ToString();
                            cCTransactionsPGWDTO.Amount = Convert.ToDouble(authorizedAmount);
                            cCTransactionsPGWDTO.TipAmount = tipAmount.ToString();

                            SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);

                            if (responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_FAILURE_STATUS))
                            {
                                //log.Error($"MakePayment(): Payment failed due to {responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition} ");
                                //throw new Exception($"Payment Failed. Error: {responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition}");
                                string errorMessage = string.IsNullOrWhiteSpace(responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition) ? "" : $"ErrorCondition: {responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition}";
                                if (additionalResponseParams != null && additionalResponseParams.ContainsKey("message"))
                                {
                                    errorMessage += $" Message: {additionalResponseParams["message"]}";
                                }
                                if (additionalResponseParams != null && additionalResponseParams.ContainsKey("refusalReason"))
                                {
                                    errorMessage += $" Refusal Reason: {additionalResponseParams["refusalReason"]}";
                                }
                                log.Error($"MakePayment(): Auth failed due to {errorMessage}");

                                cCTransactionsPGWDTO.DSIXReturnCode = errorMessage;
                                CCTransactionsPGWBL ccTrxPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                ccTrxPGWBL.Save();
                                throw new Exception($"Authorization Failed. {errorMessage}");
                            }

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);

                            try
                            {
                                log.Info("CCTransactionsPGW Save is called");
                                CCTransactionsPGWBLSave(ccTransactionsPGWBL);
                                log.Debug("Sale response saved to cCTransactionsPGW");

                            }
                            catch (Exception e)
                            {
                                log.Error("error saving CCTransactionsPGW");
                                log.Error(e);
                                log.Error("Calling VoidPayment method");
                                bool isSuccess = VoidPayment(ccRequestPGWDTO, transactionPaymentsDTO, cCTransactionsPGWDTO, serviceId);
                                cCTransactionsPGWDTO = null;
                                if (isSuccess)
                                {
                                    throw new Exception(utilities.MessageUtils.getMessage(6180)); //Payment Not complete. Amount has been refunded
                                }
                                else
                                {
                                    throw new Exception(utilities.MessageUtils.getMessage(5500)); //Something went wrong....Transaction Failed
                                }
                            }

                            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                            transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                            authorize = Convert.ToDecimal(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                            tip = Convert.ToDecimal(ccTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                            trxAmount = authorize - tip;
                            transactionPaymentsDTO.Amount = Convert.ToDouble(trxAmount);
                            transactionPaymentsDTO.TipAmount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                            transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                            if (isSubscriptionTokenCreation)
                            {
                                transactionPaymentsDTO.CreditCardExpiry = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.SensitiveCardData.ExpiryDate;
                            }

                            // check for donation made
                            if (isCreditCardDonationEnabled)
                            {
                                // check if donation params are present in the original payment response
                                if (additionalResponseParams.ContainsKey("adyen_giving_service_id") && !string.IsNullOrWhiteSpace(additionalResponseParams["adyen_giving_service_id"]))
                                {
                                    SaveDonationResponse(transactionPaymentsDTO, additionalResponseParams["adyen_giving_service_id"], Convert.ToString(transactionPaymentsDTO.CCResponseId), isManual);
                                }
                            }

                        }
                    }
                    else if (trxType == TransactionType.TATokenRequest)
                    {
                        // PreAuth
                        statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                        orderAmount = Convert.ToDecimal(transactionPaymentsDTO.Amount);
                        if (orderAmount < 1 || Convert.ToDecimal(minPreAuth) < 1)
                        {
                            log.Error("Pre-Auth amount value should be minimum 1");
                            throw new Exception("Pre-Auth amount value should be minimum 1");
                        }

                        if (isSubscriptionTokenCreation)
                        {
                            // token creation
                            result = commandHandler.DoCreateCardTokenForSubscription(transactionPaymentsDTO.Amount, transactionPaymentsDTO.CustomerCardProfileId, ccRequestPGWDTO.RequestID.ToString());
                            log.Debug($"subscription payment request with token creation result={JsonConvert.SerializeObject(result)}");
                        }
                        else
                        {
                            result = commandHandler.DoPreAuthorization(orderAmount, ccRequestPGWDTO.RequestID.ToString());
                            log.Debug($"preauth result={JsonConvert.SerializeObject(result)}");
                        }

                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"MakePayment(): Pre-Auth response was null");
                            throw new Exception("PreAuthorization Failed. No response received.");
                        }
                        PaymentResponseDto responseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
                        log.LogVariableState("responseObject", responseObject);
                        if (responseObject == null)
                        {
                            log.Error($"MakePayment(): Pre-Auth response deserialization failed");
                            throw new Exception("PreAuthorization Failed. Response deserialization failed.");
                        }

                        CheckOtherPaymentErrors(commandHandler, responseObject);

                        Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.PaymentResponse.Response.AdditionalResponse);



                        if (responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_PARTIAL_STATUS))
                        {
                            log.Debug($"MakePayment(): Pre-Auth Succeded for Partial Amount.");
                        }

                        // preauth succeded
                        decimal authorizedAmount = 0;
                        decimal tipAmount = 0;
                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                        if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult != null)
                        {
                            if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp != null)
                            {
                                authorizedAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                tipAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.TipAmount);
                            }
                            cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                            cCTransactionsPGWDTO.AuthCode = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                            cCTransactionsPGWDTO.CardType = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                            cCTransactionsPGWDTO.CaptureStatus = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode != null ? responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0] : null;
                        }

                        cCTransactionsPGWDTO.RefNo = responseObject.SaleToPOIResponse.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                        cCTransactionsPGWDTO.RecordNo = responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TransactionID;
                        cCTransactionsPGWDTO.ProcessData = getProcessData(responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TimeStamp);
                        cCTransactionsPGWDTO.ResponseOrigin = additionalResponseParams.ContainsKey("adjustAuthorisationData") ? Convert.ToString(additionalResponseParams["adjustAuthorisationData"]) : null;
                        if (isSubscriptionTokenCreation)
                        {
                            cCTransactionsPGWDTO.TokenID = additionalResponseParams.ContainsKey("recurring.recurringDetailReference") ? Convert.ToString(additionalResponseParams["recurring.recurringDetailReference"]) : null;
                        }

                        if (additionalResponseParams.Any())
                        {
                            string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                            acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                            acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";

                            cCTransactionsPGWDTO.AcqRefData = acqrefData;
                        }

                        cCTransactionsPGWDTO.TextResponse = responseObject.SaleToPOIResponse.PaymentResponse.Response.Result;
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.TATokenRequest.ToString();
                        cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        cCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();
                        cCTransactionsPGWDTO.Purchase = transactionPaymentsDTO.Amount.ToString();
                        cCTransactionsPGWDTO.Amount = Convert.ToDouble(authorizedAmount);
                        cCTransactionsPGWDTO.TipAmount = tipAmount.ToString();


                        SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);
                        if (responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_FAILURE_STATUS))
                        {

                            string errorMessage = string.IsNullOrWhiteSpace(responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition) ? "" : $"ErrorCondition: {responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition}";
                            if (additionalResponseParams != null && additionalResponseParams.ContainsKey("message"))
                            {
                                errorMessage += $" Message: {additionalResponseParams["message"]}";
                            }
                            if (additionalResponseParams != null && additionalResponseParams.ContainsKey("refusalReason"))
                            {
                                errorMessage += $" Refusal Reason: {additionalResponseParams["refusalReason"]}";
                            }
                            log.Error($"MakePayment(): Pre-Auth Auth failed due to {errorMessage}");
                            cCTransactionsPGWDTO.DSIXReturnCode = errorMessage;
                            CCTransactionsPGWBL cctrxpgwBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                            cctrxpgwBL.Save();
                            throw new Exception($"Pre-Authorization Failed. {errorMessage}");
                        }
                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        try
                        {
                            log.Info("CCTransactionsPGW Save is called");
                            CCTransactionsPGWBLSave(ccTransactionsPGWBL);
                            log.Debug("Sale response saved to cCTransactionsPGW");

                        }
                        catch (Exception e)
                        {
                            log.Error("error saving CCTransactionsPGW");
                            log.Error(e);
                            log.Error("Calling VoidPayment method");
                            bool isSuccess = VoidPayment(ccRequestPGWDTO, transactionPaymentsDTO, cCTransactionsPGWDTO, serviceId);
                            cCTransactionsPGWDTO = null;
                            if (isSuccess)
                            {
                                throw new Exception(utilities.MessageUtils.getMessage(6180)); //Payment Not complete. Amount has been refunded
                            }
                            else
                            {
                                throw new Exception(utilities.MessageUtils.getMessage(5500)); //Something went wrong....Transaction Failed
                            }
                        }

                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        transactionPaymentsDTO.Amount = 0;
                        transactionPaymentsDTO.TipAmount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                        transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;

                        // check for donation made
                        if (isCreditCardDonationEnabled)
                        {
                            // check if donation params are present in the original payment response
                            if (additionalResponseParams.ContainsKey("adyen_giving_service_id") && !string.IsNullOrWhiteSpace(additionalResponseParams["adyen_giving_service_id"]))
                            {
                                SaveDonationResponse(transactionPaymentsDTO, additionalResponseParams["adyen_giving_service_id"], Convert.ToString(transactionPaymentsDTO.CCResponseId), isManual);
                            }
                        }

                    }
                    else
                    {
                        log.Error("Invalid Transaction type");
                        throw new Exception("Invalid Transaction type");
                    }
                }
                else if (transactionPaymentsDTO != null && transactionPaymentsDTO.Amount < 0)
                {
                    // independant refund
                    statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                    log.Debug("Independant refund started");
                    trxType = TransactionType.REFUND;
                    CCRequestPGWDTO ccRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, trxType.ToString());
                    log.LogVariableState("ccRequestPGWDTO", ccRequestPGWDTO);
                    string serviceId = ccRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
                    log.Debug($"serviceId = {serviceId}");
                    decimal amount = Convert.ToDecimal(-transactionPaymentsDTO.Amount);
                    AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, isTipAllowed, isManual, isSubscriptionTokenCreation, applicationInfo, adyenTransactionTimeout, storeName);
                    result = commandHandler.DoIndependantRefund(amount, ccRequestPGWDTO.RequestID.ToString());

                    log.Debug($"independant refund result={result}");
                    if (string.IsNullOrWhiteSpace(result))
                    {
                        log.Error($"MakePayment(): Independant refund response was null");
                        throw new Exception("Refund Failed. No response received.");
                    }

                    PaymentResponseDto responseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
                    log.LogVariableState("responseObject", responseObject);
                    if (responseObject == null)
                    {
                        log.Error($"MakePayment(): Independant refund response deserialization failed");
                        throw new Exception("Independant refund Failed. Response deserialization failed.");
                    }

                    CheckOtherPaymentErrors(commandHandler, responseObject);

                    Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.PaymentResponse.Response.AdditionalResponse);

                    decimal authorizedAmount = 0;
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                    if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult != null)
                    {
                        if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp != null)
                        {
                            authorizedAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                        }


                        cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                        cCTransactionsPGWDTO.AuthCode = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                        cCTransactionsPGWDTO.CardType = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                        cCTransactionsPGWDTO.CaptureStatus =
                            responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode != null
                            ? responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0] : null;
                    }

                    cCTransactionsPGWDTO.RefNo = responseObject.SaleToPOIResponse.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                    cCTransactionsPGWDTO.RecordNo = responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TransactionID;
                    cCTransactionsPGWDTO.ProcessData = getProcessData(responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TimeStamp);

                    if (additionalResponseParams.Any())
                    {
                        string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                        acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                        acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";
                        acqrefData += additionalResponseParams.ContainsKey("pspReference") ? $" | PSP Reference: {Convert.ToString(additionalResponseParams["pspReference"])}" : "";

                        cCTransactionsPGWDTO.AcqRefData = acqrefData;
                    }

                    cCTransactionsPGWDTO.TextResponse = additionalResponseParams.ContainsKey("acquirerResponseCode") ? $"{Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : responseObject.SaleToPOIResponse.PaymentResponse.Response.Result;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();
                    //cCTransactionsPGWDTO.TipAmount = tipAmount.ToString();

                    SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);
                    if (!responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_SUCCESS_STATUS))
                    {
                        string errorMessage = string.IsNullOrWhiteSpace(responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition) ? "" : $"ErrorCondition: {responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition}";
                        if (additionalResponseParams != null && additionalResponseParams.ContainsKey("message"))
                        {
                            errorMessage += $" Message: {additionalResponseParams["message"]}";
                        }
                        if (additionalResponseParams != null && additionalResponseParams.ContainsKey("refusalReason"))
                        {
                            errorMessage += $" Refusal Reason: {additionalResponseParams["refusalReason"]}";
                        }
                        log.Error($"MakePayment(): Independant refund  failed due to {errorMessage}");
                        cCTransactionsPGWDTO.DSIXReturnCode = errorMessage;
                        CCTransactionsPGWBL cctrxpgwBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        cctrxpgwBL.Save();
                        throw new Exception($"Independant refund Failed. {errorMessage}");
                    }
                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                    ccTransactionsPGWBL.Save();
                    transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                    transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                    transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                    transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize) * -1;
                    transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                    transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;

                }
                else
                {
                    log.Fatal("Exception Incorrect object passed");
                    throw new Exception("Exception in processing Payment ");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                }
            }

            log.LogMethodExit(transactionPaymentsDTO);
            log.Info("MakePayment method - Exit");
            return transactionPaymentsDTO;

        }

        private string GetTrxType(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string trxType = "Authorization";
            CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
            List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRANSACTION_ID, transactionPaymentsDTO.TransactionId.ToString()));
            if (transactionPaymentsDTO.SplitId != -1)
            {
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SPLIT_ID, transactionPaymentsDTO.SplitId.ToString()));
            }

            List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
            if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
            {
                CCTransactionsPGWDTO firstCCTransactionsPGWDTO = cCTransactionsPGWDTOList.OrderBy(x => x.ResponseID).FirstOrDefault();
                if (firstCCTransactionsPGWDTO.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString())
                {
                    trxType = "TATokenRequest";
                }
            }
            log.LogMethodExit(trxType);
            return trxType;
        }
        private void StatusDisplayUi_CancelClicked(object sender, EventArgs e)
        {
            log.LogMethodEntry();
            IsCancelled = true;
            if (statusDisplayUi != null)
            {
                statusDisplayUi.EnableCancelButton(false);
                statusDisplayUi.DisplayText("Cancelling...");
                Thread.Sleep(1000);
            }
        }

        private void CheckOtherPaymentErrors(AdyenPosCommandhandler commandHandler, PaymentResponseDto responseObject)
        {
            log.LogMethodEntry(commandHandler, responseObject);
            try
            {
                if (responseObject.SaleToPOIRequest != null)
                {
                    // some error came
                    string errorMsg = "Failed to communicate with the terminal";
                    Dictionary<string, string> errorDetails = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIRequest.EventNotification.EventDetails);
                    if (errorDetails != null && errorDetails.ContainsKey("message"))
                    {
                        errorMsg = $"{responseObject.SaleToPOIRequest.EventNotification.EventToNotify} | {errorDetails["message"]}";
                        log.Error(errorMsg);
                        throw new Exception(errorMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit();
        }

        private void CheckOtherRefundErrors(AdyenPosCommandhandler commandHandler, RefundResponseDto responseObject)
        {
            log.LogMethodEntry(commandHandler, responseObject);
            try
            {
                if (responseObject.SaleToPOIRequest != null)
                {
                    // some error came
                    string errorMsg = "Failed to communicate with the terminal";
                    Dictionary<string, string> errorDetails = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIRequest.EventNotification.EventDetails);
                    if (errorDetails != null && errorDetails.ContainsKey("message"))
                    {
                        errorMsg = $"{responseObject.SaleToPOIRequest.EventNotification.EventToNotify} | {errorDetails["message"]}";
                        log.Error(errorMsg);
                        throw new Exception(errorMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit();
        }
        private Dictionary<string, string> BuildEsdDictionary(string shopperReference, string orderDate, string postalCode)
        {
            log.LogMethodEntry(shopperReference, orderDate, postalCode);
            Dictionary<string, string> esdInfo = new Dictionary<string, string>();
            try
            {
                esdInfo.Add("enhancedSchemeData.orderDate", orderDate);
                esdInfo.Add("enhancedSchemeData.customerReference", shopperReference);
                esdInfo.Add("enhancedSchemeData.destinationPostalCode", postalCode);
            }
            catch (Exception ex)
            {

                log.Error(ex);
            }

            return esdInfo;
        }

        public override TransactionPaymentsDTO MakePaymentForRecurringBilling(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string result;
            bool isManual = false;
            bool isSubscriptionTokenCreation = false;
            decimal amount = Convert.ToDecimal(transactionPaymentsDTO.Amount);

            try
            {
                PrintReceipt = false;
                CCTransactionsPGWDTO ccTransactionsPGWDTO = null;
                if (transactionPaymentsDTO != null)
                {
                    CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_PAYMENT);
                    string serviceId = cCRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);

                    if (transactionPaymentsDTO.SubscriptionAuthorizationMode == SubscriptionAuthorizationMode.P)
                    {
                        // later subscription payment
                        AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, isTipAllowed, isManual, isSubscriptionTokenCreation, applicationInfo, adyenTransactionTimeout, storeName);

                        log.Debug("Later Subscription Payment started");

                        string paymentToken = transactionPaymentsDTO.CreditCardNumber;
                        string shopperReference = Convert.ToString(transactionPaymentsDTO.CustomerCardProfileId);
                        string orderDate = transactionPaymentsDTO.Attribute2;
                        string postalCode = transactionPaymentsDTO.Attribute1;// TBC with Guru sir
                        result = commandHandler.DoLaterSubscriptionPayment(amount, shopperReference, paymentToken, cCRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, BuildEsdDictionary(shopperReference, orderDate, postalCode));
                        log.Debug($"later subscription payment result={result}");
                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"MakePayment(): Subscription recurring payment response was null");
                            throw new Exception("Subscription recurring payment Failed. No response received.");
                        }

                        SubscriptionPaymentResponse responseObject = JsonConvert.DeserializeObject<SubscriptionPaymentResponse>(result);
                        log.LogVariableState("responseObject", responseObject);
                        if (responseObject == null)
                        {
                            log.Error($"MakePayment(): Subscription recurring payment response deserialization failed");
                            throw new Exception("Subscription recurring payment Failed. Response deserialization failed.");
                        }

                        if (!responseObject.resultCode.Equals("Authorised"))
                        {
                            log.Error($"MakePayment(): Subscription recurring payment");
                            throw new Exception($"Subscription recurring payment Failed."); // TBC check additionalData obj for error info
                        }

                        decimal authorizedAmount = Convert.ToDecimal(commandHandler.GetAmountInMajorUnit(responseObject.amount.value, currencyConversionFactor));

                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO.InvoiceNo = responseObject.additionalData.merchantReference;
                        cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(responseObject.additionalData.cardSummary);
                        cCTransactionsPGWDTO.AuthCode = responseObject.additionalData.authCode;
                        cCTransactionsPGWDTO.CardType = responseObject.paymentMethod.brand;
                        cCTransactionsPGWDTO.RefNo = responseObject.additionalData.merchantReference;
                        cCTransactionsPGWDTO.RecordNo = responseObject.pspReference;
                        cCTransactionsPGWDTO.TokenID = paymentToken;

                        cCTransactionsPGWDTO.TextResponse = responseObject.resultCode;
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                        cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        cCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();

                        SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        try
                        {
                            log.Info("CCTransactionsPGW Save is called");
                            CCTransactionsPGWBLSave(ccTransactionsPGWBL);
                            log.Debug("Authorization response saved to cCTransactionsPGW");

                        }
                        catch (Exception e)
                        {
                            log.Error("error saving CCTransactionsPGW");
                            log.Error(e);
                            log.Error("Calling VoidPayment method");
                            bool isSuccess = VoidPayment(cCRequestPGWDTO, transactionPaymentsDTO, cCTransactionsPGWDTO, serviceId);
                            cCTransactionsPGWDTO = null;
                            if (isSuccess)
                            {
                                throw new Exception(utilities.MessageUtils.getMessage(6180)); //Payment Not complete. Amount has been refunded
                            }
                            else
                            {
                                throw new Exception(utilities.MessageUtils.getMessage(5500)); //Something went wrong....Transaction Failed
                            }
                        }
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                        transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                        transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                    }
                    else
                    {
                        throw new Exception("Invalid Subscription Type");
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error("Error occured while charging the customer card", ex);
                log.LogMethodExit(null, "throwing Exception");
                throw;
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }
        private void SaveDonationResponse(TransactionPaymentsDTO transactionPaymentsDTO, string adyen_giving_service_id, string ccOrginResponseId, bool isManual)
        {
            try
            {
                Thread.Sleep(10000);
                log.LogMethodEntry(transactionPaymentsDTO, adyen_giving_service_id, ccOrginResponseId);
                if (!string.IsNullOrWhiteSpace(adyen_giving_service_id))
                {
                    DateTime timout = DateTime.Now.AddSeconds(30);
                    statusDisplayUi.DisplayText("Donation is in progress...");
                    statusDisplayUi.EnableCancelButton(true);
                    int attempt = 0;
                    while (DateTime.Now < timout)
                    {
                        if (IsCancelled)
                        {
                            log.Error("User Cancelled");
                            throw new Exception("Donation Cancelled");
                        }
                        attempt++;
                        string serviceId = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 10);
                        log.Debug($"serviceid = {serviceId}");
                        AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, isTipAllowed, isManual, false, applicationInfo, adyenTransactionTimeout, storeName);
                        //statusDisplayUi.DisplayText($"Donation is in progress... Attempt: {attempt} Timeout:{timout.ToString()} Current Time :{DateTime.Now.ToString()}" );
                        //log.Debug($"Donation is in progress... Attempt: {attempt} Timeout:{timout.ToString()} Current Time :{DateTime.Now.ToString()}");
                        Thread.Sleep(5000);
                        string result = commandHandler.DoCheckLastTransactionStatus(donationSaleId, adyen_giving_service_id, donationMessageCategory);

                        log.Debug($"donation response={result}");

                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"MakePayment(): Donation response was null");
                            throw new Exception("Donation Failed. No response received.");
                        }

                        PaymentResponseDto responseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
                        log.Debug($"donationResponseObject={responseObject.ToString()}");

                        if (responseObject == null)
                        {
                            log.Error("Donation response was null");
                            throw new Exception("Failed to receive Donation response");
                        }

                        CheckOtherPaymentErrors(commandHandler, responseObject);

                        if (responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals("Success"))
                        {
                            log.Debug("Donation Trx succeeded");

                            Repeatedresponsemessagebody response = responseObject.SaleToPOIResponse.TransactionStatusResponse.RepeatedMessageResponse.RepeatedResponseMessageBody;

                            Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(response.PaymentResponse.Response.AdditionalResponse);

                            // save donation response
                            decimal authorizedAmount = 0;
                            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            if (response.PaymentResponse.PaymentResult != null)
                            {
                                if (response.PaymentResponse.PaymentResult.AmountsResp != null)
                                {
                                    authorizedAmount = Convert.ToDecimal(response.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                }
                                cCTransactionsPGWDTO.InvoiceNo = serviceId;
                                cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                                cCTransactionsPGWDTO.AuthCode = response.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                                cCTransactionsPGWDTO.CardType = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                                cCTransactionsPGWDTO.CaptureStatus = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0];

                            }

                            cCTransactionsPGWDTO.RefNo = adyen_giving_service_id;
                            cCTransactionsPGWDTO.RecordNo = response.PaymentResponse.POIData.POITransactionID.TransactionID;
                            cCTransactionsPGWDTO.ProcessData = getProcessData(response.PaymentResponse.POIData.POITransactionID.TimeStamp);
                            if (additionalResponseParams.Any())
                            {
                                string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                                acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                                acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";
                                acqrefData += additionalResponseParams.ContainsKey("pspReference") ? $" | pspReference: {Convert.ToString(additionalResponseParams["pspReference"])}" : "";
                                acqrefData += $" | MerchantId: {response.PaymentResponse.PaymentResult.PaymentAcquirerData.MerchantID}";

                                cCTransactionsPGWDTO.AcqRefData = acqrefData;
                            }

                            cCTransactionsPGWDTO.TextResponse = response.PaymentResponse.Response.Result;
                            cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.DONATION.ToString();
                            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            cCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();
                            cCTransactionsPGWDTO.ParentResponseId = Convert.ToInt32(ccOrginResponseId);

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                            ccTransactionsPGWBL.Save();

                            break;
                        }
                        else if (responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals("Failure") && !responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition.Equals("InProgress"))
                        {

                            Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.AdditionalResponse);

                            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            cCTransactionsPGWDTO.InvoiceNo = serviceId;
                            cCTransactionsPGWDTO.TextResponse = responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result;
                            cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.DONATION.ToString();
                            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            cCTransactionsPGWDTO.DSIXReturnCode = additionalResponseParams.ContainsKey("detailedStatus") ? additionalResponseParams["detailedStatus"] : additionalResponseParams.ContainsKey("status") ? additionalResponseParams["status"] : "";
                            cCTransactionsPGWDTO.ParentResponseId = Convert.ToInt32(ccOrginResponseId);

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                            ccTransactionsPGWBL.Save();

                            string errorMessage = $"{responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition} | ";
                            errorMessage += additionalResponseParams.ContainsKey("detailedStatus") ? additionalResponseParams["detailedStatus"] + " |" : "";
                            errorMessage += additionalResponseParams.ContainsKey("status") ? additionalResponseParams["status"] : "";
                            log.Error($"Donation Transaction failed. {errorMessage}");

                            statusDisplayUi.DisplayText($"Donation Transaction failed. {errorMessage}");
                            throw new Exception($"Donation Transaction failed. {errorMessage}");
                        }
                        else if (responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals("Failure") &&
                            responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition.Equals("InProgress"))
                        {

                            Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.AdditionalResponse);

                            string errorMessage = $"{responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition} | ";
                            errorMessage += additionalResponseParams.ContainsKey("detailedStatus") ? additionalResponseParams["detailedStatus"] + " |" : "";
                            errorMessage += additionalResponseParams.ContainsKey("status") ? additionalResponseParams["status"] : "";
                            log.Error($"Donation is in progress: {errorMessage}");


                        }
                        else
                        {
                            log.Error("Unknown Error Occured");
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                //throw;
            }
            log.LogMethodExit();
        }


        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.Info("transactionPaymentsDTO.CCResponseId = " + transactionPaymentsDTO.CCResponseId);
            try
            {
                TransactionPaymentsDTO originalTrxPayment = null;
                TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL();
                List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>> transactionsPaymentsSearchParam = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>();
                transactionsPaymentsSearchParam.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));
                transactionsPaymentsSearchParam.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                List<TransactionPaymentsDTO> trxPayment = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParam);
                if (trxPayment != null && trxPayment.Any())
                {
                    originalTrxPayment = trxPayment[0];

                }
                log.Info("originalTrxPayment = " + JsonConvert.SerializeObject(originalTrxPayment));

                //Form activeForm = GetActiveForm();

                if (transactionPaymentsDTO != null)
                {
                    // TBC will we ever get a -ve amount in here? as we have already handled it in sale
                    //if (transactionPaymentsDTO.Amount < 0)
                    //{
                    //    throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, "Variable Refund Not Supported"));
                    //}
                    statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(4202, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "Adyen Payment Gateway");
                    //{
                    //statusDisplayUi.CancelClicked += StatusDisplayUi_CancelClicked;
                    statusDisplayUi.EnableCancelButton(false);
                    Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
                    try
                    {
                        //Form form = statusDisplayUi as Form;
                        //form.Show(activeForm);
                        //SetNativeEnabled(activeForm, false);
                        thr.Start();
                        statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));


                        string result;
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.REFUND.ToString());
                        CCTransactionsPGWBL ccOrigTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
                        CCTransactionsPGWDTO currentCcOrigTransactionsPGWDTO = ccOrigTransactionsPGWBL.CCTransactionsPGWDTO;

                        string serviceId = cCRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
                        log.Debug($"serviceId = {serviceId}");

                        AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, false, false, false, applicationInfo, adyenTransactionTimeout, storeName);

                        if ((currentCcOrigTransactionsPGWDTO.ParentResponseId != null && currentCcOrigTransactionsPGWDTO.ParentResponseId > -1))
                        {
                            log.Debug("Initiating web refund");
                            CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(Convert.ToInt32(currentCcOrigTransactionsPGWDTO.ParentResponseId));
                            CCTransactionsPGWDTO preAuthCCTransactionpgwDTO = cCTransactionsPGWBL.CCTransactionsPGWDTO;
                            CCTransactionsPGWDTO lastestChildCCTransactionpgwDTO = GetLatestChildCCTransactionPGWDTO(transactionPaymentsDTO.TransactionId, transactionPaymentsDTO.SplitId, preAuthCCTransactionpgwDTO);

                            decimal latestAmount = Convert.ToDecimal(lastestChildCCTransactionpgwDTO.Amount);
                            decimal amountPaid = Convert.ToDecimal(transactionPaymentsDTO.Amount);
                            decimal tipAmount = Convert.ToDecimal(transactionPaymentsDTO.TipAmount);

                            decimal preAuthAdjustAmount = latestAmount - (amountPaid + tipAmount);
                            string preauthPaymentId = commandHandler.GetTransactionId(preAuthCCTransactionpgwDTO.RecordNo);
                            log.Info("preAuthAdjustAmount = " + preAuthAdjustAmount);
                            log.Info("preauthPaymentId = " + preauthPaymentId);
                            log.Info("lastestChildCCTransactionpgwDTO.ResponseOrigin = " + lastestChildCCTransactionpgwDTO.ResponseOrigin);
                            log.Info("currencyConversionFactor = " + currencyConversionFactor);
                            log.Info("preAuthAdjustApiUrl = " + preAuthAdjustApiUrl);


                            if (preAuthAdjustAmount == 0)
                            {
                                HandleCancelAuthorization(transactionPaymentsDTO, cCRequestPGWDTO, currentCcOrigTransactionsPGWDTO, commandHandler, preauthPaymentId);
                                if (preAuthCCTransactionpgwDTO.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString())
                                {
                                    RefundPreAuth(cCRequestPGWDTO, preAuthCCTransactionpgwDTO, preAuthAdjustAmount);
                                }
                            }
                            else
                            {
                                result = commandHandler.DoPreAuthorizationAdjustment(preAuthAdjustAmount, preauthPaymentId, lastestChildCCTransactionpgwDTO.ResponseOrigin, cCRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, preAuthAdjustApiUrl);
                                log.Debug($"Preauth Adjustment response={JsonConvert.SerializeObject(result)}");

                                if (string.IsNullOrWhiteSpace(result))
                                {
                                    log.Error($"MakePayment(): Auth response was null");
                                    throw new Exception("Authorization Failed. No response received.");
                                }

                                AdjustAuthResponseDto authAdjustResponseObject = JsonConvert.DeserializeObject<AdjustAuthResponseDto>(result);
                                log.LogVariableState("authAdjustResponseObject", authAdjustResponseObject);

                                if (authAdjustResponseObject == null)
                                {
                                    log.Error($"MakePayment(): Auth Adjust response deserialization failed");
                                    throw new Exception("Authorization Failed.");
                                }

                                if (!authAdjustResponseObject.response.Equals("Authorised"))
                                {
                                    log.Error($"MakePayment(): Auth Adjust failed");
                                    throw new Exception("Authorization Failed.");
                                }

                                decimal authAdjustuthorizedAmount = preAuthAdjustAmount;

                                CCTransactionsPGWDTO authresonseCcTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                authresonseCcTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                                authresonseCcTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
                                authresonseCcTransactionsPGWDTO.AuthCode = authAdjustResponseObject.additionalData.authCode;
                                authresonseCcTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                                authresonseCcTransactionsPGWDTO.RefNo = authAdjustResponseObject.additionalData.merchantReference;
                                authresonseCcTransactionsPGWDTO.RecordNo = authAdjustResponseObject.pspReference;
                                authresonseCcTransactionsPGWDTO.ResponseOrigin = authAdjustResponseObject.additionalData.adjustAuthorisationData;

                                authresonseCcTransactionsPGWDTO.TextResponse = authAdjustResponseObject.response;
                                authresonseCcTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.AUTHORIZATION.ToString();
                                authresonseCcTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                authresonseCcTransactionsPGWDTO.Authorize = (-transactionPaymentsDTO.Amount).ToString();
                                authresonseCcTransactionsPGWDTO.Purchase = (-transactionPaymentsDTO.Amount).ToString();
                                authresonseCcTransactionsPGWDTO.Amount = Convert.ToDouble(authAdjustuthorizedAmount);
                                authresonseCcTransactionsPGWDTO.ParentResponseId = preAuthCCTransactionpgwDTO.ResponseID;




                                cCTransactionsPGWBL = new CCTransactionsPGWBL(authresonseCcTransactionsPGWDTO);
                                cCTransactionsPGWBL.Save();
                                List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>> transactionsPaymentsSearchParams = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>();
                                transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, lastestChildCCTransactionpgwDTO.ResponseID.ToString()));
                                transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                List<TransactionPaymentsDTO> transactionPaymentsDTOs = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParams);
                                if (transactionPaymentsDTOs != null && transactionPaymentsDTOs.Any())
                                {
                                    TransactionPaymentsDTO latestTransactionPaymentDTO = transactionPaymentsDTOs[0];
                                    latestTransactionPaymentDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                    TransactionPaymentsBL transactionPaymentsBL = new TransactionPaymentsBL(utilities.ExecutionContext, latestTransactionPaymentDTO);
                                    transactionPaymentsBL.Save();
                                }


                                CCTransactionsPGWDTO refundCCTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, authresonseCcTransactionsPGWDTO.InvoiceNo, authresonseCcTransactionsPGWDTO.TokenID, authresonseCcTransactionsPGWDTO.RecordNo, authresonseCcTransactionsPGWDTO.DSIXReturnCode, authresonseCcTransactionsPGWDTO.StatusID, authresonseCcTransactionsPGWDTO.TextResponse, authresonseCcTransactionsPGWDTO.AcctNo, authresonseCcTransactionsPGWDTO.CardType, PaymentGatewayTransactionType.REFUND.ToString(), authresonseCcTransactionsPGWDTO.RefNo, string.Empty, (transactionPaymentsDTO.Amount).ToString(), authresonseCcTransactionsPGWDTO.TransactionDatetime, authresonseCcTransactionsPGWDTO.AuthCode, authresonseCcTransactionsPGWDTO.ProcessData, authresonseCcTransactionsPGWDTO.ResponseOrigin, authresonseCcTransactionsPGWDTO.UserTraceData, authresonseCcTransactionsPGWDTO.CaptureStatus, authresonseCcTransactionsPGWDTO.AcqRefData, null, string.Empty, string.Empty, authresonseCcTransactionsPGWDTO.CustomerCardProfileId, authresonseCcTransactionsPGWDTO.ParentResponseId, null);

                                cCTransactionsPGWBL = new CCTransactionsPGWBL(refundCCTransactionsPGWDTO);
                                cCTransactionsPGWBL.Save();


                                SendPrintReceiptRequest(transactionPaymentsDTO, authresonseCcTransactionsPGWDTO);

                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(authresonseCcTransactionsPGWDTO);
                                ccTransactionsPGWBL.Save();
                                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                                transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                                transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                            }




                        }
                        else if (originalTrxPayment != null && originalTrxPayment.SubscriptionAuthorizationMode == SubscriptionAuthorizationMode.P || originalTrxPayment != null && !string.IsNullOrWhiteSpace(originalTrxPayment.CustomerCardProfileId) && originalTrxPayment.SubscriptionAuthorizationMode != SubscriptionAuthorizationMode.I)
                        {
                            // check if its a subscription payment
                            // call web refund api
                            log.Debug("Initiating web refund");
                            result = commandHandler.DoRefundSubscriptionPayment(cCRequestPGWDTO.RequestID.ToString(), currentCcOrigTransactionsPGWDTO.RecordNo);
                            log.Debug($"Web Refund response = {result}");

                            if (string.IsNullOrWhiteSpace(result))
                            {
                                log.Error($"RefundAmount(): response was null");
                                throw new Exception("Refund Failed. No response received.");
                            }

                            WebRefundResponseDto responseObject = JsonConvert.DeserializeObject<WebRefundResponseDto>(result);
                            if (responseObject == null)
                            {
                                log.Error($"RefundAmount(): Refund response deserialization failed");
                                throw new Exception("Refund Failed. Response deserialization failed.");
                            }

                            if (responseObject.status != "received")
                            {
                                log.Error($"RefundAmount(): Refund failed");
                                throw new Exception("Refund Failed.");
                            }

                            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                            cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
                            cCTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                            cCTransactionsPGWDTO.RefNo = cCRequestPGWDTO.RequestID.ToString();
                            cCTransactionsPGWDTO.RecordNo = responseObject.pspReference;
                            cCTransactionsPGWDTO.TextResponse = responseObject.status;
                            cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            cCTransactionsPGWDTO.Authorize = (transactionPaymentsDTO.Amount + transactionPaymentsDTO.TipAmount).ToString();


                            SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                            ccTransactionsPGWBL.Save();
                            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                            transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                            transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                            transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;

                        }
                        else
                        {
                            if (unsupportedCCSchemesforRefRefund.Contains(currentCcOrigTransactionsPGWDTO.CardType.ToLower()))
                            {
                                HandleRefRefundforUnspportedCC(transactionPaymentsDTO, cCRequestPGWDTO, commandHandler);
                            }
                            else
                            {

                                // terminal refund request
                                log.Info("Terminal refund request initiated");
                                decimal amount = Convert.ToDecimal(transactionPaymentsDTO.Amount + transactionPaymentsDTO.TipAmount);
                                decimal refundAmount;
                                string terminalId = string.Empty;

                                if (!string.IsNullOrWhiteSpace(reversalTerminalId))
                                {
                                    terminalId = reversalTerminalId;
                                }

                                decimal authorisedAmount = Convert.ToDecimal(currentCcOrigTransactionsPGWDTO.Authorize);
                                log.Debug($"authorisedAmount={authorisedAmount}");
                                if (amount > authorisedAmount)
                                {
                                    log.Error("Refund failed due to Invalid amount passed");
                                    throw new Exception("Refund failed due to Invalid amount passed");
                                }


                                //added this logic as part of processdata format issue . 15-05-25
                                long secondsSinceEpoch;
                                string creationTime = string.Empty;
                                string processDataofOriginalTransaction = currentCcOrigTransactionsPGWDTO.ProcessData;
                                if (!string.IsNullOrWhiteSpace(processDataofOriginalTransaction))
                                {
                                    try
                                    {
                                        if (long.TryParse(processDataofOriginalTransaction, out secondsSinceEpoch))
                                        {
                                            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                                            DateTime dateTime = epoch.AddSeconds(secondsSinceEpoch);

                                            creationTime = dateTime.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                                        }
                                        else
                                        {
                                            DateTime dateTime;
                                            try
                                            {
                                                dateTime = DateTime.ParseExact(processDataofOriginalTransaction, "yyyy-MM-dd'T'HH:mm:ss'Z'", CultureInfo.InvariantCulture);
                                            }
                                            catch (Exception)
                                            {
                                                dateTime = Convert.ToDateTime(processDataofOriginalTransaction);
                                            }
                                            creationTime = dateTime.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                                            log.Info($"creationTime = {creationTime}");
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        log.Error(e);
                                        throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6070)); //DatetimeConversion Failed.
                                    }
                                }

                                string txDateTime = commandHandler.getUTCDateTime(creationTime);
                                log.Debug($"txDateTime = {txDateTime}");
                                log.Info("currentCcOrigTransactionsPGWDTO.RecordNo.ToString() =" + currentCcOrigTransactionsPGWDTO.RecordNo.ToString());
                                log.Info("amount =" + amount);

                                if ((!string.IsNullOrEmpty(currentCcOrigTransactionsPGWDTO.Authorize) && authorisedAmount != amount))
                                {
                                    // Partial refund
                                    log.Debug("Partial Refund Initiated");


                                    result = commandHandler.DoRefund(currentCcOrigTransactionsPGWDTO.RecordNo.ToString(), txDateTime, amount, cCRequestPGWDTO.RequestID.ToString(), terminalId);
                                    log.Debug($"partial refund result={JsonConvert.SerializeObject(result)}");
                                }
                                else
                                {
                                    // full Refund
                                    log.Debug("Full Refund Initiated");
                                    result = commandHandler.DoRefund(currentCcOrigTransactionsPGWDTO.RecordNo.ToString(), txDateTime, amount, cCRequestPGWDTO.RequestID.ToString(), terminalId);
                                    log.Debug($"full refund result={JsonConvert.SerializeObject(result)}");
                                }


                                if (string.IsNullOrWhiteSpace(result))
                                {
                                    log.Error($"RefundAmount(): response was null");
                                    throw new Exception("Refund Failed. No response received.");
                                }

                                RefundResponseDto responseObject = JsonConvert.DeserializeObject<RefundResponseDto>(result);
                                log.LogVariableState("responseObject", responseObject);
                                if (responseObject == null)
                                {
                                    log.Error($"RefundAmount(): Refund response deserialization failed");
                                    throw new Exception("Refund Failed. Response deserialization failed.");
                                }

                                CheckOtherRefundErrors(commandHandler, responseObject);

                                Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.ReversalResponse.Response.AdditionalResponse);

                                if (responseObject.SaleToPOIResponse.ReversalResponse.Response.Result.Equals(REFUND_FAILURE_STATUS))
                                {
                                    string errorMessage = commandHandler.GetRefundErrorMessage(responseObject, additionalResponseParams);
                                    log.Error($"RefundAmount(): Refund failed due to {errorMessage} ");
                                    throw new Exception($"Refund Failed. Error: {errorMessage}");
                                }

                                // refund succeeded
                                refundAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.ReversalResponse.ReversedAmount);

                                log.Debug($"refundAmount={refundAmount}");

                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.AcctNo = additionalResponseParams.ContainsKey("cardSummary") ? String.Concat("XXXXXXXXXXXX", Convert.ToString(additionalResponseParams["cardSummary"])) : currentCcOrigTransactionsPGWDTO.AcctNo;
                                cCTransactionsPGWDTO.AuthCode = additionalResponseParams.ContainsKey("authCode") ? Convert.ToString(additionalResponseParams["authCode"]) : "";
                                cCTransactionsPGWDTO.CardType = additionalResponseParams.ContainsKey("paymentMethod") ? Convert.ToString(additionalResponseParams["paymentMethod"]) : "";
                                cCTransactionsPGWDTO.CaptureStatus = additionalResponseParams.ContainsKey("posEntryMode") ? Convert.ToString(additionalResponseParams["posEntryMode"]) : "";
                                cCTransactionsPGWDTO.RefNo = cCRequestPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.RecordNo = responseObject.SaleToPOIResponse.ReversalResponse.POIData.POITransactionID.TransactionID;
                                //cCTransactionsPGWDTO.AcqRefData = responseObject.payment.cardTransaction.referenceId;

                                cCTransactionsPGWDTO.TextResponse = additionalResponseParams.ContainsKey("acquirerResponseCode") ? Convert.ToString(additionalResponseParams["acquirerResponseCode"]) : responseObject.SaleToPOIResponse.ReversalResponse.Response.Result;
                                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.Authorize = refundAmount.ToString();

                                SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);


                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                                ccTransactionsPGWBL.Save();
                                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                                transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                                transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        //statusDisplayUi.DisplayText("Error occured while Refunding the Amount");
                        log.Error("Error occured while Refunding the Amount", ex);
                        log.Fatal("Ends -RefundAmount(transactionPaymentsDTO) method " + ex.ToString());
                        log.LogMethodExit(null, "throwing Exception");
                        throw;
                    }
                    finally
                    {
                        try
                        {
                            if (statusDisplayUi != null)
                            {
                                Application.DoEvents();
                                statusDisplayUi.CloseStatusWindow();
                            }
                        }
                        catch { }
                    }
                }
                log.LogMethodExit(transactionPaymentsDTO);
                return transactionPaymentsDTO;
            }
            catch (Exception ex)
            {
                log.Error("Error occured while Refunding the Amount", ex);
                log.Fatal("Ends -RefundAmount(transactionPaymentsDTO) method " + ex.ToString());
                log.LogMethodExit(null, "throwing Exception");
                throw new Exception("Refund failed exception :" + ex.Message);
            }
            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                }
            }
        }

        private void RefundPreAuth(CCRequestPGWDTO cCRequestPGWDTO, CCTransactionsPGWDTO preAuthCCTransactionpgwDTO, decimal preAuthAdjustAmount)
        {
            log.LogMethodEntry(cCRequestPGWDTO, preAuthCCTransactionpgwDTO, preAuthAdjustAmount);
            CCTransactionsPGWBL cCTransactionsPGWBL;
            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO(-1, cCRequestPGWDTO.RequestID.ToString(), preAuthCCTransactionpgwDTO.TokenID, preAuthCCTransactionpgwDTO.RecordNo, preAuthCCTransactionpgwDTO.DSIXReturnCode, preAuthCCTransactionpgwDTO.StatusID, preAuthCCTransactionpgwDTO.TextResponse, preAuthCCTransactionpgwDTO.AcctNo, preAuthCCTransactionpgwDTO.CardType, PaymentGatewayTransactionType.REFUND.ToString(), preAuthCCTransactionpgwDTO.RefNo, string.Empty, preAuthCCTransactionpgwDTO.Authorize.ToString(), preAuthCCTransactionpgwDTO.TransactionDatetime, preAuthCCTransactionpgwDTO.AuthCode, preAuthCCTransactionpgwDTO.ProcessData, preAuthCCTransactionpgwDTO.ResponseOrigin, preAuthCCTransactionpgwDTO.UserTraceData, preAuthCCTransactionpgwDTO.CaptureStatus, preAuthCCTransactionpgwDTO.AcqRefData, preAuthCCTransactionpgwDTO.TipAmount, string.Empty, string.Empty, preAuthCCTransactionpgwDTO.CustomerCardProfileId, null, Convert.ToDouble(preAuthAdjustAmount));
            cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
            cCTransactionsPGWBL.Save();
            TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL();
            List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>> transactionsPaymentsSearchParams = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>();
            transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, preAuthCCTransactionpgwDTO.ResponseID.ToString()));
            transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
            List<TransactionPaymentsDTO> transactionPaymentsDTOs = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParams);
            if (transactionPaymentsDTOs != null && transactionPaymentsDTOs.Any())
            {
                TransactionPaymentsDTO latestTransactionPaymentDTO = transactionPaymentsDTOs[0];
                latestTransactionPaymentDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                TransactionPaymentsBL transactionPaymentsBL = new TransactionPaymentsBL(utilities.ExecutionContext, latestTransactionPaymentDTO);
                transactionPaymentsBL.Save();
            }
            log.LogMethodExit();
        }

        private void HandleCancelAuthorization(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO, CCTransactionsPGWDTO currentCcOrigTransactionsPGWDTO, AdyenPosCommandhandler commandHandler, string preauthPaymentId)
        {
            log.LogMethodEntry();
            string result = commandHandler.DoCancelAuthorization(preauthPaymentId, cCRequestPGWDTO.RequestID.ToString());
            if (string.IsNullOrWhiteSpace(result))
            {
                log.Error($"RefundAmount(): response was null");
                throw new Exception("Refund Failed. No response received.");
            }

            WebRefundResponseDto responseObject = JsonConvert.DeserializeObject<WebRefundResponseDto>(result);
            if (responseObject == null)
            {
                log.Error($"RefundAmount(): Refund response deserialization failed");
                throw new Exception("Refund Failed. Response deserialization failed.");
            }

            if (responseObject.status != "received")
            {
                log.Error($"RefundAmount(): Refund failed");
                throw new Exception("Refund Failed.");
            }

            CCTransactionsPGWDTO responseCCTransactionsPGWDTO = new CCTransactionsPGWDTO();
            responseCCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
            responseCCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
            responseCCTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
            responseCCTransactionsPGWDTO.RefNo = cCRequestPGWDTO.RequestID.ToString();
            responseCCTransactionsPGWDTO.RecordNo = responseObject.pspReference;
            responseCCTransactionsPGWDTO.TextResponse = responseObject.status;
            responseCCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
            responseCCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
            responseCCTransactionsPGWDTO.Authorize = (transactionPaymentsDTO.Amount + transactionPaymentsDTO.TipAmount).ToString();
            responseCCTransactionsPGWDTO.Amount = 0;

            SendPrintReceiptRequest(transactionPaymentsDTO, responseCCTransactionsPGWDTO);

            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(responseCCTransactionsPGWDTO);
            ccTransactionsPGWBL.Save();
            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
            transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
            transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
            transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
            log.LogMethodExit();
        }

        private void HandleRefRefundforUnspportedCC(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO, AdyenPosCommandhandler commandHandler)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCRequestPGWDTO, commandHandler);
            string result = commandHandler.DoIndependantRefund(Convert.ToDecimal(transactionPaymentsDTO.Amount), cCRequestPGWDTO.RequestID.ToString());
            log.Debug($"independant refund result={result}");
            if (string.IsNullOrWhiteSpace(result))
            {
                log.Error($"MakePayment(): Independant refund response was null");
                throw new Exception("Refund Failed. No response received.");
            }

            PaymentResponseDto responseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
            log.LogVariableState("responseObject", responseObject);
            if (responseObject == null)
            {
                log.Error($"MakePayment(): Independant refund response deserialization failed");
                throw new Exception("Independant refund Failed. Response deserialization failed.");
            }

            CheckOtherPaymentErrors(commandHandler, responseObject);

            Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.PaymentResponse.Response.AdditionalResponse);


            decimal authorizedAmount = 0;
            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
            cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();

            if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult != null)
            {
                if (responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp != null)
                {
                    authorizedAmount = Convert.ToDecimal(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);

                }
                cCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                cCTransactionsPGWDTO.AuthCode = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                cCTransactionsPGWDTO.CardType = responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                cCTransactionsPGWDTO.CaptureStatus =
                    responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode != null ? responseObject.SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0] : null;
            }



            cCTransactionsPGWDTO.RefNo = responseObject.SaleToPOIResponse.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
            cCTransactionsPGWDTO.RecordNo = responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TransactionID;
            cCTransactionsPGWDTO.ProcessData = getProcessData(responseObject.SaleToPOIResponse.PaymentResponse.POIData.POITransactionID.TimeStamp);

            if (additionalResponseParams.Any())
            {
                string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";
                acqrefData += additionalResponseParams.ContainsKey("pspReference") ? $" | PSP Reference: {Convert.ToString(additionalResponseParams["pspReference"])}" : "";

                cCTransactionsPGWDTO.AcqRefData = acqrefData;
            }

            cCTransactionsPGWDTO.TextResponse = additionalResponseParams.ContainsKey("acquirerResponseCode") ? $"{Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : responseObject.SaleToPOIResponse.PaymentResponse.Response.Result;
            cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
            cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
            cCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();


            SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);
            if (!responseObject.SaleToPOIResponse.PaymentResponse.Response.Result.Equals(PAYMENT_SUCCESS_STATUS))
            {
                string errorMessage = string.IsNullOrWhiteSpace(responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition) ? "" : $"ErrorCondition: {responseObject.SaleToPOIResponse.PaymentResponse.Response.ErrorCondition}";
                if (additionalResponseParams != null && additionalResponseParams.ContainsKey("message"))
                {
                    errorMessage += $" Message: {additionalResponseParams["message"]}";
                }
                if (additionalResponseParams != null && additionalResponseParams.ContainsKey("refusalReason"))
                {
                    errorMessage += $" Refusal Reason: {additionalResponseParams["refusalReason"]}";
                }
                cCTransactionsPGWDTO.DSIXReturnCode = errorMessage;

                CCTransactionsPGWBL cCTrxPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                cCTrxPGWBL.Save();
                log.Error($"MakePayment(): Independant refund failed. {errorMessage}");
                throw new Exception($"Independant refund Failed. {errorMessage}");
            }
            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
            ccTransactionsPGWBL.Save();
            transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
            transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
            transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
            transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize) * -1;
            transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
            transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
        }

        //public override List<CCTransactionsPGWDTO> GetAllUnsettledCreditCardTransactions()
        //{
        //    log.LogMethodEntry();

        //    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = null;
        //    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
        //    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
        //    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, TransactionType.AUTHORIZATION.ToString()));
        //    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
        //    //if (cCTransactionsPGWDTOList == null || cCTransactionsPGWDTOList.Any() == false)
        //    //{
        //    //    searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
        //    //    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, TransactionType.AUTHORIZATION.ToString()));
        //    //    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
        //    //}

        //    log.LogMethodExit(cCTransactionsPGWDTOList);
        //    return cCTransactionsPGWDTOList;
        //}

        public override List<CCTransactionsPGWDTO> GetAllUnsettledCreditCardTransactions()
        {
            log.LogMethodEntry();
            List<CCTransactionsPGWDTO> result = null;
            CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
            List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, TransactionType.AUTHORIZATION.ToString()));
            List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
            if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Any())
            {
                result = new List<CCTransactionsPGWDTO>();
                foreach (CCTransactionsPGWDTO cCTransactionsPGWDTO in cCTransactionsPGWDTOList)
                {
                    if (cCTransactionsPGWDTO.ParentResponseId != null && cCTransactionsPGWDTO.ParentResponseId > -1)
                    {
                        CCTransactionsPGWBL parentCCTransactionsPGWBL = new CCTransactionsPGWBL(Convert.ToInt32(cCTransactionsPGWDTO.ParentResponseId));
                        CCTransactionsPGWDTO preAuthCCTransactionsPGWDTO = parentCCTransactionsPGWBL.CCTransactionsPGWDTO;
                        searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                        searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, preAuthCCTransactionsPGWDTO.ResponseID.ToString()));
                        List<CCTransactionsPGWDTO> childCCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                        if (childCCTransactionsPGWDTOList != null && childCCTransactionsPGWDTOList.Any())
                        {
                            CCTransactionsPGWDTO LatestCCTransactionsPGWDTO = childCCTransactionsPGWDTOList.OrderByDescending(x => x.ResponseID).FirstOrDefault();
                            if (!result.Exists(x => x.ResponseID == LatestCCTransactionsPGWDTO.ParentResponseId))
                            {
                                result.Add(LatestCCTransactionsPGWDTO);
                            }
                        }
                    }
                    else
                    {
                        result.Add(cCTransactionsPGWDTO);
                    }
                }
            }
            log.LogMethodExit(result);
            return result;
        }

        public override TransactionPaymentsDTO PayTip(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            // TBC add preauth-adjust code
            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
            CCTransactionsPGWDTO currentCcOrigTransactionsPGWDTO = ccTransactionsPGWBL.CCTransactionsPGWDTO;
            if (currentCcOrigTransactionsPGWDTO.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString())
            {
                string errorMessage = MessageContainerList.GetMessage(utilities.ExecutionContext, 2275, currentCcOrigTransactionsPGWDTO.TranCode);
                log.Error(errorMessage);
                throw new Exception(errorMessage);
            }
            if (currentCcOrigTransactionsPGWDTO.TranCode == PaymentGatewayTransactionType.AUTHORIZATION.ToString()
                && (currentCcOrigTransactionsPGWDTO.ParentResponseId == null || currentCcOrigTransactionsPGWDTO.ParentResponseId <= -1))
            {
                if (Convert.ToDecimal(currentCcOrigTransactionsPGWDTO.TipAmount) > 0)
                {
                    string errorMessage = MessageContainerList.GetMessage(utilities.ExecutionContext, 4096);
                    log.Error(errorMessage);
                    throw new Exception(errorMessage);
                }
            }
            transactionPaymentsDTO = PerformSettlement(transactionPaymentsDTO, true);
            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        public TransactionPaymentsDTO VoidPayment(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);
                if (transactionPaymentsDTO != null)
                {
                    double voidAmount;
                    string result;
                    CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.VOID.ToString());
                    CCTransactionsPGWBL ccOrigTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = ccOrigTransactionsPGWBL.CCTransactionsPGWDTO;

                    string serviceId = cCRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
                    AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, false, false, false, applicationInfo, adyenTransactionTimeout, storeName);

                    string txDtTime = commandHandler.getUTCDateTime(ccOrigTransactionsPGWDTO.ProcessData.ToString());
                    log.Debug($"txDtTime = {txDtTime}");

                    result = commandHandler.DoRefund(ccOrigTransactionsPGWDTO.RecordNo.ToString(), txDtTime, Convert.ToDecimal(ccOrigTransactionsPGWDTO.Authorize), cCRequestPGWDTO.RequestID.ToString(), string.Empty);

                    if (string.IsNullOrWhiteSpace(result))
                    {
                        log.Error($"VoidPayment(): response was null");
                        throw new Exception("Void Failed. No response received.");
                    }

                    RefundResponseDto responseObject = JsonConvert.DeserializeObject<RefundResponseDto>(result);
                    log.LogVariableState("responseObject", responseObject);
                    if (responseObject == null)
                    {
                        log.Error($"VoidPayment(): Void response deserialization failed");
                        throw new Exception("Void Failed. Response deserialization failed.");
                    }

                    CheckOtherRefundErrors(commandHandler, responseObject);
                    Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.ReversalResponse.Response.AdditionalResponse);
                    if (responseObject.SaleToPOIResponse.ReversalResponse.Response.Result.Equals(REFUND_FAILURE_STATUS))
                    {
                        string errorMessage = commandHandler.GetRefundErrorMessage(responseObject, additionalResponseParams);
                        log.Error($"VoidPayment(): Reversal failed. {errorMessage} ");
                        throw new Exception($"Reversal Failed. {errorMessage}");
                    }

                    // void succeeded
                    voidAmount = responseObject.SaleToPOIResponse.ReversalResponse.ReversedAmount;
                    log.Debug($"voidAmount={voidAmount}");

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.AcctNo = String.Concat(new string('X', 12), Convert.ToString(additionalResponseParams["cardSummary"]));
                    cCTransactionsPGWDTO.AuthCode = Convert.ToString(additionalResponseParams["authCode"]);
                    cCTransactionsPGWDTO.CardType = Convert.ToString(additionalResponseParams["paymentMethod"]);
                    cCTransactionsPGWDTO.CaptureStatus = Convert.ToString(additionalResponseParams["posEntryMode"]);
                    cCTransactionsPGWDTO.RefNo = cCRequestPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = responseObject.SaleToPOIResponse.ReversalResponse.POIData.POITransactionID.TransactionID;

                    cCTransactionsPGWDTO.TextResponse = Convert.ToString(additionalResponseParams["acquirerResponseCode"]);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.VOID.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();

                    SendPrintReceiptRequest(transactionPaymentsDTO, cCTransactionsPGWDTO);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                    ccTransactionsPGWBL.Save();
                    transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                    transactionPaymentsDTO.CreditCardName = ccTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                    transactionPaymentsDTO.Reference = ccTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                    transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWBL.CCTransactionsPGWDTO.Authorize);
                    transactionPaymentsDTO.CreditCardNumber = ccTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                    transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                    return transactionPaymentsDTO;

                }
                else
                {
                    log.Error("trxPaymentsdto was null");
                    throw new Exception("Exception in processing Payment ");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw new Exception("Exception in processing Payment ");
            }
            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                }
            }
        }

        public override TransactionPaymentsDTO PerformSettlement(TransactionPaymentsDTO transactionPaymentsDTO, bool IsForcedSettlement = false)
        {
            log.LogMethodEntry(transactionPaymentsDTO, IsForcedSettlement);
            log.Info("PerformSettlement method - Enter");
            //Form activeForm = GetActiveForm();
            try
            {
                bool threadStarted = false;
                if (transactionPaymentsDTO != null)
                {

                    string result;
                    statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage(1839, transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL)), "Adyen Payment Gateway");
                    //{
                    statusDisplayUi.EnableCancelButton(false);
                    Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
                    try
                    {
                        CCTransactionsPGWDTO preAuthCcOrigTransactionsPGWDTO;
                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
                        CCTransactionsPGWDTO currentCcOrigTransactionsPGWDTO = ccTransactionsPGWBL.CCTransactionsPGWDTO;
                        log.Debug("currentCcOrigTransactionsPGWDTO = " + JsonConvert.SerializeObject(currentCcOrigTransactionsPGWDTO));


                        if (currentCcOrigTransactionsPGWDTO.ParentResponseId != null && currentCcOrigTransactionsPGWDTO.ParentResponseId > -1)
                        {
                            // get parentRespid = responseid of preauth
                            // fetch preauth 
                            ccTransactionsPGWBL = new CCTransactionsPGWBL(Convert.ToInt32(currentCcOrigTransactionsPGWDTO.ParentResponseId));
                            preAuthCcOrigTransactionsPGWDTO = ccTransactionsPGWBL.CCTransactionsPGWDTO;
                            log.Info("auth adjustment ccOrigTransactionsPGWDTO" + JsonConvert.SerializeObject(preAuthCcOrigTransactionsPGWDTO));
                        }
                        else
                        {
                            // ccOrigTransactionsPGWDTO => is for preauth
                            preAuthCcOrigTransactionsPGWDTO = currentCcOrigTransactionsPGWDTO;
                            log.Info("PreAuth transaction itself ccOrigTransactionsPGWDTO" + JsonConvert.SerializeObject(preAuthCcOrigTransactionsPGWDTO));
                        }



                        CCRequestPGWDTO ccRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_PAYMENT);
                        string serviceId = ccRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
                        log.Info("serviceId = " + serviceId);

                        AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, isTipAllowed, false, false, applicationInfo, adyenTransactionTimeout, storeName);
                        statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                        decimal baseAmount = Convert.ToDecimal(transactionPaymentsDTO.Amount);
                        decimal tipAmount = Convert.ToDecimal(transactionPaymentsDTO.TipAmount);


                        if (currentCcOrigTransactionsPGWDTO.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString()
                            || currentCcOrigTransactionsPGWDTO.ResponseID != preAuthCcOrigTransactionsPGWDTO.ResponseID)
                        {
                            CCTransactionsPGWDTO latestChildCCTransactionsPGWDTO = GetLatestChildCCTransactionPGWDTO(transactionPaymentsDTO.TransactionId, transactionPaymentsDTO.SplitId, preAuthCcOrigTransactionsPGWDTO);
                            log.Info("latestChildCCTransactionsPGWDTO = " + JsonConvert.SerializeObject(latestChildCCTransactionsPGWDTO));
                            baseAmount = Convert.ToDecimal(latestChildCCTransactionsPGWDTO.Amount);
                            log.Info("baseAmount = " + baseAmount);
                            log.Info("tipAmount = " + tipAmount);
                            if (!IsForcedSettlement && ((transactionPaymentsDTO.TipAmount <= 0 && currentCcOrigTransactionsPGWDTO.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString()) || (currentCcOrigTransactionsPGWDTO.ResponseID != preAuthCcOrigTransactionsPGWDTO.ResponseID)))
                            {
                                frmFinalizeTransaction frmFinalizeTransaction = new frmFinalizeTransaction(utilities, overallTransactionAmount, overallTipAmountEntered, (currentCcOrigTransactionsPGWDTO.ResponseID == preAuthCcOrigTransactionsPGWDTO.ResponseID) ? Convert.ToDecimal(baseAmount) : Convert.ToDecimal(transactionPaymentsDTO.Amount), Convert.ToDecimal(transactionPaymentsDTO.TipAmount), transactionPaymentsDTO.CreditCardNumber, showMessageDelegate);
                                if (frmFinalizeTransaction.ShowDialog() != DialogResult.Cancel)
                                {
                                    tipAmount = Convert.ToDecimal(frmFinalizeTransaction.TipAmount);
                                }
                                else
                                {
                                    log.LogMethodExit(transactionPaymentsDTO);
                                    throw new Exception(utilities.MessageUtils.getMessage("CANCELLED"));
                                }
                            }
                            else
                            {
                                thr.Start();
                                threadStarted = true;
                            }
                            if (tipAmount <= 0)
                            {
                                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                                ccTransactionsPGWDTO.AcctNo = latestChildCCTransactionsPGWDTO.AcctNo;
                                ccTransactionsPGWDTO.AuthCode = latestChildCCTransactionsPGWDTO.AuthCode;
                                ccTransactionsPGWDTO.CardType = latestChildCCTransactionsPGWDTO.CardType;
                                ccTransactionsPGWDTO.RefNo = latestChildCCTransactionsPGWDTO.RefNo;
                                ccTransactionsPGWDTO.RecordNo = latestChildCCTransactionsPGWDTO.RecordNo;
                                ccTransactionsPGWDTO.ResponseOrigin = latestChildCCTransactionsPGWDTO.ResponseOrigin;

                                ccTransactionsPGWDTO.TextResponse = latestChildCCTransactionsPGWDTO.TextResponse;
                                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                                ccTransactionsPGWDTO.Authorize = tipAmount.ToString();
                                ccTransactionsPGWDTO.Amount = latestChildCCTransactionsPGWDTO.Amount;
                                ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                                ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                                SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                                CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                                cCTransactionsPGWBL.Save();

                                transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                                transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                            }
                            else
                            {
                                decimal authAdjustAmount = baseAmount + tipAmount;
                                log.Debug($"authAdjustAmount = {authAdjustAmount}");
                                // go for praauth adjust
                                log.Debug("Pre-auth adjust initiated");
                                log.Info("latestChildCCTransactionsPGWDTO.ResponseOrigin = " + latestChildCCTransactionsPGWDTO.ResponseOrigin);
                                log.Info("currencyConversionFactor = " + currencyConversionFactor);
                                log.Info("preAuthAdjustApiUrl = " + preAuthAdjustApiUrl);

                                string preauthPaymentId = commandHandler.GetTransactionId(preAuthCcOrigTransactionsPGWDTO.RecordNo);
                                log.Debug("preauthPaymentId: " + preauthPaymentId);
                                result = commandHandler.DoPreAuthorizationAdjustment(authAdjustAmount, preauthPaymentId, latestChildCCTransactionsPGWDTO.ResponseOrigin, ccRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, preAuthAdjustApiUrl);
                                log.Debug($"Preauth Adjustment response={JsonConvert.SerializeObject(result)}");

                                if (string.IsNullOrWhiteSpace(result))
                                {
                                    log.Error($"MakePayment(): Auth response was null");
                                    throw new Exception("Authorization Failed. No response received.");
                                }

                                AdjustAuthResponseDto authAdjustResponseObject = JsonConvert.DeserializeObject<AdjustAuthResponseDto>(result);
                                log.LogVariableState("authAdjustResponseObject", JsonConvert.SerializeObject(authAdjustResponseObject));

                                if (authAdjustResponseObject == null)
                                {
                                    log.Error($"MakePayment(): Auth Adjust response deserialization failed");
                                    throw new Exception("Authorization Failed.");
                                }

                                if (!authAdjustResponseObject.response.Equals("Authorised"))
                                {
                                    log.Error($"MakePayment(): Auth Adjust failed");
                                    throw new Exception("Authorization Failed.");
                                }

                                decimal authAdjustuthorizedAmount = Convert.ToDecimal(authAdjustAmount);

                                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                                ccTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
                                ccTransactionsPGWDTO.AuthCode = authAdjustResponseObject.additionalData.authCode;
                                ccTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                                ccTransactionsPGWDTO.RefNo = authAdjustResponseObject.additionalData.merchantReference;
                                ccTransactionsPGWDTO.RecordNo = authAdjustResponseObject.pspReference;
                                ccTransactionsPGWDTO.ResponseOrigin = authAdjustResponseObject.additionalData.adjustAuthorisationData;

                                ccTransactionsPGWDTO.TextResponse = authAdjustResponseObject.response;
                                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                ccTransactionsPGWDTO.Authorize = (tipAmount).ToString();
                                ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                                ccTransactionsPGWDTO.Amount = Convert.ToDouble(authAdjustAmount);
                                ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                                ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                                CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                                cCTransactionsPGWBL.Save();

                                //Get Latest Child TransactionPaymentDTO

                                SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                                if (currentCcOrigTransactionsPGWDTO.ResponseID != latestChildCCTransactionsPGWDTO.ResponseID)
                                {
                                    TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL();
                                    List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>> transactionsPaymentsSearchParams = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>();
                                    transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, latestChildCCTransactionsPGWDTO.ResponseID.ToString()));
                                    transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                    List<TransactionPaymentsDTO> transactionPaymentsDTOs = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParams);
                                    if (transactionPaymentsDTOs != null && transactionPaymentsDTOs.Any())
                                    {
                                        TransactionPaymentsDTO latestTransactionPaymentDTO = transactionPaymentsDTOs[0];
                                        transactionPaymentsDTO = latestTransactionPaymentDTO;
                                    }
                                }
                                transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                                transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;

                                SettleChildPayments(preAuthCcOrigTransactionsPGWDTO);
                            }
                        }
                        else
                        {
                            if (!IsForcedSettlement)
                            {
                                frmFinalizeTransaction frmFinalizeTransaction = new frmFinalizeTransaction(utilities, overallTransactionAmount, overallTipAmountEntered, Convert.ToDecimal(baseAmount), Convert.ToDecimal(transactionPaymentsDTO.TipAmount), transactionPaymentsDTO.CreditCardNumber, showMessageDelegate);
                                if (frmFinalizeTransaction.ShowDialog() != DialogResult.Cancel)
                                {
                                    tipAmount = Convert.ToDecimal(frmFinalizeTransaction.TipAmount);
                                }
                                else
                                {
                                    log.LogMethodExit(transactionPaymentsDTO);
                                    throw new Exception(utilities.MessageUtils.getMessage("CANCELLED"));
                                }
                            }
                            else
                            {
                                thr.Start();
                                threadStarted = true;
                                // commented because tip is handled on the terminal
                            }
                            if (tipAmount <= 0)
                            {
                                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                                ccTransactionsPGWDTO.AcctNo = currentCcOrigTransactionsPGWDTO.AcctNo;
                                ccTransactionsPGWDTO.AuthCode = currentCcOrigTransactionsPGWDTO.AuthCode;
                                ccTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                                ccTransactionsPGWDTO.RefNo = currentCcOrigTransactionsPGWDTO.RefNo;
                                ccTransactionsPGWDTO.RecordNo = currentCcOrigTransactionsPGWDTO.RecordNo;
                                ccTransactionsPGWDTO.ResponseOrigin = currentCcOrigTransactionsPGWDTO.ResponseOrigin;

                                ccTransactionsPGWDTO.TextResponse = currentCcOrigTransactionsPGWDTO.TextResponse;
                                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                                ccTransactionsPGWDTO.Authorize = tipAmount.ToString();
                                ccTransactionsPGWDTO.Amount = currentCcOrigTransactionsPGWDTO.Amount;
                                ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                                ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                                SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                                CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                                cCTransactionsPGWBL.Save();

                                transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                                transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                            }
                            else
                            {
                                decimal authAdjustAmount = baseAmount + tipAmount;
                                log.Debug($"authAdjustAmount = {authAdjustAmount}");
                                // go for praauth adjust
                                log.Debug("Pre-auth adjust initiated");
                                log.Info("latestChildCCTransactionsPGWDTO.ResponseOrigin = " + currentCcOrigTransactionsPGWDTO.ResponseOrigin);
                                log.Info("currencyConversionFactor = " + currencyConversionFactor);
                                log.Info("preAuthAdjustApiUrl = " + preAuthAdjustApiUrl);

                                string preauthPaymentId = commandHandler.GetTransactionId(preAuthCcOrigTransactionsPGWDTO.RecordNo);
                                log.Debug("preauthPaymentId: " + preauthPaymentId);
                                result = commandHandler.DoPreAuthorizationAdjustment(authAdjustAmount, preauthPaymentId, currentCcOrigTransactionsPGWDTO.ResponseOrigin, ccRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, preAuthAdjustApiUrl);
                                log.Debug($"Preauth Adjustment response={JsonConvert.SerializeObject(result)}");

                                if (string.IsNullOrWhiteSpace(result))
                                {
                                    log.Error($"MakePayment(): Auth response was null");
                                    throw new Exception("Authorization Failed. No response received.");
                                }

                                AdjustAuthResponseDto authAdjustResponseObject = JsonConvert.DeserializeObject<AdjustAuthResponseDto>(result);
                                log.Info("authAdjustResponseObject = " + JsonConvert.SerializeObject(authAdjustResponseObject));

                                if (authAdjustResponseObject == null)
                                {
                                    log.Error($"MakePayment(): Auth Adjust response deserialization failed");
                                    throw new Exception("Authorization Failed.");
                                }

                                if (!authAdjustResponseObject.response.Equals("Authorised"))
                                {
                                    log.Error($"MakePayment(): Auth Adjust failed");
                                    throw new Exception("Authorization Failed.");
                                }

                                decimal authAdjustuthorizedAmount = authAdjustAmount;

                                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                                ccTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
                                ccTransactionsPGWDTO.AuthCode = authAdjustResponseObject.additionalData.authCode;
                                ccTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                                ccTransactionsPGWDTO.RefNo = authAdjustResponseObject.additionalData.merchantReference;
                                ccTransactionsPGWDTO.RecordNo = authAdjustResponseObject.pspReference;
                                ccTransactionsPGWDTO.ResponseOrigin = authAdjustResponseObject.additionalData.adjustAuthorisationData;

                                ccTransactionsPGWDTO.TextResponse = authAdjustResponseObject.response;
                                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                                ccTransactionsPGWDTO.Authorize = tipAmount.ToString();
                                ccTransactionsPGWDTO.Amount = Convert.ToDouble(authAdjustAmount);
                                ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                                ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                                SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                                CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                                cCTransactionsPGWBL.Save();

                                transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                                transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                                transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                                transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                                transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                                transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                            }
                        }


                    }
                    catch (Exception ex)
                    {
                        if (statusDisplayUi != null)
                            statusDisplayUi.DisplayText("Error occured while performing settlement");
                        log.Error("Error occured while performing settlement", ex);
                        log.LogMethodExit(null, "Throwing Exception " + ex);
                        throw;
                    }
                    finally
                    {
                        if (statusDisplayUi != null && threadStarted)
                        {
                            statusDisplayUi.CloseStatusWindow();
                        }
                    }
                }
                else
                {
                    //statusDisplayUi.DisplayText("Invalid payment data.");
                    throw new Exception(utilities.MessageUtils.getMessage("Invalid payment data."));
                }
                log.LogMethodExit(transactionPaymentsDTO);
                return transactionPaymentsDTO;
            }
            finally
            {
                if (statusDisplayUi != null)
                {
                    statusDisplayUi.CloseStatusWindow();
                }
            }
        }

        private void SettleChildPayments(CCTransactionsPGWDTO preAuthCCTransactionsPGWDTO)
        {
            log.LogMethodEntry();
            log.Info("SettleChildPayments method -Enter");
            if (preAuthCCTransactionsPGWDTO.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString())
            {
                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, TransactionType.AUTHORIZATION.ToString()));
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, preAuthCCTransactionsPGWDTO.ResponseID.ToString()));
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Any())
                {
                    foreach (CCTransactionsPGWDTO cCTransactionsPGWDTO in cCTransactionsPGWDTOList)
                    {
                        cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();
                    }
                }
            }
            log.Info("SettleChildPayments method -Exit");
            log.LogMethodExit();
        }
        public override TransactionPaymentsDTO SettleTransactionPayment(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.Info("SettleTransactionPayment method- Enter");
            try
            {

                CCTransactionsPGWDTO preAuthCcOrigTransactionsPGWDTO = null;
                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(transactionPaymentsDTO.CCResponseId);
                CCTransactionsPGWDTO currentCcOrigTransactionsPGWDTO = ccTransactionsPGWBL.CCTransactionsPGWDTO;
                log.Info("currentCcOrigTransactionsPGWDTO = " + JsonConvert.SerializeObject(currentCcOrigTransactionsPGWDTO));

                string result;
                if (currentCcOrigTransactionsPGWDTO.ParentResponseId != null && currentCcOrigTransactionsPGWDTO.ParentResponseId > -1)
                {
                    // get parentRespid = responseid of preauth
                    // fetch preauth 
                    ccTransactionsPGWBL = new CCTransactionsPGWBL(Convert.ToInt32(currentCcOrigTransactionsPGWDTO.ParentResponseId));
                    preAuthCcOrigTransactionsPGWDTO = ccTransactionsPGWBL.CCTransactionsPGWDTO;
                    log.LogVariableState("auth adjustment ccOrigTransactionsPGWDTO", JsonConvert.SerializeObject(preAuthCcOrigTransactionsPGWDTO));
                }
                else
                {
                    // ccOrigTransactionsPGWDTO => is for preauth
                    preAuthCcOrigTransactionsPGWDTO = currentCcOrigTransactionsPGWDTO;
                    log.LogVariableState("PreAuth itself is  a ccOrigTransactionsPGWDTO", JsonConvert.SerializeObject(preAuthCcOrigTransactionsPGWDTO));
                }
                CCRequestPGWDTO ccRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_PAYMENT);
                string serviceId = ccRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
                log.Info("serviceId= " + serviceId);



                decimal baseAmount = Convert.ToDecimal(transactionPaymentsDTO.Amount);
                decimal tipAmount = Convert.ToDecimal(transactionPaymentsDTO.TipAmount);
                log.Info("baseAmount= " + baseAmount);
                log.Info("tipAmount= " + tipAmount);

                AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, isTipAllowed, false, false, applicationInfo, adyenTransactionTimeout, storeName);



                if (currentCcOrigTransactionsPGWDTO.TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString()
                    || currentCcOrigTransactionsPGWDTO.ResponseID != preAuthCcOrigTransactionsPGWDTO.ResponseID)
                {
                    CCTransactionsPGWDTO latestChildCCTransactionsPGWDTO = GetLatestChildCCTransactionPGWDTO(transactionPaymentsDTO.TransactionId, transactionPaymentsDTO.SplitId, preAuthCcOrigTransactionsPGWDTO);
                    baseAmount = Convert.ToDecimal(latestChildCCTransactionsPGWDTO.Amount);



                    if (tipAmount <= 0)
                    {
                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.AcctNo = latestChildCCTransactionsPGWDTO.AcctNo;
                        ccTransactionsPGWDTO.AuthCode = latestChildCCTransactionsPGWDTO.AuthCode;
                        ccTransactionsPGWDTO.CardType = latestChildCCTransactionsPGWDTO.CardType;
                        ccTransactionsPGWDTO.RefNo = latestChildCCTransactionsPGWDTO.RefNo;
                        ccTransactionsPGWDTO.RecordNo = latestChildCCTransactionsPGWDTO.RecordNo;
                        ccTransactionsPGWDTO.ResponseOrigin = latestChildCCTransactionsPGWDTO.ResponseOrigin;

                        ccTransactionsPGWDTO.TextResponse = latestChildCCTransactionsPGWDTO.TextResponse;
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                        ccTransactionsPGWDTO.Authorize = tipAmount.ToString();
                        ccTransactionsPGWDTO.Amount = latestChildCCTransactionsPGWDTO.Amount;
                        ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                        ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                        SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();

                        transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                        transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                        transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                    }
                    else
                    {
                        decimal authAdjustAmount = baseAmount + tipAmount;
                        log.Debug($"authAdjustAmount = {authAdjustAmount}");
                        // go for praauth adjust
                        log.Debug("Pre-auth adjust initiated");
                        string preauthPaymentId = commandHandler.GetTransactionId(preAuthCcOrigTransactionsPGWDTO.RecordNo);
                        log.Debug("preauthPaymentId: " + preauthPaymentId);
                        log.Info("latestChildCCTransactionsPGWDTO.ResponseOrigin = " + latestChildCCTransactionsPGWDTO.ResponseOrigin);
                        log.Info("currencyConversionFactor = " + currencyConversionFactor);
                        log.Info("preAuthAdjustApiUrl = " + preAuthAdjustApiUrl);

                        result = commandHandler.DoPreAuthorizationAdjustment(authAdjustAmount, preauthPaymentId, latestChildCCTransactionsPGWDTO.ResponseOrigin, ccRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, preAuthAdjustApiUrl);
                        log.Debug($"Preauth Adjustment response={JsonConvert.SerializeObject(result)}");

                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"MakePayment(): Auth response was null");
                            throw new Exception("Authorization Failed. No response received.");
                        }

                        AdjustAuthResponseDto authAdjustResponseObject = JsonConvert.DeserializeObject<AdjustAuthResponseDto>(result);
                        log.LogVariableState("authAdjustResponseObject", JsonConvert.SerializeObject(authAdjustResponseObject));

                        if (authAdjustResponseObject == null)
                        {
                            log.Error($"MakePayment(): Auth Adjust response deserialization failed");
                            throw new Exception("Authorization Failed.");
                        }

                        if (!authAdjustResponseObject.response.Equals("Authorised"))
                        {
                            log.Error($"MakePayment(): Auth Adjust failed");
                            throw new Exception("Authorization Failed.");
                        }

                        //Getting Latest Pre-Auth TransactionPaymentDTO
                        if (currentCcOrigTransactionsPGWDTO.ResponseID != latestChildCCTransactionsPGWDTO.ResponseID)
                        {
                            TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL();
                            List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>> transactionsPaymentsSearchParams = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>();
                            transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, latestChildCCTransactionsPGWDTO.ResponseID.ToString()));
                            transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                            List<TransactionPaymentsDTO> transactionPaymentsDTOs = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParams);
                            if (transactionPaymentsDTOs != null && transactionPaymentsDTOs.Any())
                            {
                                TransactionPaymentsDTO latestTransactionPaymentDTO = transactionPaymentsDTOs[0];
                                transactionPaymentsDTO = latestTransactionPaymentDTO;
                            }
                        }

                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
                        ccTransactionsPGWDTO.AuthCode = authAdjustResponseObject.additionalData.authCode;
                        ccTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                        ccTransactionsPGWDTO.RefNo = authAdjustResponseObject.additionalData.merchantReference;
                        ccTransactionsPGWDTO.RecordNo = authAdjustResponseObject.pspReference;
                        ccTransactionsPGWDTO.ResponseOrigin = authAdjustResponseObject.additionalData.adjustAuthorisationData;

                        ccTransactionsPGWDTO.TextResponse = authAdjustResponseObject.response;
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.Authorize = (tipAmount).ToString();
                        ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                        ccTransactionsPGWDTO.Amount = Convert.ToDouble(authAdjustAmount);
                        ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                        ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();

                        //Get Latest Child TransactionPaymentDTO

                        SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);
                        transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                        transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                        transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                    }



                }
                else
                {
                    if (tipAmount <= 0)
                    {
                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
                        ccTransactionsPGWDTO.AuthCode = currentCcOrigTransactionsPGWDTO.AuthCode;
                        ccTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                        ccTransactionsPGWDTO.RefNo = currentCcOrigTransactionsPGWDTO.RefNo;
                        ccTransactionsPGWDTO.RecordNo = currentCcOrigTransactionsPGWDTO.RecordNo;
                        ccTransactionsPGWDTO.ResponseOrigin = currentCcOrigTransactionsPGWDTO.ResponseOrigin;

                        ccTransactionsPGWDTO.TextResponse = currentCcOrigTransactionsPGWDTO.TextResponse;
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                        ccTransactionsPGWDTO.Authorize = tipAmount.ToString();
                        ccTransactionsPGWDTO.Amount = currentCcOrigTransactionsPGWDTO.Amount;
                        ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                        ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                        SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();

                        transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                        transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                        transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                    }
                    else
                    {
                        decimal authAdjustAmount = baseAmount + tipAmount;
                        log.Debug($"authAdjustAmount = {authAdjustAmount}");
                        // go for praauth adjust
                        log.Debug("Pre-auth adjust initiated");
                        string preauthPaymentId = commandHandler.GetTransactionId(preAuthCcOrigTransactionsPGWDTO.RecordNo);
                        log.Debug("preauthPaymentId: " + preauthPaymentId);
                        log.Info("latestChildCCTransactionsPGWDTO.ResponseOrigin = " + currentCcOrigTransactionsPGWDTO.ResponseOrigin);
                        log.Info("currencyConversionFactor = " + currencyConversionFactor);
                        log.Info("preAuthAdjustApiUrl = " + preAuthAdjustApiUrl);

                        result = commandHandler.DoPreAuthorizationAdjustment(authAdjustAmount, preauthPaymentId, currentCcOrigTransactionsPGWDTO.ResponseOrigin, ccRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, preAuthAdjustApiUrl);
                        log.Debug($"Preauth Adjustment response={JsonConvert.SerializeObject(result)}");

                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"MakePayment(): Auth response was null");
                            throw new Exception("Authorization Failed. No response received.");
                        }

                        AdjustAuthResponseDto authAdjustResponseObject = JsonConvert.DeserializeObject<AdjustAuthResponseDto>(result);
                        log.LogVariableState("authAdjustResponseObject", JsonConvert.SerializeObject(authAdjustResponseObject));

                        if (authAdjustResponseObject == null)
                        {
                            log.Error($"MakePayment(): Auth Adjust response deserialization failed");
                            throw new Exception("Authorization Failed.");
                        }

                        if (!authAdjustResponseObject.response.Equals("Authorised"))
                        {
                            log.Error($"MakePayment(): Auth Adjust failed");
                            throw new Exception("Authorization Failed.");
                        }

                        decimal authAdjustuthorizedAmount = authAdjustAmount;

                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = ccRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(currentCcOrigTransactionsPGWDTO.AcctNo);
                        ccTransactionsPGWDTO.AuthCode = authAdjustResponseObject.additionalData.authCode;
                        ccTransactionsPGWDTO.CardType = currentCcOrigTransactionsPGWDTO.CardType;
                        ccTransactionsPGWDTO.RefNo = authAdjustResponseObject.additionalData.merchantReference;
                        ccTransactionsPGWDTO.RecordNo = authAdjustResponseObject.pspReference;
                        ccTransactionsPGWDTO.ResponseOrigin = authAdjustResponseObject.additionalData.adjustAuthorisationData;

                        ccTransactionsPGWDTO.TextResponse = authAdjustResponseObject.response;
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.Purchase = tipAmount.ToString();
                        ccTransactionsPGWDTO.Authorize = tipAmount.ToString();
                        ccTransactionsPGWDTO.Amount = Convert.ToDouble(authAdjustAmount);
                        ccTransactionsPGWDTO.TipAmount = tipAmount.ToString();
                        ccTransactionsPGWDTO.ParentResponseId = preAuthCcOrigTransactionsPGWDTO.ResponseID;


                        SendPrintReceiptRequest(transactionPaymentsDTO, ccTransactionsPGWDTO);

                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();

                        transactionPaymentsDTO.CCResponseId = cCTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;
                        transactionPaymentsDTO.CreditCardName = cCTransactionsPGWBL.CCTransactionsPGWDTO.CardType;
                        transactionPaymentsDTO.Reference = cCTransactionsPGWBL.CCTransactionsPGWDTO.RefNo;
                        transactionPaymentsDTO.TipAmount = Convert.ToDouble(cCTransactionsPGWBL.CCTransactionsPGWDTO.TipAmount);
                        transactionPaymentsDTO.CreditCardNumber = cCTransactionsPGWBL.CCTransactionsPGWDTO.AcctNo;
                        transactionPaymentsDTO.CreditCardAuthorization = ccTransactionsPGWBL.CCTransactionsPGWDTO.AuthCode;
                    }
                }

            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw ex;
            }
            log.LogMethodExit(transactionPaymentsDTO);
            log.Info("SettleTransactionPayment method- Exit");
            return transactionPaymentsDTO;
        }

        private CCTransactionsPGWDTO GetPreAuthorizationCCTransactionsPGWDTO(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            CCTransactionsPGWDTO preAuthorizationCCTransactionsPGWDTO = null;
            if (utilities.getParafaitDefaults("ALLOW_CREDIT_CARD_AUTHORIZATION").Equals("Y"))
            {
                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRANSACTION_ID, transactionPaymentsDTO.TransactionId.ToString()));
                if (transactionPaymentsDTO.SplitId != -1)
                {
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SPLIT_ID, transactionPaymentsDTO.SplitId.ToString()));
                }
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, TransactionType.TATokenRequest.ToString()));
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
                {
                    preAuthorizationCCTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                }
            }
            log.LogMethodExit(preAuthorizationCCTransactionsPGWDTO);
            return preAuthorizationCCTransactionsPGWDTO;
        }

        private CCTransactionsPGWDTO GetLatestChildCCTransactionPGWDTO(int transactionId, int splitId, CCTransactionsPGWDTO preAuthCCTransactionsPGWDTO)
        {
            log.LogMethodEntry();
            CCTransactionsPGWDTO latestChildCCTransactionsPGWDTO = null;
            CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
            List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRANSACTION_ID, transactionId.ToString()));
            if (splitId != -1)
            {
                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SPLIT_ID, splitId.ToString()));
            }

            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, preAuthCCTransactionsPGWDTO.ResponseID.ToString()));
            List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
            if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
            {
                latestChildCCTransactionsPGWDTO = cCTransactionsPGWDTOList.OrderByDescending(x => x.ResponseID).FirstOrDefault();
            }

            log.LogMethodExit(latestChildCCTransactionsPGWDTO);
            return latestChildCCTransactionsPGWDTO;
        }

        public override bool IsTipAllowed(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            bool isAllowed = false;
            log.LogMethodEntry(transactionPaymentsDTO);
            log.Info("IsTipAllowed method- Enter");
            log.Info("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));

            if (isAuthEnabled)
            {
                if (transactionPaymentsDTO != null && transactionPaymentsDTO.CCResponseId != -1
                    && transactionPaymentsDTO.TipAmount <= 0)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, PaymentGatewayTransactionType.AUTHORIZATION.ToString()));
                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                    if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
                    {
                        if (cCTransactionsPGWDTOList[0].ParentResponseId == null || cCTransactionsPGWDTOList[0].ParentResponseId <= -1)
                        {
                            isAllowed = true;
                        }
                        else
                        {
                            CCTransactionsPGWDTO latestChildCCTransactionsPGWDTO = null;
                            cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                            searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRANSACTION_ID, transactionPaymentsDTO.TransactionId.ToString()));
                            if (transactionPaymentsDTO.SplitId != -1)
                            {
                                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SPLIT_ID, transactionPaymentsDTO.SplitId.ToString()));
                            }

                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, cCTransactionsPGWDTOList[0].ParentResponseId.ToString()));
                            List<CCTransactionsPGWDTO> childCCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                            if (childCCTransactionsPGWDTOList != null && childCCTransactionsPGWDTOList.Count > 0)
                            {
                                latestChildCCTransactionsPGWDTO = childCCTransactionsPGWDTOList.OrderByDescending(x => x.ResponseID).FirstOrDefault();
                                if (latestChildCCTransactionsPGWDTO.ResponseID == cCTransactionsPGWDTOList[0].ResponseID)
                                {
                                    isAllowed = true;
                                }
                            }

                        }
                    }
                    if (!isAllowed)
                    {
                        searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                        searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));
                        searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, PaymentGatewayTransactionType.CAPTURE.ToString()));
                        cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                        if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Any())
                        {
                            CCTransactionsPGWDTO latestChildCCTransactionsPGWDTO = null;
                            cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                            searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRANSACTION_ID, transactionPaymentsDTO.TransactionId.ToString()));
                            if (transactionPaymentsDTO.SplitId != -1)
                            {
                                searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SPLIT_ID, transactionPaymentsDTO.SplitId.ToString()));
                            }

                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, cCTransactionsPGWDTOList[0].ParentResponseId.ToString()));
                            List<CCTransactionsPGWDTO> childCCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                            if (childCCTransactionsPGWDTOList != null && childCCTransactionsPGWDTOList.Count > 0)
                            {
                                latestChildCCTransactionsPGWDTO = childCCTransactionsPGWDTOList.OrderByDescending(x => x.ResponseID).FirstOrDefault();
                                log.Info("latestChildCCTransactionsPGWDTO = " + latestChildCCTransactionsPGWDTO);
                                if (latestChildCCTransactionsPGWDTO.ResponseID == cCTransactionsPGWDTOList[0].ResponseID)
                                {
                                    isAllowed = true;
                                }
                            }
                        }
                    }
                }
            }
            log.Info("isAllowed = " + isAllowed);
            log.LogMethodExit(isAllowed);
            log.Info("IsTipAllowed method- Exit");
            return isAllowed;
        }


        public override bool IsSettlementPending(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.Info("IsSettlementPending method Enter");
            bool returnValue = false;
            if (isAuthEnabled)
            {
                if (transactionPaymentsDTO != null && transactionPaymentsDTO.CCResponseId != -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));
                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                    if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Any())
                    {
                        if (cCTransactionsPGWDTOList[0].TranCode == PaymentGatewayTransactionType.AUTHORIZATION.ToString())
                        {
                            if (cCTransactionsPGWDTOList[0].ParentResponseId == null || cCTransactionsPGWDTOList[0].ParentResponseId <= -1)
                            {
                                returnValue = true;
                            }
                        }
                        if (cCTransactionsPGWDTOList[0].TranCode == PaymentGatewayTransactionType.TATokenRequest.ToString())
                        {
                            searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));
                            searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, PaymentGatewayTransactionType.AUTHORIZATION.ToString()));
                            List<CCTransactionsPGWDTO> childCCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                            if (childCCTransactionsPGWDTOList != null && childCCTransactionsPGWDTOList.Any())
                            {
                                returnValue = true;
                            }
                        }
                    }
                }
            }
            log.LogMethodExit(returnValue);
            log.Info("IsSettlementPending method Exit");
            return returnValue;
        }



        private CCTransactionsPGWDTO PerformLasttrxCheck(CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(cCRequestPGWDTO);
            log.Info("PerformLasttrxCheck method- Enter");
            CCTransactionsPGWDTO ccTransactionsPGWDTOResponse = new CCTransactionsPGWDTO();
            string lasttrxServiceId = cCRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
            string serviceId = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 10);
            AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, false, false, false, applicationInfo, adyenTransactionTimeout, storeName);
            string messageCategory = "Payment";
            if (LastTrxCheckMessageCategory.ContainsKey(cCRequestPGWDTO.TransactionType))
            {
                messageCategory = LastTrxCheckMessageCategory[cCRequestPGWDTO.TransactionType];
            }
            log.Info("posId = " + posId);
            log.Info("messageCategory = " + messageCategory);
            log.Info("lasttrxServiceId = " + lasttrxServiceId);

            string resultPayment = commandHandler.DoCheckLastTransactionStatus(saleId: posId, p_serviceId: lasttrxServiceId, messageCategory: messageCategory);
            log.Debug($"resultPayment={JsonConvert.SerializeObject(resultPayment)}");
            if (string.IsNullOrWhiteSpace(resultPayment))
            {
                log.Error($"SendLastTransactionStatusCheckRequest(): response was null");
                throw new Exception("No response received."); // TBC
            }
            else
            {
                GetTransactionStatusResponseDto responseObject = JsonConvert.DeserializeObject<GetTransactionStatusResponseDto>(resultPayment);
                log.Debug($"responseObject={JsonConvert.SerializeObject(responseObject)}");
                if (responseObject == null)
                {
                    log.Error($"SendLastTransactionStatusCheckRequest(): response object was null. Deserialization failed.");
                    //ccTransactionsPGWDTOResponse.TextResponse = "Txn not found";
                    //ccTransactionsPGWDTOResponse.TransactionDatetime = utilities.getServerTime();
                    throw new Exception("Last transaction check failed.Txn not found");
                }
                else
                {

                    // check for other errors
                    if (responseObject.SaleToPOIRequest != null)
                    {
                        // some error came
                        string errorMsg = MessageContainerList.GetMessage(utilities.ExecutionContext, "Failed to communicate with the terminal");
                        Dictionary<string, string> errorDetails = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIRequest.EventNotification.EventDetails);
                        if (errorDetails != null && errorDetails.ContainsKey("message"))
                        {
                            errorMsg = $"{responseObject.SaleToPOIRequest.EventNotification.EventToNotify} | {errorDetails["message"]}";
                            log.Error(errorMsg);
                        }
                        throw new Exception(errorMsg);

                    }

                    if (responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition != null && responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition == "InProgress")
                    {
                        throw new Exception("Transaction is in progress.");
                    }

                    if (!responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals(PAYMENT_SUCCESS_STATUS))
                    {
                        log.Error(responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result);
                        log.Error("Last transaction status is not available." + ((cCRequestPGWDTO == null) ? "" : " RequestId:" + cCRequestPGWDTO.RequestID + ", Amount:" + cCRequestPGWDTO.POSAmount));//ccrequestId etc
                        log.Error("Last transaction check failed");
                        throw new Exception("Last transaction status is not available.");
                    }
                    else
                    {
                        Repeatedresponsemessagebody response = responseObject.SaleToPOIResponse.TransactionStatusResponse.RepeatedMessageResponse.RepeatedResponseMessageBody;
                        decimal resamount = 0;
                        decimal tipAmount = 0;
                        ccTransactionsPGWDTOResponse.InvoiceNo = response.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                        if (response.PaymentResponse.PaymentResult != null)
                        {
                            if (response.PaymentResponse.PaymentResult.AmountsResp != null)
                            {
                                resamount = Convert.ToDecimal(response.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                tipAmount = Convert.ToDecimal(response.PaymentResponse.PaymentResult.AmountsResp.TipAmount);
                            }
                            ccTransactionsPGWDTOResponse.AcctNo = GetMaskedCardNumber(response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                            ccTransactionsPGWDTOResponse.AuthCode = response.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                            ccTransactionsPGWDTOResponse.CardType = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                            ccTransactionsPGWDTOResponse.CaptureStatus = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode != null ? response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0] : null;
                        }


                        ccTransactionsPGWDTOResponse.RecordNo = response.PaymentResponse.POIData.POITransactionID.TransactionID;
                        Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(response.PaymentResponse.Response.AdditionalResponse);
                        if (additionalResponseParams.Any())
                        {
                            string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                            acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                            acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";

                            ccTransactionsPGWDTOResponse.AcqRefData = acqrefData;
                            ccTransactionsPGWDTOResponse.RefNo = additionalResponseParams.ContainsKey("adyen_giving_service_id") ? additionalResponseParams["adyen_giving_service_id"] : "";
                        }

                        ccTransactionsPGWDTOResponse.TextResponse = response.PaymentResponse.Response.Result;
                        ccTransactionsPGWDTOResponse.TranCode = PaymentGatewayTransactionType.CAPTURE.ToString(); // TBC Tx type
                        ccTransactionsPGWDTOResponse.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTOResponse.Authorize = resamount.ToString();
                        ccTransactionsPGWDTOResponse.TipAmount = tipAmount.ToString();
                        if (!response.PaymentResponse.Response.Result.Equals("Success"))
                        {
                            log.Error("Last transaction check failed");
                            string errorMessage = string.IsNullOrWhiteSpace(responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition) ? "" : $"ErrorCondition: {responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition}";
                            if (additionalResponseParams != null && additionalResponseParams.ContainsKey("message"))
                            {
                                errorMessage += $" Message: {additionalResponseParams["message"]}";
                            }
                            if (additionalResponseParams != null && additionalResponseParams.ContainsKey("refusalReason"))
                            {
                                errorMessage += $" Refusal Reason: {additionalResponseParams["refusalReason"]}";
                            }
                            log.Error($"MakePayment(): Auth failed due to {errorMessage}");
                            ccTransactionsPGWDTOResponse.DSIXReturnCode =
                               errorMessage;
                            log.Error("Last transaction check failed. Txn not found");
                            //throw new Exception("Last transaction check failed. Txn not found");
                        }
                    }
                }
            }
            log.LogMethodExit(ccTransactionsPGWDTOResponse);
            return ccTransactionsPGWDTOResponse;
        }

        public override void SendLastTransactionStatusCheckRequest(CCRequestPGWDTO cCRequestPGWDTO, CCTransactionsPGWDTO cCTransactionsPGWDTO)
        {
            log.LogMethodEntry(cCRequestPGWDTO, cCTransactionsPGWDTO);
            try
            {
                //Form activeForm = GetActiveForm();
                TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL(utilities.ExecutionContext);
                List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>> transactionsPaymentsSearchParams = new List<KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>>();
                CCTransactionsPGWListBL ccTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> ccTransactionsSearchParams = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();

                statusDisplayUi = DisplayUIFactory.GetStatusUI(utilities.ExecutionContext, isUnattended, utilities.MessageUtils.getMessage("Checking the transaction status" + ((cCRequestPGWDTO != null) ? " of TrxId:" + cCRequestPGWDTO.InvoiceNo + " Amount:" + cCRequestPGWDTO.POSAmount : ".")), "Adyen Payment Gateway");
                //{
                statusDisplayUi.EnableCancelButton(false);
                Thread thr = new Thread(statusDisplayUi.ShowStatusWindow);
                try
                {
                    thr.Start();
                    //Form form = statusDisplayUi as Form;
                    //form.Show(activeForm);
                    //SetNativeEnabled(activeForm, false);
                    statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage(1008));
                    string serviceId = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 10);
                    string lasttrxServiceId = cCRequestPGWDTO.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
                    AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, false, false, false, applicationInfo, adyenTransactionTimeout, storeName);
                    CCTransactionsPGWDTO ccTransactionsPGWDTOResponse = null;


                    if (cCTransactionsPGWDTO != null)
                    {
                        log.Debug("cCTransactionsPGWDTO is not null");
                        List<CCTransactionsPGWDTO> cCTransactionsPGWDTOcapturedList = null;
                        if (!string.IsNullOrEmpty(cCTransactionsPGWDTO.RecordNo) && !cCTransactionsPGWDTO.TranCode.Equals(TransactionType.REFUND.ToString()) && !cCTransactionsPGWDTO.TranCode.Equals(TransactionType.VOID.ToString()))
                        {
                            if (cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.AUTHORIZATION.ToString()))
                            {
                                ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, cCTransactionsPGWDTO.ResponseID.ToString()));
                                ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = ccTransactionsPGWListBL.GetCCTransactionsPGWDTOList(ccTransactionsSearchParams);
                                if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
                                {
                                    if (cCTransactionsPGWDTOList[0].TranCode.Equals(PaymentGatewayTransactionType.CAPTURE.ToString()))
                                    {
                                        ccTransactionsSearchParams = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                                        ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, cCTransactionsPGWDTOList[0].ResponseID.ToString()));
                                        ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                        cCTransactionsPGWDTOcapturedList = ccTransactionsPGWListBL.GetCCTransactionsPGWDTOList(ccTransactionsSearchParams);
                                        if (cCTransactionsPGWDTOcapturedList != null && cCTransactionsPGWDTOcapturedList.Count > 0)
                                        {
                                            log.Debug("The authorized transaction is captured.");
                                            return;
                                        }
                                    }
                                    else if (cCTransactionsPGWDTOList[0].TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
                                    {
                                        ccTransactionsSearchParams = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                                        ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, cCTransactionsPGWDTOList[0].ResponseID.ToString()));
                                        ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                        cCTransactionsPGWDTOcapturedList = ccTransactionsPGWListBL.GetCCTransactionsPGWDTOList(ccTransactionsSearchParams);
                                        if (cCTransactionsPGWDTOcapturedList != null && cCTransactionsPGWDTOcapturedList.Count > 0)
                                        {
                                            log.Debug("The authorized transaction is adjusted for tip.");
                                            return;
                                        }
                                    }
                                    transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, cCTransactionsPGWDTOList[0].ResponseID.ToString()));
                                    transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                    List<TransactionPaymentsDTO> transactionPaymentsDTOs = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParams);
                                    if (transactionPaymentsDTOs == null || transactionPaymentsDTOs.Count == 0)
                                    {
                                        cCTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                                    }
                                    else
                                    {
                                        log.Debug("The capture/tip adjusted transaction exists for the authorization request with requestId:" + cCTransactionsPGWDTO.InvoiceNo + " and its upto date");
                                        return;
                                    }
                                }
                            }
                            else if (cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.CAPTURE.ToString()))
                            {
                                ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, cCTransactionsPGWDTO.ResponseID.ToString()));
                                ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = ccTransactionsPGWListBL.GetCCTransactionsPGWDTOList(ccTransactionsSearchParams);

                                if (cCTransactionsPGWDTOList != null && cCTransactionsPGWDTOList.Count > 0)
                                {
                                    if (cCTransactionsPGWDTOList[0].TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
                                    {
                                        ccTransactionsSearchParams = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                                        ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ORIGIN, cCTransactionsPGWDTOList[0].ResponseID.ToString()));
                                        ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                        cCTransactionsPGWDTOcapturedList = ccTransactionsPGWListBL.GetCCTransactionsPGWDTOList(ccTransactionsSearchParams);
                                        if (cCTransactionsPGWDTOcapturedList != null && cCTransactionsPGWDTOcapturedList.Count > 0)
                                        {
                                            log.Debug("The captured transaction is adjusted for tip.");
                                            return;
                                        }
                                    }
                                    transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, cCTransactionsPGWDTOList[0].ResponseID.ToString()));
                                    transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                                    List<TransactionPaymentsDTO> transactionPaymentsDTOs = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParams);
                                    if (transactionPaymentsDTOs == null || transactionPaymentsDTOs.Count == 0)
                                    {
                                        cCTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                                    }
                                    else
                                    {
                                        log.Debug("The tip adjusted transaction exists for the capture request with requestId:" + cCTransactionsPGWDTO.InvoiceNo + " and its upto date");
                                        return;
                                    }
                                }

                            }
                            else if (cCTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
                            {
                                log.Debug("credit card transaction is tip adjustment.");
                                log.LogMethodExit(true);
                                return;
                            }
                            statusDisplayUi.DisplayText(utilities.MessageUtils.getMessage("Last Transaction Status Check is Processing..."));
                            //DisplayInDevice(displayCommand, utilities.MessageUtils.getMessage("Last Transaction Status Check is Processing..."));

                            // TBC I need to find ccRequestPgwdto row linked to this cCTransactionsPGWDTO.InvoiceNo
                            //serviceId = ccRequestPGWDTO.GUID.ToString().Replace("-", string.Empty).Substring(0, 10);
                            string messageCategory = "Payment";
                            if (LastTrxCheckMessageCategory.ContainsKey(cCRequestPGWDTO.TransactionType))
                            {
                                messageCategory = LastTrxCheckMessageCategory[cCRequestPGWDTO.TransactionType];
                            }
                            string resultPayment = commandHandler.DoCheckLastTransactionStatus(saleId: posId, p_serviceId: lasttrxServiceId, messageCategory: messageCategory);
                            log.Debug($"resultPayment={resultPayment}");
                            if (string.IsNullOrWhiteSpace(resultPayment))
                            {
                                log.Error($"SendLastTransactionStatusCheckRequest(): response was null");
                                return;
                                //throw new Exception("No response received."); // TBC do we have to throw exception?
                            }
                            else
                            {
                                GetTransactionStatusResponseDto responseObject = JsonConvert.DeserializeObject<GetTransactionStatusResponseDto>(resultPayment);
                                log.Debug($"responseObject={JsonConvert.SerializeObject(responseObject)}");
                                if (responseObject == null)
                                {
                                    log.Error($"SendLastTransactionStatusCheckRequest(): response object was null. Deserialization failed.");
                                    log.Error("Last transaction status is not available." + ((cCRequestPGWDTO == null) ? "" : " RequestId:" + cCRequestPGWDTO.RequestID + ", Amount:" + cCRequestPGWDTO.POSAmount));
                                    return;
                                    //throw new Exception("Last transaction check failed"); // TBC
                                }
                                else
                                {
                                    // check for other errors
                                    if (responseObject.SaleToPOIRequest != null)
                                    {
                                        // some error came
                                        string errorMsg = "Failed to communicate with the terminal";
                                        Dictionary<string, string> errorDetails = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIRequest.EventNotification.EventDetails);
                                        if (errorDetails != null && errorDetails.ContainsKey("message"))
                                        {
                                            errorMsg = $"{responseObject.SaleToPOIRequest.EventNotification.EventToNotify} | {errorDetails["message"]}";
                                            log.Error(errorMsg);
                                            //throw new Exception(errorMsg);
                                        }
                                        return;
                                    }
                                    if (!responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals(PAYMENT_SUCCESS_STATUS))
                                    {
                                        log.Error(responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result);
                                        log.Error("Last transaction status is not available." + ((cCRequestPGWDTO == null) ? "" : " RequestId:" + cCRequestPGWDTO.RequestID + ", Amount:" + cCRequestPGWDTO.POSAmount));//ccrequestId etc
                                        log.Error("Last transaction check failed");
                                        return;
                                    }
                                    else
                                    {
                                        Repeatedresponsemessagebody response = responseObject.SaleToPOIResponse.TransactionStatusResponse.RepeatedMessageResponse.RepeatedResponseMessageBody;
                                        decimal resamount = 0;
                                        decimal tipAmount = 0;
                                        ccTransactionsPGWDTOResponse = new CCTransactionsPGWDTO();
                                        if (response.PaymentResponse.PaymentResult != null)
                                        {
                                            if (response.PaymentResponse.PaymentResult.AmountsResp != null)
                                            {
                                                resamount = Convert.ToDecimal(response.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                                tipAmount = Convert.ToDecimal(response.PaymentResponse.PaymentResult.AmountsResp.TipAmount);
                                            }
                                            ccTransactionsPGWDTOResponse.AcctNo = GetMaskedCardNumber(response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                                            ccTransactionsPGWDTOResponse.AuthCode = response.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                                            ccTransactionsPGWDTOResponse.CardType = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                                            ccTransactionsPGWDTOResponse.CaptureStatus = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0];
                                        }

                                        //ccTransactionsPGWDTOResponse.RefNo = response.PaymentResponse.SaleData.SaleTransactionID.TransactionID;

                                        ccTransactionsPGWDTOResponse.RecordNo = response.PaymentResponse.POIData.POITransactionID.TransactionID;
                                        //ccTransactionsPGWDTOResponse.AcqRefData = "AID:" + responsePaymentObject.payment.cardTransaction.extra.applicationIdentifier + "|Reference Id:" + responsePaymentObject.payment.cardTransaction.referenceId;
                                        Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(response.PaymentResponse.Response.AdditionalResponse);

                                        if (additionalResponseParams.Any())
                                        {
                                            string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                                            acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                                            acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";

                                            ccTransactionsPGWDTOResponse.AcqRefData = acqrefData;
                                            ccTransactionsPGWDTOResponse.RefNo = additionalResponseParams.ContainsKey("adyen_giving_service_id") ? additionalResponseParams["adyen_giving_service_id"] : "";
                                        }

                                        ccTransactionsPGWDTOResponse.TextResponse = response.PaymentResponse.Response.Result;
                                        if (!response.PaymentResponse.Response.Result.Equals("Success"))
                                        {
                                            ccTransactionsPGWDTOResponse.DSIXReturnCode = additionalResponseParams["message"];
                                        }
                                        ccTransactionsPGWDTOResponse.TranCode = cCRequestPGWDTO.TransactionType; // TBC tx type
                                        ccTransactionsPGWDTOResponse.TransactionDatetime = utilities.getServerTime();
                                        ccTransactionsPGWDTOResponse.Authorize = resamount.ToString();
                                        ccTransactionsPGWDTOResponse.TipAmount = tipAmount.ToString();
                                        ccTransactionsPGWDTOResponse.ProcessData = getProcessData(response.PaymentResponse.POIData.POITransactionID.TimeStamp);

                                        // check if donation performed
                                        if (isCreditCardDonationEnabled && additionalResponseParams.ContainsKey("adyen_giving_service_id") && !string.IsNullOrWhiteSpace(additionalResponseParams["adyen_giving_service_id"]))
                                        {
                                            CheckDonation(cCTransactionsPGWDTO, additionalResponseParams["adyen_giving_service_id"]);
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            log.Debug("credit card transaction done from this POS is not approved.");
                            log.LogMethodExit(true);
                            return;
                        }
                    }
                    else if (cCRequestPGWDTO != null)
                    {
                        log.Debug("cCRequestPGWDTO is not null");
                        ccTransactionsPGWDTOResponse = new CCTransactionsPGWDTO();


                        string messageCategory = "Payment";
                        if (LastTrxCheckMessageCategory.ContainsKey(cCRequestPGWDTO.TransactionType))
                        {
                            messageCategory = LastTrxCheckMessageCategory[cCRequestPGWDTO.TransactionType];
                        }
                        string resultPayment = commandHandler.DoCheckLastTransactionStatus(saleId: posId, p_serviceId: lasttrxServiceId, messageCategory: messageCategory);
                        log.Debug($"resultPayment={resultPayment}");
                        if (string.IsNullOrWhiteSpace(resultPayment))
                        {
                            log.Error($"SendLastTransactionStatusCheckRequest(): response was null");
                            return;
                        }
                        else
                        {
                            GetTransactionStatusResponseDto responseObject = JsonConvert.DeserializeObject<GetTransactionStatusResponseDto>(resultPayment);
                            log.Debug($"responseObject={JsonConvert.SerializeObject(responseObject)}");
                            if (responseObject == null)
                            {
                                log.Error($"SendLastTransactionStatusCheckRequest(): response object was null. Deserialization failed.");

                                log.Error("Last transaction status is not available." + ((cCRequestPGWDTO == null) ? "" : " RequestId:" + cCRequestPGWDTO.RequestID + ", Amount:" + cCRequestPGWDTO.POSAmount));//ccrequestId etc
                                return;
                            }
                            else
                            {
                                // check for other errors
                                if (responseObject.SaleToPOIRequest != null)
                                {
                                    // some error came
                                    string errorMsg = "Failed to communicate with the terminal";
                                    Dictionary<string, string> errorDetails = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIRequest.EventNotification.EventDetails);
                                    if (errorDetails != null && errorDetails.ContainsKey("message"))
                                    {
                                        errorMsg = $"{responseObject.SaleToPOIRequest.EventNotification.EventToNotify} | {errorDetails["message"]}";
                                        log.Error(errorMsg);
                                        //throw new Exception(errorMsg);
                                    }
                                    return;
                                }
                                if (!responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals(PAYMENT_SUCCESS_STATUS))
                                {
                                    log.Error(responseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result);
                                    log.Error("Last transaction status is not available." + ((cCRequestPGWDTO == null) ? "" : " RequestId:" + cCRequestPGWDTO.RequestID + ", Amount:" + cCRequestPGWDTO.POSAmount));//ccrequestId etc
                                    return;
                                }
                                else
                                {
                                    Repeatedresponsemessagebody response = responseObject.SaleToPOIResponse.TransactionStatusResponse.RepeatedMessageResponse.RepeatedResponseMessageBody;
                                    decimal resamount = 0;
                                    decimal tipAmount = 0;
                                    if (response.PaymentResponse.PaymentResult != null)
                                    {
                                        if (response.PaymentResponse.PaymentResult.AmountsResp != null)
                                        {
                                            resamount = Convert.ToDecimal(response.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                            tipAmount = Convert.ToDecimal(response.PaymentResponse.PaymentResult.AmountsResp.TipAmount);
                                        }
                                        ccTransactionsPGWDTOResponse.InvoiceNo = response.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                                        ccTransactionsPGWDTOResponse.AcctNo = GetMaskedCardNumber(response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                                        ccTransactionsPGWDTOResponse.AuthCode = response.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                                        ccTransactionsPGWDTOResponse.CardType = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                                        ccTransactionsPGWDTOResponse.CaptureStatus = response.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0];
                                    }

                                    //ccTransactionsPGWDTOResponse.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();

                                    //ccTransactionsPGWDTOResponse.RefNo = response.PaymentResponse.SaleData.SaleTransactionID.TransactionID;

                                    ccTransactionsPGWDTOResponse.RecordNo = response.PaymentResponse.POIData.POITransactionID.TransactionID;
                                    //ccTransactionsPGWDTOResponse.AcqRefData = "AID:" + responsePaymentObject.payment.cardTransaction.extra.applicationIdentifier + "|Reference Id:" + responsePaymentObject.payment.cardTransaction.referenceId;
                                    Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(response.PaymentResponse.Response.AdditionalResponse);

                                    if (additionalResponseParams.Any())
                                    {
                                        string acqrefData = additionalResponseParams.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams["AID"])}" : "";
                                        acqrefData += additionalResponseParams.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams["acquirerResponseCode"])}" : "";
                                        acqrefData += additionalResponseParams.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams["fundingSource"])}" : "";

                                        ccTransactionsPGWDTOResponse.AcqRefData = acqrefData;
                                        ccTransactionsPGWDTOResponse.RefNo = additionalResponseParams.ContainsKey("adyen_giving_service_id") ? additionalResponseParams["adyen_giving_service_id"] : "";
                                    }

                                    ccTransactionsPGWDTOResponse.TextResponse = response.PaymentResponse.Response.Result;
                                    if (!response.PaymentResponse.Response.Result.Equals("Success"))
                                    {
                                        ccTransactionsPGWDTOResponse.DSIXReturnCode = additionalResponseParams.ContainsKey("message") ? additionalResponseParams["message"] : "";
                                    }
                                    ccTransactionsPGWDTOResponse.TranCode = cCRequestPGWDTO.TransactionType; // TBC Tx type
                                    ccTransactionsPGWDTOResponse.TransactionDatetime = utilities.getServerTime();
                                    ccTransactionsPGWDTOResponse.Authorize = resamount.ToString();
                                    ccTransactionsPGWDTOResponse.TipAmount = tipAmount.ToString();
                                    ccTransactionsPGWDTOResponse.ProcessData = getProcessData(response.PaymentResponse.POIData.POITransactionID.TimeStamp);
                                }
                            }
                        }
                    }
                    if (ccTransactionsPGWDTOResponse == null)
                    {
                        log.Debug("ccTransactionsPGWDTOResponse is null");
                        log.Error("Last transaction status is not available." + ((cCRequestPGWDTO == null) ? "" : " RequestId:" + cCRequestPGWDTO.RequestID + ", Amount:" + cCRequestPGWDTO.POSAmount));//ccrequestId etc
                        return;
                    }
                    else
                    {
                        log.Debug("ccTransactionsPGWDTOResponse is not null");
                        try
                        {
                            log.LogVariableState("ccTransactionsPGWDTOResponse", ccTransactionsPGWDTOResponse);

                            ccTransactionsPGWDTOResponse.TranCode = cCRequestPGWDTO.TransactionType;
                            if (cCTransactionsPGWDTO == null)
                            {
                                log.Debug("Saving ccTransactionsPGWDTOResponse.");
                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTOResponse);
                                ccTransactionsPGWBL.Save();
                                if (isCreditCardDonationEnabled && !string.IsNullOrWhiteSpace(ccTransactionsPGWDTOResponse.RefNo))
                                {
                                    CheckDonation(ccTransactionsPGWDTOResponse, ccTransactionsPGWDTOResponse.RefNo);
                                }
                            }
                            log.LogVariableState("ccTransactionsPGWDTOResponse", ccTransactionsPGWDTOResponse);
                            if (cCRequestPGWDTO.TransactionType == TransactionType.SALE.ToString() ||
                                cCRequestPGWDTO.TransactionType == TransactionType.AUTHORIZATION.ToString() ||
                                cCRequestPGWDTO.TransactionType == TransactionType.TATokenRequest.ToString())
                            {
                                TransactionPaymentsDTO transactionPaymentsDTO = new TransactionPaymentsDTO();
                                try
                                {
                                    transactionPaymentsDTO.TransactionId = Convert.ToInt32(cCRequestPGWDTO.InvoiceNo);
                                }
                                catch
                                {
                                    log.Debug("Transaction id conversion is failed");
                                }
                                transactionPaymentsDTO.Amount = Convert.ToDouble(ccTransactionsPGWDTOResponse.Authorize);
                                transactionPaymentsDTO.CCResponseId = (cCTransactionsPGWDTO == null) ? ccTransactionsPGWDTOResponse.ResponseID : cCTransactionsPGWDTO.ResponseID;
                                log.LogVariableState("transactionPaymentsDTO", transactionPaymentsDTO);
                                log.Debug("Calling RefundAmount()");
                                if (statusDisplayUi != null)
                                {
                                    statusDisplayUi.CloseStatusWindow();
                                }
                                transactionPaymentsDTO = RefundAmount(transactionPaymentsDTO);
                            }
                        }
                        catch (Exception ex)
                        {
                            log.Debug("Exception one");
                            if (!isUnattended && showMessageDelegate != null)
                            {
                                //showMessageDelegate(utilities.MessageUtils.getMessage("Last transaction status check is failed. :" + ((cCRequestPGWDTO != null) ? " TransactionID:" + cCRequestPGWDTO.InvoiceNo + " Amount:" + cCRequestPGWDTO.POSAmount : ".")), "Last Transaction Status Check", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            log.Error("Last transaction check failed", ex);
                            throw;
                        }
                    }
                }
                finally
                {
                    try
                    {
                        if (statusDisplayUi != null)
                            statusDisplayUi.CloseStatusWindow();
                    }
                    catch (Exception ex)
                    {
                        log.Debug("Exception three.one without throw in finally");
                        log.Error(ex);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Debug("Exception two");
                log.Error(ex);
                throw;
            }
            finally
            {
                log.Debug("Reached finally.");
            }
            log.LogMethodExit();
        }

        private void CheckDonation(CCTransactionsPGWDTO cCTransactionsPGWDTO, string adyen_giving_service_id)
        {
            log.LogMethodEntry(cCTransactionsPGWDTO, adyen_giving_service_id);
            try
            {
                if (cCTransactionsPGWDTO != null)
                {
                    // search for donation
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();

                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.PARENT_RESPONSE_ID, Convert.ToString(cCTransactionsPGWDTO.ResponseID)));
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.TRAN_CODE, TransactionType.DONATION.ToString()));
                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters);
                    if (cCTransactionsPGWDTOList == null || cCTransactionsPGWDTOList.Any() == false)
                    {
                        // donation not found
                        // initiate status check
                        string serviceIdForDonation = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 10);
                        log.Debug($"serviceIdForDonation = {serviceIdForDonation}");
                        AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceIdForDonation, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, false, false, false, applicationInfo, adyenTransactionTimeout, storeName);

                        string result = commandHandler.DoCheckLastTransactionStatus(donationSaleId, adyen_giving_service_id, donationMessageCategory);

                        log.Debug($"donation response={result}");

                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"MakePayment(): Donation response was null");
                            //throw new Exception("Donation Failed. No response received.");
                        }

                        PaymentResponseDto donationResponseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
                        log.Debug($"donationResponseObject={donationResponseObject.ToString()}");

                        if (donationResponseObject == null)
                        {
                            log.Error("Donation response was null");
                            //throw new Exception("Failed to receive Donation response");
                        }

                        CheckOtherPaymentErrors(commandHandler, donationResponseObject);

                        if (donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals("Success"))
                        {
                            log.Debug("Donation Trx succeeded");

                            Repeatedresponsemessagebody response2 = donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.RepeatedMessageResponse.RepeatedResponseMessageBody;

                            Dictionary<string, string> additionalResponseParams2 = commandHandler.GetAdditionalResponseData(response2.PaymentResponse.Response.AdditionalResponse);

                            // save donation response
                            decimal authorizedAmount = 0;
                            CCTransactionsPGWDTO donationCCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            donationCCTransactionsPGWDTO.InvoiceNo = serviceIdForDonation;
                            if (response2.PaymentResponse.PaymentResult != null)
                            {
                                if (response2.PaymentResponse.PaymentResult.AmountsResp != null)
                                {
                                    authorizedAmount = Convert.ToDecimal(response2.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                                }
                                donationCCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(response2.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                                donationCCTransactionsPGWDTO.AuthCode = response2.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                                donationCCTransactionsPGWDTO.CardType = response2.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                                donationCCTransactionsPGWDTO.CaptureStatus = response2.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0];

                            }

                            //cCTransactionsPGWDTO.RefNo = response.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                            donationCCTransactionsPGWDTO.RefNo = adyen_giving_service_id;
                            donationCCTransactionsPGWDTO.RecordNo = response2.PaymentResponse.POIData.POITransactionID.TransactionID;
                            donationCCTransactionsPGWDTO.ProcessData = getProcessData(response2.PaymentResponse.POIData.POITransactionID.TimeStamp);
                            if (additionalResponseParams2.Any())
                            {
                                string acqrefData = additionalResponseParams2.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams2["AID"])}" : "";
                                acqrefData += additionalResponseParams2.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams2["acquirerResponseCode"])}" : "";
                                acqrefData += additionalResponseParams2.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams2["fundingSource"])}" : "";
                                acqrefData += additionalResponseParams2.ContainsKey("pspReference") ? $" | pspReference: {Convert.ToString(additionalResponseParams2["pspReference"])}" : "";
                                acqrefData += $" | MerchantId: {response2.PaymentResponse.PaymentResult.PaymentAcquirerData.MerchantID}";

                                donationCCTransactionsPGWDTO.AcqRefData = acqrefData;
                            }

                            donationCCTransactionsPGWDTO.TextResponse = response2.PaymentResponse.Response.Result;
                            donationCCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.DONATION.ToString();
                            donationCCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            donationCCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();
                            donationCCTransactionsPGWDTO.ParentResponseId = Convert.ToInt32(cCTransactionsPGWDTO.ResponseID);

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(donationCCTransactionsPGWDTO);
                            ccTransactionsPGWBL.Save();
                        }
                        else if (donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals("Failure") && !donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition.Equals("InProgress"))
                        {

                            Dictionary<string, string> additionalResponseParams2 = commandHandler.GetAdditionalResponseData(donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.AdditionalResponse);

                            CCTransactionsPGWDTO cCTransactionsPGWDTO2 = new CCTransactionsPGWDTO();
                            cCTransactionsPGWDTO2.InvoiceNo = serviceIdForDonation;
                            cCTransactionsPGWDTO2.TextResponse = donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result;
                            cCTransactionsPGWDTO2.TranCode = PaymentGatewayTransactionType.DONATION.ToString();
                            cCTransactionsPGWDTO2.TransactionDatetime = utilities.getServerTime();
                            cCTransactionsPGWDTO2.DSIXReturnCode = additionalResponseParams2.ContainsKey("detailedStatus") ? additionalResponseParams2["detailedStatus"] : additionalResponseParams2.ContainsKey("status") ? additionalResponseParams2["status"] : "";
                            cCTransactionsPGWDTO2.ParentResponseId = Convert.ToInt32(cCTransactionsPGWDTO.ResponseID);

                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO2);
                            ccTransactionsPGWBL.Save();

                            string errorMessage = $"{donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition} | ";
                            errorMessage += additionalResponseParams2.ContainsKey("detailedStatus") ? additionalResponseParams2["detailedStatus"] + " |" : "";
                            errorMessage += additionalResponseParams2.ContainsKey("status") ? additionalResponseParams2["status"] : "";
                            log.Error($"Donation Transaction failed. {errorMessage}");
                        }
                    }
                }
                else
                {
                    // initiate status check
                    string serviceIdForDonation = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 10);
                    log.Debug($"serviceIdForDonation = {serviceIdForDonation}");
                    AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceIdForDonation, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, false, false, false, applicationInfo, adyenTransactionTimeout, storeName);

                    string result = commandHandler.DoCheckLastTransactionStatus(donationSaleId, adyen_giving_service_id, donationMessageCategory);

                    log.Debug($"donation response={result}");

                    if (string.IsNullOrWhiteSpace(result))
                    {
                        log.Error($"MakePayment(): Donation response was null");
                        //throw new Exception("Donation Failed. No response received.");
                    }

                    PaymentResponseDto donationResponseObject = JsonConvert.DeserializeObject<PaymentResponseDto>(result);
                    log.Debug($"donationResponseObject={donationResponseObject.ToString()}");

                    if (donationResponseObject == null)
                    {
                        log.Error("Donation response was null");
                        //throw new Exception("Failed to receive Donation response");
                    }

                    CheckOtherPaymentErrors(commandHandler, donationResponseObject);

                    if (donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals("Success"))
                    {
                        log.Debug("Donation Trx succeeded");

                        Repeatedresponsemessagebody response2 = donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.RepeatedMessageResponse.RepeatedResponseMessageBody;

                        Dictionary<string, string> additionalResponseParams2 = commandHandler.GetAdditionalResponseData(response2.PaymentResponse.Response.AdditionalResponse);

                        // save donation response
                        decimal authorizedAmount = 0;
                        CCTransactionsPGWDTO donationCCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        donationCCTransactionsPGWDTO.InvoiceNo = serviceIdForDonation;
                        if (response2.PaymentResponse.PaymentResult != null)
                        {
                            if (response2.PaymentResponse.PaymentResult.AmountsResp != null)
                            {
                                authorizedAmount = Convert.ToDecimal(response2.PaymentResponse.PaymentResult.AmountsResp.AuthorizedAmount);
                            }
                            donationCCTransactionsPGWDTO.AcctNo = GetMaskedCardNumber(response2.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.MaskedPan);
                            donationCCTransactionsPGWDTO.AuthCode = response2.PaymentResponse.PaymentResult.PaymentAcquirerData.ApprovalCode;
                            donationCCTransactionsPGWDTO.CardType = response2.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.PaymentBrand;
                            donationCCTransactionsPGWDTO.CaptureStatus = response2.PaymentResponse.PaymentResult.PaymentInstrumentData.CardData.EntryMode[0];
                        }

                        //cCTransactionsPGWDTO.RefNo = response.PaymentResponse.SaleData.SaleTransactionID.TransactionID;
                        donationCCTransactionsPGWDTO.RefNo = adyen_giving_service_id;
                        donationCCTransactionsPGWDTO.RecordNo = response2.PaymentResponse.POIData.POITransactionID.TransactionID;
                        donationCCTransactionsPGWDTO.ProcessData = getProcessData(response2.PaymentResponse.POIData.POITransactionID.TimeStamp);
                        if (additionalResponseParams2.Any())
                        {
                            string acqrefData = additionalResponseParams2.ContainsKey("AID") ? $"AID: {Convert.ToString(additionalResponseParams2["AID"])}" : "";
                            acqrefData += additionalResponseParams2.ContainsKey("acquirerResponseCode") ? $" | Acquirer Response Code: {Convert.ToString(additionalResponseParams2["acquirerResponseCode"])}" : "";
                            acqrefData += additionalResponseParams2.ContainsKey("fundingSource") ? $" | Funding Source: {Convert.ToString(additionalResponseParams2["fundingSource"])}" : "";
                            acqrefData += additionalResponseParams2.ContainsKey("pspReference") ? $" | pspReference: {Convert.ToString(additionalResponseParams2["pspReference"])}" : "";
                            acqrefData += $" | MerchantId: {response2.PaymentResponse.PaymentResult.PaymentAcquirerData.MerchantID}";

                            donationCCTransactionsPGWDTO.AcqRefData = acqrefData;
                        }

                        donationCCTransactionsPGWDTO.TextResponse = response2.PaymentResponse.Response.Result;
                        donationCCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.DONATION.ToString();
                        donationCCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        donationCCTransactionsPGWDTO.Authorize = authorizedAmount.ToString();
                        donationCCTransactionsPGWDTO.ParentResponseId = Convert.ToInt32(cCTransactionsPGWDTO.ResponseID);

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(donationCCTransactionsPGWDTO);
                        ccTransactionsPGWBL.Save();
                    }
                    else if (donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result.Equals("Failure") && !donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition.Equals("InProgress"))
                    {

                        Dictionary<string, string> additionalResponseParams2 = commandHandler.GetAdditionalResponseData(donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.AdditionalResponse);

                        CCTransactionsPGWDTO cCTransactionsPGWDTO2 = new CCTransactionsPGWDTO();
                        cCTransactionsPGWDTO2.InvoiceNo = serviceIdForDonation;
                        cCTransactionsPGWDTO2.TextResponse = donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.Result;
                        cCTransactionsPGWDTO2.TranCode = PaymentGatewayTransactionType.DONATION.ToString();
                        cCTransactionsPGWDTO2.TransactionDatetime = utilities.getServerTime();
                        cCTransactionsPGWDTO2.DSIXReturnCode = additionalResponseParams2.ContainsKey("detailedStatus") ? additionalResponseParams2["detailedStatus"] : additionalResponseParams2.ContainsKey("status") ? additionalResponseParams2["status"] : "";
                        cCTransactionsPGWDTO2.ParentResponseId = Convert.ToInt32(cCTransactionsPGWDTO.ResponseID);

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO2);
                        ccTransactionsPGWBL.Save();

                        string errorMessage = $"{donationResponseObject.SaleToPOIResponse.TransactionStatusResponse.Response.ErrorCondition} | ";
                        errorMessage += additionalResponseParams2.ContainsKey("detailedStatus") ? additionalResponseParams2["detailedStatus"] + " |" : "";
                        errorMessage += additionalResponseParams2.ContainsKey("status") ? additionalResponseParams2["status"] : "";
                        log.Error($"Donation Transaction failed. {errorMessage}");
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error("Failed to get Donation Response: " + ex.Message);
                log.Error(ex);
            }
        }
        private void SendPrintReceiptRequest(TransactionPaymentsDTO transactionPaymentsDTO, CCTransactionsPGWDTO ccTransactionsPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, ccTransactionsPGWDTO);
            try
            {
                if (utilities.getParafaitDefaults("PRINT_CUSTOMER_RECEIPT") == "Y")
                {
                    transactionPaymentsDTO.Memo = GetReceiptText(transactionPaymentsDTO, ccTransactionsPGWDTO, false);
                }
                if (utilities.getParafaitDefaults("PRINT_MERCHANT_RECEIPT") == "Y" && !isUnattended)
                {
                    transactionPaymentsDTO.Memo += GetReceiptText(transactionPaymentsDTO, ccTransactionsPGWDTO, true);
                }

            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit();
        }
        private string GetReceiptText(TransactionPaymentsDTO trxPaymentsDTO, CCTransactionsPGWDTO ccTransactionsPGWDTO, bool IsMerchantCopy)
        {
            log.LogMethodEntry(trxPaymentsDTO, ccTransactionsPGWDTO, IsMerchantCopy);
            try
            {
                string[] addressArray = utilities.ParafaitEnv.SiteAddress.Split(',');
                string receiptText = "";
                receiptText += AllignText(utilities.ParafaitEnv.SiteName, Alignment.Center);
                if (addressArray != null && addressArray.Length > 0)
                {
                    for (int i = 0; i < addressArray.Length; i++)
                    {
                        receiptText += Environment.NewLine + AllignText(addressArray[i] + ((i != addressArray.Length - 1) ? "," : ""), Alignment.Center);
                    }
                }
                receiptText += Environment.NewLine;
                string maskedMerchantId = (new String('X', merchantAccount.Length - 4) + ((merchantAccount.Length > 4) ? merchantAccount.Substring(merchantAccount.Length - 4)
                                                                                         : merchantAccount));
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Merchant ID") + "     : ".PadLeft(11) + maskedMerchantId, Alignment.Left);
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Date") + ": ".PadLeft(4) + ccTransactionsPGWDTO.TransactionDatetime.ToString("MMM dd yyyy HH:mm"), Alignment.Left);
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Type") + ": ".PadLeft(4) + ccTransactionsPGWDTO.TranCode, Alignment.Left);
                receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Trx Id") + "  : ".PadLeft(22) + "@invoiceNo", Alignment.Left);
                object userName = utilities.executeScalar(@"select username + case when EmpLastName is null OR EmpLastName = '' then '' else ' ' + substring(EmpLastName, 1, 1) end 
                                                              from users u
                                                             where user_id = @userId", new SqlParameter("@userId", utilities.ExecutionContext.UserPKId));
                if (userName != null && userName != DBNull.Value)
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Cashier Name") + "  : ".PadLeft(8) + userName.ToString(), Alignment.Left);
                }
                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.AuthCode))
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Authorization") + "   : ".PadLeft(10) + ccTransactionsPGWDTO.AuthCode, Alignment.Left);
                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.CardType))
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Payment Mode") + ": ".PadLeft(8) + ccTransactionsPGWDTO.CardType, Alignment.Left);
                //receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Cardholder Name") + ": ".PadLeft(3) + trxPaymentsDTO.NameOnCreditCard, Alignment.Left);
                //string maskedPAN = ((string.IsNullOrEmpty(ccTransactionsPGWDTO.AcctNo) ? trxPaymentsDTO.CreditCardNumber
                //                                                             : (new String('X', 12) + ((trxPaymentsDTO.CreditCardNumber.Length > 4)
                //                                                                                     ? trxPaymentsDTO.CreditCardNumber.Substring(trxPaymentsDTO.CreditCardNumber.Length - 4)
                //                                                                                     : trxPaymentsDTO.CreditCardNumber))));
                if (!string.IsNullOrWhiteSpace(ccTransactionsPGWDTO.AcctNo))
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("PAN") + ": ".PadLeft(24) + ccTransactionsPGWDTO.AcctNo, Alignment.Left);
                }
                if (!string.IsNullOrWhiteSpace(ccTransactionsPGWDTO.CaptureStatus))
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Entry Mode") + ": ".PadLeft(13) + ccTransactionsPGWDTO.CaptureStatus, Alignment.Left);
                }

                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.AcqRefData))
                {
                    //receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Reference Id") + ": ".PadLeft(15) + ccTransactionsPGWDTO.AcqRefData, Alignment.Left);
                    string[] data = null;
                    string[] emvData = ccTransactionsPGWDTO.AcqRefData.Split('|');
                    for (int i = 0; i < emvData.Length; i++)
                    {
                        data = emvData[i].Split(':');
                        if (data[0].Equals("AID") && !string.IsNullOrEmpty(data[1]))
                        {
                            receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("AID") + ": ".PadLeft(25) + data[1], Alignment.Left);
                        }
                        else if (data[0].Equals("Acquirer Response Code") && !string.IsNullOrEmpty(data[1]))
                        {
                            receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Reference Id") + ": ".PadLeft(10) + data[1], Alignment.Left);
                        }
                        //else if (data[0].Equals("IAD") && !string.IsNullOrEmpty(data[1]))
                        //{
                        //    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("IAD") + ": ".PadLeft(25) + data[1], Alignment.Left);
                        //}
                    }
                }
                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.RefNo))
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Reference") + ": ".PadLeft(14) + ccTransactionsPGWDTO.RefNo, Alignment.Left);
                }
                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.RecordNo))
                {
                    string tender = GetTender(ccTransactionsPGWDTO.RecordNo);
                    if (!string.IsNullOrEmpty(tender))
                    {
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Tender") + ": ".PadLeft(20) + tender, Alignment.Left);
                    }
                }
                if (!string.IsNullOrEmpty(terminalId))
                {

                    if (!string.IsNullOrEmpty(terminalId))
                    {
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("TID") + ": ".PadLeft(25) + (new String('X', terminalId.Length - 4) + ((terminalId.Length > 4) ? terminalId.Substring(terminalId.Length - 4)
                                                                                         : terminalId)), Alignment.Left);
                    }
                }
                //receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("POS User") + ": ".PadLeft(15) + utilities.ExecutionContext.UserId, Alignment.Left);
                //receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage((ccTransactionsPGWDTO.RecordNo.Equals("A")) ? "APPROVED" : (ccTransactionsPGWDTO.RecordNo.Equals("B")) ? "RETRY" : "DECLINED") + "-" + ccTransactionsPGWDTO.DSIXReturnCode, Alignment.Center);
                //if (ccTransactionsPGWDTO.RecordNo.Equals("C"))
                //{
                //    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage(ccTransactionsPGWDTO.TextResponse), Alignment.Center);
                //}
                if (successfullPaymentResponseList.Contains(ccTransactionsPGWDTO.TextResponse.ToLower()))
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("APPROVED"), Alignment.Center);
                }
                else
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage(ccTransactionsPGWDTO.TextResponse), Alignment.Center);
                }

                receiptText += Environment.NewLine;
                if (ccTransactionsPGWDTO.TranCode.Equals(TransactionType.CAPTURE.ToString()) || ccTransactionsPGWDTO.TranCode.Equals(PaymentGatewayTransactionType.TIPADJUST.ToString()))
                {
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Amount") + "  : " + Convert.ToDecimal(trxPaymentsDTO.Amount).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                    if (!string.IsNullOrWhiteSpace(trxPaymentsDTO.ExternalSourceReference) && trxPaymentsDTO.ExternalSourceReference == "G")
                    {
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Gratuity has already been included."), Alignment.Center);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Additional Tip") + ": ".PadLeft(28 - utilities.MessageUtils.getMessage("Additional Tip").Length) + "_____________", Alignment.Left);
                    }
                    else
                    {
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Tip") + ": ".PadLeft(35 - utilities.MessageUtils.getMessage("Tip").Length) + Convert.ToDecimal(trxPaymentsDTO.TipAmount).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                    }
                }
                if (ccTransactionsPGWDTO.TranCode.Equals(TransactionType.AUTHORIZATION.ToString()) || ccTransactionsPGWDTO.TranCode.Equals(TransactionType.SALE.ToString()))
                {
                    decimal tipAmount = Convert.ToDecimal(ccTransactionsPGWDTO.TipAmount);
                    decimal amount = Convert.ToDecimal(trxPaymentsDTO.Amount);
                    if (tipAmount > 0)
                    {
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Amount") + " : " + (amount).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Tip") + ": ".PadLeft(33 - utilities.MessageUtils.getMessage("Tip").Length) + tipAmount.ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Total") + ": ".PadLeft(32 - utilities.MessageUtils.getMessage("Total").Length) + (tipAmount + amount).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                    }
                    else
                    {
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Transaction Amount") + " : " + (amount).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL), Alignment.Left);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Tip") + ": ".PadLeft(33 - utilities.MessageUtils.getMessage("Tip").Length) + "_____________", Alignment.Left);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Total") + "                         : " + "_____________", Alignment.Left);
                    }

                    receiptText += Environment.NewLine;
                }
                if (IsMerchantCopy)
                {
                    if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.TextResponse) && successfullPaymentResponseList.Contains((ccTransactionsPGWDTO.TextResponse.ToLower())))
                    {
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText("_______________________", Alignment.Center);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("Signature"), Alignment.Center);
                        receiptText += Environment.NewLine;
                        receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage(1180), Alignment.Center);
                        //}
                        if (ccTransactionsPGWDTO.TranCode.Equals(TransactionType.AUTHORIZATION.ToString()))
                        {
                            LookupValuesDTO lookupValuesDTO = GetLookupValues("ADDITIONAL_PRINT_FIELDS", "@SuggestiveTipText");
                            if (!string.IsNullOrEmpty(lookupValuesDTO.Description))
                            {
                                receiptText += Environment.NewLine;
                                receiptText += Environment.NewLine + AllignText(lookupValuesDTO.Description, Alignment.Center);
                            }
                            lookupValuesDTO = GetLookupValues("ADDITIONAL_PRINT_FIELDS", "@SuggestiveTipValues");
                            if (!string.IsNullOrEmpty(lookupValuesDTO.Description))
                            {
                                string[] tipPercentage = lookupValuesDTO.Description.Split('|');
                                string line = "";
                                foreach (string s in tipPercentage)
                                {
                                    if (!string.IsNullOrEmpty(s))
                                    {
                                        line = s + utilities.MessageUtils.getMessage("% is") + " " + (((Convert.ToDecimal(ccTransactionsPGWDTO.Authorize) == 0) ? (trxPaymentsDTO.Amount + trxPaymentsDTO.TipAmount) : Convert.ToDouble(overallTransactionAmount)) * (int.Parse(s) / 100.0)).ToString(utilities.ParafaitEnv.AMOUNT_WITH_CURRENCY_SYMBOL);
                                        receiptText += Environment.NewLine + AllignText(line, Alignment.Center);
                                    }
                                }
                                receiptText += Environment.NewLine;
                            }
                        }
                    }
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + AllignText("**" + utilities.MessageUtils.getMessage("Merchant Copy") + "**", Alignment.Center);
                }
                else
                {
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + AllignText(utilities.MessageUtils.getMessage("IMPORTANT— retain this copy for your records"), Alignment.Center);
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + AllignText("**" + utilities.MessageUtils.getMessage("Cardholder Copy") + " **", Alignment.Center);
                }

                receiptText += Environment.NewLine;
                receiptText += AllignText(" " + utilities.MessageUtils.getMessage("Thank You"), Alignment.Center);
                if ((!ccTransactionsPGWDTO.TranCode.Equals("CAPTURE") || (ccTransactionsPGWDTO.TranCode.Equals("CAPTURE") && IsMerchantCopy)))
                {
                    if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.TextResponse) && successfullPaymentResponseList.Contains((ccTransactionsPGWDTO.TextResponse.ToLower())))
                    {
                        if (IsMerchantCopy)
                        {
                            ccTransactionsPGWDTO.MerchantCopy = receiptText;
                        }
                        else
                        {
                            ccTransactionsPGWDTO.CustomerCopy = receiptText;
                        }

                    }
                    else
                    {
                        receiptText = receiptText.Replace("@invoiceNo", "");
                        Print(receiptText);
                    }
                }
                log.LogMethodExit(receiptText);
                return receiptText;
            }
            catch (Exception ex)
            {
                log.Fatal("GetReceiptText() failed to print receipt exception:" + ex.ToString());
                return null;
            }
        }
        public static string AllignText(string text, Alignment align)
        {
            log.LogMethodEntry(text, align);

            int pageWidth = 40;
            string res;
            if (align.Equals(Alignment.Right))
            {
                string returnValueNew = text.PadLeft(pageWidth, ' ');
                log.LogMethodExit(returnValueNew);
                return returnValueNew;
            }
            else if (align.Equals(Alignment.Center))
            {
                int len = (pageWidth - text.Length);
                int len2 = len / 2;
                len2 = len2 + text.Length;
                res = text.PadLeft(len2);
                if (res.Length > pageWidth && res.Length > text.Length)
                {
                    res = res.Substring(res.Length - pageWidth);
                }

                log.LogMethodExit(res);
                return res;
            }
            else
            {
                //res= text.PadLeft(5 + text.Length);  
                log.LogMethodExit(text);
                return text;
            }
        }
        private string GetMaskedCardNumber(string responseField)
        {
            log.LogMethodEntry();
            log.LogMethodEntry();
            //triming the Merchant id
            string maskedResponseField = string.Empty;
            if (!string.IsNullOrWhiteSpace(responseField))
            {
                responseField = responseField.Trim();
                maskedResponseField = responseField.Length > 4 ? new string('X', responseField.Length - 4) + responseField.Substring(responseField.Length - 4) : responseField;
            }
            log.LogMethodExit(maskedResponseField);
            return maskedResponseField;
        }

        public string GetTender(string recordNo)
        {
            log.LogMethodEntry(recordNo);
            string tender = string.Empty;
            try
            {

                if (string.IsNullOrWhiteSpace(recordNo))
                {
                    log.Error("TransactionId was null");
                    throw new Exception("TransactionId was null");
                }

                string[] responseArray = recordNo.Split('.');
                if (responseArray.Length > 0)
                {
                    tender = responseArray[0];
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }
            log.LogMethodExit(tender);
            return tender;
        }

        /// <summary>
        /// GetCreditCardExpiryMonth
        /// </summary>
        /// <param name="cardExpiryData"></param>
        public override int GetCreditCardExpiryMonth(string cardExpiryData)
        {
            log.LogMethodEntry(cardExpiryData);
            int monthValue;
            if (string.IsNullOrWhiteSpace(cardExpiryData) || cardExpiryData.Length < 3
                || int.TryParse(cardExpiryData.Substring(0, 2), out monthValue) == false)
            {
                throw new ValidationException(MessageContainerList.GetMessage(utilities.ExecutionContext, 597));
                //Invalid date format in Expiry Date
            }
            log.LogMethodExit(monthValue);
            return monthValue;
        }

        /// <summary>
        /// GetCreditCardExpiryYear
        /// </summary>
        /// <param name="cardExpiryData"></param>
        public override int GetCreditCardExpiryYear(string cardExpiryData)
        {
            log.LogMethodEntry(cardExpiryData);
            int yearValue;
            string yearData = ServerDateTime.Now.Year.ToString().Substring(0, 2);
            log.Info("yearData: " + yearData);
            if (string.IsNullOrWhiteSpace(cardExpiryData) || cardExpiryData.Length < 4
              || int.TryParse(yearData + cardExpiryData.Substring(2, 2), out yearValue) == false)
            {
                throw new ValidationException(MessageContainerList.GetMessage(utilities.ExecutionContext, 597));
                //Invalid date format in Expiry Date
            }
            log.LogMethodExit(yearValue);
            return yearValue;
        }
        private string getProcessData(DateTime dateTime)
        {
            string processdata;
            log.Error("getProcessData method start");
            try
            {

                DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                long secondsSinceEpoch = (long)(dateTime.ToUniversalTime() - epoch).TotalSeconds;
                processdata = secondsSinceEpoch.ToString();

            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("Error in getProcessData method");
                throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6070));
            }
            log.Error("getProcessData method end");
            return processdata;
        }

        private bool VoidPayment(CCRequestPGWDTO cCRequestPGWDTO, TransactionPaymentsDTO transactionPaymentsDTO, CCTransactionsPGWDTO cCTransactionsPGWDTO, string serviceId)
        {
            log.LogMethodEntry(cCRequestPGWDTO, transactionPaymentsDTO);
            string result;
            CCRequestPGWDTO cCRequestPGW = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.REFUND.ToString());
            serviceId = cCRequestPGW.Guid.ToString().Replace("-", string.Empty).Substring(0, 10);
            AdyenPosCommandhandler commandHandler = new AdyenPosCommandhandler(serviceId, posId, terminalId, tapiUrl, webApiHostUrl, webApiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isCreditCardDonationEnabled, isPartialPaymentEnabled, false, false, false, applicationInfo, adyenTransactionTimeout, storeName);
            bool isSuccess = false;

            try
            {

                 if (transactionPaymentsDTO != null && transactionPaymentsDTO.SubscriptionAuthorizationMode == SubscriptionAuthorizationMode.P || transactionPaymentsDTO != null && !string.IsNullOrWhiteSpace(transactionPaymentsDTO.CustomerCardProfileId) && transactionPaymentsDTO.SubscriptionAuthorizationMode != SubscriptionAuthorizationMode.I)
                {
                    // check if its a subscription payment
                    // call web refund api
                    log.Info("Subscription product refund");
                    log.Debug("Initiating web refund");
                    result = commandHandler.DoRefundSubscriptionPayment(serviceId, cCTransactionsPGWDTO.RecordNo);
                    log.Debug($"Web Refund response = {result}");

                    if (string.IsNullOrWhiteSpace(result))
                    {
                        log.Error($"RefundAmount(): response was null");
                        throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                    }

                    WebRefundResponseDto responseObject = JsonConvert.DeserializeObject<WebRefundResponseDto>(result);
                    if (responseObject == null)
                    {
                        log.Error($"RefundAmount(): Refund response deserialization failed");
                        throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                    }

                    if (responseObject.status != "received")
                    {
                        log.Error($"RefundAmount(): Refund failed");
                        throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                    }
                    else
                    {
                        isSuccess = true;
                        log.Info("Refund has been successfull for the reuqest Id " + cCRequestPGWDTO.RequestID);
                    }
                }
                else if (cCTransactionsPGWDTO.ParentResponseId != null && cCTransactionsPGWDTO.ParentResponseId > -1)
                {
                    log.Debug("Initiating web refund");
                    CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(Convert.ToInt32(cCTransactionsPGWDTO.ParentResponseId));
                    CCTransactionsPGWDTO preAuthCCTransactionpgwDTO = cCTransactionsPGWBL.CCTransactionsPGWDTO;
                    log.Info("preAuthCCTransactionpgwDTO = " + preAuthCCTransactionpgwDTO.ToString());
                    CCTransactionsPGWDTO lastestChildCCTransactionpgwDTO = cCTransactionsPGWDTO;
                    log.Info("lastestChildCCTransactionpgwDTO = " + lastestChildCCTransactionpgwDTO.ToString());

                    decimal latestAmount = Convert.ToDecimal(lastestChildCCTransactionpgwDTO.Amount);
                    decimal amountPaid = Convert.ToDecimal(transactionPaymentsDTO.Amount);
                    decimal tipAmount = Convert.ToDecimal(transactionPaymentsDTO.TipAmount);
                    log.Info("latestAmount = " + latestAmount);
                    log.Info("amountPaid = " + amountPaid);
                    log.Info("tipAmount = " + tipAmount);

                    decimal preAuthAdjustAmount = latestAmount - (amountPaid + tipAmount);
                    string preauthPaymentId = commandHandler.GetTransactionId(preAuthCCTransactionpgwDTO.RecordNo);
                    log.Info("preAuthAdjustAmount = " + preAuthAdjustAmount);
                    log.Info("preauthPaymentId = " + preauthPaymentId);
                    log.Info("lastestChildCCTransactionpgwDTO.ResponseOrigin = " + lastestChildCCTransactionpgwDTO.ResponseOrigin);
                    log.Info("currencyConversionFactor = " + currencyConversionFactor);
                    log.Info("preAuthAdjustApiUrl = " + preAuthAdjustApiUrl);


                    if (preAuthAdjustAmount == 0)
                    {
                         log.Info("Preauth Cancellation has been initiated");
                         result = commandHandler.DoCancelAuthorization(preauthPaymentId, serviceId);
                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"RefundAmount(): response was null");
                            log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                            log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                            log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                            throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 

                        }

                        WebRefundResponseDto responseObject = JsonConvert.DeserializeObject<WebRefundResponseDto>(result);
                        if (responseObject == null)
                        {
                            log.Error($"WebRefundResponseDto responseObject is null");
                            log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                            log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                            log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                            throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                        }

                        if (responseObject.status != "received")
                        {
                            log.Error("responseObject.status not equal to received");
                            log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                            log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                            log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                            throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                        }
                        else
                        {
                            isSuccess = true;
                            log.Info("Refund has been successfull for the reuqest Id " + cCRequestPGWDTO.RequestID);
                        }
                    }
                    else
                    {
                        log.Info("Authorization adjustment initiated");
                        result = commandHandler.DoPreAuthorizationAdjustment(preAuthAdjustAmount, preauthPaymentId, lastestChildCCTransactionpgwDTO.ResponseOrigin, cCRequestPGWDTO.RequestID.ToString(), currencyConversionFactor, preAuthAdjustApiUrl);
                        log.Debug($"Preauth Adjustment response={JsonConvert.SerializeObject(result)}");

                        if (string.IsNullOrWhiteSpace(result))
                        {
                            log.Error($"MakePayment(): Auth response was null");
                            log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                            log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                            log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                            throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                        }

                        AdjustAuthResponseDto authAdjustResponseObject = JsonConvert.DeserializeObject<AdjustAuthResponseDto>(result);
                        log.LogVariableState("authAdjustResponseObject", authAdjustResponseObject);

                        if (authAdjustResponseObject == null)
                        {
                            log.Error($"authAdjustResponseObject is null");
                            log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                            log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                            log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                            throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                        }

                        if (!authAdjustResponseObject.response.Equals("Authorised"))
                        {
                            log.Error("authAdjustResponseObject.response. not equals to Authorised");
                            log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                            log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                            log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                            throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                        }
                        else
                        {
                            isSuccess = true;
                            log.Info("Refund has been successfull for the reuqest Id " + cCRequestPGWDTO.RequestID);
                        }
                    }
                }
                else
                {
                    // terminal refund request
                    log.Info("Terminal refund request initiated");
                    decimal tipAmount = !string.IsNullOrWhiteSpace(cCTransactionsPGWDTO.TipAmount) && cCTransactionsPGWDTO.TipAmount != "0" ? Convert.ToDecimal(cCTransactionsPGWDTO.TipAmount) : !string.IsNullOrWhiteSpace(transactionPaymentsDTO.TipAmount.ToString()) ? Math.Abs(Convert.ToDecimal(transactionPaymentsDTO.TipAmount)) : 0;
                    decimal amount = Convert.ToDecimal(transactionPaymentsDTO.Amount + Convert.ToDouble(tipAmount));

                    decimal authorisedAmount = Convert.ToDecimal(cCTransactionsPGWDTO.Authorize);
                    log.Debug($"authorisedAmount={authorisedAmount}");
                    if (amount > authorisedAmount)
                    {
                        log.Error("Refund failed due to Invalid amount passed");
                        log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                        log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                        log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                        throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                    }


                    //added this logic as part of processdata format issue . 15-05-25
                    long secondsSinceEpoch;
                    string creationTime = string.Empty;
                    string processDataofOriginalTransaction = cCTransactionsPGWDTO.ProcessData;
                    if (!string.IsNullOrWhiteSpace(processDataofOriginalTransaction))
                    {
                        try
                        {
                            if (long.TryParse(processDataofOriginalTransaction, out secondsSinceEpoch))
                            {
                                DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                                DateTime dateTime = epoch.AddSeconds(secondsSinceEpoch);

                                creationTime = dateTime.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                            }
                            else
                            {
                                DateTime dateTime;
                                try
                                {
                                    dateTime = DateTime.ParseExact(processDataofOriginalTransaction, "yyyy-MM-dd'T'HH:mm:ss'Z'", CultureInfo.InvariantCulture);
                                }
                                catch (Exception)
                                {
                                    dateTime = Convert.ToDateTime(processDataofOriginalTransaction);
                                }
                                creationTime = dateTime.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                                log.Info($"creationTime = {creationTime}");
                            }
                        }
                        catch (Exception e)
                        {
                            log.Error(e);
                            throw new Exception(MessageContainerList.GetMessage(utilities.ExecutionContext, 6070)); //DatetimeConversion Failed.
                        }
                    }

                    string txDateTime = commandHandler.getUTCDateTime(creationTime);
                    log.Debug($"txDateTime = {txDateTime}");
                    log.Info("currentCcOrigTransactionsPGWDTO.RecordNo.ToString() =" + cCTransactionsPGWDTO.RecordNo.ToString());
                    log.Info("amount =" + amount);

                    if ((!string.IsNullOrEmpty(cCTransactionsPGWDTO.Authorize) && authorisedAmount != amount))
                    {
                        // Partial refund
                        log.Debug("Partial Refund Initiated");
                        result = commandHandler.DoRefund(cCTransactionsPGWDTO.RecordNo.ToString(), txDateTime, amount, cCRequestPGW.RequestID.ToString(), terminalId);
                        log.Debug($"partial refund result={JsonConvert.SerializeObject(result)}");
                    }
                    else
                    {
                        // full Refund
                        log.Debug("Full Refund Initiated");
                        result = commandHandler.DoRefund(cCTransactionsPGWDTO.RecordNo.ToString(), txDateTime, amount, cCRequestPGW.RequestID.ToString(), terminalId);
                        log.Debug($"full refund result={JsonConvert.SerializeObject(result)}");
                    }


                    if (string.IsNullOrWhiteSpace(result))
                    {
                        log.Error($"RefundAmount(): response was null");
                        log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                        log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                        log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                        throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                    }

                    RefundResponseDto responseObject = JsonConvert.DeserializeObject<RefundResponseDto>(result);
                    log.LogVariableState("responseObject", responseObject);
                    if (responseObject == null)
                    {
                        log.Error($"RefundResponseDto responseObject is null");
                        log.Error("cCRequestPGWDTO = " + JsonConvert.SerializeObject(cCRequestPGWDTO));
                        log.Error("transactionPaymentsDTO = " + JsonConvert.SerializeObject(transactionPaymentsDTO));
                        log.Error("cCTransactionsPGWDTO = " + cCTransactionsPGWDTO.ToString());
                        throw new Exception(utilities.MessageUtils.getMessage(5502)); //Refund failed 
                    }

                    CheckOtherRefundErrors(commandHandler, responseObject);

                    Dictionary<string, string> additionalResponseParams = commandHandler.GetAdditionalResponseData(responseObject.SaleToPOIResponse.ReversalResponse.Response.AdditionalResponse);

                    if (responseObject.SaleToPOIResponse.ReversalResponse.Response.Result.Equals(REFUND_FAILURE_STATUS))
                    {
                        string errorMessage = commandHandler.GetRefundErrorMessage(responseObject, additionalResponseParams);
                        log.Error("responseObject.SaleToPOIResponse.ReversalResponse.Response.Result.Equals(REFUND_FAILURE_STATUS)");
                        log.Error($"RefundAmount(): Refund failed due to {errorMessage} ");
                        throw new Exception($"Refund Failed. Error: {errorMessage}");
                    }
                    else
                    {
                        isSuccess = true;
                        log.Info("Refund has been successfull for the reuqest Id " + cCRequestPGWDTO.RequestID);
                    }

                }
                log.LogMethodExit();
            }
            catch (Exception e)
            {
                log.Error("Error in VoidPayment method");
                log.Error(e);
            }
            return isSuccess;
        }

    }
}
