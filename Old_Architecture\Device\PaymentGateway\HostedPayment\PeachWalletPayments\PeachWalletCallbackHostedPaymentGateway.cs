﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PeachWalletPayments
{
    class PeachWalletCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string ENTITY_ID;
        private string SECRET_TOKEN;
        private string CLIENT_ID;
        private string CLIENT_SECRET;
        private string MERCHANT_ID;
        private string REFERER_URL;
        private string POST_URL;
        private string BASE_URL;
        private string CURRENCY;
        private string paymentPageLink;
        private const string DEBIT_PAYMENT_TYPE = "DB";
        private const string REFUND_PAYMENT_TYPE = "RF";

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";

        PeachWalletHostedCommandHandler peachWalletCommandHandler;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "000.000.000", PaymentStatusType.SUCCESS }, // Transaction succeeded
            { "000.000.100", PaymentStatusType.SUCCESS }, // successful request
            { "000.100.105", PaymentStatusType.SUCCESS }, // Chargeback Representment is successful
            { "000.100.106", PaymentStatusType.SUCCESS }, // Chargeback Representment cancellation is successful
            { "000.100.110", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Integrator Test Mode'
            { "000.100.111", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Validator Test Mode'
            { "000.100.112", PaymentStatusType.SUCCESS }, // Request successfully processed in 'Merchant in Connector Test Mode'
            { "000.300.000", PaymentStatusType.SUCCESS }, // Two-step transaction succeeded
            { "000.300.100", PaymentStatusType.SUCCESS }, // Risk check successful
            { "000.300.101", PaymentStatusType.SUCCESS }, // Risk bank account check successful
            { "000.300.102", PaymentStatusType.SUCCESS }, // Risk report successful
            { "000.300.103", PaymentStatusType.SUCCESS }, // Exemption check successful
            { "000.310.100", PaymentStatusType.SUCCESS }, // Account updated
            { "000.310.101", PaymentStatusType.SUCCESS }, // Account updated (Credit card expired)
            { "000.310.110", PaymentStatusType.SUCCESS }, // No updates found, but account is valid
            { "000.400.110", PaymentStatusType.SUCCESS }, // Authentication successful (frictionless flow)
            { "000.400.120", PaymentStatusType.SUCCESS }, // Authentication successful (data only flow)
            { "000.600.000", PaymentStatusType.SUCCESS }, // transaction succeeded due to external update

            { "000.400.000", PaymentStatusType.PENDING }, // Transaction succeeded (review for fraud suspicion)
            { "000.400.010", PaymentStatusType.PENDING }, // Transaction succeeded (review for AVS return code)
            { "000.400.020", PaymentStatusType.PENDING }, // Transaction succeeded (review for CVV return code)
            { "000.400.040", PaymentStatusType.PENDING }, // Transaction succeeded (review for amount mismatch)
            { "000.400.050", PaymentStatusType.PENDING }, // Transaction succeeded (pending)
            { "000.400.060", PaymentStatusType.PENDING }, // Transaction succeeded (merchant’s risk)
            { "000.400.070", PaymentStatusType.PENDING }, // Transaction succeeded (external risk review)
            { "000.400.080", PaymentStatusType.PENDING }, // Transaction succeeded (service unavailable)
            { "000.400.081", PaymentStatusType.PENDING }, // Transaction succeeded (network timeout)
            { "000.400.082", PaymentStatusType.PENDING }, // Transaction succeeded (processing timeout)
            { "000.400.090", PaymentStatusType.PENDING }, // Transaction succeeded (external risk check)
            { "000.400.100", PaymentStatusType.PENDING }, // Transaction succeeded, risk after payment rejected

            { "000.200.000", PaymentStatusType.PENDING }, // Transaction pending
            { "000.200.001", PaymentStatusType.PENDING }, // Transaction pending for acquirer
            { "000.200.100", PaymentStatusType.PENDING }, // successfully created checkout
            { "000.200.101", PaymentStatusType.PENDING }, // successfully updated checkout
            { "000.200.102", PaymentStatusType.PENDING }, // successfully deleted checkout
            { "000.200.103", PaymentStatusType.PENDING }, // checkout is pending
            { "000.200.200", PaymentStatusType.PENDING }, // Transaction initialized
            { "000.200.201", PaymentStatusType.PENDING }, // QR Scanned/Link Clicked
            { "100.400.500", PaymentStatusType.PENDING }, // waiting for external risk
            { "800.400.500", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant payment
            { "800.400.501", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant debit
            { "800.400.502", PaymentStatusType.PENDING }, // Waiting for confirmation of non-instant refund

            { "000.400.101", PaymentStatusType.FAILED }, // card not participating/authentication unavailable
            { "000.400.102", PaymentStatusType.FAILED }, // user not enrolled
            { "000.400.103", PaymentStatusType.FAILED }, // Technical Error in 3D system
            { "000.400.104", PaymentStatusType.FAILED }, // Missing or malformed 3DSecure Configuration for Channel
            { "000.400.105", PaymentStatusType.FAILED }, // Unsupported User Device
            { "000.400.106", PaymentStatusType.FAILED }, // invalid payer authentication response
            { "000.400.107", PaymentStatusType.FAILED }, // Communication Error to Scheme Directory Server
            { "000.400.108", PaymentStatusType.FAILED }, // Cardholder Not Found
            { "000.400.109", PaymentStatusType.FAILED }, // Card not enrolled for 3DS version 2
            { "000.400.111", PaymentStatusType.FAILED }, // Data Only request failed
            { "000.400.112", PaymentStatusType.FAILED }, // 3RI transaction not permitted
            { "000.400.200", PaymentStatusType.FAILED }, // risk management check communication error
            { "100.396.101", PaymentStatusType.FAILED }, // Cancelled by user error
            { "100.396.102", PaymentStatusType.FAILED }, // Not confirmed by user error
            { "100.396.103", PaymentStatusType.FAILED }, // Previously pending transaction timed out
            { "100.396.104", PaymentStatusType.FAILED }, // Uncertain status - probably cancelled by user
            { "100.396.106", PaymentStatusType.FAILED }, // User did not agree to payment method terms
            { "100.397.101", PaymentStatusType.FAILED }, //Cancelled by user due to external update
            { "100.397.102", PaymentStatusType.FAILED },
            { "100.380.401", PaymentStatusType.FAILED },//User Authentication Failed
            { "100.380.501", PaymentStatusType.FAILED },//Risk management transaction timeout
            { "100.400.000", PaymentStatusType.FAILED },//transaction declined (Wrong Address)
            { "100.400.001", PaymentStatusType.FAILED },//transaction declined (Wrong Identification)
            { "100.400.002", PaymentStatusType.FAILED },//Transaction declined (Insufficient credibility score)
            { "100.400.005", PaymentStatusType.FAILED },//transaction must be executed for German address
            { "100.400.007", PaymentStatusType.FAILED },//System error ( possible incorrect/missing input data)
            { "100.400.020", PaymentStatusType.FAILED },//transaction declined
            { "100.400.021", PaymentStatusType.FAILED },//transaction declined for country
            { "100.400.030", PaymentStatusType.FAILED },//transaction not authorized. Please check manually
            { "100.400.039", PaymentStatusType.FAILED },//transaction declined for other error
            { "100.400.040", PaymentStatusType.FAILED },//authorization failure
            { "100.400.041", PaymentStatusType.FAILED },//transaction must be executed for German address
            { "100.400.042", PaymentStatusType.FAILED },//transaction declined by SCHUFA (Insufficient credibility score)
        };

        public PeachWalletCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12 | SecurityProtocolType.Ssl3;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            this.BuildTransactions = false;
            Initialize();
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();

            MERCHANT_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            ENTITY_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            SECRET_TOKEN = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            CLIENT_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            CLIENT_SECRET = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            POST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            REFERER_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            CURRENCY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");

            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }
            if (POST_URL.EndsWith("/"))
            {
                POST_URL = POST_URL.Remove(POST_URL.Length - 1);
            }
            if (REFERER_URL.EndsWith("/"))
            {
                REFERER_URL = REFERER_URL.Remove(REFERER_URL.Length - 1);
            }
            peachWalletCommandHandler = new PeachWalletHostedCommandHandler(ENTITY_ID, SECRET_TOKEN, BASE_URL, POST_URL, REFERER_URL);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;



            if (string.IsNullOrWhiteSpace(SECRET_TOKEN))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(MERCHANT_ID))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(REFERER_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            }
            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            string apiSite = "";
            string webSite = "";
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API") != null)
            {
                apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;

            }
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB") != null)
            {
                webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;

            }
            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.PeachWalletCallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    System.Collections.Specialized.NameValueCollection queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.PeachWalletCallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PeachWalletCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description;
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description;
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description;
            }
            hostedGatewayDTO.PGSuccessResponseMessage = "OK";
            hostedGatewayDTO.PGFailedResponseMessage = "OK";

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;

            log.LogMethodExit();
        }

        /// <summary>
        /// Initiates the payment processing based on the provided gateway response.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the initialized payment details.
        /// </returns>
        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            if (hostedGatewayDTO == null)
            {
                hostedGatewayDTO = new HostedGatewayDTO();
            }
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();

            // proceed with processing
            PeachPaymentsTrxSeachResponseDTO responseObj = GetResposeObj(gatewayResponse);
            log.Debug("Response after deserializing: " + responseObj.ToString());
            if (responseObj != null)
            {
                string merchantInvoiceId = responseObj.MerchantInvoiceId;
                if (!string.IsNullOrEmpty(responseObj.MerchantTransactionId))
                {
                    string merchantTransactionId = responseObj.MerchantTransactionId;
                    hostedGatewayDTO.TrxId = Convert.ToInt32(responseObj.MerchantTransactionId.Replace("OrderNo", ""));

                }
                else
                {
                    log.Error("Both MerchantInvoiceId and MerchantTransactionId are null or empty.");
                    throw new Exception("Unable to process the transaction due to missing identifiers.");
                }
            }
            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }
        private PeachPaymentsTrxSeachResponseDTO GetResposeObj(string gatewayResponse)
        {
            //log.LogMethodEntry(gatewayResponse);
            PeachPaymentsTrxSeachResponseDTO responseObj = null;

            string jsonString = ConvertQueryStringToJson(gatewayResponse);

            //log.Debug("Converted JSON " + jsonString.ToString());
            responseObj = JsonConvert.DeserializeObject<PeachPaymentsTrxSeachResponseDTO>(jsonString);
            log.LogMethodExit(responseObj);
            return responseObj;
        }

        private string ConvertQueryStringToJson(string gatewayResponse)
        {
            log.LogMethodEntry();
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(gatewayResponse);

            Dictionary<string, string> responseDictionary = new Dictionary<string, string>();

            foreach (string key in responseCollection.AllKeys)
            {
                responseDictionary.Add(key, responseCollection[key]);
            }

            string responseJson = JsonConvert.SerializeObject(responseDictionary);

            log.LogMethodExit(responseJson);
            return responseJson;
        }

        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            try
            {
                log.LogMethodEntry();
                log.Debug("CCRequestSite:" + utilities.ExecutionContext.GetSiteId());
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
                requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
                requestParamsDict.Add("paymentToken", paymentToken);

                hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
                hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(hostedGatewayDTO.GatewayRequestString, paymentPageLink);

                log.Debug("Request string:" + hostedGatewayDTO.GatewayRequestString);
                log.Debug("Direct request link:" + hostedGatewayDTO.PaymentRequestLink);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());

            return hostedGatewayDTO;

        }

        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            PeachPaymentsHostedResponseDTO checkoutResponse;

            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("TransactionPaymentsDTO is null");
                    throw new ArgumentNullException(nameof(transactionPaymentsDTO));
                }
                double amount = transactionPaymentsDTO.Amount;
                string formattedAmount = amount.ToString("F2", CultureInfo.InvariantCulture);
                PeachPaymentsHostedRequestDTO peachPaymentsRequestDto = new PeachPaymentsHostedRequestDTO
                {
                    AuthenticationEntityId = ENTITY_ID,
                    MerchantTransactionId = "OrderNo" + transactionPaymentsDTO.TransactionId.ToString(),
                    Amount = formattedAmount,
                    Nonce = transactionPaymentsDTO.TransactionId.ToString(),
                    ShopperResultUrl = successResponseAPIURL,
                    Currency = CURRENCY,
                    PaymentType = DEBIT_PAYMENT_TYPE,
                    NotificationUrl = callbackResponseAPIURL,
                    CancelUrl = cancelResponseAPIURL
                };
                checkoutResponse = peachWalletCommandHandler.CreateCheckout(peachPaymentsRequestDto);

                if (checkoutResponse == null)
                {
                    log.Error("CreateGatewayPaymentRequest(): Checkout Transaction Response was empty");
                    throw new Exception("Error: could not create payment session");
                }

                if (string.IsNullOrWhiteSpace(checkoutResponse.RedirectUrl))
                {
                    log.Error("redirectUrl was null");
                    throw new Exception("Error creating the payment request");
                }
                string checkoutRedirectUrl = checkoutResponse.RedirectUrl;
                log.Debug($"CreateGatewayPaymentRequest(): Payment ResponseDto: {checkoutResponse}");
                log.Debug($"CreateGatewayPaymentRequest(): Payment request is created, redirecting to Checkout URL: {checkoutRedirectUrl}");
                hostedGatewayDTO.RequestURL = checkoutRedirectUrl;

                hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(checkoutRedirectUrl, "fromPeachWalletForm", "GET");

                log.Debug("checkoutRedirectUrl:" + checkoutRedirectUrl);
                log.Debug("GatewayRequestFormString:" + hostedGatewayDTO.GatewayRequestFormString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }


        private string GetSubmitFormKeyValueList(string redirectUrl, string formName, string submitMethod = "POST")
        {
            log.LogMethodEntry(redirectUrl, formName);
            // Parse the URL to get query parameters
            Uri uri = new Uri(redirectUrl);
            System.Collections.Specialized.NameValueCollection queryParams = System.Web.HttpUtility.ParseQueryString(uri.Query);

            // Extract "plugin" and "checkoutId" from the URL
            string plugin = queryParams["plugin"];
            string checkoutId = queryParams["checkoutId"];

            // Build the form
            string method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();

            builder.Append("<html>");
            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", formName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", formName, method, uri.GetLeftPart(UriPartial.Path))); // Use the base URL without query params

            // Add plugin and checkoutId as hidden form fields
            if (!string.IsNullOrEmpty(plugin))
            {
                builder.Append(string.Format("<input name=\"plugin\" type=\"hidden\" value=\"{0}\" />", plugin));
            }
            if (!string.IsNullOrEmpty(checkoutId))
            {
                builder.Append(string.Format("<input name=\"checkoutId\" type=\"hidden\" value=\"{0}\" />", checkoutId));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");
            log.LogMethodExit();
            return builder.ToString();
        }


        /// <summary>
        /// Processes the response received from the payment gateway and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the updated payment details and status.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment or updating the payment status.</exception>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            PeachPaymentsTrxSeachResponseDTO peachCardResponse = null;
            try
            {
                log.Debug("Entering GetResposeObj. ");
                peachCardResponse = GetResposeObj(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + peachCardResponse.ToString());

                if (peachCardResponse.MerchantTransactionId != null)
                {
                    log.Debug("Transaction id: " + peachCardResponse.MerchantTransactionId);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(peachCardResponse.MerchantTransactionId.Replace("OrderNo", ""));
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                PeachPaymentsTrxSeachResponseDTO trxSearchResponse = peachWalletCommandHandler.VerifyPayment(peachCardResponse.MerchantTransactionId);

                if (string.IsNullOrEmpty(trxSearchResponse.ResultCode))
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }

                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(trxSearchResponse.Amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = CURRENCY;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.MerchantTransactionId;

                PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.ResultCode, PaymentGatewayTransactionType.SALE);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Payment status is success");
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.SUCCESS;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                    hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.Id.ToString();
                    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = trxSearchResponse.PaymentType; //auth_codec
                    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = trxSearchResponse.CardLast4Digits;
                    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardExpiry = trxSearchResponse.CardExpiryMonth + "/" + trxSearchResponse.CardExpiryYear;
                    hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = trxSearchResponse.PaymentBrand;
                    hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = trxSearchResponse.CardHolder;
                }
                else if (salePaymentStatus == PaymentStatusType.PENDING)
                {
                    log.Debug("Payment status is pending");
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.PENDING;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                }
                else if (salePaymentStatus == PaymentStatusType.FAILED)
                {
                    log.Debug("Payment status is failed");
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }
                else
                {
                    log.Error("Payment status is unknown. Considering status as failed Status: " + salePaymentStatus.ToString());
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }

                //check if ccTransactionPGW updated
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;
                if (!String.IsNullOrEmpty(cCRequestsPGWDTO.ReferenceNo))
                {
                    string[] resvalues = cCRequestsPGWDTO.ReferenceNo.ToString().Split('|');
                    foreach (string word in resvalues)
                    {
                        if (word.Contains("PaymentModeId") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(word.Split(':')[1]);
                        }
                        else if (word.Contains("CurrencyCode") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = word.Split(':')[1];
                        }
                    }
                }

                log.Debug("Trying to update the CC request to payment processing status");
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);
                int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(cCRequestsPGWDTO.PaymentProcessStatus, hostedGatewayDTO.PaymentProcessStatus.ToString());

                if (rowsUpdated == 0)
                {
                    log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                }
                else
                {
                    log.Debug("CC request updated to " + hostedGatewayDTO.PaymentProcessStatus.ToString());
                }

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Debug("No reference id/Transaction present");
                    cCTransactionsPGWDTOList = null;
                }

                if (cCTransactionsPGWDTOList == null)
                {  // update the CCTransactionsPGWDTO
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", hostedGatewayDTO.TransactionPaymentsDTO.Amount);
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", hostedGatewayDTO.TransactionPaymentsDTO.Amount);
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.TextResponse = trxSearchResponse.ResultCode;
                    cCTransactionsPGWDTO.DSIXReturnCode = trxSearchResponse.ResultDescription;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.CardType = !string.IsNullOrEmpty(trxSearchResponse.PaymentBrand) ? trxSearchResponse.PaymentBrand : "";
                    cCTransactionsPGWDTO.AcctNo = !string.IsNullOrEmpty(trxSearchResponse.CardLast4Digits) ? "XXXXXXXXXXXX" + trxSearchResponse.CardLast4Digits : "";
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse.Timestamp);

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }
                else
                {
                    //if YES
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.ERROR;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        private DateTime GetPaymentDate(string peachTime)
        {
            log.LogMethodEntry(peachTime);
            DateTime paymentDate = new DateTime();

            if (!string.IsNullOrEmpty(peachTime))
            {
                if (DateTime.TryParseExact(peachTime, "yyyyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }

        /// <summary>
        /// Retrieves the status of a transaction based on the provided transaction ID.
        /// </summary>
        /// <param name="trxId">The ID of the transaction to retrieve status for.</param>
        /// <returns>
        /// Returns a JSON string containing the status information of the transaction, including success or failure status, transaction amount, reference number, account number, and any relevant messages.
        /// </returns>
        /// <exception cref="Exception">Thrown when there are insufficient parameters passed to the request or when an error occurs during the processing of the transaction.</exception>
        //public override string GetTransactionStatus(string trxId)
        //{
        //    log.LogMethodEntry(trxId);
        //    Dictionary<string, Object> dict = new Dictionary<string, Object>();
        //    dynamic resData;

        //    try
        //    {
        //        if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
        //        {
        //            log.Error("No Transaction id passed");
        //            throw new Exception("Insufficient Params passed to the request");
        //        }

        //        CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
        //        List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
        //        searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
        //        CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

        //        PayStackVerifyTransactionResponseDTO txSearchResponseDTO = paystackCommandHandler.VerifyPayment(trxId);
        //        if (!txSearchResponseDTO.status)
        //        {
        //            log.Error("No matching transaction found for the specified conditions.");
        //            throw new Exception("No matching transaction found.");
        //        }
        //        if (txSearchResponseDTO.data == null)
        //        {
        //            log.Error("No matching transaction found for the specified conditions.");
        //            throw new Exception("No matching transaction found.");
        //        }
        //        if (txSearchResponseDTO.data.authorization == null)
        //        {
        //            log.Error("No matching transaction found for the specified conditions.");
        //            throw new Exception("No matching transaction found.");
        //        }
        //        log.Debug($"TxSearch Response for TrxId: {trxId}: " + txSearchResponseDTO);

        //        if (txSearchResponseDTO != null)
        //        {


        //            CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
        //            cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
        //            cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.data.amount * 0.01));
        //            cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.data.amount * 0.01));
        //            cCTransactionsPGWDTO.RefNo = txSearchResponseDTO.data.id.ToString(); //paymentId
        //            cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
        //            cCTransactionsPGWDTO.TextResponse = txSearchResponseDTO.data.status;
        //            cCTransactionsPGWDTO.DSIXReturnCode = txSearchResponseDTO.message + " | Payment: " + txSearchResponseDTO.data.status;
        //            cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
        //            cCTransactionsPGWDTO.CardType = txSearchResponseDTO.data.authorization.card_type;

        //            cCTransactionsPGWDTO.AcctNo = txSearchResponseDTO.data.authorization.last4 != null ? "XXXXXXXXXXXX" + txSearchResponseDTO.data.authorization.last4 : null;
        //            cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(txSearchResponseDTO);

        //            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
        //            ccTransactionsPGWBL.Save();

        //            if (txSearchResponseDTO.data.status == SUCCESS)
        //            {
        //                dict.Add("status", "1");
        //                dict.Add("message", "success");
        //                dict.Add("retref", cCTransactionsPGWDTO.RefNo);
        //                dict.Add("amount", cCTransactionsPGWDTO.Authorize);
        //                dict.Add("orderId", trxId);
        //                dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
        //            }
        //            else if (txSearchResponseDTO.data.status == PENDING || txSearchResponseDTO.data.status == PROCESSING || txSearchResponseDTO.data.status == ONGOING)
        //            {
        //                log.Error("GetTransactionStatus(): Error updating the payment status");

        //                //cancel the Tx in Parafait DB
        //                dict.Add("status", "0");
        //                dict.Add("message", (txSearchResponseDTO.data.status));
        //                dict.Add("orderId", trxId);
        //                //throw new Exception("redirect checkoutmessage");
        //            }

        //            else
        //            {
        //                log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
        //                //cancel the Tx in Parafait DB
        //                dict.Add("status", "0");
        //                dict.Add("message", (txSearchResponseDTO.data.status));
        //                dict.Add("orderId", trxId);
        //            }

        //        }
        //        resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

        //        log.LogMethodExit(resData);
        //        return resData;
        //    }
        //    catch (Exception ex)
        //    {
        //        log.Error(ex);
        //        throw;
        //    }
        //}

        /// <summary>
        /// Initiates a refund process for a transaction based on the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details for initiating the refund.</param>
        /// <returns>
        /// Returns the updated TransactionPaymentsDTO after processing the refund.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefund = false;
            refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    //to get original TrxId(in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }

                log.Debug("Refund processing started");
                PeachPaymentsRefundResponseDTO refundResponseDTO = null;
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                PeachPaymentsRefundRequestDTO requestDto = new PeachPaymentsRefundRequestDTO
                {
                    AuthenticationEntityId = ENTITY_ID,
                    Id = transactionPaymentsDTO.Reference,
                    Amount = transactionPaymentsDTO.Amount.ToString(),
                    Currency = CURRENCY,
                    PaymentType = REFUND_PAYMENT_TYPE,
                };

                log.Debug("Peach Refund Request has been created, RequestDTO: " + requestDto.ToString());

                refundResponseDTO = peachWalletCommandHandler.CreateRefund(requestDto);
                log.Debug("Peach Refund Response refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }
                PaymentStatusType refundStatus = MapPaymentStatus(refundResponseDTO.Result.Code, PaymentGatewayTransactionType.REFUND);
                log.Debug("Value of refundPaymentStatus: " + refundStatus.ToString());

                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                ccTransactionsPGWDTO.DSIXReturnCode = refundResponseDTO.Result.Description;
                ccTransactionsPGWDTO.RefNo = refundResponseDTO.Id.ToString(); //paystack paymentId

                if (refundStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Refund Success for trxId: " + refundTrxId);
                    isRefund = true;
                    ccTransactionsPGWDTO.TextResponse = refundResponseDTO.Result.Code;
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.Result.Code;
                    ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", refundResponseDTO.Amount.ToString());
                    ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", refundResponseDTO.Amount.ToString());
                    ccTransactionsPGWDTO.PaymentStatus = refundStatus.ToString();
                }
                else
                {
                    //refund failed
                    isRefund = false;
                    string errorMessage = refundResponseDTO.Result.Description;
                    log.Error($"Refund Failed. Error Message received: {errorMessage}");
                    ccTransactionsPGWDTO.TextResponse = "FAILED";
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.Result.Code;
                    ccTransactionsPGWDTO.PaymentStatus = PaymentStatusType.FAILED.ToString();
                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PeachPaymentsTrxSeachResponseDTO orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = peachWalletCommandHandler.VerifyPayment("OrderNo" + trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                log.Debug($"TxSearch Response for TrxId: {orderStatusResult.MerchantTransactionId}: " + orderStatusResult);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(orderStatusResult.ResultCode, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());


                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO != null ? cCRequestsPGWDTO.RequestID.ToString() : trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", orderStatusResult.Amount);
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", orderStatusResult.Amount);
                cCTransactionsPGWDTO.RefNo = orderStatusResult.Id;
                cCTransactionsPGWDTO.RecordNo = trxIdString;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.ResultCode;
                cCTransactionsPGWDTO.DSIXReturnCode = orderStatusResult.ResultDescription + " | Payment: " + salePaymentStatus.ToString();
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.CardType = orderStatusResult.PaymentBrand;
                cCTransactionsPGWDTO.AcctNo = orderStatusResult.CardLast4Digits; //Based on payment status job decides whether payment os applied or not

                cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();

            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }
    }

}
