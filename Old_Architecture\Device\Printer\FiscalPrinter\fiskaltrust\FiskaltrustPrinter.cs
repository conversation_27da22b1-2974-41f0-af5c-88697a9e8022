﻿/********************************************************************************************
 * Project Name - Device
 * Description  - Printer implementation for Fiskaltrust
 * 
 **************
 **Version Log
 **************
 *Version     Date             Modified By            Remarks          
 *********************************************************************************************
 *2.90.0     14-Jul-2020      Gururaja Kanjan    Created for fiskaltrust integration.
*2.110.0     22-Dec-2020      Girish Kundar      Modified :FiscalTrust changes - Shift open/Close/PayIn/PayOut to be fiscalized
 ********************************************************************************************/

using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Semnox.Core.GenericUtilities;
using Semnox.Core.Utilities;
using Semnox.Parafait.Device.PaymentGateway;
using Semnox.Parafait.Device.Printer.FiscalPrinter;
using Semnox.Parafait.Device.Printer.FiscalPrinter.fiskaltrust;
using Semnox.Parafait.JobUtils;
using Semnox.Parafait.Languages;

namespace Semnox.Parafait.Device.Printer.FiscalPrint
{
    public class FiskaltrustPrinter : Semnox.Parafait.Device.Printer.FiscalPrint.FiscalPrinter
    {
        private static readonly Semnox.Parafait.logging.Logger log =
            new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private const string POS_MACHINE = "POSMachines";
        private const string INITIAL_OPERATION = "InitialOperation";
        private const string CLOSE_OPERATION = "CloseOperation";
        private const string INITIAL_OP_NAME = "Initial Operation";
        private const string CLOSE_OP_NAME = "Close Operation";
        private const string SUCCESS = "SUCCESS";
        private const string FAILED = "FAILED";
        private const string FISCAL_PRINTER = "FISCAL_PRINTER";
        private const string FISCALIZATION = "FISCALIZATION";
        private const string FISKALTRUST_DAILY_OPERATION = "FiskalTrust Daily Operations";
        private const string FISKALTRUST_DAILY_OPERATION_PROGRAM_EXE = "FiskalTrustDailyOperationsProgram.exe";
        private const string FISKAL_TRUST = "FiskalTrust";
        private const string SUCCESS_STATE = "SUCCESS_STATE";
        private const string OUT_OF_SERVICE_STATE = "OUT_OF_SERVICE_STATE";
        private const string SCU_FAIL_STATE = "SCU_FAIL_STATE";
        private int concurrentProgramId = -1;

        private const string PROGRAM_NAME = "FiskalTrustDailyOperationsProgram";
        private const string URL_KEY = "FISCAL_DEVICE_TCP/IP_ADDRESS";
        private const string PRINTER_TOKEN_KEY = "FISCAL_PRINTER_PASSWORD";
        private const string FT_CASHBOX = "FISCAL_CASH_REGISTER_ID";

        private static string FISKALTRUST_URL;
        private static string FISKALTRUST_TOKEN;
        private static string FISKALTRUST_CASHBOX;

        public const string FISKALTRUST = "FISKALTRUST";


        public FiskaltrustPrinter(Utilities _Utilities) : base(_Utilities)
        {
            log.LogMethodEntry(_Utilities);

            try
            {
                if (FISKALTRUST_URL == null)
                {
                    FISKALTRUST_URL =
                        ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, URL_KEY);
                    FISKALTRUST_TOKEN =
                        ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, PRINTER_TOKEN_KEY);
                    FISKALTRUST_CASHBOX = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, FT_CASHBOX);
                    if (string.IsNullOrEmpty(FISKALTRUST_URL) || string.IsNullOrEmpty(FISKALTRUST_TOKEN) || string.IsNullOrEmpty(FISKALTRUST_CASHBOX))
                    {
                        log.Error("FISCAL_PRINTER_PASSWORD or FISCAL_DEVICE_TCP/IP_ADDRESS or FISKALTRUST_CASHBOX is not correct");
                        log.Debug("Please check the set up ");
                        return;
                    }
                    log.Debug("FISKALTRUST_URL :" + FISKALTRUST_URL);
                    log.Debug("FISKALTRUST_TOKEN :" + FISKALTRUST_TOKEN);
                    log.Debug("FISKALTRUST_CASHBOX :" + FISKALTRUST_CASHBOX);

                }
                //FiskalTrustInitialCloseOperation();
            }
            catch (System.Net.WebException webex)
            {
                log.Error("Error occured during initialization", webex);
            }
            catch (Exception ex)
            {
                log.Error("Error occured during initialization", ex);
            }

            log.LogMethodExit();
        }


        public override void FiskalTrustInitialCloseOperation()
        {
            log.LogMethodEntry();
            GetConcurrentProgramId();
            ExecuteFiskalTrustDailyOperation();
            log.LogMethodExit();
        }

        private void GetConcurrentProgramId()
        {
            log.LogMethodEntry();
            try
            {
                ConcurrentProgramList concurrentProgramList = new ConcurrentProgramList(utilities.ExecutionContext);
                List<KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>> programSearch = new List<KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>>();
                programSearch.Add(new KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>(ConcurrentProgramsDTO.SearchByProgramsParameters.EXECUTABLE_NAME, FISKALTRUST_DAILY_OPERATION_PROGRAM_EXE));
                programSearch.Add(new KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>(ConcurrentProgramsDTO.SearchByProgramsParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                List<ConcurrentProgramsDTO> concurrentProgramsListDTO = concurrentProgramList.GetAllConcurrentPrograms(programSearch, true, true);
                if (concurrentProgramsListDTO != null && concurrentProgramsListDTO.Any())
                {
                    concurrentProgramId = concurrentProgramsListDTO[0].ProgramId;
                    log.LogVariableState("concurrentProgramId", concurrentProgramId);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }
            log.LogMethodExit(concurrentProgramId);
        }


        private void ExecuteFiskalTrustDailyOperation()
        {
            log.LogMethodEntry();
            try
            {
                int posMachineId = utilities.ExecutionContext.GetMachineId();
                ConcurrentRequestDetailsListBL concurrentRequestDetailsListBL = new ConcurrentRequestDetailsListBL(utilities.ExecutionContext);
                List<ConcurrentRequestDetailsDTO> openCloseOpList = concurrentRequestDetailsListBL.GetInitialCloseOperationList(posMachineId, concurrentProgramId, utilities.ExecutionContext.SiteId);
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }

            log.LogMethodExit();
        }

        public string POSTInitialReport(string posId, string posGuId)
        {
            log.LogMethodEntry();
            string fiscalPrinterName = string.Empty;
            FiscalPrinter fiscalPrinter = null;
            string ftState = string.Empty;
            bool isSuccessful = false;
            string status = FAILED;
            string errorDetails = string.Empty;
            try
            {
                FiscalPrinterFactory.GetInstance().Initialize(utilities);
                fiscalPrinterName = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, FISCAL_PRINTER);
                if (string.IsNullOrEmpty(fiscalPrinterName) == false)
                {
                    if (fiscalPrinterName == FISKAL_TRUST)
                    {
                        fiscalPrinter = FiscalPrinterFactory.GetInstance().GetFiscalPrinter(fiscalPrinterName);
                    }
                    else
                    {
                        string msg = MessageContainerList.GetMessage(utilities.ExecutionContext, 165, FISKAL_TRUST);
                        //&1 is mandatory
                        ValidationException ve = new ValidationException(msg);
                        throw ve;
                    }
                }
                if (fiscalPrinter != null)
                {
                    ftState = fiscalPrinter.PublishInitialReceipt(posId);
                    if (ftState == FiscaltrustDefaults.GetFiscaltrustDefault(SUCCESS_STATE))
                    {
                        isSuccessful = true;
                        status = SUCCESS;
                    }
                    else if (ftState == FiscaltrustDefaults.GetFiscaltrustDefault(OUT_OF_SERVICE_STATE))
                    {
                        status = FAILED;
                        errorDetails = MessageContainerList.GetMessage(utilities.ExecutionContext, 12500);// "Out Of Service"
                    }
                    else if (ftState == FiscaltrustDefaults.GetFiscaltrustDefault(SCU_FAIL_STATE))
                    {
                        status = FAILED;
                        errorDetails = MessageContainerList.GetMessage(utilities.ExecutionContext, 5801); //5801, 'Scu Communication Failed'
                    }
                    else
                    {
                        status = FAILED;
                        errorDetails = MessageContainerList.GetMessage(utilities.ExecutionContext, 5078, INITIAL_OP_NAME, ftState);//Unexpected error occurred while &1. Error : &2
                    }
                }
                else
                {
                    string msg = MessageContainerList.GetMessage(utilities.ExecutionContext, 2932, FISCAL_PRINTER, FISCALIZATION);
                    //2932	&1 details are missing in &2 setup.
                    ValidationException ve = new ValidationException(msg);
                    throw ve;
                }
                ConcurrentRequestDetailsDTO concurrentRequestDetailsDTO = new ConcurrentRequestDetailsDTO(-1, -1, ServerDateTime.Now, concurrentProgramId, POS_MACHINE, Convert.ToInt32(posId), posGuId, isSuccessful, status, ftState, INITIAL_OPERATION, errorDetails, true);
                ConcurrentRequestDetailsBL concurrentRequestDetailsBL = new ConcurrentRequestDetailsBL(utilities.ExecutionContext, concurrentRequestDetailsDTO);
                concurrentRequestDetailsBL.Save();
                log.LogMethodExit(errorDetails);
                return errorDetails;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                try
                {
                    string message = ex.Message;
                    if (ex.InnerException != null)
                    {
                        message = message + " " + ex.InnerException.Message;
                    }
                    ConcurrentRequestDetailsDTO concurrentRequestDetailsDTO = new ConcurrentRequestDetailsDTO(-1, -1, ServerDateTime.Now, -1, POS_MACHINE, Convert.ToInt32(posId), posGuId, isSuccessful, status, ftState, INITIAL_OPERATION, message, true);
                    ConcurrentRequestDetailsBL concurrentRequestDetailsBL = new ConcurrentRequestDetailsBL(utilities.ExecutionContext, concurrentRequestDetailsDTO);
                    concurrentRequestDetailsBL.Save();
                }
                catch (Exception exx)
                {
                    log.Error(exx);
                }
                return MessageContainerList.GetMessage(utilities.ExecutionContext, 5078, INITIAL_OP_NAME, ex.Message);//Unexpected error occurred while &1. Error : &2
            }
        }

        /// <summary>
        /// POST Invoice XMLS
        /// </summary>
        public string POSTEndOfDayReport(string posId, string posGuId)
        {
            log.LogMethodEntry();
            string fiscalPrinterName = string.Empty;
            FiscalPrinter fiscalPrinter = null;
            string ftState = string.Empty;
            bool isSuccessful = false;
            string status = FAILED;
            string errorDetails = string.Empty;
            try
            {

                FiscalPrinterFactory.GetInstance().Initialize(utilities);
                fiscalPrinterName = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, FISCAL_PRINTER);
                if (string.IsNullOrEmpty(fiscalPrinterName) == false)
                {
                    if (fiscalPrinterName == FISKAL_TRUST)
                    {
                        fiscalPrinter = FiscalPrinterFactory.GetInstance().GetFiscalPrinter(fiscalPrinterName);
                    }
                    else
                    {
                        string msg = MessageContainerList.GetMessage(utilities.ExecutionContext, 165, FISKAL_TRUST);
                        //&1 is mandatory
                        ValidationException ve = new ValidationException(msg);
                        throw ve;
                    }
                }
                if (fiscalPrinter != null)
                {
                    ftState = fiscalPrinter.SendEodReport(posId);
                    if (ftState == FiscaltrustDefaults.GetFiscaltrustDefault(SUCCESS_STATE))
                    {
                        isSuccessful = true;
                        status = SUCCESS;
                    }
                    else if (ftState == FiscaltrustDefaults.GetFiscaltrustDefault(OUT_OF_SERVICE_STATE))
                    {
                        status = FAILED;
                        errorDetails = MessageContainerList.GetMessage(utilities.ExecutionContext, 12500);// "Out Of Service"
                    }
                    else if (ftState == FiscaltrustDefaults.GetFiscaltrustDefault(SCU_FAIL_STATE))
                    {
                        status = FAILED;
                        errorDetails = MessageContainerList.GetMessage(utilities.ExecutionContext, 5801); //5801, 'Scu Communication Failed'
                    }
                    else
                    {
                        status = FAILED;
                        errorDetails = MessageContainerList.GetMessage(utilities.ExecutionContext, 5078, CLOSE_OP_NAME, ftState);//Unexpected error occurred while &1. Error : &2
                    }
                }
                else
                {
                    string msg = MessageContainerList.GetMessage(utilities.ExecutionContext, 2932, FISCAL_PRINTER, FISCALIZATION);
                    //2932	&1 details are missing in &2 setup.
                    ValidationException ve = new ValidationException(msg);
                    throw ve;
                }
                ConcurrentRequestDetailsDTO concurrentRequestDetailsDTO = new ConcurrentRequestDetailsDTO(-1, -1, ServerDateTime.Now, concurrentProgramId, POS_MACHINE, Convert.ToInt32(posId), posGuId, isSuccessful, status, ftState, CLOSE_OPERATION, errorDetails, true);
                ConcurrentRequestDetailsBL concurrentRequestDetailsBL = new ConcurrentRequestDetailsBL(utilities.ExecutionContext, concurrentRequestDetailsDTO);
                concurrentRequestDetailsBL.Save();
                log.LogMethodExit(errorDetails);
                return errorDetails;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                try
                {
                    string message = ex.Message;
                    if (ex.InnerException != null)
                    {
                        message = message + " " + ex.InnerException.Message;
                    }
                    ConcurrentRequestDetailsDTO concurrentRequestDetailsDTO = new ConcurrentRequestDetailsDTO(-1, -1, ServerDateTime.Now, -1, POS_MACHINE, Convert.ToInt32(posId), posGuId, isSuccessful, status, ftState, CLOSE_OPERATION, message, true);
                    ConcurrentRequestDetailsBL concurrentRequestDetailsBL = new ConcurrentRequestDetailsBL(utilities.ExecutionContext, concurrentRequestDetailsDTO);
                    concurrentRequestDetailsBL.Save();
                }
                catch (Exception exx)
                {
                    log.Error(exx);
                }
                return MessageContainerList.GetMessage(utilities.ExecutionContext, 5078, CLOSE_OP_NAME, ex.Message);//Unexpected error occurred while &1. Error : &2
            }

        }
        
        /// <summary>
        /// PrintReceipt method
        /// </summary>
        /// <param name="receiptRequest"></param>
        /// <returns></returns>
        public override bool PrintReceipt(FiscalizationRequest receiptRequest, ref string Message)
        {
            log.LogMethodEntry(receiptRequest);
            bool fiscalizationResult = false;
            if (receiptRequest != null)
            {
                try
                {
                    Message = SendInvoiceForFiscalization(receiptRequest);
                    if (Message.StartsWith("ERROR"))
                    {
                        log.Debug("Error occurred while fiscalizing the request.");
                        if (receiptRequest.transactionId > 0)
                        {
                            LogFiscalizationError(receiptRequest.transactionId, -1, Message);
                        }
                        else
                        {
                            LogFiscalizationError(-1, receiptRequest.shiftLogId, Message);
                        }
                        Message = string.Empty;
                    }
                    else
                    {
                        fiscalizationResult = true;
                    }
                }
                catch (Exception ex)
                {
                    log.Error("Exception in PrintReceipt", ex);
                }
            }
            log.LogMethodExit(fiscalizationResult);
            return fiscalizationResult;
        }

        /// <summary>
        /// publishInitialReceipt method
        /// </summary>
        /// <returns></returns>
        public override string PublishInitialReceipt(string posId)
        {
            log.LogMethodEntry(posId);
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12;
            string ftState = string.Empty;
            string errorMessage = string.Empty;
            Dictionary<string, string> headers = new Dictionary<string, string>
                {
                    { "accesstoken", FISKALTRUST_TOKEN },
                    { "cashboxid", FISKALTRUST_CASHBOX }
                };
            ExecutionContext executionContext = utilities.ExecutionContext;
            FiskaltrustMapper fiskaltrustMapper = new FiskaltrustMapper(utilities.ExecutionContext);
            ReceiptRequest zeroReceipt = fiskaltrustMapper.GetZeroRequest(executionContext, posId);
            var res = JsonConvert.SerializeObject(zeroReceipt, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            log.LogVariableState("zeroresult", res);
            JObject JZeroRObject = JsonConvert.DeserializeObject<JObject>(res);
            string zeroReceiptRequestJSON = JZeroRObject.ToString();
            log.Debug("zeroReceiptRequestJSON :" + zeroReceiptRequestJSON);
            try
            {
                HTTPServiceUtil httpZeroRecService = new HTTPServiceUtil(executionContext);
                Dictionary<string, string> zeroRecResponseDictionary = httpZeroRecService.Post(zeroReceiptRequestJSON, FiskaltrustPrinter.FISKALTRUST_URL, headers: headers); 

                if (zeroRecResponseDictionary.ContainsKey("OK"))
                {
                    log.Debug("Successful invocation of Zero Receipt.");
                    ReceiptRequest receipt = fiskaltrustMapper.GetInitialRequest(executionContext, posId);
                    //string receiptRequestJSON = JsonConvert.SerializeObject(receipt);
                    var result = JsonConvert.SerializeObject(receipt, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                    log.LogVariableState("result", result);
                    JObject JObject = JsonConvert.DeserializeObject<JObject>(result);
                    string receiptRequestJSON = JObject.ToString();
                    log.Debug("receiptRequestJSON :" + receiptRequestJSON);
                    try
                    {
                        HTTPServiceUtil httpService = new HTTPServiceUtil(executionContext);
                        Dictionary<string, string> initialResponseDictionary = httpService.Post(receiptRequestJSON, FiskaltrustPrinter.FISKALTRUST_URL, headers: headers);  //

                        if (initialResponseDictionary.ContainsKey("OK"))
                        {
                            log.Debug("Successful invocation of Initial Receipt.");
                            foreach (KeyValuePair<string, string> responseMessage in initialResponseDictionary)
                            {
                                string returnFtState = responseMessage.Value;
                                Dictionary<string, object> responseDictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(returnFtState);

                                // Extract the ftState value
                                if (responseDictionary != null && responseDictionary.ContainsKey("ftState"))
                                {
                                    ftState = responseDictionary["ftState"].ToString();
                                    log.LogVariableState("ftState:", ftState);
                                }
                                else
                                {
                                    errorMessage = MessageContainerList.GetMessage(executionContext, "ftState key not found in the response.");
                                    log.Debug(errorMessage);
                                }
                            }
                        }
                        else
                        {
                            errorMessage = MessageContainerList.GetMessage(executionContext, "ERROR ... in publishInitialReceipt ...");
                            log.Error(errorMessage);
                            foreach (KeyValuePair<string, string> responseMessage in initialResponseDictionary)
                            {
                                log.Error("\tKey {responseMessage.key}: Value={responseMessage.Value}");
                            }
                        }
                    }
                    catch (System.Net.WebException webex)
                    {
                        errorMessage = MessageContainerList.GetMessage(executionContext, "Error occurred during publish to fiskaltrust");
                        log.Error(errorMessage, webex);
                    }

                }
                else
                {
                    errorMessage = MessageContainerList.GetMessage(executionContext, "ERROR ... in publishZeroReceipt ...");
                    log.Error(errorMessage);
                    foreach (KeyValuePair<string, string> responseMessage in zeroRecResponseDictionary)
                    {
                        log.Error("\tKey {responseMessage.key}: Value={responseMessage.Value}");
                        errorMessage = errorMessage + responseMessage.Value;
                    }
                }
            }
            catch (System.Net.WebException webex)
            {
                errorMessage = MessageContainerList.GetMessage(executionContext, "Error occurred during publish to fiskaltrust");
                errorMessage = errorMessage + webex.Message;
                log.Error(errorMessage, webex);
            }
            log.LogMethodExit(string.IsNullOrEmpty(ftState) ? errorMessage : ftState);

            return string.IsNullOrEmpty(ftState) ? errorMessage : ftState;

        }

        public override string SendEodReport(string posId)
        {
            log.LogMethodEntry(posId);
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            string ftState = string.Empty;
            string errorMessage = string.Empty;
            Dictionary<string, string> headers = new Dictionary<string, string>
                {
                    { "accesstoken", FISKALTRUST_TOKEN },
                    { "cashboxid", FISKALTRUST_CASHBOX }
                };
            ExecutionContext executionContext = utilities.ExecutionContext;
            FiskaltrustMapper fiskaltrustMapper = new FiskaltrustMapper(utilities.ExecutionContext);
            ReceiptRequest closeReceipt = fiskaltrustMapper.GetCloseRequest(posId);
            var res = JsonConvert.SerializeObject(closeReceipt, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            log.LogVariableState("closeresult", res);
            JObject JCloseRObject = JsonConvert.DeserializeObject<JObject>(res);
            string closeReceiptRequestJSON = JCloseRObject.ToString();
            log.Debug("closeReceiptRequestJSON :" + closeReceiptRequestJSON);
            try
            {
                HTTPServiceUtil httpZeroRecService = new HTTPServiceUtil(executionContext);
                Dictionary<string, string> closeRecResponseDictionary = httpZeroRecService.Post(closeReceiptRequestJSON, FiskaltrustPrinter.FISKALTRUST_URL, headers: headers);  //
                if (closeRecResponseDictionary.ContainsKey("OK"))
                {
                    log.Debug("Successful invocation of Close Receipt.");
                    foreach (KeyValuePair<string, string> responseMessage in closeRecResponseDictionary)
                    {
                        string returnFtState = responseMessage.Value;
                        Dictionary<string, object> responseDictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(returnFtState);

                        // Extract the ftState value
                        if (responseDictionary != null && responseDictionary.ContainsKey("ftState"))
                        {
                            ftState = responseDictionary["ftState"].ToString();
                            log.LogVariableState("ftState:", ftState);
                        }
                        else
                        {
                            errorMessage = MessageContainerList.GetMessage(executionContext, "ftState key not found in the response.");
                            log.Debug(errorMessage);
                        }
                    }
                }
                else
                {
                    errorMessage = MessageContainerList.GetMessage(executionContext, "ERROR ... in publishCloseReceipt ...");
                    log.Error(errorMessage);
                    foreach (KeyValuePair<string, string> responseMessage in closeRecResponseDictionary)
                    {
                        log.Error("\tKey {responseMessage.key}: Value={responseMessage.Value}");
                        errorMessage = errorMessage + responseMessage.Value;
                    }
                }
            }
            catch(System.Net.WebException webex)
            {
                errorMessage = MessageContainerList.GetMessage(executionContext, "Error occured during publish to fiskaltrust");
                errorMessage = errorMessage + webex.Message;
                log.Error(errorMessage, webex);
            }
            log.LogMethodExit(string.IsNullOrEmpty(ftState) ? errorMessage : ftState);
            return string.IsNullOrEmpty(ftState) ? errorMessage : ftState;
        }

        /// <summary>
        /// Calls the fiskaltrust REST API for fiscalization.
        /// </summary>
        /// <param name="fiscalizationRequest">FiscalizationRequest object</param>
        /// <returns>signature returned by fiscalization</returns>
        public override string SendInvoiceForFiscalization(FiscalizationRequest fiscalizationRequest)
        {
            log.LogMethodEntry(fiscalizationRequest);
            string signature = "ERROR ....";
            bool isSuccess = false;
            FiskaltrustMapper fiskaltrustMapper = new FiskaltrustMapper(utilities.ExecutionContext);

            try
            {
                Dictionary<string, string> headers = new Dictionary<string, string>
                {
                    { "accesstoken", FISKALTRUST_TOKEN },
                    { "cashboxid", FISKALTRUST_CASHBOX }
                };
                ReceiptRequest receiptRequest = fiskaltrustMapper.GetFiskaltrustRequest(fiscalizationRequest);
                var result = JsonConvert.SerializeObject(receiptRequest, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                log.LogVariableState("result" , result);
                JObject JObject = JsonConvert.DeserializeObject<JObject>(result);
                string receiptRequestJSON = JObject.ToString();
                log.Debug("String Request :" + receiptRequestJSON);
                HTTPServiceUtil httpService = new HTTPServiceUtil(utilities.ExecutionContext);
                Dictionary<string, string> responseDictionary = httpService.Post(receiptRequestJSON,
                    FiskaltrustPrinter.FISKALTRUST_URL, headers: headers);

                if (responseDictionary.ContainsKey("OK"))
                {
                    log.Debug("Success in publish to fiskaltrust ..");
                    string fiskaltrustResponse = responseDictionary["OK"];
                    ReceiptResponse receiptResponse = JsonConvert.DeserializeObject<ReceiptResponse>(fiskaltrustResponse);

                    string tempSignature = fiskaltrustMapper.GetSignature(receiptResponse);
                    if (!string.IsNullOrEmpty(tempSignature))
                    {
                        isSuccess = true;
                        signature = tempSignature;
                    }
                }

                if (!isSuccess)
                {
                    foreach (KeyValuePair<string, string> responseMessage in responseDictionary)
                    {
                        log.Error("\tKey {responseMessage.key}: Value={responseMessage.Value}");
                        signature = signature + responseMessage.Value;
                    }
                    log.Error("ERROR ... in publish ...");
                }
            }
            catch (System.Net.WebException webex)
            {
                log.Error("Error occured during publish to fiskaltrust", webex);
                signature = signature + webex.Message;
            }

            log.Debug("Signature from fiskaltrust :" + signature);
            log.LogMethodExit(signature);

            return signature;
        }
 



        /// <summary>
        /// Creates Concurrent request for failed fiscalization's
        /// </summary>
        /// <param name="utilities"></param>
        /// <param name="trxId"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private void LogFiscalizationError(int trxId, int shiftlogId, string message)
        {
            log.LogMethodEntry(trxId, shiftlogId, message);
            LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            DateTime dt = lookupValuesList.GetServerDateTime();
            string currentDate = dt.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);
            currentDate = string.Concat(currentDate, " 00:00:00");
            int fiskalTrustProgramId = -1; 
            log.Debug("The request start date : " + currentDate);

            List<KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>> searchByProgramsParameters =
                new List<KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>>();
            searchByProgramsParameters.Add(new KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>
                (ConcurrentProgramsDTO.SearchByProgramsParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
            searchByProgramsParameters.Add(new KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>
                (ConcurrentProgramsDTO.SearchByProgramsParameters.PROGRAM_NAME, "fiskaltrust"));
            searchByProgramsParameters.Add(new KeyValuePair<ConcurrentProgramsDTO.SearchByProgramsParameters, string>
                (ConcurrentProgramsDTO.SearchByProgramsParameters.ACTIVE_FLAG, "1"));
            ConcurrentProgramList concurrentProgramList = new ConcurrentProgramList(utilities.ExecutionContext);
            List<ConcurrentProgramsDTO> concurrentProgramsDTOList =
                concurrentProgramList.GetAllConcurrentPrograms(searchByProgramsParameters);

            if (concurrentProgramsDTOList != null)
            {
                log.Debug("Concurrent program ID :" + concurrentProgramsDTOList.First().ProgramId);
                fiskalTrustProgramId = concurrentProgramsDTOList.First().ProgramId;
                string parafaitObject = string.Empty;
                int parafaitObjectId = -1;
                if (trxId > 0)
                {
                    parafaitObject = "Transaction";
                    parafaitObjectId = trxId;
                }
                else
                {
                    parafaitObject = "ShiftOperation";
                    parafaitObjectId = shiftlogId;
                }

                ConcurrentRequestDetailsListBL concurrentRequestDetailsListBL = new ConcurrentRequestDetailsListBL(utilities.ExecutionContext);
                List<KeyValuePair<ConcurrentRequestDetailsDTO.SearchByParameters, string>> rsearchParameters = new List<KeyValuePair<ConcurrentRequestDetailsDTO.SearchByParameters, string>>();
                rsearchParameters.Add(new KeyValuePair<ConcurrentRequestDetailsDTO.SearchByParameters, string>(ConcurrentRequestDetailsDTO.SearchByParameters.CONCURRENT_PROGRAM_ID, fiskalTrustProgramId.ToString()));
                rsearchParameters.Add(new KeyValuePair<ConcurrentRequestDetailsDTO.SearchByParameters, string>(ConcurrentRequestDetailsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                rsearchParameters.Add(new KeyValuePair<ConcurrentRequestDetailsDTO.SearchByParameters, string>(ConcurrentRequestDetailsDTO.SearchByParameters.PARAFAIT_OBJECT_ID, parafaitObjectId.ToString()));
                List<ConcurrentRequestDetailsDTO> result = concurrentRequestDetailsListBL.GetConcurrentRequestDetailsDTOList(rsearchParameters);
                if (result == null || result.Any() == false)
                {
                    ConcurrentRequestsDTO concurrentRequestsDTO = new ConcurrentRequestsDTO(-1, concurrentProgramsDTOList.First().ProgramId, -1,
                                          DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                                          utilities.ExecutionContext.GetUserId(),
                                          DateTime.Now.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture),
                                          DateTime.Now.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture), string.Empty,
                                          "Running", "Normal", false, string.Empty, string.Empty,
                                          string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty,
                                          string.Empty, string.Empty, -1, -1, false);
                    ConcurrentRequests concurrentRequests = new ConcurrentRequests(utilities.ExecutionContext, concurrentRequestsDTO);
                    concurrentRequests.Save();
                    concurrentRequestsDTO = concurrentRequests.GetconcurrentRequests;
                    log.Debug("Concurrent Request ID :" + concurrentRequestsDTO.RequestId);
                    ConcurrentRequestDetailsDTO concurrentRequestDetailsDTO = new ConcurrentRequestDetailsDTO(-1, concurrentRequestsDTO.RequestId, dt,
                        concurrentRequestsDTO.ProgramId, parafaitObject, parafaitObjectId, String.Empty, false, "Failed", String.Empty, String.Empty, message, true);
                    ConcurrentRequestDetailsBL concurrentRequestDetailsBL = new ConcurrentRequestDetailsBL(utilities.ExecutionContext, concurrentRequestDetailsDTO);
                    concurrentRequestDetailsBL.Save();
                }
            }
            else
            {
                log.Error("ERROR: Concurrent Program for fiskaltrust does not exit");
            }
            log.LogMethodExit();
        }


        public override bool OpenPort()
        {
            log.LogMethodEntry();
            bool result = true;
            log.LogMethodExit(result);
            return result;
        }
        public override void ClosePort()
        {
            log.LogMethodEntry();
            log.LogMethodExit();
        }
    }

}
