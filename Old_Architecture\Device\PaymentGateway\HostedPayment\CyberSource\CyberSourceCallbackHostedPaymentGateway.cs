﻿/********************************************************************************************
 * Project Name -  CyberSource Hosted Payment Gateway                                                                     
 * Description  -  Class to handle the payment of CyberSource Hosted Payment Gateway - Callback for Angular
 ********************************************************************************************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 ********************************************************************************************
*2.152.00     28-Nov-2023    Yashodhara C H                 Created for website
 ********************************************************************************************/

using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.CyberSource
{
    class CyberSourceCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        //HOSTED CHECKOUT Config params
        private string access_key;
        private string locale;
        private string profile_id;
        private string signed_date_time;
        private string signed_field_names;
        private string transaction_type;
        private string transaction_uuid;
        private string unsigned_field_names;
        private string secret_key;
        //private string customerIpAddress;
        private string post_url;
        private string signature;
        string objectGuid;
        string webformurl;
        private string SECRET_KEY;

        //API config params
        private string HOST_URL;
        private string REST_SECRET_KEY;
        private string PUBLIC_KEY;
        private string MERCHANT_ID;
        const string SCHEME = "https://";
        const string ALGORITHM = "HmacSHA256";

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";
        private string paymentPageLink;

        string ignore_avs;
        string currency;
        string payer_authentication_transaction_mode;
        string payer_authentication_challenge_code;

        private Dictionary<string, string> configParameters = new Dictionary<string, string>();
        private HostedGatewayDTO hostedGatewayDTO;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"100", PaymentStatusType.SUCCESS},
            {"110", PaymentStatusType.FAILED}, //Partial Payment
            {"104", PaymentStatusType.FAILED}, //Duplicate payment
            {"102", PaymentStatusType.FAILED},
            {"150", PaymentStatusType.FAILED},
            {"151", PaymentStatusType.FAILED},
            {"152", PaymentStatusType.FAILED},
            {"200", PaymentStatusType.FAILED},
            {"201", PaymentStatusType.FAILED},
            {"202", PaymentStatusType.FAILED},
            {"203", PaymentStatusType.FAILED},
            {"204", PaymentStatusType.FAILED},
            {"205", PaymentStatusType.FAILED},
            {"207", PaymentStatusType.FAILED},
            {"208", PaymentStatusType.FAILED},
            {"210", PaymentStatusType.FAILED},
            {"211", PaymentStatusType.FAILED},
            {"221", PaymentStatusType.FAILED},
            {"222", PaymentStatusType.FAILED},
            {"230", PaymentStatusType.FAILED},
            {"231", PaymentStatusType.FAILED},
            {"232", PaymentStatusType.FAILED},
            {"233", PaymentStatusType.FAILED},
            {"234", PaymentStatusType.FAILED},
            {"236", PaymentStatusType.FAILED},
            {"240", PaymentStatusType.FAILED},
            {"475", PaymentStatusType.FAILED},
            {"476", PaymentStatusType.FAILED},
            {"481", PaymentStatusType.FAILED},
            {"520", PaymentStatusType.FAILED},
            {"-1", PaymentStatusType.NONE},
            {"-2", PaymentStatusType.NONE},
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"PENDING", PaymentStatusType.SUCCESS},// Ref link: https://developer.cybersource.com/api-reference-assets/index.html#payments_refund_refund-a-payment_responsefielddescription_201_status
        };
        private static readonly Dictionary<string, PaymentStatusType> VoidStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"VOIDED", PaymentStatusType.SUCCESS},
            {"CANCELLED", PaymentStatusType.FAILED},// Ref link: https://developer.cybersource.com/api-reference-assets/index.html#payments_void_void-a-payment_responsefielddescription_201_status
            {"FAILED", PaymentStatusType.FAILED},
        };

        public CyberSourceCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            this.hostedGatewayDTO = new HostedGatewayDTO();
            this.Initialize();
            this.BuildTransactions = false;
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();
            //HOSTED CHECKOUT Config params

            this.transaction_uuid = Guid.NewGuid().ToString();
            this.signed_field_names = "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,merchant_defined_data1";
            this.unsigned_field_names = "";
            this.signed_date_time = DateTime.Now.ToUniversalTime().ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
            this.locale = "en-us";
            this.transaction_type = "sale";  //sale or authorization
            //this.customerIpAddress = "";// Utilities.GetIPAddress();

            // Rest API Config params
            HOST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            REST_SECRET_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            PUBLIC_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            MERCHANT_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            configParameters.Clear();
            LoadConfigParams();

            this.profile_id = utilities.getParafaitDefaults("CYBERSOURCE_HOSTED_PAYMENT_PROFILE_ID");
            this.access_key = utilities.getParafaitDefaults("CYBERSOURCE_HOSTED_PAYMENT_ACCESS_KEY");
            SECRET_KEY = SystemOptionContainerList.GetSystemOption(utilities.ParafaitEnv.SiteId, "Hosted Payment keys", "CyberSource secret key");

            
            this.post_url = utilities.getParafaitDefaults("CYBERSOURCE_HOSTED_PAYMENT_PAYMENT_URL");
            webformurl = "/account/CyberSource";

            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";

            if (string.IsNullOrWhiteSpace(this.profile_id))
            {
                errMsg = String.Format(errMsgFormat, "CYBERSOURCE_HOSTED_PAYMENT_PROFILE_ID");
            }
            else if (string.IsNullOrWhiteSpace(this.access_key))
            {
                errMsg = String.Format(errMsgFormat, "CYBERSOURCE_HOSTED_PAYMENT_ACCESS_KEY");
            }
            else if (string.IsNullOrWhiteSpace(this.SECRET_KEY))
            {
                errMsg = String.Format(errMsgFormat, "CyberSource secret key");
            }
            else if (string.IsNullOrWhiteSpace(post_url))
            {
                errMsg = String.Format(errMsgFormat, "CYBERSOURCE_HOSTED_PAYMENT_PAYMENT_URL");
            }

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }



            this.hostedGatewayDTO.PGSuccessResponseMessage = "OK";
            this.hostedGatewayDTO.PGFailedResponseMessage = "OK";

            String apiSite = "";
            String webSite = "";

            apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;
            
            log.Debug("apiSite " + apiSite);

            webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;
            //log.Debug("webSite " + webSite);

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }
            
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    var queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.CyberSourceCallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                //log.Debug("successResponseAPIURL " + successResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                //log.Debug("failureResponseAPIURL " + failureResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                //log.Debug("cancelResponseAPIURL " + cancelResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                //log.Debug("callbackResponseAPIURL " + callbackResponseAPIURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                //log.Debug("successRedirectURL " + this.hostedGatewayDTO.SuccessURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.CyberSourceCallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }



            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;

            log.Debug(successResponseAPIURL);
            log.LogMethodExit();
        }

        /// <summary>
        /// Creates a initial gateway request.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="paymentToken"></param>
        /// <returns>HostedGatewayDTO</returns>
        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO, paymentToken);
                this.hostedGatewayDTO.RequestURL = this.post_url;
                log.LogMethodEntry("CCRequestSite:" + utilities.ExecutionContext.GetSiteId());
                //this.customerIpAddress = transactionPaymentsDTO.Memo;
                objectGuid = transactionPaymentsDTO.Reference;
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;
                CCRequestPGWDTO cCRequestPGWDTO = this.CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
                requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
                requestParamsDict.Add("paymentToken", paymentToken);

                this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
                this.hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(this.hostedGatewayDTO.GatewayRequestString, paymentPageLink);
                this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(requestParamsDict, paymentPageLink, "authForm");

                log.Debug("request url:" + this.hostedGatewayDTO.RequestURL);
                log.Debug("request string:" + this.hostedGatewayDTO.GatewayRequestString);
                log.Debug("direct request link:" + this.hostedGatewayDTO.PaymentRequestLink);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + this.hostedGatewayDTO.ToString());
            return this.hostedGatewayDTO;

        }



        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, transactionPaymentsDTO.Guid);//here, transactionPaymentsDTO.guid contains guid of ccrequestpgw
                CCRequestPGWDTO cCRequestPGWDTO = cCRequestPGWBL.CCRequestPGWDTO;

                this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO));
                this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO), this.post_url, "fromCyberSourceForm");

                log.Debug("Request string:" + this.hostedGatewayDTO.GatewayRequestString);
                log.Debug("Request Form string:" + this.hostedGatewayDTO.GatewayRequestFormString);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + this.hostedGatewayDTO.ToString());
            return this.hostedGatewayDTO;
        }

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="postparamslist"></param>
        ///// <param name="URL"></param>
        ///// <param name="FormName"></param>
        ///// <param name="submitMethod"></param>
        ///// <returns></returns>
        //private string GetSubmitFormValue(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        //{
        //    string Method = submitMethod;
        //    System.Text.StringBuilder builder = new System.Text.StringBuilder();
        //    builder.Clear();
        //    builder.Append("<html><head>");
        //    builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
        //    builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
        //    builder.Append(string.Format("</head><body onload=\"document.{0}.submit()\">", FormName));
        //    builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

        //    foreach (KeyValuePair<string, string> param in postparamslist)
        //    {
        //        builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
        //    }

        //    builder.Append("</form>");
        //    builder.Append("</body></html>");

        //    return builder.ToString();
        //}

        /// <summary>
        /// GetSubmitFormKeyValueList
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append(string.Format("</head><body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");

            return builder.ToString();
        }

        /// <summary>
        /// Creates A IDictionary
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns></returns>
        private IDictionary<string, string> SetPostParameters(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCRequestPGWDTO);

            SecurityTokenBL securityTokenBL = new SecurityTokenBL(utilities.ExecutionContext);
            string guid = string.IsNullOrEmpty(objectGuid) ? Guid.NewGuid().ToString() : objectGuid;
            int tokenLifeTime = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "JWT_TOKEN_LIFE_TIME", 0);
            securityTokenBL.GenerateNewJWTToken("External POS", guid, utilities.ExecutionContext.GetSiteId().ToString(), "-1", "-1", "Customer", "-1", null, tokenLifeTime);

            IDictionary<string, string> postparamslist = new Dictionary<string, string>();

            postparamslist.Clear();
            postparamslist.Add("access_key", this.access_key);
            postparamslist.Add("profile_id", this.profile_id);
            postparamslist.Add("transaction_uuid", this.transaction_uuid);
            postparamslist.Add("unsigned_field_names", this.unsigned_field_names);
            postparamslist.Add("signed_date_time", this.signed_date_time);
            postparamslist.Add("locale", this.locale);
            postparamslist.Add("transaction_type", this.transaction_type);
            postparamslist.Add("reference_number", transactionPaymentsDTO.TransactionId.ToString());
            postparamslist.Add("amount", transactionPaymentsDTO.Amount.ToString("0.00"));
            postparamslist.Add("currency", transactionPaymentsDTO.CurrencyCode);
            postparamslist.Add("merchant_defined_data1", transactionPaymentsDTO.TransactionId.ToString());

            if (!string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardName) && !string.IsNullOrWhiteSpace(transactionPaymentsDTO.NameOnCreditCard) && !string.IsNullOrWhiteSpace(transactionPaymentsDTO.Memo))
            {
                postparamslist.Add("bill_to_forename", transactionPaymentsDTO.CreditCardName);

                postparamslist.Add("bill_to_email", transactionPaymentsDTO.NameOnCreditCard);

                postparamslist.Add("bill_to_surname", transactionPaymentsDTO.Memo);
                this.signed_field_names += ",bill_to_forename,bill_to_email,bill_to_surname";
            }

            postparamslist.Add("signed_field_names", this.signed_field_names);

            //postparamslist.Add("customer_ip_address", this.customerIpAddress);

            validate(postparamslist);

            this.signature = PaymentSecurity.sign(postparamslist, SECRET_KEY);
            postparamslist.Add("signature", this.signature);
            postparamslist.Add("customerToken", securityTokenBL.GetSecurityTokenDTO.Token);
            postparamslist.Add("usedId", transactionPaymentsDTO.CustomerCardProfileId);
            postparamslist.Add("email", transactionPaymentsDTO.NameOnCreditCard);

            log.LogMethodExit(postparamslist);
            return postparamslist;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="postparamslist"></param>
        private void validate(IDictionary<string, string> postparamslist)
        {
            List<string> mandatoryFields = new List<string>();
            mandatoryFields.Clear();
            mandatoryFields.Add("access_key");
            mandatoryFields.Add("profile_id");
            mandatoryFields.Add("secret_key");
            //mandatoryFields.Add("customer_ip_address");
            mandatoryFields.Add("paymentUrl");
            mandatoryFields.Add("transaction_uuid");
            mandatoryFields.Add("signed_field_names");
            mandatoryFields.Add("signed_date_time");
            mandatoryFields.Add("locale");
            mandatoryFields.Add("transaction_type");
            mandatoryFields.Add("reference_number");
            mandatoryFields.Add("amount");
            mandatoryFields.Add("currency");
            mandatoryFields.Add("merchant_defined_data1");

            foreach (string mandatoryField in mandatoryFields)
            {
                foreach (KeyValuePair<string, string> keyValue in postparamslist)
                {
                    if (keyValue.Key == mandatoryField)
                    {
                        if (string.IsNullOrEmpty(keyValue.Value))
                        {
                            throw new Exception(keyValue.Key + " is mandatory field");
                        }
                    }
                }
            }
        }

        private CyberSourceResponseDTO GetResposeObj(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            //utf8=%E2%9C%93&auth_cv_result=2&req_locale=en-gb&req_payer_authentication_indicator=01&req_card_type_selection_indicator=1&payer_authentication_enroll_veres_enrolled=U&req_bill_to_surname=Pai&req_card_expiry_date=12-2023&card_type_name=Visa&auth_amount=15.00&auth_response=0&bill_trans_ref_no=155728&req_payment_method=card&req_payer_authentication_merchant_name=Test+Company&auth_time=2023-01-16T125802Z&payer_authentication_enroll_e_commerce_indicator=internet&transaction_id=6738738818486230204002&req_card_type=001&payer_authentication_transaction_id=mntJCHI2jJQJTY2PRUH0&req_payer_authentication_transaction_mode=S&req_merchant_defined_data1=155728&req_merchant_defined_data2=636&auth_avs_code=U&auth_code=15&payer_authentication_specification_version=2.1.0&req_bill_to_address_country=IN&auth_cv_result_raw=3&req_profile_id=8F564CA8-C495-40DC-8DE6-6B478F5073EF&signed_date_time=2023-01-16T12%3A58%3A02Z&req_bill_to_address_line1=mangalore&req_card_number=xxxxxxxxxxxx3705&signature=AO10gF6Friey8H3a5pcmpkzvIYHAJ%2BAI4l39oFnvxNk%3D&req_bill_to_address_city=Mangalore&req_bill_to_address_postal_code=575001&reason_code=100&req_bill_to_forename=Nitin&req_payer_authentication_acs_window_size=03&request_token=Axj%2F%2FwSTbU7JlAt3Rj5iABIMxatW7JwojlScBjsBURypOAx3nQu7heRDJpJl6MWZ7cQYOJNtTsmUC3dGPmIAzw0a&req_amount=15&req_bill_to_email=nitin.pai%40semnox.com&payer_authentication_reason_code=100&auth_avs_code_raw=00&req_payer_authentication_challenge_code=04&req_currency=GBP&decision=ACCEPT&message=Request+was+processed+successfully.&signed_field_names=transaction_id%2Cdecision%2Creq_access_key%2Creq_profile_id%2Creq_transaction_uuid%2Creq_transaction_type%2Creq_reference_number%2Creq_amount%2Creq_currency%2Creq_locale%2Creq_payment_method%2Creq_bill_to_forename%2Creq_bill_to_surname%2Creq_bill_to_email%2Creq_bill_to_address_line1%2Creq_bill_to_address_city%2Creq_bill_to_address_country%2Creq_bill_to_address_postal_code%2Creq_card_number%2Creq_card_type%2Creq_card_type_selection_indicator%2Creq_card_expiry_date%2Ccard_type_name%2Creq_merchant_defined_data1%2Creq_merchant_defined_data2%Creq_payer_authentication_acs_window_size%2Creq_payer_authentication_indicator%2Creq_payer_authentication_challenge_code%2Creq_payer_authentication_transaction_mode%2Creq_payer_authentication_merchant_name%2Cmessage%2Creason_code%2Cauth_avs_code%2Cauth_avs_code_raw%2Cauth_response%2Cauth_amount%2Cauth_code%2Cauth_cv_result%2Cauth_cv_result_raw%2Cauth_time%2Crequest_token%2Cbill_trans_ref_no%2Cpayer_authentication_reason_code%2Cpayer_authentication_enroll_e_commerce_indicator%2Cpayer_authentication_specification_version%2Cpayer_authentication_transaction_id%2Cpayer_authentication_enroll_veres_enrolled%2Csigned_field_names%2Csigned_date_time&req_transaction_uuid=7c904494-2e0f-43b1-9ec2-34dfd8bfb3f1&req_transaction_type=sale&req_access_key=248d9649918d38c2b1deaeca0253c7e1&req_reference_number=155728

            CyberSourceResponseDTO responseObj = null;

            string jsonString = ConvertQueryStringToJson(gatewayResponse);
            log.Debug("Response as JSON: " + jsonString);

            responseObj = JsonConvert.DeserializeObject<CyberSourceResponseDTO>(jsonString.ToString());

            log.LogMethodExit(JsonConvert.SerializeObject(responseObj));
            return responseObj;
        }

        private string ConvertQueryStringToJson(string gatewayResponse)
        {
            log.LogMethodEntry();
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(gatewayResponse);

            Dictionary<string, string> responseDictionary = new Dictionary<string, string>();

            foreach (var key in responseCollection.AllKeys)
            {
                responseDictionary.Add(key, responseCollection[key]);
            }

            string responseJson = JsonConvert.SerializeObject(responseDictionary);

            log.LogMethodExit(responseJson);
            return responseJson;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="gatewayResponse"></param>
        /// <returns></returns>
        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            if (this.hostedGatewayDTO == null)
            {
                this.hostedGatewayDTO = new HostedGatewayDTO();
            }
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();

            // proceed with processing
            CyberSourceResponseDTO responseObj = GetResposeObj(gatewayResponse);
            log.Debug("Response after deserializing: " + responseObj.ToString());
            if (responseObj != null && responseObj.req_reference_number != null)
            {
                log.Debug(responseObj.req_reference_number + ":" + responseObj.transaction_id);
                hostedGatewayDTO.TrxId = Convert.ToInt32(responseObj.req_reference_number);
                hostedGatewayDTO.GatewayReferenceNumber = responseObj.transaction_id;
                log.Debug(hostedGatewayDTO.TrxId + ":" + hostedGatewayDTO.GatewayReferenceNumber);
            }
            else
            {
                log.Error("Response for Sale Transaction doesn't contain TrxId.");
                throw new Exception("Error processing your payment");
            }

            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            this.hostedGatewayDTO.CCTransactionsPGWDTO = null;
            string paymentStatus = "";
            PaymentStatusType salePaymentStatus = PaymentStatusType.FAILED;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            try
            {
                if (string.IsNullOrWhiteSpace(gatewayResponse))
                {
                    log.Error("Response for Sale Transaction was empty.");
                    throw new Exception("Error processing your payment");
                }

                CyberSourceResponseDTO response = GetResposeObj(gatewayResponse);
                
                if(response == null)
                {
                    log.Error("Response for Sale Transaction was empty.");
                    throw new Exception("Error processing your payment");
                }

                string currencyCode = response.req_currency == null ? "" : response.req_currency;
                string orderReference = response.req_reference_number == null ? "" : response.req_reference_number;
                string responseDecision = response.decision == null ? "" : response.decision;

                if (!string.IsNullOrWhiteSpace(responseDecision) && responseDecision.ToLower() == "accept" && !String.IsNullOrWhiteSpace(response.signature))
                {
                    string serializedResponse = ConvertQueryStringToJson(gatewayResponse);
                    bool result = verifySignature(serializedResponse);
                    if (!result)
                    {
                        throw new Exception("Payment signature verification failed!");
                    }

                    salePaymentStatus = MapPaymentStatus(response.reason_code.ToString(), PaymentGatewayTransactionType.SALE);
                    log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                    if(salePaymentStatus == PaymentStatusType.SUCCESS)
                    {
                        hostedGatewayDTO.PaymentStatus = salePaymentStatus;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                        paymentStatus = "APPROVED_";

                    }
                    else
                    {
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                        paymentStatus = "FAILED_";
                    }
                }
                else
                {
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    paymentStatus = "FAILED_";
                }

                hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(orderReference);
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = response.auth_code == null ? "" : response.auth_code;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = response.transaction_id == null ? "" : response.transaction_id;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = response.req_card_number == null ? "" : response.req_card_number;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = response.card_type_name == null ? "" : response.card_type_name;
                hostedGatewayDTO.TransactionPaymentsDTO.Amount = response.auth_amount == null ? 0.0 : Convert.ToDouble(response.auth_amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = currencyCode;
                hostedGatewayDTO.PaymentStatusMessage = response.message == null ? "" : response.message;

                log.Debug("Got the DTO " + hostedGatewayDTO.ToString());

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                this.TransactionSiteId = cCRequestsPGWDTO.SiteId;
                log.Debug("Trying to update the CC request to payment processing status");
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);


                int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(PaymentProcessStatusType.PAYMENT_PROCESSING.ToString(), hostedGatewayDTO.PaymentProcessStatus.ToString());

                if (rowsUpdated == 0)
                {
                    log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                }
                else
                {
                    log.Debug("CC request updated to " + hostedGatewayDTO.PaymentProcessStatus.ToString());
                }

                if (!String.IsNullOrEmpty(cCRequestsPGWDTO.ReferenceNo))
                {
                    string[] resvalues = cCRequestsPGWDTO.ReferenceNo.ToString().Split('|');
                    foreach (string word in resvalues)
                    {
                        if (word.Contains("PaymentModeId") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(word.Split(':')[1]);
                        }
                        else if (word.Contains("CurrencyCode") == true)
                        {
                            hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = word.Split(':')[1];
                        }
                    }
                }

                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = null;

                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }

                if (cCTransactionsPGWDTOList == null)
                {
                    log.Debug("No CC Transactions found");

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString(); 
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.Authorize = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.Purchase = hostedGatewayDTO.TransactionPaymentsDTO.Amount.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.AcctNo = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber;
                    cCTransactionsPGWDTO.CardType = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName;
                    cCTransactionsPGWDTO.DSIXReturnCode = string.IsNullOrWhiteSpace(response.message) ? "" : response.message;
                    cCTransactionsPGWDTO.TextResponse = string.Concat(paymentStatus, string.IsNullOrWhiteSpace(response.message) ? "" : response.message);
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                    this.hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }

                log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
                log.LogMethodExit(hostedGatewayDTO);
            }
            catch (Exception ex)
            {
                log.Error("Payment Processing failed", ex);
                throw new Exception(ex.Message);
            }
            return hostedGatewayDTO;
        }


        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = null;

            if (transactionPaymentsDTO != null)
            {
                try
                {
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;

                    if (transactionPaymentsDTO.CCResponseId > -1)
                    {
                        CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                        List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                        searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                        List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                        // get transaction type of sale CCRequest record
                        ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                        log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO);

                        // to get original TrxId  (in case of POS refund)
                        refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                        log.Debug("Original TrxId for refund: " + refundTrxId);
                    }
                    else
                    {
                        refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                        log.Debug("Refund TrxId for refund: " + refundTrxId);
                    }

                    DateTime originalPaymentDate = new DateTime();
                    CCRequestPGWDTO ccOrigRequestPGWDTO = null;
                    CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                    List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                    searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, refundTrxId));
                    List<CCRequestPGWDTO> cCRequestPGWDTOList = cCRequestPGWListBL.GetCCRequestPGWDTOList(searchParametersPGW);

                    if (cCRequestPGWDTOList != null)
                    {
                        ccOrigRequestPGWDTO = cCRequestPGWDTOList[0]; // to get SALE Tx Type
                    }
                    else
                    {
                        log.Error("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                        throw new Exception("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                    }

                    if (ccOrigRequestPGWDTO != null)
                    {
                        originalPaymentDate = ccOrigRequestPGWDTO.RequestDatetime;
                    }

                    // Define Business Start and End Time
                    DateTime bussStartTime = utilities.getServerTime().Date.AddHours(Convert.ToInt32(ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "BUSINESS_DAY_START_TIME")));
                    DateTime bussEndTime = bussStartTime.AddDays(1);
                    if (utilities.getServerTime() < bussStartTime)
                    {
                        bussStartTime = bussStartTime.AddDays(-1);
                        bussEndTime = bussStartTime.AddDays(1);
                    }

                    // Decide Void vs Refund basis the Date
                    if ((originalPaymentDate >= bussStartTime) && (originalPaymentDate <= bussEndTime))
                    {
                        // same day: VOID
                        log.Info("Same day: Void");
                        bool isVoidSuccess = false;
                        CyberSourceRequestDTO cyberSourceRequestDTO = null;
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);
                        CyberSourceCommandHandler cyberSourceCommandHandler = new CyberSourceCommandHandler();
                        cyberSourceRequestDTO = cyberSourceCommandHandler.getRequestDTO(transactionPaymentsDTO.Reference);
                        log.Debug("getRequestDTO- cyberSourceRequestDTO: " + cyberSourceRequestDTO);
                        VoidRequestDTO voidRequestDTO = null;
                        voidRequestDTO = new VoidRequestDTO
                        {
                            clientReferenceInformation = new Clientreferenceinformation
                            {
                                code = refundTrxId, // ccRequestId
                            },
                        };
                        VoidResponseDTO voidResponseDTO;
                        voidResponseDTO = cyberSourceCommandHandler.CreateVoid(cyberSourceRequestDTO, voidRequestDTO, configParameters);
                        log.Debug("voidResponseDTO: " + voidResponseDTO);
                        if (voidResponseDTO == null)
                        {
                            //throw error
                            string errorMessage = "Void failed";
                            throw new Exception(errorMessage);
                        }
                        string rawVoidStatus = voidResponseDTO.status;

                        PaymentStatusType voidPaymentStatus = MapPaymentStatus(rawVoidStatus, PaymentGatewayTransactionType.VOID);

                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.RecordNo = voidResponseDTO?.clientReferenceInformation?.code ?? refundTrxId; //parafait TrxId
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.VOID.ToString();
                        ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                        ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                        ccTransactionsPGWDTO.Authorize = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.Purchase = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.TextResponse = voidResponseDTO?.status;
                        ccTransactionsPGWDTO.PaymentStatus = voidPaymentStatus.ToString();

                        if (voidPaymentStatus == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Void Success");
                            isVoidSuccess = true;
                            ccTransactionsPGWDTO.Authorize = voidResponseDTO.voidAmountDetails?.voidAmount;
                            ccTransactionsPGWDTO.RefNo = voidResponseDTO.id; //paymentId

                        }
                        else
                        {
                            log.Error("Void failed");
                            isVoidSuccess = false;
                        }

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                        if (!isVoidSuccess)
                        {
                            throw new Exception("Void failed");
                        }
                    }
                    else
                    {
                        // Next Day: Refund
                        log.Info("Next Day: Refund");
                        bool isRefundSuccess = false;
                        CyberSourceRequestDTO cyberSourceRequestDTO = null;
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);
                        CyberSourceCommandHandler cyberSourceCommandHandler = new CyberSourceCommandHandler();
                        cyberSourceRequestDTO = cyberSourceCommandHandler.getRequestDTO(transactionPaymentsDTO.Reference);
                        log.Debug("getRequestDTO- cyberSourceRequestDTO: " + cyberSourceRequestDTO);
                        RefundResponseDTO refundResponseDTO = null;
                        RefundRequestDTO refundRequestDTO = null;
                        refundRequestDTO = new RefundRequestDTO
                        {
                            clientReferenceInformation = new Clientreferenceinformation
                            {
                                code = refundTrxId, // ccRequestId
                            },
                            orderInformation = new Orderinformation
                            {
                                amountDetails = new Amountdetails
                                {
                                    totalAmount = Convert.ToString(transactionPaymentsDTO.Amount),
                                    //currency = CURRENCY_CODE,
                                }
                            },
                        };
                        refundResponseDTO = cyberSourceCommandHandler.CreateRefund(cyberSourceRequestDTO, refundRequestDTO, configParameters);
                        log.Debug("refundResponseDTO: " + refundResponseDTO);

                        if (refundResponseDTO == null)
                        {
                            //throw error
                            string errorMessage = "Refund failed";
                            throw new Exception(errorMessage);
                        }
                        string rawRefundStatus = refundResponseDTO.status;

                        PaymentStatusType refundPaymentStatus = MapPaymentStatus(rawRefundStatus, PaymentGatewayTransactionType.REFUND);

                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.RecordNo = refundResponseDTO?.clientReferenceInformation?.code ?? refundTrxId; //parafait TrxId
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                        ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.TextResponse = refundResponseDTO?.status;
                        ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();

                        if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Refund Success");
                            ccTransactionsPGWDTO.Authorize = refundResponseDTO.refundAmountDetails.refundAmount;
                            ccTransactionsPGWDTO.RefNo = refundResponseDTO.id; //paymentId
                            isRefundSuccess = true;
                        }
                        else
                        {
                            log.Error("Refund failed");
                            isRefundSuccess = false;
                        }

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();
                        transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                        if (!isRefundSuccess)
                        {
                            throw new Exception("Refund failed");
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw;
                }
            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType defaultStatus = PaymentStatusType.FAILED; //default status
            PaymentStatusType paymentStatusType = defaultStatus;

            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.VOID:
                        pgwStatusMappingDict = VoidStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to failed.");
                    paymentStatusType = defaultStatus;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentStatusType = defaultStatus;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentGatewayTransactionType);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PaymentStatusType mappedPaymentStatus = PaymentStatusType.NONE;
            string trxIdString = string.Empty;
            CyberSourceCommandHandler commandHandler = new CyberSourceCommandHandler();

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("cCRequestsPGWDTO is null. Failed to perform Make payment status");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                TxSearchRequestDTO searchRequestDTO = new TxSearchRequestDTO
                {
                    query = "clientReferenceInformation.code:" + trxIdString,
                    sort = "id:desc",
                };

                TxSearchResponseDTO txSearchResponse = commandHandler.CreateTxSearch(searchRequestDTO, configParameters);
                log.Info("txSearchResponse:" + txSearchResponse.ToString());

                if (txSearchResponse != null && txSearchResponse.totalCount > 0)
                {
                    log.Info("Total count of txSearchResponse: " + txSearchResponse.totalCount.ToString());
                    TxStatusDTO txStatus = commandHandler.GetTxStatusFromSearchResponse(txSearchResponse);

                    log.Debug("GetTxStatusFromSearchResponse- txStatus: " + txStatus);

                    mappedPaymentStatus = MapPaymentStatus(txStatus?.reasonCode.ToString(), PaymentGatewayTransactionType.STATUSCHECK);
                    log.Debug("Value of mappedPaymentStatus: " + mappedPaymentStatus.ToString());

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = trxIdString; //parafait TrxId
                    cCTransactionsPGWDTO.TextResponse = txStatus?.TextResponse;
                    cCTransactionsPGWDTO.DSIXReturnCode = txStatus?.reasonCode.ToString();
                    cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                    cCTransactionsPGWDTO.PaymentStatus = mappedPaymentStatus.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.RefNo = txStatus?.RecordNo; //paymentId
                    cCTransactionsPGWDTO.Authorize = txStatus?.Authorize;
                    cCTransactionsPGWDTO.Purchase = txStatus?.Purchase;
                    cCTransactionsPGWDTO.AuthCode = txStatus?.AuthCode;
                    cCTransactionsPGWDTO.AcctNo = txStatus?.AcctNo;

                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                    CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    cCTransactionsPGWBL.Save();
                }
                else
                {
                    log.Error("Response count is 0");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }

        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);

            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            CyberSourceCommandHandler cyberSourcecommandHandler = new CyberSourceCommandHandler();
            dynamic resData;
            try
            {
                if (string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("No Transaction id passed");
                }

                // build Tx Search requestDTO
                TxSearchRequestDTO searchRequestDTO = cyberSourcecommandHandler.GetTxSearchRequestDTO(trxId);
                log.Debug("GetTxSearchRequestDTO- searchRequestDTO: " + searchRequestDTO);
                TxSearchResponseDTO txSearchResponseDTO = cyberSourcecommandHandler.CreateTxSearch(searchRequestDTO, configParameters);
                log.Debug("CreateTxSearch- txSearchResponseDTO: " + txSearchResponseDTO);

                if (txSearchResponseDTO != null && txSearchResponseDTO.totalCount > 0)
                {
                    log.Info("Total count of txSearchResponse: " + txSearchResponseDTO.totalCount.ToString());

                    TxStatusDTO txStatus = cyberSourcecommandHandler.GetTxStatusFromSearchResponse(txSearchResponseDTO);

                    log.Debug("GetTxStatusFromSearchResponse- txStatus: " + txStatus);
                    if (txStatus.reasonCode != -2 && txStatus.reasonCode != -1)
                    {
                        //Tx found
                        // Tx is either Sale/VOID/REFUND
                        log.Info("Tx Status reasonCode: " + txStatus.reasonCode.ToString());

                        // check if sale/void/refund Tx present
                        // if yes then proceed

                        if (txStatus.TxType == "SALE")
                        {
                            CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                            List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                            searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                            CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                            if (txStatus.reasonCode == 100)
                            {
                                log.Info("CC Transactions found with reasonCode 100");
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                                cCTransactionsPGWDTO.AuthCode = txStatus.AuthCode;
                                cCTransactionsPGWDTO.Authorize = txStatus.Authorize;
                                cCTransactionsPGWDTO.Purchase = txStatus.Purchase;
                                cCTransactionsPGWDTO.TransactionDatetime = txStatus.TransactionDatetime;
                                cCTransactionsPGWDTO.RefNo = txStatus.RecordNo; //paymentId
                                cCTransactionsPGWDTO.AcctNo = txStatus.AcctNo;

                                cCTransactionsPGWDTO.TextResponse = txStatus.TextResponse;
                                cCTransactionsPGWDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
                                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                                ccTransactionsPGWBL.Save();

                                dict.Add("status", "1");
                                dict.Add("message", "success");
                                dict.Add("retref", txStatus.paymentId);
                                dict.Add("amount", txStatus.Authorize);

                                dict.Add("orderId", trxId);
                                dict.Add("acctNo", txStatus.AcctNo);
                            }
                            else
                            {
                                log.Info("CC Transactions found with reasonCode other than 100");
                                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                                cCTransactionsPGWDTO.Authorize = !String.IsNullOrEmpty(txStatus.Authorize) ? txStatus.Authorize : String.Empty;
                                cCTransactionsPGWDTO.Purchase = txStatus.Purchase;
                                cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                                cCTransactionsPGWDTO.RefNo = txStatus.RecordNo; //paymentId
                                cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                                cCTransactionsPGWDTO.AcctNo = !String.IsNullOrEmpty(txStatus.AcctNo) ? txStatus.AcctNo : String.Empty;
                                cCTransactionsPGWDTO.TextResponse = txStatus.TextResponse;
                                cCTransactionsPGWDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
                                cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                                ccTransactionsPGWBL.Save();

                                dict.Add("status", "0");
                                dict.Add("message", "Transaction found with reasonCode other than 100");
                                dict.Add("retref", txStatus.paymentId);
                                dict.Add("amount", txStatus.Authorize);
                                dict.Add("orderId", trxId);
                                dict.Add("acctNo", txStatus.AcctNo);
                            }

                            resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                            log.LogMethodExit(resData);
                            return resData;
                        }
                    }
                }
                // cancel the Tx in Parafait DB
                dict.Add("status", "0");
                dict.Add("message", "no transaction found");
                dict.Add("orderId", trxId);
                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        private void LoadConfigParams()
        {
            try
            {
                configParameters.Add("SCHEME", SCHEME);
                configParameters.Add("REST_SECRET_KEY", REST_SECRET_KEY);
                configParameters.Add("PUBLIC_KEY", PUBLIC_KEY);
                configParameters.Add("ALGORITHM", ALGORITHM);
                configParameters.Add("MERCHANT_ID", MERCHANT_ID);
                configParameters.Add("HOST_URL", HOST_URL);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public bool verifySignature(string response)
        {
            log.LogMethodEntry(response);
            bool result = false;
            string response_signature = string.Empty;

            if (response != null)
            {
                Dictionary<string, string> responseParams = new Dictionary<string, string>();
                Dictionary<string, string> signatureParams = new Dictionary<string, string>();
                responseParams = JsonConvert.DeserializeObject<Dictionary<string, string>>(response);
                log.Debug("\n responseParams:  " + responseParams);
                foreach (KeyValuePair<string, string> pair in responseParams)
                {
                    signatureParams.Add(pair.Key, pair.Value);
                    if (pair.Key == "signature")
                    {
                        response_signature = pair.Value;
                    }

                }

                string sign = PaymentSecurity.sign(signatureParams, SECRET_KEY);
                if (sign.Equals(response_signature))
                {
                    result = true;
                }
            }
            log.LogMethodExit(result);
            return result;
        }
    }
}
