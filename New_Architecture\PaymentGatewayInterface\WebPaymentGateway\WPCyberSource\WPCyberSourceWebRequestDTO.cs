﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;


namespace Semnox.Parafait.PaymentGatewayInterface.WPCyberSource
{
    public class WorldPayWebRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public Processinginformation processingInformation { get; set; }
        public Paymentinformation paymentInformation { get; set; }
        public Orderinformation orderInformation { get; set; }
        public string paymentId { get; set; }
    }


    public class TxSearchRequestDTO
    {
        public string query { get; set; }
        public string sort { get; set; }

    }


    public class WPRefundRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
        public Orderinformation orderInformation { get; set; }
    }

    public class VoidRequestDTO
    {
        public Clientreferenceinformation clientReferenceInformation { get; set; }
    }
}
