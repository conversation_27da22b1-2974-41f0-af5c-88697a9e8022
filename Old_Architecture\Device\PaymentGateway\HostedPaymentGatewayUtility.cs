﻿/********************************************************************************************
 * Project Name - Hosted Payments                                                                     
 * Description  - Class to manage utility class for hosted payment gateways
 *
 ********************************************************************************************/
using System.Collections.Generic;
using System.Net;

namespace Semnox.Parafait.Device.PaymentGateway
{
    /// <summary>
    /// Abstract base class for managing class for hosted payment gateways for utility.
    /// </summary>
    public abstract class HostedPaymentGatewayUtility
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        Dictionary<string, string> paymentCredentailsList = new Dictionary<string, string>();

        /// <summary>
        /// Gets or sets the payment credentials list.
        /// </summary>
        public Dictionary<string, string> PaymentCredentailsList
        {
            get
            {
                return paymentCredentailsList;
            }

            set
            {
                paymentCredentailsList = value;
            }
        }
        public class TrxSearchUtilityDTO
        {
            public string TransactionId { get; set; }
            public string PaymentStatus { get; set; }
            public string FormattedResponse { get; set; }
            public string ErrorMessage { get; set; }
            public TrxSearchUtilityDTO SetError(string errorMessage, string formattedResponse, string paymentStatus = null)
            {
                ErrorMessage = errorMessage;
                FormattedResponse = formattedResponse;
                PaymentStatus = paymentStatus ?? PaymentStatus;
                return this;
            }
            public TrxSearchUtilityDTO SetSuccess(string formattedResponse, string paymentStatus)
            {
                FormattedResponse = formattedResponse;
                PaymentStatus = paymentStatus;
                return this;
            }
            public override string ToString()
            {
                return $"Transaction ID: {TransactionId}\n" +
                       $"\nResponse: {FormattedResponse}\n" +
                       $"\nThe Payment Status for trxId {TransactionId} is: {PaymentStatus}\n" +
                       (string.IsNullOrWhiteSpace(ErrorMessage) ? string.Empty : $"\nError Message: {ErrorMessage}");
            }
        }

        //constructor
        public HostedPaymentGatewayUtility()
        {
            log.LogMethodEntry();
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; }); // Certificate validation procedure for the SSL/TLS secure channel
            log.LogMethodExit();
        }

        /// <summary>
        /// MapPaymentStatus() - Used for mapping the payment status from raw response to semnox payment status
        /// </summary>
        /// <param name="rawPaymentGatewayStatus"></param>
        /// <returns>paymentStatusType</returns>
        internal abstract PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType = PaymentGatewayTransactionType.SALE);

        /// <summary>
        /// Retrieves the payment status search result for a transaction ID.
        /// </summary>
        /// <param name="trxId">The transaction ID to search for.</param>
        /// <returns>The payment status search result.</returns>
        public abstract TrxSearchUtilityDTO GetPaymentStatusSearch(string trxId);
    }
}
