﻿using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Site;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    class AdyenWebPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public AdyenWebPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            log.LogMethodEntry();
            log.Debug("Initializing AdyenWeb configuration");

            PaymentModeAttributesContainerDTO paymentModeAttributes = paymentModeContainerDTO.PaymentModeAttributesContainerDTO;

            if (paymentModeAttributes == null)
            {
                log.Error($"PaymentModeAttributesContainerDTO is null for PaymentModeId {paymentModeContainerDTO.PaymentModeId}, site_id: {Convert.ToString(executionContext.GetSiteId())}. Cannot initialize AdyenWebPaymentConfiguration.");
                log.Error("PaymentModeDTO: " + paymentModeContainerDTO.ToString());
                throw new PaymentModeAttributesNotFoundException("PaymentMode attributes not found.");
            }

            // Use PaymentModeAttributesContainerDTO for encrypted credentials and configuration
            SetConfiguration("ADYEN_HOSTED_PAYMENT_API_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewaySecretKey));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayPublishableKey));
            SetConfiguration("ADYEN_HOSTED_PAYMENT_MERCHANT_ID", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayMerchantId));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL", paymentModeAttributes.PaymentGatewayAPIURL);
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_BASE_URL", paymentModeAttributes.PaymentGatewayBaseURL);

            SetConfiguration("CURRENCY_CODE", paymentModeAttributes.CurrencyCode);
            SetConfiguration("BUSINESS_DAY_START_TIME", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "BUSINESS_DAY_START_TIME", "6"));
            SetConfiguration("ENABLE_ADDRESS_VALIDATION", paymentModeAttributes.EnableAddressValidation?.ToUpper() == "Y" ? "Y" : "N");

            OperatingSystem os = Environment.OSVersion;
            SetConfiguration("MERCHANT_DEVICE_OS_VERSION", Convert.ToString(os.Version));
            SetConfiguration("PINCODE", SiteViewContainerList.GetCurrentSiteContainerDTO(executionContext.GetSiteId()).PinCode);
            SetConfiguration("MERCHANT_OS_VERSION", SiteViewContainerList.GetCurrentSiteContainerDTO(executionContext.GetSiteId()).Version);
            SetConfiguration("CHANNEL", "web");
            SetConfiguration("PaymentModeId", paymentModeContainerDTO.PaymentModeId.ToString());


            LookupsContainerDTO adyenWebPaymentConfiguration = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "ADYEN_WEB_PAYMENT_CONFIGURATION");
            List<LookupValuesContainerDTO> adyenWebPaymentConfigurationList = adyenWebPaymentConfiguration?.LookupValuesContainerDTOList ?? new List<LookupValuesContainerDTO>();

            if (adyenWebPaymentConfigurationList.Any(x => x.LookupValue == "SHOPPER_LOCALE"))
            {
                SetConfiguration("SHOPPER_LOCALE", adyenWebPaymentConfigurationList.First(x => x.LookupValue == "SHOPPER_LOCALE").Description);
            }
            if (adyenWebPaymentConfigurationList.Any(x => x.LookupValue == "CURRENCY_CONVERSION_FACTOR"))
            {
                SetConfiguration("CURRENCY_CONVERSION_FACTOR", adyenWebPaymentConfigurationList.First(x => x.LookupValue == "CURRENCY_CONVERSION_FACTOR").Description);
            }
            if (adyenWebPaymentConfigurationList.Any(x => x.LookupValue == "API_VERSION"))
            {
                SetConfiguration("API_VERSION", adyenWebPaymentConfigurationList.First(x => x.LookupValue == "API_VERSION").Description);
            }
            if (adyenWebPaymentConfigurationList.Any(x => x.LookupValue == "ASSETS_VERSION"))
            {
                SetConfiguration("ASSETS_VERSION", adyenWebPaymentConfigurationList.First(x => x.LookupValue == "ASSETS_VERSION").Description);
            }
            if (adyenWebPaymentConfigurationList.Any(x => x.LookupValue == "COUNTRY_CODE"))
            {
                SetConfiguration("COUNTRY_CODE", adyenWebPaymentConfigurationList.First(x => x.LookupValue == "COUNTRY_CODE").Description);
            }
            if (adyenWebPaymentConfigurationList.Any(x => x.LookupValue == "STORE_NAME"))
            {
                SetConfiguration("STORE_NAME", adyenWebPaymentConfigurationList.First(x => x.LookupValue == "STORE_NAME").Description);
            }
            if (adyenWebPaymentConfigurationList.Any(x => x.LookupValue == "PAYMENT_ENVIRONMENT"))
            {
                SetConfiguration("PAYMENT_ENVIRONMENT", adyenWebPaymentConfigurationList.First(x => x.LookupValue == "PAYMENT_ENVIRONMENT").Description);
            }


            LookupsContainerDTO adyenPaymentConfiguration = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "ADYEN_PAYMENT_CONFIGURATION");
            List<LookupValuesContainerDTO> adyenPaymentConfigurationList = adyenPaymentConfiguration?.LookupValuesContainerDTOList ?? new List<LookupValuesContainerDTO>();

            if (adyenPaymentConfigurationList.Any(x => x.LookupValue == "POS_SUPPLIER_NAME"))
            {
                SetConfiguration("POS_SUPPLIER_NAME", adyenPaymentConfigurationList.First(x => x.LookupValue == "POS_SUPPLIER_NAME").Description);
            }
            if (adyenPaymentConfigurationList.Any(x => x.LookupValue == "SERVICE_INTEGRATOR_NAME"))
            {
                SetConfiguration("SERVICE_INTEGRATOR_NAME", adyenPaymentConfigurationList.First(x => x.LookupValue == "SERVICE_INTEGRATOR_NAME").Description);
            }
            if (adyenPaymentConfigurationList.Any(x => x.LookupValue == "MERCHANT_APPLICATION_NAME"))
            {
                SetConfiguration("MERCHANT_APPLICATION_NAME", adyenPaymentConfigurationList.First(x => x.LookupValue == "MERCHANT_APPLICATION_NAME").Description);
            }
            if (adyenPaymentConfigurationList.Any(x => x.LookupValue == "MERCHANT_OS"))
            {
                SetConfiguration("MERCHANT_OS", adyenPaymentConfigurationList.First(x => x.LookupValue == "MERCHANT_OS").Description);
            }


            LookupsContainerDTO lookupsContainerDTO = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            List<LookupValuesContainerDTO> lookupValuesContainerDTOList = lookupsContainerDTO?.LookupValuesContainerDTOList ?? new List<LookupValuesContainerDTO>();

            string API_URL = string.Empty;
            string WEBSITE_URL = string.Empty;

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "ANGULAR_PAYMENT_API"))
            {
                API_URL = lookupValuesContainerDTOList.First(x => x.LookupValue == "ANGULAR_PAYMENT_API").Description;
                SetConfiguration("ANGULAR_PAYMENT_API", API_URL);
            }
            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "ANGULAR_PAYMENT_WEB"))
            {
                WEBSITE_URL = lookupValuesContainerDTOList.First(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").Description;
                // Note: ANGULAR_PAYMENT_WEB is already set from paymentModeAttributes.AngularPaymentWeb above
                // This lookup is used only to get WEBSITE_URL for other configurations
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK"))
            {
                SetConfiguration("ANGULAR_PAYMENT_WEB_PAGE_LINK", WEBSITE_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL"))
            {
                SetConfiguration("SUCCESS_RESPONSE_API_URL", API_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }
            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "FAILURE_RESPONSE_API_URL"))
            {
                SetConfiguration("FAILURE_RESPONSE_API_URL", API_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }
            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "CANCEL_RESPONSE_API_URL"))
            {
                SetConfiguration("CANCEL_RESPONSE_API_URL", API_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }
            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL"))
            {
                SetConfiguration("CALLBACK_RESPONSE_API_URL", API_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "SUCCESS_REDIRECT_URL"))
            {
                SetConfiguration("SUCCESS_REDIRECT_URL", WEBSITE_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "SUCCESS_REDIRECT_URL").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }
            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "FAILURE_REDIRECT_URL"))
            {
                SetConfiguration("FAILURE_REDIRECT_URL", WEBSITE_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "FAILURE_REDIRECT_URL").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }
            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "CANCEL_REDIRECT_URL"))
            {
                SetConfiguration("CANCEL_REDIRECT_URL", WEBSITE_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "CANCEL_REDIRECT_URL").Description?.Replace("@gateway", $"{PaymentGateways.AdyenCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }

            log.LogMethodExit();
        }
    }
}
