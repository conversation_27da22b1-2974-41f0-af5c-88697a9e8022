﻿/********************************************************************************************
 * Project Name - Hosted Payment
 * Description  - DTO Classes created as part of payment response processing
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/

using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.Transaction.V2;

namespace Semnox.Parafait.PaymentGateway
{
    public class HostedPaymentResponseDTO
    {
        public string RedirectURL { get; set; }
        public string CallbackResponseMessage { get; set; }
        public bool RedirectResponseToWebsite { get; set; }

        /// <summary>
        /// Additional response data for complex payment flows (e.g., 3DS authentication, redirects)
        /// When not null, indicates additional actions are required before completing the payment
        /// </summary>
        public AdditionalResponseDTO AdditionalResponseDTO { get; set; }

        public HostedPaymentResponseDTO()
        {
            RedirectResponseToWebsite = true;
            CallbackResponseMessage = "OK";
        }

        /// <summary>
        /// Indicates whether additional actions are required
        /// </summary>
        public bool HasAdditionalResponse => AdditionalResponseDTO != null;
    }
}
