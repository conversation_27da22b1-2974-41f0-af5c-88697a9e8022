﻿
/********************************************************************************************
 * Project Name - Payment GatewayInterface
 * Description  - Adyen POS CommandHandler
 * 
 **************
 **Version Log
 **************
 *Version     Date              Modified By                    Remarks          
 *********************************************************************************************
 *  2.200.0    24-Sep-2024       Amrutha                        Adyen POS Redesign
 *******************************************************************************************/

using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using Newtonsoft.Json;


namespace Semnox.Parafait.PaymentGatewayInterface
{
    internal class AdyenPosCommandhandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string posId, terminalId, tapiUrl, webApiUrl, serviceId, apiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency;
        private const string ENABLE_DONATION = "AskGiving";
        private const string ENABLE_PARTIAL_PAYMENT = "AllowPartialAuthorisation";
        private const string ENABLE_TIP = "AskGratuity";
        private bool isDonationEnabled, isPartialPaymentAllowed, isTipAllowed, isManual, isSubscriptionTokenCreation;
        private string strSaleToAcquirerData;
        private Transactionconditions manualKeyEntryParams;
        private Dictionary<string, string> applicationInfo = new Dictionary<string, string>();
        private int adyenTransactionTimeout;
        //private ExecutionContext executionContext;
        //private Dictionary<string, string> esdInfo = new Dictionary<string, string>();
        
        public AdyenPosCommandhandler(string serviceId, string posId, string terminalId, string tapiUrl, string webApiUrl, string apiKey, string merchantAccount, string protocolVersion, string messageClass, string shopperCurrency, bool isDonationEnabled, bool isPartialPaymentAllowed, bool isTipAllowed, bool isManual, bool isSubscriptionTokenCreation, Dictionary<string, string> applicationInfo, int adyenTransactionTimeout)
        {
            log.LogMethodEntry(serviceId, posId, terminalId, tapiUrl, webApiUrl, apiKey, merchantAccount, protocolVersion, messageClass, shopperCurrency, isDonationEnabled, isPartialPaymentAllowed, isTipAllowed,isManual, isSubscriptionTokenCreation, applicationInfo);
            this.posId = posId;
            this.terminalId = terminalId;
            this.tapiUrl = tapiUrl;
            this.webApiUrl = webApiUrl;
            this.adyenTransactionTimeout = adyenTransactionTimeout;
            //this.webApiVersion = webApiVersion;
            //this.deviceIpAddress = deviceIpAddress;
            //this.devicePort = devicePort;

            this.serviceId = serviceId; // this should be unique within 48 hours
            this.apiKey = apiKey;
            this.merchantAccount = merchantAccount;
            this.protocolVersion = protocolVersion;
            this.messageClass = messageClass;
            this.shopperCurrency = shopperCurrency;
            this.isDonationEnabled = isDonationEnabled;
            this.isPartialPaymentAllowed = isPartialPaymentAllowed;
            this.isTipAllowed = isTipAllowed;
            this.isManual = isManual;
           // this.isSubscriptionTokenCreation = isSubscriptionTokenCreation;
            this.applicationInfo = applicationInfo;
            //this.esdInfo = esdInfo;

            strSaleToAcquirerData = GetTenderOptions(isDonationEnabled, isPartialPaymentAllowed, isTipAllowed);
            log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");

            string strApplicationInfoParams = GetApplicationInfoParams(this.applicationInfo);
            log.Debug($"strApplicationInfoParams = {strApplicationInfoParams}");

            //string strESDInfoParams = GetESDInfoParams(this.esdInfo);
            //log.Debug($"strESDInfoParams = {strESDInfoParams}");

           

            if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
            {
                strSaleToAcquirerData = $"{strApplicationInfoParams}&{strSaleToAcquirerData}";
            }
            else
            {
                strSaleToAcquirerData = $"&{strApplicationInfoParams}";
            }

            log.Debug($"strSaleToAcquirerData after adding application and esd info={strSaleToAcquirerData}");

            manualKeyEntryParams = GetManualKeyEntryParams(isManual);
            log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");
            log.LogMethodExit();
        }

        //public AdyenPosCommandhandler(ExecutionContext executionContext)
        //{
        //    log.LogMethodEntry(executionContext);
        //    this.executionContext = executionContext;
        //    log.LogMethodExit(executionContext);
        //}

        private string GetESDInfoParams(Dictionary<string, string> esdInfo)
        {
            log.LogMethodEntry(esdInfo);
            try
            {
                log.LogMethodEntry(applicationInfo);
                string strESDInfoParams = string.Empty;

                if (esdInfo.Keys.Count > 0)
                {
                    foreach (var item in esdInfo)
                    {
                        strESDInfoParams += $"{item.Key}={item.Value}&";
                    }
                    strESDInfoParams.Remove(strESDInfoParams.Length - 1);
                    log.Debug($"strESDInfoParams = {strESDInfoParams}");
                }

                log.LogMethodExit(strESDInfoParams);
                return strESDInfoParams;
                
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            
        }

        private string GetApplicationInfoParams(Dictionary<string, string> applicationInfo)
        {
            log.LogMethodEntry(applicationInfo);
            try
            {
                log.LogMethodEntry(applicationInfo);
                string strApplicationInfoParams = string.Empty;

                if (applicationInfo.Keys.Count > 0)
                {
                    foreach (var item in applicationInfo)
                    {
                        strApplicationInfoParams += $"{item.Key}={item.Value}&";
                    }
                    strApplicationInfoParams.Remove(strApplicationInfoParams.Length - 1);
                    log.Debug($"strApplicationInfoParams = {strApplicationInfoParams}");
                }

                log.LogMethodExit(strApplicationInfoParams);
                return strApplicationInfoParams;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public string DoPayment(decimal amount, string ccRequestId)
        {
            log.LogMethodEntry(amount, ccRequestId);
            string paymentResponse = string.Empty;
            RequestDto requestDto=null;
            try
            {
                 requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData

                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = Convert.ToDouble(amount),
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Payment requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Sale response was null");
                    log.Error($"requestDto{requestDto}");
                    throw new PaymentFailedException("Payment failed: could not receive response"); //Payment failed: could not receive response
                }

            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error($"paymentResponse={paymentResponse}");
                log.Error($"requestDto{requestDto}");
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoAuthorization(decimal amount, string ccRequestId)
        {
            
            log.LogMethodEntry(amount, ccRequestId);
            string paymentResponse = string.Empty;
            RequestDto requestDto = null;
            try
            {
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"authorisationType=FinalAuth&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"authorisationType=FinalAuth";
                }
                log.Debug($"DoAuthorization(): strSaleToAcquirerData={strSaleToAcquirerData}");

                 requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = Convert.ToDouble(amount)
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Auth requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Auth response was null");
                    log.Error(requestDto);
                    throw new Exception("Payment failed: could not receive response"); //Payment failed: could not receive response
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error($"Auth requestDto={requestDto.ToString()}");
                log.Error($"Auth paymentResponse={paymentResponse}");
                log.Error(requestDto);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoPreAuthorization(decimal amount, string ccRequestId)
        {
            log.LogMethodEntry(amount, ccRequestId);
            string paymentResponse = string.Empty;
            RequestDto requestDto=null;
            try
            {
                log.LogMethodEntry(amount, ccRequestId);
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"authorisationType=PreAuth&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"authorisationType=PreAuth";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                 requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = Convert.ToDouble(amount),
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Preauth requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("paymentResponse was null for DoPreAuthorization method");
                    log.Error("requestDto= " + requestDto);
                    throw new PaymentFailedException("Payment failed: could not receive response"); //Payment failed: could not receive response
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("amount=" + amount + "ccRequestId=" + ccRequestId);
                log.Error("requestDto= " + requestDto);
                log.Error("DoPreAuthorization method failed.");
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoPreAuthorizationAdjustment(decimal amount, string paymentId, string adjustAuthBlob, string ccRequestId, int? currencyConversionFactor, string apiUrl)
        {
            log.LogMethodEntry(amount, paymentId, adjustAuthBlob, ccRequestId, currencyConversionFactor, apiUrl);
            string paymentResponse = string.Empty;
            AdjustAuthorizationRequestDto requestDto = null;
            try
            {
                log.LogMethodEntry(amount, paymentId, adjustAuthBlob, ccRequestId, currencyConversionFactor, apiUrl);
                int authAmount = GetAmountInMinorUnit(amount, currencyConversionFactor);
                log.Info($"authAmount={authAmount}");
                requestDto = new AdjustAuthorizationRequestDto
                {
                    merchantAccount = merchantAccount,
                    originalReference = paymentId,
                    modificationAmount = new Amount
                    {
                        currency = shopperCurrency,
                        value = authAmount
                    },
                    reference = ccRequestId,
                    additionalData = new Additionaldata
                    {
                        adjustAuthorisationData = adjustAuthBlob,
                        // ESD
                        //enhancedSchemeData_customerReference = esdInfo["enhancedSchemeData.customerReference"],
                        //enhancedSchemeData_destinationPostalCode = esdInfo["enhancedSchemeData.destinationPostalCode"],
                        //enhancedSchemeData_orderDate = esdInfo["enhancedSchemeData.orderDate"],
                    },
                    allowPartialAuth = isPartialPaymentAllowed ? "true" : "false"
                    
                };

                log.Info($"Preauth adjust requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteWebApiRequests(requestDto.ToString(), apiUrl);
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Preauth adjust response was null-DoPreAuthorizationAdjustment method");

                    throw new PaymentFailedException("Payment failed: could not receive response"); //Payment failed: could not receive response
                }
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                log.Error("requestDto= " + requestDto);
                log.Error("amount : " +  amount+ "paymentId : "+ paymentId + " " + "adjustAuthBlob :" +  adjustAuthBlob + " " + "ccRequestId: " + ccRequestId + " " + "currencyConversionFactor :" + currencyConversionFactor + " " + "apiUrl :" + apiUrl);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("Error in the DoPreAuthorizationAdjustment method");
                log.Error("requestDto= " + requestDto);
                log.Error("amount : " + amount + "paymentId : " + paymentId + " " + "adjustAuthBlob :" + adjustAuthBlob + " " + "ccRequestId: " + ccRequestId + " " + "currencyConversionFactor :" + currencyConversionFactor + " " + "apiUrl :" + apiUrl);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoCapture(string paymentId, decimal amount, string ccRequestId, int currencyConversionFactor)
        {
            log.LogMethodEntry(paymentId, amount, ccRequestId, currencyConversionFactor);
            string paymentResponse = string.Empty;
            CapturePaymentRequestDto requestDto = null;
            try
            {
                log.LogMethodEntry(paymentId, amount, ccRequestId, currencyConversionFactor);
                int captureAmount = GetAmountInMinorUnit(amount, currencyConversionFactor);
                log.Debug($"captureAmount={captureAmount}");
                requestDto = new CapturePaymentRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount,
                    amount = new Amount
                    {
                        value = captureAmount,
                        currency = shopperCurrency
                    }
                };

                log.Debug($"Capture requestDto={requestDto.ToString()}");
                paymentResponse = MakeCaptureRequest(paymentId, requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Capture response was null");
                    log.Error("requestDto= " + requestDto);
                    throw new Exception("Payment failed: could not receive response"); //Payment failed: could not receive response
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("requestDto= " + requestDto);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoCancelAuthorization(string paymentId, string ccRequestId)
        {
            log.LogMethodEntry(paymentId, ccRequestId);
            string paymentResponse = string.Empty;
            CancelAuthRequestDto requestDto = null;
            try
            {
                requestDto = new CancelAuthRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount,
                };

                log.Info($"DoCancelAuthorization requestDto={requestDto.ToString()}");
                paymentResponse = MakeCancelAuthRequest(paymentId, requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("CancelAuth response was null");
                    log.Error("paymentId: " + " " + "ccRequestId :" + ccRequestId);
                    log.Error("requestDto= " + requestDto.ToString());
                    throw new PaymentFailedException("Payment failed: could not receive response"); //Payment failed: could not receive response
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("Payment failed: could not receive response in DoCancelAuthorization method" );
                log.Error("DoCancelAuthorization requestDto= " + requestDto.ToString());
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoAbortTransaction()
        {
            string paymentResponse = string.Empty;
            return paymentResponse;
        }

        public string DoFullRefund(string paymentId, string paymentDate, string ccRequestId)
        {
            string refundResponse = string.Empty;
            try
            {
                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageClass = messageClass,
                            MessageCategory = "Reversal",
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        ReversalRequest = new ReversalRequestDto
                        {
                            OriginalPOITransaction = new OriginalPOITransaction
                            {
                                POITransactionID = new POITransactionID
                                {
                                    TransactionID = paymentId,
                                    TimeStamp = paymentDate
                                }
                            },
                            ReversalReason = "MerchantCancel",
                            SaleData = new Saledata
                            {
                                SaleToAcquirerData = strSaleToAcquirerData,
                            }
                        }
                    }
                };

                log.Debug($"Refund requestDto={requestDto.ToString()}");
                refundResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(refundResponse))
                {
                    log.Error("Refund response was null");
                    throw new Exception("Payment failed: could not receive response"); //Payment failed: could not receive response
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            return refundResponse;
        }

        public string DoRefund(string paymentId, string paymentTimestamp, decimal amount, string ccRequestId)
        {
            log.LogMethodEntry(paymentId, paymentTimestamp, amount, ccRequestId);
            string refundResponse = string.Empty;
            RequestDto requestDto = null;
            try
            {
                log.LogMethodEntry(paymentId, paymentTimestamp, amount, ccRequestId);
                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"currency={shopperCurrency}";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageClass = messageClass,
                            MessageCategory = "Reversal",
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        ReversalRequest = new ReversalRequestDto
                        {
                            OriginalPOITransaction = new OriginalPOITransaction
                            {
                                POITransactionID = new POITransactionID
                                {
                                    TransactionID = paymentId,
                                    TimeStamp = paymentTimestamp
                                }
                            },
                            ReversalReason = "MerchantCancel",
                            ReversedAmount = Convert.ToDouble(amount),
                            SaleData = new Saledata
                            {
                                SaleToAcquirerData = strSaleToAcquirerData,
                                SaleTransactionID = new Saletransactionid
                                {
                                    TimeStamp = getUTCDateTime(),
                                    TransactionID = ccRequestId
                                }
                            }
                        }
                    }
                };

                log.Debug($"Refund requestDto={requestDto.ToString()}");
                refundResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Info($"refundResponse={refundResponse}");
                if (string.IsNullOrWhiteSpace(refundResponse))
                {
                    log.Error("Refund response was null -DoRefund method");
                    log.Error("RequestDto  :" + requestDto);
                    throw new PaymentFailedException("Payment failed: could not receive response"); //Payment failed: could not receive response
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("Error occured in the DoRefund method");
                log.Error("paymentId : " + paymentId + " " + "paymentTimestamp : " + paymentTimestamp + " " + "amount : " + amount + " " + "ccRequestId: " + ccRequestId);
                log.Error("RequestDto  :" + requestDto);
                throw;
            }
            log.LogMethodExit(refundResponse);
            return refundResponse;
        }
        public string DoRefundAuthAdjustPayment(string ccRequestId, string paymentId)
        {
            
            log.LogMethodEntry(ccRequestId, paymentId);
            string responseFromServer = string.Empty;
            WebRefundRequestDto requestDto = null;
            try
            {
                 requestDto = new WebRefundRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount
                };

                string jsonRequest = JsonConvert.SerializeObject(requestDto);
                log.Debug($"jsonRequest = {jsonRequest}");

                string apiUrl = $"{webApiUrl}/payments/{paymentId}/cancels";
                log.Debug($"apiUrl = {apiUrl}");

                responseFromServer = ExecuteWebApiRequests(jsonRequest, apiUrl);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("Error occured in the DoRefundAuthAdjustPayment method");
                log.Error("requestDto= " + requestDto);
                throw;
            }

            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        public string DoRefundSubscriptionPayment(string ccRequestId, string paymentId)
        {
            
            log.LogMethodEntry(ccRequestId, paymentId);
            string responseFromServer = string.Empty;
            try
            {
                WebRefundRequestDto requestDto = new WebRefundRequestDto
                {
                    reference = ccRequestId,
                    merchantAccount = merchantAccount
                };

                string jsonRequest = JsonConvert.SerializeObject(requestDto);
                log.Debug($"jsonRequest = {jsonRequest}");

                string apiUrl = $"{webApiUrl}/payments/{paymentId}/reversals";
                log.Debug($"apiUrl = {apiUrl}");

                responseFromServer = ExecuteWebApiRequests(jsonRequest, apiUrl);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        public string DoIndependantRefund(decimal refundAmount, string ccRequestId)
        {
            log.LogMethodEntry(refundAmount, ccRequestId);
            string paymentResponse = string.Empty;
            RequestDto requestDto = null;
            try
            {
                log.LogMethodEntry(refundAmount, ccRequestId);
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                

                requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = (double)refundAmount,
                                },
                                TransactionConditions = manualKeyEntryParams
                            },
                            PaymentData = new Paymentdata
                            {
                                PaymentType = "Refund"
                            }
                        }
                    }
                };

                log.Debug($"IndependantRefund requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("IndependantRefund response was null");
                    log.Error("requestDto= " + requestDto);
                    throw new Exception("Independednt Refund Failed"); //Independednt Refund Failed
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("Error in the DoIndependantRefund method ");
                log.Error("requestDto= " + requestDto);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string  DoCheckLastTransactionStatus(string saleId, string p_serviceId, string messageCategory)
        {
            log.LogMethodEntry(saleId, p_serviceId, messageCategory);
            string lastTransactionResponse = string.Empty;
            RequestDto requestDto=null;
            try
            {
                log.LogMethodEntry(saleId, p_serviceId, messageCategory);
                 requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageClass = messageClass,
                            MessageCategory = "TransactionStatus",
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId,
                            POIID = terminalId
                        },
                        TransactionStatusRequest = new Transactionstatusrequest
                        {
                            ReceiptReprintFlag = true,
                            DocumentQualifier = new string[]
                            {
                                "CashierReceipt",
                                "CustomerReceipt"
                            },
                            MessageReference = new Messagereference
                            {
                                SaleID = saleId,
                                ServiceID = p_serviceId,
                                MessageCategory = messageCategory
                            }
                        }
                    }
                };

                log.Info($"LastTransactionStatus requestDto={requestDto.ToString()}");
                lastTransactionResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Info($"lastTransactionResponse={lastTransactionResponse}");
                if (string.IsNullOrWhiteSpace(lastTransactionResponse))
                {
                    log.Error("LastTransactionStatus response was null");
                    log.Error("requestDto" + requestDto);
                    throw new LastTransactionCheckFailedException("Last Transaction Check Failed."); //Last Transaction Check Failed.
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("Error occured in the DoCheckLastTransactionStatus method");
                log.Error("requestDto" + requestDto);
                throw;
            }
            log.LogMethodExit(lastTransactionResponse);
            return lastTransactionResponse;
        }

        public string DoCreateCardTokenForSubscription(decimal amount, string shopperRef, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(amount, shopperRef, ccRequestId);
                manualKeyEntryParams = GetManualKeyEntryParams(isManual);
                log.Debug($"manualKeyEntryParams={JsonConvert.SerializeObject(manualKeyEntryParams)}");

                if (!string.IsNullOrWhiteSpace(strSaleToAcquirerData))
                {
                    strSaleToAcquirerData = $"recurringProcessingModel=Subscription&shopperReference={shopperRef}&{strSaleToAcquirerData}";
                }
                else
                {
                    strSaleToAcquirerData = $"recurringProcessingModel=Subscription&shopperReference={shopperRef}";
                }
                log.Debug($"strSaleToAcquirerData={strSaleToAcquirerData}");
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId, //This must be diff from ccRequestId and unique for each request. Max 10 alphanumeric chars
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData,
                                TokenRequestedType = "Customer"
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount =Convert.ToDouble(amount),
                                },
                                TransactionConditions = manualKeyEntryParams
                            }
                        }
                    }
                };

                log.Debug($"Create Card Token For Subscription requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Create Card Token For Subscription response was null");
                    throw new Exception("Create Card Token For Subscription failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        public string DoLaterSubscriptionPayment(decimal amount, string shopperRef, string paymentToken, string ccRequestId, int currencyConversionFactor, Dictionary<string, string> esdInfo)
        {
            string paymentResponse = string.Empty;
            try
            {
                log.LogMethodEntry(amount, shopperRef, paymentToken, ccRequestId);
                int subscriptionAmount = GetAmountInMinorUnit(amount, currencyConversionFactor);
                log.Debug($"subscriptionAmount={subscriptionAmount}");

                ApplicationInfo appInfo = GetAppInfoObject();
                MakeSubscriptionPaymentRequestDto subscriptionPaymentRequestDto = new MakeSubscriptionPaymentRequestDto
                {
                    amount = new Amount
                    {
                        value = subscriptionAmount,
                        currency = shopperCurrency,
                    },
                    reference = ccRequestId,
                    paymentMethod = new Paymentmethod
                    {
                        type = "scheme",
                        storedPaymentMethodId = paymentToken
                    },
                    shopperReference = shopperRef,
                    merchantAccount = merchantAccount,
                    shopperInteraction = "ContAuth",
                    recurringProcessingModel = "Subscription",
                    returnUrl = "https://semnox.com", // TBC,
                    applicationInfo = appInfo,
                    additionalData = new Additionaldata
                    {
                        enhancedSchemeData_customerReference = esdInfo["enhancedSchemeData.customerReference"],
                        enhancedSchemeData_destinationPostalCode = esdInfo["enhancedSchemeData.destinationPostalCode"],
                        enhancedSchemeData_orderDate = esdInfo["enhancedSchemeData.orderDate"],
                    }
                };

                log.Debug($"Subscription payment requestDto={subscriptionPaymentRequestDto.ToString()}");
                paymentResponse = MakeSubcriptionPayment(subscriptionPaymentRequestDto.ToString());
                log.Debug($"paymentResponse={paymentResponse}");
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("Subscription payment response was null");
                    throw new Exception("Subscription payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentResponse);
            return paymentResponse;
        }

        private ApplicationInfo GetAppInfoObject()
        {
            ApplicationInfo appInfo = null;
            try
            {
                log.LogMethodEntry(applicationInfo);

                appInfo = new ApplicationInfo
                {
                    externalPlatform = new ExternalPlatform
                    {
                        name = applicationInfo["applicationInfo.externalPlatform.name"],
                        version = applicationInfo["applicationInfo.externalPlatform.version"],
                        integrator = applicationInfo["applicationInfo.externalPlatform.integrator"],
                    },
                    merchantApplication = new MerchantApplication
                    {
                        name = applicationInfo["applicationInfo.merchantApplication.name"],
                        version = applicationInfo["applicationInfo.merchantApplication.version"],
                    },
                    merchantDevice = new MerchantDevice
                    {
                        os = applicationInfo["applicationInfo.merchantDevice.os"],
                        osVersion = applicationInfo["applicationInfo.merchantDevice.osVersion"],
                    }
                };

                log.LogVariableState("appInfo", appInfo);

                log.LogMethodExit(appInfo);
                return appInfo;

            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public string DoQRPayment(decimal orderAmount, decimal tipAmount, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {
                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId, // we need to maintain a sequence for this. This must be diff from ccRequestId and unique for each request. Max 10 alphanumeric chars
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleToAcquirerData = strSaleToAcquirerData
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = Convert.ToDouble(orderAmount),
                                    TipAmount =Convert.ToDouble(tipAmount)
                                },
                                TransactionConditions = new Transactionconditions
                                {
                                    //AllowedPaymentBrand = new string[]
                                    //{
                                    //    //"alipay"
                                    //}
                                }
                            }
                        }
                    }
                };

                log.Debug($"QR Payment requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("QR Payment response was null");
                    throw new Exception("QR Payment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            return paymentResponse;
        }

        public string DoPartialPayment(decimal orderAmount, decimal tipAmount, string ccRequestId)
        {
            string paymentResponse = string.Empty;
            try
            {

                RequestDto requestDto = new RequestDto
                {
                    SaleToPOIRequest = new Saletopoirequest
                    {
                        MessageHeader = new Messageheader
                        {
                            ProtocolVersion = protocolVersion,
                            MessageCategory = "Payment",
                            MessageClass = messageClass,
                            MessageType = "Request",
                            SaleID = posId,
                            ServiceID = serviceId, // we need to maintain a sequence for this. This must be diff from ccRequestId and unique for each request. Max 10 alphanumeric chars
                            POIID = terminalId
                        },
                        PaymentRequest = new Paymentrequest
                        {
                            SaleData = new Saledata
                            {
                                SaleTransactionID = new Saletransactionid
                                {
                                    TransactionID = ccRequestId,
                                    TimeStamp = getUTCDateTime()
                                },
                                SaleReferenceID = $"{ccRequestId}-1",
                                SaleToAcquirerData = "tenderOption=AllowPartialAuthorisation"
                            },
                            PaymentTransaction = new Paymenttransaction
                            {
                                AmountsReq = new Amountsreq
                                {
                                    Currency = shopperCurrency,
                                    RequestedAmount = Convert.ToDouble(orderAmount),
                                    TipAmount = Convert.ToDouble(tipAmount)
                                },
                            }
                        }
                    }
                };

                log.Debug($"PartialPayment requestDto={requestDto.ToString()}");
                paymentResponse = ExecuteTerminalRequests(requestDto.ToString());
                if (string.IsNullOrWhiteSpace(paymentResponse))
                {
                    log.Error("PartialPayment response was null");
                    throw new Exception("PartialPayment failed: could not receive response");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            return paymentResponse;
        }

        public String getUTCDateTime()
        {
            try
            {
                log.LogMethodEntry();
                DateTime time = DateTime.Now.ToUniversalTime();
                string utcTime = time.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                log.LogMethodExit(utcTime);
                return utcTime;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }
        public string getUTCDateTime(string dateTime)
        {
            try
            {
                log.LogMethodEntry(dateTime);
                DateTime dt = DateTimeOffset.Parse(dateTime).UtcDateTime;
                string utcTime = dt.ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                log.LogMethodExit(utcTime);
                return utcTime;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            
        }

        public string ExecuteTerminalRequests(string request)
        {
            log.LogMethodEntry(request);
            string responseFromServer = string.Empty;
            string API_URL = tapiUrl;
            log.Info($"ExecuteTerminalRequests API_URL={API_URL}");
            try
            {
                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";
                myHttpWebRequest.Timeout = adyenTransactionTimeout;
                log.Debug($"Transaction timeout {adyenTransactionTimeout}");
                log.Debug($"ExecuteTerminalRequests(): request json={request}");

                byte[] data = Encoding.UTF8.GetBytes(request);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", apiKey);
                //myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;
                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                log.Error($"ExecuteTerminalRequests(): responseFromServer={responseFromServer}");
                log.Error($"request{request}");
                log.Error("API_URL" + API_URL);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error($"ExecuteTerminalRequests(): responseFromServer={responseFromServer}");
                log.Error("API_URL" + API_URL);
                log.Error($"request{request}");
                throw;
            }
            log.LogMethodExit(responseFromServer);         
             return responseFromServer;
        }

        private string ExecuteWebApiRequests(string request, string apiUrl)
        {
            log.LogMethodEntry(request, apiUrl);
            string responseFromServer = string.Empty;
            string API_URL = apiUrl;
            log.Info($"ExecuteWebApiRequests API_URL={API_URL}");
            try
            {
                log.LogMethodEntry(request, apiUrl);
                

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";
                myHttpWebRequest.Timeout = adyenTransactionTimeout;
                log.Debug($"Transaction timeout {adyenTransactionTimeout}");
                log.Debug($"ExecuteWebApiRequests(): request json={request}");

                byte[] data = Encoding.UTF8.GetBytes(request);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", apiKey);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"ExecuteWebApiRequests(): responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error("API_URL :" + API_URL);
                log.Error($"request{request}");
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("API_URL :" + API_URL);
                log.Error($"request{request}");
                throw;
            }
            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        public string MakeCaptureRequest(string paymentId, string jsonRequestDto)
        {
            log.LogMethodEntry(paymentId, jsonRequestDto);
            string response = string.Empty;
            string API_URL=string.Empty;
            try
            {
                log.LogMethodEntry(paymentId, jsonRequestDto);
                 API_URL = $"{webApiUrl}/payments/{paymentId}/captures";
                log.Debug($"MakeCaptureRequest API_URL={API_URL}");
                response = ExecuteWebApiRequests(jsonRequestDto, API_URL);
                log.Debug($"Capture response={response}");
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                log.Error("API_URL :" + API_URL);
                log.Error($"jsonRequestDto= {jsonRequestDto}");
                log.Error($"paymentId= {paymentId}");
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("API_URL :" + API_URL);
                log.Error($"jsonRequestDto= {jsonRequestDto}");
                log.Error($"paymentId= {paymentId}");
                throw;
            }
            log.LogMethodExit(response);
            return response;
        }

        public string MakeCancelAuthRequest(string paymentId, string jsonRequestDto)
        {
            log.LogMethodEntry(paymentId, jsonRequestDto);
            string response = string.Empty;
            string API_URL = string.Empty;
            try
            {
                log.LogMethodEntry(paymentId, jsonRequestDto);
                 API_URL = $"{webApiUrl}/payments/{paymentId}/cancels";
                log.Debug($"MakeCaptureRequest API_URL={API_URL}");
                response = ExecuteWebApiRequests(jsonRequestDto, API_URL);
                log.Debug($"CancelAuth response={response}");
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error(errMsg);
                log.Error("API_URL :" + API_URL);
                log.Error($"jsonRequestDto= {jsonRequestDto}");
                log.Error($"paymentId= {paymentId}");
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("API_URL :" + API_URL);
                log.Error($"jsonRequestDto= {jsonRequestDto}");
                log.Error($"paymentId= {paymentId}");
                throw;
            }
            log.LogMethodExit(response);
            return response;
        }

        public string MakeSubcriptionPayment(string jsonRequestDto)
        {
            log.LogMethodEntry(jsonRequestDto);
            string responseFromServer = string.Empty;
            string API_URL = string.Empty;
            try
            {
                log.LogMethodEntry(jsonRequestDto);
                 API_URL = $"{webApiUrl}/payments";
                log.Debug($"MakeSubcriptionPayment(): API_URL={API_URL}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";

                log.Debug($"MakeSubcriptionPayment(): request json={jsonRequestDto}");

                byte[] data = Encoding.UTF8.GetBytes(jsonRequestDto);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-api-key", apiKey);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"MakeSubcriptionPayment(): responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();
            }
            catch (WebException webEx)
            {
                string errMsg = GetErrorMessage(webEx);
                log.Error("API_URL :" + API_URL);
                log.Error($"jsonRequestDto= {jsonRequestDto}");
                log.Error(errMsg);
                throw new Exception(errMsg);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("API_URL :" + API_URL);
                log.Error($"jsonRequestDto= {jsonRequestDto}");
                throw;
            }
            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        private string GetErrorMessage(WebException ex)
        {
            log.LogMethodEntry(ex);
            try
            {
                log.LogMethodEntry(ex);
                using (var stream = ex?.Response?.GetResponseStream())
                    if (stream != null)
                        using (var reader = new StreamReader(stream))
                        {
                            string webException = reader.ReadToEnd();
                            log.Error(webException);
                            dynamic errorObj = JsonConvert.DeserializeObject(webException);
                            if (errorObj != null)
                            {
                                //log.LogMethodExit($"{errorObj.errorCode} | {errorObj.message}");
                                return $"{errorObj.errorCode} | {errorObj.message}";
                            }
                            else
                            {
                                //log.LogMethodExit(ex.Message);
                                return ex.Message;
                            }
                        }
                log.LogMethodExit(ex.Message);
                return ex.Message;

            }
            catch (Exception exp)
            {
                log.Error(exp);
                throw;
            }
        }

        public string GetTransactionId(string transactionId)
        {

            string adyenPspReference = string.Empty;
            try
            {
                log.LogMethodEntry(transactionId);
                if (string.IsNullOrWhiteSpace(transactionId))
                {
                    log.Error("TransactionId was null in GetTransactionId method");
                    log.Error("Transaction Id :" + transactionId);
                    throw new Exception("TransactionId was null");
                }

                string[] responseArray = transactionId.Split('.');
                log.Info("Response Array : " + responseArray);
                if (responseArray.Length > 0)
                {
                    adyenPspReference = responseArray[1];
                    log.Info("adyenPspReference" + adyenPspReference);
                }
            }
            catch (Exception ex)
            {
                log.Error("Error occured while getting the transactionId" + transactionId + ex);
                log.Error(transactionId);
                throw;
            }
            log.LogMethodExit(adyenPspReference);
            return adyenPspReference;
        }



        public Dictionary<string, string> ToDictionary(NameValueCollection nvc)
        {
            try
            {
                log.LogMethodEntry(nvc);
                Dictionary<string, string> keyValuePairs = nvc.AllKeys.ToDictionary(k => k, k => nvc[k]);
                //return nvc.AllKeys.ToDictionary(k => k, k => nvc[k]);
                log.LogMethodExit(keyValuePairs);
                return keyValuePairs;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public int GetAmountInMinorUnit(decimal amount, int? currencyConversionFactor)
        {
            int amountInMinorUnit;
            try
            {
                log.LogMethodEntry(amount, currencyConversionFactor);
                switch (currencyConversionFactor)
                {
                    case 1:
                        amountInMinorUnit = Convert.ToInt32(amount * 10); break;
                    case 2:
                        amountInMinorUnit = Convert.ToInt32(amount * 100); break;
                    case 3:
                        amountInMinorUnit = Convert.ToInt32(amount * 1000); break;
                    default:
                        amountInMinorUnit = Convert.ToInt32(amount);
                        break;
                }
                log.LogMethodExit(amountInMinorUnit);
                return amountInMinorUnit;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public double GetAmountInMajorUnit(int amount, int currencyConversionFactor)
        {
            double amountInMajorUnit;
            try
            {
                log.LogMethodEntry(amount, currencyConversionFactor);
                switch (currencyConversionFactor)
                {
                    case 1:
                        amountInMajorUnit = amount * 0.1; break;
                    case 2:
                        amountInMajorUnit = amount * 0.01; break;
                    case 3:
                        amountInMajorUnit = amount * 0.001; break;
                    default:
                        amountInMajorUnit = amount; break;
                }
                log.LogMethodExit(amountInMajorUnit);
                return amountInMajorUnit;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        public Dictionary<string, string> GetAdditionalResponseData(string AdditionalResponse)
        {
            try
            {
                log.LogMethodEntry(AdditionalResponse);
                if (string.IsNullOrWhiteSpace(AdditionalResponse))
                {
                    log.Error("AdditionalResponse was empty");
                    throw new Exception("Payment processing failed. Additional response not received.");
                }
                var additionalParamsKeyValue = HttpUtility.ParseQueryString(AdditionalResponse);
                log.Debug($"GetAdditionalResponseData(): additionalParamsKeyValue={additionalParamsKeyValue}");
                Dictionary<string, string> additionalResponseParams = ToDictionary(additionalParamsKeyValue);
                log.LogMethodExit(additionalResponseParams);
                return additionalResponseParams;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                log.Error("AdditionalResponse =" + AdditionalResponse);
                throw;
            }

        }

        public string GetRefundErrorMessage(RefundResponseDto responseObject, Dictionary<string, string> additionalResponseParams)
        {
            string errorMessage = "Unknown Error";
            try
            {
                log.LogMethodEntry(responseObject, additionalResponseParams);
                if (additionalResponseParams.Any())
                {
                    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | ";
                    if (additionalResponseParams.ContainsKey("message"))
                    {
                        errorMessage += additionalResponseParams["message"] + " | ";
                    }
                    if (additionalResponseParams.ContainsKey("refusalReason"))
                    {
                        errorMessage += additionalResponseParams["refusalReason"] + " | ";
                    }
                    if (additionalResponseParams.ContainsKey("errors"))
                    {
                        errorMessage += additionalResponseParams["errors"];
                    }
                    //if (!string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["errors"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["message"]} | {additionalResponseParams["errors"]} | {additionalResponseParams["refusalReason"]}";
                    //}
                    //else if (string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["errors"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["errors"]}";
                    //}
                    //else if (!string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]) && string.IsNullOrWhiteSpace(additionalResponseParams["errors"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["message"]}";
                    //}
                    //else if (string.IsNullOrWhiteSpace(additionalResponseParams["message"]) && !string.IsNullOrWhiteSpace(additionalResponseParams["refusalReason"]) && string.IsNullOrWhiteSpace(additionalResponseParams["errors"]))
                    //{
                    //    errorMessage = $"{responseObject.SaleToPOIResponse.ReversalResponse.Response.ErrorCondition.ToString()} | {additionalResponseParams["refusalReason"]}";
                    //}
                    //else
                    //{
                    //    errorMessage = "Unknown Error";
                    //}
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(errorMessage);
            return errorMessage;
        }

        private string GetTenderOptions(bool isDonationEnabled, bool isPartialPaymentAllowed, bool isTipAllowed)
        {
            string strTenderOptions = string.Empty;
            try
            {
                log.LogMethodEntry(isDonationEnabled, isPartialPaymentAllowed, isTipAllowed);
                if (isDonationEnabled && isPartialPaymentAllowed && isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION},{ENABLE_PARTIAL_PAYMENT},{ENABLE_TIP}";
                }
                else if (isDonationEnabled && !isPartialPaymentAllowed && !isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION}";
                }
                else if (!isDonationEnabled && !isTipAllowed && isPartialPaymentAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_PARTIAL_PAYMENT}";
                }
                else if (!isDonationEnabled && isTipAllowed && !isPartialPaymentAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_TIP}";
                }
                else if (isDonationEnabled && isPartialPaymentAllowed && !isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION},{ENABLE_PARTIAL_PAYMENT}";
                }
                else if (!isDonationEnabled && isPartialPaymentAllowed && isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_PARTIAL_PAYMENT},{ENABLE_TIP}";
                }
                else if (isDonationEnabled && !isPartialPaymentAllowed && isTipAllowed)
                {
                    strTenderOptions = $"tenderOption={ENABLE_DONATION},{ENABLE_TIP}";
                }
                else
                {
                    strTenderOptions = string.Empty;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(strTenderOptions);
            return strTenderOptions;
        }

        private Transactionconditions GetManualKeyEntryParams(bool isManualKeyEntryMode)
        {
            Transactionconditions transactionconditions = null;
            try
            {
                log.LogMethodEntry(isManualKeyEntryMode);
                if (isManualKeyEntryMode)
                {
                    transactionconditions = new Transactionconditions
                    {
                        ForceEntryMode = new string[]
                        {
                            "Keyed"
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(transactionconditions);
            return transactionconditions;
        }

        public bool ValidateCCRequestId(string reqCCRequestId, string respCCRequestId)
        {
            try
            {
                log.LogMethodEntry(reqCCRequestId, respCCRequestId);
                if (reqCCRequestId.Equals(respCCRequestId))
                {
                    log.LogMethodExit(true);
                    return true;
                }
                else
                {
                    log.LogMethodExit(false);
                    return false;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }
    }
}
