﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PeachWalletPayments
{
    public class PeachWalletHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private readonly string ENTITY_ID;

        private readonly string SECRET_KEY;
        private readonly string BASE_URL;
        private readonly string POST_URL;
        private readonly string REFERER_URL;
        private readonly string CHECKOUT_URL = "/checkout/initiate";
        private readonly string VERIFY_URL = "/status";
        private readonly string REFUND_URL = "/v1/checkout/refund";

        public PeachWalletHostedCommandHandler(string ENTITY_ID, string SECRET_KEY, string BASE_URL, string POST_URL, string REFERER_URL)
        {
            this.ENTITY_ID = ENTITY_ID;
            this.SECRET_KEY = SECRET_KEY;
            this.BASE_URL = BASE_URL;
            this.POST_URL = POST_URL;
            this.REFERER_URL = REFERER_URL;
        }
        public static string EncodeToBase64(string input)
        {
            log.LogMethodEntry(input);
            byte[] bytesToEncode = System.Text.Encoding.UTF8.GetBytes(input);
            string result = Convert.ToBase64String(bytesToEncode);
            log.LogMethodExit(result);
            return result;
        }
        public PeachPaymentsHostedResponseDTO CreateCheckout(PeachPaymentsHostedRequestDTO peachPaymentsRequestDTO)
        {
            log.LogMethodEntry(peachPaymentsRequestDTO);
            PeachPaymentsHostedResponseDTO checkoutResponseDto = null;
            try
            {
                if (peachPaymentsRequestDTO == null)
                {
                    throw new Exception("Error creating payment request");
                }

                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("authentication.entityId", peachPaymentsRequestDTO.AuthenticationEntityId));

                formData.Add(new KeyValuePair<string, string>("merchantTransactionId", peachPaymentsRequestDTO.MerchantTransactionId));

                formData.Add(new KeyValuePair<string, string>("amount", peachPaymentsRequestDTO.Amount.ToString()));

                formData.Add(new KeyValuePair<string, string>("paymentType", peachPaymentsRequestDTO.PaymentType));

                formData.Add(new KeyValuePair<string, string>("currency", peachPaymentsRequestDTO.Currency));

                formData.Add(new KeyValuePair<string, string>("nonce", peachPaymentsRequestDTO.Nonce));

                formData.Add(new KeyValuePair<string, string>("shopperResultUrl", peachPaymentsRequestDTO.ShopperResultUrl));

                string signature = GenerateSignature(formData.ToDictionary(kv => kv.Key, kv => kv.Value));

                formData.Add(new KeyValuePair<string, string>("signature", signature));
                // Send the API request to initiate the transaction
                WebRequestClient webRequestClient = new WebRequestClient(BASE_URL + CHECKOUT_URL, HttpVerb.POST, formData);
                webRequestClient.ContentType = "application/x-www-form-urlencoded";
                webRequestClient.IsBasicAuthentication = false;
                webRequestClient.Referer = REFERER_URL;
                string responseFromServer = webRequestClient.MakeRequest();

                // Deserialize the response from the server
                checkoutResponseDto = JsonConvert.DeserializeObject<PeachPaymentsHostedResponseDTO>(responseFromServer);
            }
            catch (Exception ex)
            {
                throw new Exception("Error creating checkout", ex);
            }
            return checkoutResponseDto;
        }

        public string GenerateSignature(Dictionary<string, string> fields)
        {
            log.LogMethodEntry(fields.ToString());
            Dictionary<string, string> sortedFields = fields.OrderBy(kv => kv.Key)
                .ToDictionary(kv => kv.Key, kv => kv.Value);

            string concatenatedKeyValuePairs = string.Join("", sortedFields.Select(kv => $"{kv.Key}{kv.Value}"));

            byte[] secretKeyBytes = Encoding.UTF8.GetBytes(SECRET_KEY);

            byte[] concatenatedValueBytes = Encoding.UTF8.GetBytes(concatenatedKeyValuePairs);

            using (HMACSHA256 hmac = new HMACSHA256(secretKeyBytes))
            {
                byte[] hashBytes = hmac.ComputeHash(concatenatedValueBytes);
                string result = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
                log.LogMethodExit(result);

                return result;
            }
        }
        public PeachPaymentsTrxSeachResponseDTO VerifyPayment(string reference)
        {
            log.LogMethodEntry(reference);
            PeachPaymentsTrxSeachResponseDTO checkoutResponseDto = null;
            try
            {
                if (reference == null)
                {
                    log.Error("Error occurred while processing your payment: TrxId is null.");
                    throw new Exception("Error occurred while processing your payment");
                }


                PeachPaymentsTrxSearchRequestDTO requestDto = new PeachPaymentsTrxSearchRequestDTO
                {
                    AuthenticationEntityId = ENTITY_ID,
                    MerchantTransactionId = reference
                };
                log.Debug("Request params: " + requestDto.ToString());
                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("authentication.entityId", requestDto.AuthenticationEntityId));
                formData.Add(new KeyValuePair<string, string>("merchantTransactionId", requestDto.MerchantTransactionId));

                string signature = GenerateSignature(formData.ToDictionary(kv => kv.Key, kv => kv.Value));
                requestDto.Signature = signature;

                formData.Add(new KeyValuePair<string, string>("signature", requestDto.Signature));

                string API_URL = BASE_URL + VERIFY_URL + "?authentication.entityId=" + ENTITY_ID +
                "&merchantTransactionId=" + reference + "&signature=" + signature;

                string responseFromServer;

                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.GET);
                webRequestClient.ContentType = "application/json";
                webRequestClient.IsBasicAuthentication = true;
                webRequestClient.Username = SECRET_KEY;

                responseFromServer = webRequestClient.GetResponse();
                log.Debug("Raw response: " + responseFromServer);
                checkoutResponseDto = JsonConvert.DeserializeObject<PeachPaymentsTrxSeachResponseDTO>(responseFromServer);
                log.Debug("PeachPaymentsTrxSeachResponse: " + checkoutResponseDto);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit();
            return checkoutResponseDto;
        }

        public PeachPaymentsRefundResponseDTO CreateRefund(PeachPaymentsRefundRequestDTO peachPaymentsRequestDTO)
        {
            log.LogMethodEntry(peachPaymentsRequestDTO.ToString());
            PeachPaymentsRefundResponseDTO checkoutResponseDto;
            try
            {
                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("authentication.entityId", peachPaymentsRequestDTO.AuthenticationEntityId));

                formData.Add(new KeyValuePair<string, string>("id", peachPaymentsRequestDTO.Id));

                formData.Add(new KeyValuePair<string, string>("amount", peachPaymentsRequestDTO.Amount));

                formData.Add(new KeyValuePair<string, string>("paymentType", peachPaymentsRequestDTO.PaymentType));

                formData.Add(new KeyValuePair<string, string>("currency", peachPaymentsRequestDTO.Currency));

                string signature = GenerateSignature(formData.ToDictionary(kv => kv.Key, kv => kv.Value));

                formData.Add(new KeyValuePair<string, string>("signature", signature));

                if (peachPaymentsRequestDTO == null)
                {
                    log.Error("Error creating payment request: peachPaymentsRequestDTO is null.");
                    throw new Exception("Error creating payment request");
                }
                WebRequestClient webRequestClient = new WebRequestClient(POST_URL + REFUND_URL, HttpVerb.POST, formData);
                webRequestClient.ContentType = "application/x-www-form-urlencoded";
                webRequestClient.IsBasicAuthentication = false;
                webRequestClient.Referer =REFERER_URL;
                string responseFromServer = webRequestClient.MakeRequest();
                log.Debug("Raw Response: " + responseFromServer);
                // Deserialize the response from the server
                checkoutResponseDto = JsonConvert.DeserializeObject<PeachPaymentsRefundResponseDTO>(responseFromServer);
                log.LogMethodExit(checkoutResponseDto.ToString());
                return checkoutResponseDto;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }


    }
}
