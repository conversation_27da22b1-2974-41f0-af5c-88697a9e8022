﻿/********************************************************************************************
 * Project Name -  IPay88 Payment Gateway with callback                                                                    
 * Description  - Class to handle IPay88 Payment Gateway with callback
 *
 **************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 *********************************************************************************************
 *2.152.0    11-Nov-2023    Yashodhara C H                 Created for Website
 *2.152.1     5-Jul-2024    Prajwal Shrikanth Hegde        Payment Standardization - Added MapPaymentStatus() and GetPaymentStatusSearch()
 ********************************************************************************************/
using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using Semnox.Parafait.Site;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Collections.Specialized;
using System.Web;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.IPay88
{
    class Ipay88CallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        string merchantCode;
        string merchantKey;
        string iPay88ServerUrl;
        string iPay88RequeryUrl;
        string iPay88VoidUrl;
        string currencyCode;
        string baseApiUrl;
        private string txSearchApiUrl;
        string voidApiUrl;
        string refundApiUrl;
        private string post_url;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";

        string paymentId;
        string refNo;
        string amount;
        string prodDesc;
        string userName;
        string userEmail;
        string userContact;
        string Xfield1;
        string transId;
        string authCode;
        string lang;
        string signatureType;
        string signature;
        string errDesc;
        string currency_Code;
        string hostedPaymentSubmissionPage;
        string creditCardNumber;
        string nameOnCreditCard;

        private HostedGatewayDTO hostedGatewayDTO;
        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        private string paymentPageLink;

        private Dictionary<string, string> voidTransactionErrCode = new Dictionary<string, string>();

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"1", PaymentStatusType.SUCCESS},
            {"0", PaymentStatusType.FAILED},
        };
        private static readonly Dictionary<string, PaymentStatusType> StatusCheckStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"1", PaymentStatusType.SUCCESS},
            {"0", PaymentStatusType.FAILED},
            {"6", PaymentStatusType.PENDING},
            {"20", PaymentStatusType.PENDING},
        };
        private static readonly Dictionary<string, PaymentStatusType> VoidStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"0", PaymentStatusType.SUCCESS},
            {"1", PaymentStatusType.FAILED},
            {"3", PaymentStatusType.FAILED},
            {"4", PaymentStatusType.FAILED},
            {"5", PaymentStatusType.FAILED},
            {"6", PaymentStatusType.FAILED},
            {"7", PaymentStatusType.FAILED},
            {"12", PaymentStatusType.FAILED},
            {"13", PaymentStatusType.FAILED},
            {"14", PaymentStatusType.FAILED},
            {"15", PaymentStatusType.FAILED},
            {"19", PaymentStatusType.FAILED},
            {"20", PaymentStatusType.FAILED},
            {"21", PaymentStatusType.FAILED},
            {"22", PaymentStatusType.FAILED},
            {"30", PaymentStatusType.FAILED},
            {"33", PaymentStatusType.FAILED},
            {"34", PaymentStatusType.FAILED},
            {"36", PaymentStatusType.FAILED},
            {"41", PaymentStatusType.FAILED},
            {"43", PaymentStatusType.FAILED},
            {"51", PaymentStatusType.FAILED},
            {"54", PaymentStatusType.FAILED},
            {"59", PaymentStatusType.FAILED},
            {"61", PaymentStatusType.FAILED},
            {"62", PaymentStatusType.FAILED},
            {"63", PaymentStatusType.FAILED},
            {"65", PaymentStatusType.FAILED},
            {"91", PaymentStatusType.FAILED},
            {"96", PaymentStatusType.FAILED},
            {"1001", PaymentStatusType.FAILED},
            {"1002", PaymentStatusType.FAILED},
            {"1003", PaymentStatusType.FAILED},
            {"1004", PaymentStatusType.FAILED},
            {"1005", PaymentStatusType.FAILED},
            {"1006", PaymentStatusType.FAILED},
            {"1007", PaymentStatusType.FAILED},
            {"1008", PaymentStatusType.FAILED},
            {"1009", PaymentStatusType.FAILED},
            {"1010", PaymentStatusType.FAILED},
            {"1011", PaymentStatusType.FAILED},
            {"1012", PaymentStatusType.FAILED},
            {"9999", PaymentStatusType.FAILED}
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            {"5", PaymentStatusType.SUCCESS},
            {"0", PaymentStatusType.FAILED},
            {"1", PaymentStatusType.FAILED},
            {"2", PaymentStatusType.FAILED},
            {"3", PaymentStatusType.FAILED},
            {"4", PaymentStatusType.FAILED},
        };

        public Ipay88CallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
            : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            this.hostedGatewayDTO = new HostedGatewayDTO();
            this.Initialize();
            this.BuildTransactions = false;
            log.LogMethodExit(null);
        }

        public override void Initialize()
        {
            log.LogMethodEntry();
            merchantCode = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            merchantKey = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            iPay88ServerUrl = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            iPay88RequeryUrl = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            currencyCode = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");
            baseApiUrl = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            post_url = "/account/Payments";

            voidTransactionErrCode.Clear();
            InitializeVoidErrorCode();

            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", merchantCode);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", merchantKey);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_API_URL", iPay88ServerUrl);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_REQUERY_URL", iPay88RequeryUrl);
            log.LogVariableState("HOSTED_PAYMENT_GATEWAY_BASE_URL", baseApiUrl);

            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;
            string errMsg = "";

            if (string.IsNullOrWhiteSpace(merchantCode))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            }
            else if (string.IsNullOrWhiteSpace(merchantKey))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            }
            else if (string.IsNullOrWhiteSpace(iPay88ServerUrl))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            else if (string.IsNullOrWhiteSpace(iPay88RequeryUrl))
            {
                errMsg = String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_REQUERY_URL");
            }
            if (string.IsNullOrWhiteSpace(baseApiUrl))
            {
                errMsg += String.Format(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }

            this.hostedGatewayDTO.PGSuccessResponseMessage = "RECEIVEOK";
            this.hostedGatewayDTO.PGFailedResponseMessage = "RECEIVEOK";

            String apiSite = "";
            String webSite = "";

            apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;
            //log.Debug("apiSite " + apiSite);

            webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;
            //log.Debug("webSite " + webSite);

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    var queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.ipay88CallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                //log.Debug("successResponseAPIURL " + successResponseAPIURL);
            }

            if(LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                //log.Debug("failureResponseAPIURL " + failureResponseAPIURL);
            }

            if(LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                //log.Debug("cancelResponseAPIURL " + cancelResponseAPIURL);
            }

            if(LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                //log.Debug("callbackResponseAPIURL " + callbackResponseAPIURL);
            }

            if(LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                //log.Debug("successRedirectURL " + this.hostedGatewayDTO.SuccessURL);
            }

            if(LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if(LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                this.hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description.Replace("@gateway", PaymentGateways.ipay88CallbackHostedPayment.ToString());
                //log.Debug("failureCancelRedirectURL " + this.hostedGatewayDTO.CancelURL);
            }

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEB_SITE_CONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;

            log.LogMethodExit();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns></returns>
        private IDictionary<string, string> SetPostParameters(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO, cCRequestPGWDTO);
            IDictionary<string, string> postparamslist = new Dictionary<string, string>();

            signatureType = "HMACSHA512";
            postparamslist.Clear();
            postparamslist.Add("MerchantCode", this.merchantCode);
            postparamslist.Add("PaymentId", "");
            postparamslist.Add("RefNo", transactionPaymentsDTO.TransactionId.ToString());
            postparamslist.Add("Amount", transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_FORMAT));
            postparamslist.Add("Currency", this.currencyCode);
            postparamslist.Add("ProdDesc", TransactionType.SALE.ToString());
            postparamslist.Add("UserName", transactionPaymentsDTO.CreditCardName);
            postparamslist.Add("UserEmail", transactionPaymentsDTO.NameOnCreditCard);
            postparamslist.Add("UserContact", transactionPaymentsDTO.CardEntitlementType);

            SiteList siteList = new SiteList(utilities.ExecutionContext);
            List<KeyValuePair<SiteDTO.SearchBySiteParameters, string>> searchParameters = new List<KeyValuePair<SiteDTO.SearchBySiteParameters, string>>();
            searchParameters.Add(new KeyValuePair<SiteDTO.SearchBySiteParameters, string>(SiteDTO.SearchBySiteParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
            List<SiteDTO> siteDTOList = siteList.GetAllSites(searchParameters);
            string siteName = (siteDTOList != null && siteDTOList.Any()) ? siteDTOList[0].SiteShortName : "";
            log.Debug("siteName: " + siteName);
            Xfield1 = utilities.ExecutionContext.GetSiteId().ToString() + "|" + transactionPaymentsDTO.PaymentModeId + "|" + siteName; //Xfield1 assigned for signature calculation
            postparamslist.Add("Xfield1", Xfield1);
            string signatureString = GetPaymentSignatureString(transactionPaymentsDTO, cCRequestPGWDTO);
            postparamslist.Add("SignatureType", this.signatureType);
            this.signature = GetPaymentSignature(signatureString);

            postparamslist.Add("Signature", this.signature);
            postparamslist.Add("ResponseURL", this.successResponseAPIURL);
            postparamslist.Add("BackendURL", this.callbackResponseAPIURL);
            //postparamslist.Add("PostURL", iPay88ServerUrl);


            log.LogMethodExit(postparamslist);
            return postparamslist;
        }

        /// <summary>
        /// Creates a initial gateway request.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="paymentToken"></param>
        /// <returns>HostedGatewayDTO</returns>
        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentToken);
            this.hostedGatewayDTO.RequestURL = this.iPay88ServerUrl;
            log.LogMethodEntry("CCRequestSite:" + utilities.ExecutionContext.GetSiteId());
            CCRequestPGWDTO cCRequestPGWDTO = this.CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

            IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
            requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
            requestParamsDict.Add("paymentToken", paymentToken);

            this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
            this.hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(this.hostedGatewayDTO.GatewayRequestString, paymentPageLink);

            this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO), this.iPay88ServerUrl, "frmIPay88From"); // For Old smartfun compatibility

            log.Debug("Request string:" + this.hostedGatewayDTO.GatewayRequestString);
            log.Debug("Direct request link:" + this.hostedGatewayDTO.PaymentRequestLink);
            log.Debug("GatewayRequestFormString:" + this.hostedGatewayDTO.GatewayRequestFormString);

            log.LogMethodExit("gateway dto:" + this.hostedGatewayDTO.ToString());
            return this.hostedGatewayDTO;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);

            CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, transactionPaymentsDTO.Guid);//here, transactionPaymentsDTO.guid contains guid of ccrequestpgw
            CCRequestPGWDTO cCRequestPGWDTO = cCRequestPGWBL.CCRequestPGWDTO;

            this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO));
            this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(SetPostParameters(transactionPaymentsDTO, cCRequestPGWDTO), this.iPay88ServerUrl, "frmIPay88From");

            log.Debug("Request string:" + this.hostedGatewayDTO.GatewayRequestString);
            log.Debug("Request form string:" + this.hostedGatewayDTO.GatewayRequestFormString);

            log.LogMethodExit(this.hostedGatewayDTO);
            return this.hostedGatewayDTO;
        }

        private string GetSubmitFormKeyValueList(IDictionary<string, string> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {
            log.LogMethodEntry(postparamslist, URL, FormName, submitMethod);
            string Method = submitMethod;
            StringBuilder builder = new StringBuilder();
            builder.Clear();
            builder.Append("<html>");

            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }

            builder.Append("</form>");
            builder.Append("</body></html>");
            log.LogMethodExit(builder.ToString());
            return builder.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="cCRequestPGWDTO"></param>
        /// <returns></returns>
        private string GetPaymentSignatureString(TransactionPaymentsDTO transactionPaymentsDTO, CCRequestPGWDTO cCRequestPGWDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string signatureString = string.Empty;
            signatureString += merchantKey + merchantCode + transactionPaymentsDTO.TransactionId;
            string amount = Regex.Replace(transactionPaymentsDTO.Amount.ToString(utilities.ParafaitEnv.AMOUNT_FORMAT), @"[^0-9]+", "");
            signatureString += amount + currencyCode + Xfield1;

            log.LogMethodExit(signatureString);
            return signatureString;
        }

        private string GetResponseSignatureString(dynamic response)
        {
            log.LogMethodEntry(response);
            string signatureString = string.Empty;
            signatureString += merchantKey + merchantCode + response["PaymentId"] + response["RefNo"];
            string amount = Regex.Replace(response["Amount"].ToString(), @"[^0-9]+", "");
            signatureString += amount + currencyCode + response["Status"];

            log.LogMethodExit(signatureString);
            return signatureString;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="rawData"></param>
        /// <returns></returns>
        private string GetPaymentSignature(string rawData)
        {
            log.LogMethodEntry(rawData);
            byte[] keyBytes = Encoding.UTF8.GetBytes(merchantKey);
            // Create a HMACSHA512   
            using (HMACSHA512 hMACSHA512 = new HMACSHA512(keyBytes))
            {
                // ComputeHash - returns byte array  
                byte[] bytes = hMACSHA512.ComputeHash(Encoding.UTF8.GetBytes(rawData));

                // Convert byte array to a string   
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                log.LogMethodExit(builder.ToString());
                return builder.ToString();
            }
        }
        /// <summary>
        /// Process the gateway response
        /// </summary>
        /// <param name="gatewayResponse"></param>
        /// <returns></returns>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            string paymentStatus = "";
            this.hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            try
            {
                if (string.IsNullOrWhiteSpace(gatewayResponse))
                {
                    log.Error("Response for Sale Transaction was empty.");
                    throw new Exception("Error processing your payment");
                }

                string encResponse = gatewayResponse;
                string[] result = new string[] { };
                log.Info(encResponse);

                dynamic response = GetResposeObj(gatewayResponse);
                log.Debug("response yas " + response);


                refNo = response["RefNo"] == null ? "" : Convert.ToString(response["RefNo"]);
                paymentId = response["PaymentId"] == null ? "" : Convert.ToString(response["PaymentId"]);
                amount = response["Amount"] == null ? "" : Convert.ToString(response["Amount"]);
                Xfield1 = response["Xfield1"] == null ? "" : Convert.ToString(response["Xfield1"]);
                transId = response["TransId"] == null ? "" : Convert.ToString(response["TransId"]);
                authCode = response["AuthCode"] == null ? "" : Convert.ToString(response["AuthCode"]);
                errDesc = response["ErrDesc"] == null ? "" : Convert.ToString(response["ErrDesc"]);
                currency_Code = response["Currency"] == null ? "" : Convert.ToString(response["Currency"]);

                log.Debug("extracted result");
                if (!string.IsNullOrEmpty(Xfield1))
                {
                    result = Xfield1.Split('|');
                }
                
                creditCardNumber = response["CCNo"] != null ? Convert.ToString(response["CCNo"]) : "";
                if (!string.IsNullOrWhiteSpace(creditCardNumber) && creditCardNumber.Length > 4)
                {
                    creditCardNumber = creditCardNumber.Substring(creditCardNumber.Length - 4).PadLeft(creditCardNumber.Length, 'X');
                }

                nameOnCreditCard = response["CCName"] != null ? Convert.ToString(response["CCName"]) : "";
                if (!string.IsNullOrWhiteSpace(nameOnCreditCard))
                {
                    nameOnCreditCard = response["CCName"] != null ? Convert.ToString(response["CCName"]) : "";
                }
                
                hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = nameOnCreditCard;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = creditCardNumber;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = transId;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization = transId;
                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(amount);
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = result.Length >= 2 ? Convert.ToInt32(result[1]) : -1;
                hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(refNo);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = currency_Code;

                PaymentStatusType salePaymentStatus = MapPaymentStatus(Convert.ToString(response["Status"]), PaymentGatewayTransactionType.SALE);
                
                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("success");
                    string responseSignature = GetResponseSignatureString(response);
                    this.signature = GetPaymentSignature(responseSignature);
                    if (this.signature.Equals(Convert.ToString(response["Signature"])))
                    {
                        hostedGatewayDTO.PaymentStatus = salePaymentStatus;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                        paymentStatus = "APPROVED_";
                    }
                    else
                    {
                        hostedGatewayDTO.PaymentStatusMessage = "Signature Miss Match";
                        log.Error(hostedGatewayDTO.PaymentStatusMessage);
                        hostedGatewayDTO.PaymentStatus = PaymentStatusType.ERROR;
                        hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                        paymentStatus = "FAILED_";
                        log.Debug("Payment Failed");
                    }
                }
                else
                {
                    hostedGatewayDTO.PaymentStatusMessage = errDesc;
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                    paymentStatus = "FAILED_";
                    log.Debug("Payment Failed");
                }

                
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                this.TransactionSiteId = cCRequestsPGWDTO.SiteId;

                log.Debug("Trying to update the CC request to payment processing status");
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);


                int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(PaymentProcessStatusType.PAYMENT_PROCESSING.ToString(), hostedGatewayDTO.PaymentProcessStatus.ToString());

                if (rowsUpdated == 0)
                {
                    log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                }
                else
                {
                    log.Debug("CC request updated to " + hostedGatewayDTO.PaymentProcessStatus.ToString());
                }

                CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = null;
                List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                if (cCTransactionsPGWDTOList == null)
                {
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.TokenID = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.Authorize = amount.ToString();
                    cCTransactionsPGWDTO.Purchase = amount.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.TextResponse = string.Concat(paymentStatus, hostedGatewayDTO.PaymentStatus.ToString());
                    cCTransactionsPGWDTO.AuthCode = authCode;
                    cCTransactionsPGWDTO.AcctNo = creditCardNumber.ToString();
                    cCTransactionsPGWDTO.DSIXReturnCode = hostedGatewayDTO.PaymentStatusMessage;
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();

                    if (!string.IsNullOrEmpty(paymentId))
                    {
                        List<LookupValuesDTO> lookupValuesDTOList;
                        LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
                        List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> lookUpValueSearchParameters = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
                        lookUpValueSearchParameters.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, "IPAY_PAYMENT_OPTIONS"));
                        lookupValuesDTOList = lookupValuesList.GetAllLookupValues(lookUpValueSearchParameters);


                        if (lookupValuesDTOList != null && lookupValuesDTOList.Any())
                        {
                            var lookupValuesDTO = lookupValuesDTOList.Where(x => x.LookupValue == paymentId).FirstOrDefault();
                            if (lookupValuesDTO != null)
                            {
                                cCTransactionsPGWDTO.CardType = lookupValuesDTO.Description;
                            }
                        }
                    }
                    this.hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment Processing failed", ex);
                throw;
            }
            
            
            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="gatewayResponse"></param>
        /// <returns></returns>
        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            if (this.hostedGatewayDTO == null)
            {
                this.hostedGatewayDTO = new HostedGatewayDTO();
            }

            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            log.Debug("DeserializeObject Initiated");

            dynamic responseObj = GetResposeObj(gatewayResponse);
            if (responseObj != null)
            {
                hostedGatewayDTO.TrxId = Convert.ToInt32(responseObj.RefNo);
                hostedGatewayDTO.GatewayReferenceNumber = responseObj.TransId;
            }
            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }

        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = null;

            voidApiUrl = baseApiUrl + "/ePayment/WebService//VoidAPI/VoidFunction.asmx";
            log.Debug("Void API URL: " + voidApiUrl);
            refundApiUrl = baseApiUrl + "/epayment/Webservice/RefundAPI_V2/Refund/RefundRequest";
            log.Debug("Refund API URL: " + refundApiUrl);

            if (transactionPaymentsDTO != null)
            {
                try
                {
                    CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;

                    if (transactionPaymentsDTO.CCResponseId > -1)
                    {
                        CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                        List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                        searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                        List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                        // get transaction type of sale CCRequest record
                        ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                        log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO);

                        // to get original TrxId  (in case of POS refund)
                        refundTrxId = ccOrigTransactionsPGWDTO.InvoiceNo;
                        log.Debug("Original TrxId for refund: " + refundTrxId);
                    }
                    else
                    {
                        refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                        log.Debug("Refund TrxId for refund: " + refundTrxId);
                    }

                    DateTime originalPaymentDate = new DateTime();
                    CCRequestPGWDTO ccOrigRequestPGWDTO = null;
                    CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                    List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                    searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, refundTrxId));
                    List<CCRequestPGWDTO> cCRequestPGWDTOList = cCRequestPGWListBL.GetCCRequestPGWDTOList(searchParametersPGW);

                    if (cCRequestPGWDTOList != null)
                    {
                        ccOrigRequestPGWDTO = cCRequestPGWDTOList[0]; // to get SALE Tx Type
                    }
                    else
                    {
                        log.Error("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                        throw new Exception("No CCRequestPGW found for trxid:" + transactionPaymentsDTO.TransactionId.ToString());
                    }

                    if (ccOrigRequestPGWDTO != null)
                    {
                        originalPaymentDate = ccOrigRequestPGWDTO.RequestDatetime;
                    }


                    DateTime bussStartTime = utilities.getServerTime().Date.AddHours(Convert.ToInt32(ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "BUSINESS_DAY_START_TIME")));
                    DateTime bussEndTime = bussStartTime.AddDays(1);
                    if (utilities.getServerTime() < bussStartTime)
                    {
                        bussStartTime = bussStartTime.AddDays(-1);
                        bussEndTime = bussStartTime.AddDays(1);
                    }

                    Ipay88HostedCommandHandler ipayCmdHandler = new Ipay88HostedCommandHandler(merchantKey, merchantCode, currencyCode);
                    if ((originalPaymentDate >= bussStartTime) && (originalPaymentDate <= bussEndTime))
                    {
                        //SAME DAY: VOID
                        log.Debug("SAME DAY: VOID");
                        string errorMessage = string.Empty;
                        bool isVoidSuccess = false;
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);

                        try
                        {
                            string voidStatus = ipayCmdHandler.VoidTransaction(transactionPaymentsDTO.Reference, transactionPaymentsDTO.Amount,
                                voidApiUrl);

                            PaymentStatusType voidPaymentStatus = MapPaymentStatus(voidStatus, PaymentGatewayTransactionType.VOID);

                            CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                            ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                            ccTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString();
                            ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.VOID.ToString();
                            ccTransactionsPGWDTO.CardType = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.CardType : "";
                            ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : "";
                            ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                            ccTransactionsPGWDTO.PaymentStatus = voidPaymentStatus.ToString();

                            if (voidPaymentStatus == PaymentStatusType.SUCCESS)
                            {
                                log.Debug("Void Successfull");
                                isVoidSuccess = true;
                                ccTransactionsPGWDTO.TextResponse = "Approved";
                            }
                            else
                            {
                                isVoidSuccess = false;
                                if (voidTransactionErrCode.ContainsKey(voidStatus))
                                {
                                    errorMessage = voidTransactionErrCode[voidStatus];
                                    log.Error(errorMessage);
                                }
                                else
                                {
                                    errorMessage = "Unknown Error";
                                    log.Error(errorMessage);
                                }
                            }
                            CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                            ccTransactionsPGWBL.Save();

                            if (!isVoidSuccess)
                            {
                                throw new Exception(errorMessage);
                            }
                        }
                        catch (Exception ex)
                        {
                            log.Error("Error occured while voiding Transaction", ex);
                            log.LogMethodExit(null, "Throwing Payment Gateway Exception-" + ex.Message);
                            throw new PaymentGatewayException(ex.Message);
                        }
                    }
                    else
                    {
                        //NEXT DAY: REFUND
                        log.Debug("NEXT DAY: REFUND");
                        bool isRefundSuccess = false;
                        CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                        //Perform Refund
                        IPay88RefundResponseDTO iPay88RefundResponseDTO = ipayCmdHandler.RefundTransaction(transactionPaymentsDTO.Reference, transactionPaymentsDTO.Amount, refundApiUrl);
                        if (iPay88RefundResponseDTO == null)
                        {
                            log.Error("Refund Response is null: " + iPay88RefundResponseDTO);
                            throw new Exception("Refund Response is null.");
                        }

                        PaymentStatusType refundPaymentStatus = MapPaymentStatus(iPay88RefundResponseDTO.Status, PaymentGatewayTransactionType.REFUND);
                        
                        // save refund details
                        CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                        ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID.ToString();
                        ccTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString(); //trxId
                        ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        ccTransactionsPGWDTO.AcctNo = transactionPaymentsDTO.CreditCardNumber;
                        ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                        ccTransactionsPGWDTO.Authorize = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.Purchase = transactionPaymentsDTO.Amount.ToString();
                        ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                        ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();

                        if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                        {
                            log.Debug("Refund successful");
                            isRefundSuccess = true;
                            ccTransactionsPGWDTO.TextResponse = "SUCCESS";
                            ccTransactionsPGWDTO.DSIXReturnCode = iPay88RefundResponseDTO.ErrDesc;
                            ccTransactionsPGWDTO.AuthCode = iPay88RefundResponseDTO.Status;
                            ccTransactionsPGWDTO.RefNo = iPay88RefundResponseDTO.TransId; //iPay88 TrxId
                        }
                        else
                        {
                            log.Error("Refund failed, response status: " + iPay88RefundResponseDTO.ErrDesc);
                            isRefundSuccess = false;
                            ccTransactionsPGWDTO.TextResponse = "FAILED";
                            ccTransactionsPGWDTO.DSIXReturnCode = iPay88RefundResponseDTO.ErrDesc;
                            ccTransactionsPGWDTO.AuthCode = iPay88RefundResponseDTO.Status;
                            ccTransactionsPGWDTO.RefNo = iPay88RefundResponseDTO.TransId; //iPay88 TrxId

                        }

                        CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                        ccTransactionsPGWBL.Save();

                        if (!isRefundSuccess)
                        {
                            log.Error("Refund failed");
                            throw new Exception("Refund failed");
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw new Exception(ex.Message);
                }

            }

            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        private dynamic GetResposeObj(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);

            string jsonString = ConvertQueryStringToJson(gatewayResponse);
            log.Debug("Response as JSON: " + jsonString);

            dynamic responseObj = JsonConvert.DeserializeObject(jsonString);
            log.LogMethodExit(responseObj);
            return responseObj;
        }

        private string ConvertQueryStringToJson(string gatewayResponse)
        {
            log.LogMethodEntry();
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(gatewayResponse);

            Dictionary<string, string> responseDictionary = new Dictionary<string, string>();

            foreach (var key in responseCollection.AllKeys)
            {
                responseDictionary.Add(key, responseCollection[key]);
            }

            string responseJson = JsonConvert.SerializeObject(responseDictionary);

            log.LogMethodExit(responseJson);
            return responseJson;
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType defaultStatus = PaymentStatusType.FAILED; //default status
            PaymentStatusType paymentStatusType = defaultStatus;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = StatusCheckStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.VOID:
                        pgwStatusMappingDict = VoidStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to failed.");
                    paymentStatusType = defaultStatus;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentStatusType = defaultStatus;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentGatewayTransactionType);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PaymentStatusType mappedPaymentStatus = PaymentStatusType.NONE;
            string trxIdString = string.Empty;

            txSearchApiUrl = baseApiUrl + "/ePayment/Webservice/TxInquiryCardDetails/TxDetailsInquiry.asmx";
            log.Debug("TxSearch API URL: " + txSearchApiUrl);

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }
                //get amount from CCREQPGW 
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("No CCRequestPGW found for trxid:" + trxIdString);
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                //TxSearch
                Ipay88HostedCommandHandler ipayCmdHandler = new Ipay88HostedCommandHandler(merchantKey, merchantCode, currencyCode);
                TxDetailsInquiryCardInfoResponse txDetailsInquiryCardInfoResponse = ipayCmdHandler.RequeryPayment(trxIdString, cCRequestsPGWDTO.POSAmount, txSearchApiUrl);
                TxDetailsInquiryCardInfoResponseTxDetailsInquiryCardInfoResult transactionInquiry = txDetailsInquiryCardInfoResponse.TxDetailsInquiryCardInfoResult;

                if (transactionInquiry != null)
                {
                    log.Debug("RawStatus from gateway: " + transactionInquiry.Status);

                    mappedPaymentStatus = MapPaymentStatus(transactionInquiry.Status, PaymentGatewayTransactionType.STATUSCHECK);
                    log.Debug("Value of mappedPaymentStatus: " + mappedPaymentStatus.ToString());

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.RecordNo = transactionInquiry.RefNo ?? trxIdString; //trxId
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                    cCTransactionsPGWDTO.Purchase = cCRequestsPGWDTO.POSAmount;
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.PaymentStatus = mappedPaymentStatus.ToString();

                    if (mappedPaymentStatus == PaymentStatusType.SUCCESS)
                    {
                        log.Debug("Transaction found");

                        cCTransactionsPGWDTO.AuthCode = transactionInquiry.AuthCode;
                        cCTransactionsPGWDTO.Authorize = transactionInquiry.Amount;
                        cCTransactionsPGWDTO.RefNo = transactionInquiry.TransId; //iPay88 TrxId
                        cCTransactionsPGWDTO.TextResponse = transactionInquiry.Status;
                    }
                    else
                    {
                        log.Debug("No transaction found");
                        cCTransactionsPGWDTO.DSIXReturnCode = transactionInquiry.Errdesc;
                        cCTransactionsPGWDTO.TextResponse = transactionInquiry.Status;
                    }

                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                }
                else
                {
                    log.Error("Response is null");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }

        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);

            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;

            txSearchApiUrl = baseApiUrl + "/ePayment/Webservice/TxInquiryCardDetails/TxDetailsInquiry.asmx";
            log.Debug("TxSearch API URL: " + txSearchApiUrl);

            try
            {
                //get amount from CCREQPGW 
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                if (cCRequestsPGWDTO == null)
                {
                    log.Error("No CCRequestPGW found for trxid:" + trxId);
                    throw new Exception("No CCRequestPGW found for trxid:" + trxId);
                }

                //TxSearch
                Ipay88HostedCommandHandler ipayCmdHandler = new Ipay88HostedCommandHandler(merchantKey, merchantCode, currencyCode);
                TxDetailsInquiryCardInfoResponse txDetailsInquiryCardInfoResponse = ipayCmdHandler.RequeryPayment(trxId, cCRequestsPGWDTO.POSAmount, txSearchApiUrl);
                TxDetailsInquiryCardInfoResponseTxDetailsInquiryCardInfoResult transactionInquiry = txDetailsInquiryCardInfoResponse.TxDetailsInquiryCardInfoResult;

                if (!string.IsNullOrEmpty(transactionInquiry.Status) && transactionInquiry.Status == "1")
                {
                    log.Debug("Transaction found");

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.AuthCode = transactionInquiry.AuthCode;
                    cCTransactionsPGWDTO.Authorize = transactionInquiry.Amount;
                    cCTransactionsPGWDTO.Purchase = cCRequestsPGWDTO.POSAmount;
                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                    cCTransactionsPGWDTO.RefNo = transactionInquiry.TransId; //iPay88 TrxId
                    cCTransactionsPGWDTO.RecordNo = transactionInquiry.RefNo; //trxId
                    cCTransactionsPGWDTO.TextResponse = transactionInquiry.Status;
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    dict.Add("status", "1");
                    dict.Add("message", "success");
                    dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                    dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                    dict.Add("orderId", trxId);
                    //dict.Add("acctNo", resData["sourceOfFunds"]["provided"]["card"]["number"]);

                    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                }
                else
                {
                    log.Debug("No transaction found");

                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", trxId);
                    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                }

            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }


            log.LogMethodExit(resData);
            return resData;
        }

        private void InitializeVoidErrorCode()
        {
            voidTransactionErrCode.Add("1", "Refer to card issuer ");
            voidTransactionErrCode.Add("3", "Invalid Merchant  ");
            voidTransactionErrCode.Add("4", "Retain Card ");
            voidTransactionErrCode.Add("5", "Do not honor ");
            voidTransactionErrCode.Add("6", "System error ");
            voidTransactionErrCode.Add("7", "Pick up card (special) ");
            voidTransactionErrCode.Add("12", "Invalid transaction ");
            voidTransactionErrCode.Add("13", "Invalid Amount ");
            voidTransactionErrCode.Add("14", "Invalid card number ");
            voidTransactionErrCode.Add("15", "Invalid issuer ");
            voidTransactionErrCode.Add("19", "System timeout ");
            voidTransactionErrCode.Add("20", "Invalid response ");
            voidTransactionErrCode.Add("21", "No action taken ");
            voidTransactionErrCode.Add("22", "Suspected malfunction ");
            voidTransactionErrCode.Add("30", "Format error ");
            voidTransactionErrCode.Add("33", "Expired card ");
            voidTransactionErrCode.Add("34", "Suspected fraud ");
            voidTransactionErrCode.Add("36", "Restricted card ");
            voidTransactionErrCode.Add("41", "Pick up card (lost) ");
            voidTransactionErrCode.Add("43", "Pick up card (stolen) ");
            voidTransactionErrCode.Add("51", "Not sufficient funds ");
            voidTransactionErrCode.Add("54", "Expired card");
            voidTransactionErrCode.Add("59", "Suspected fraud ");
            voidTransactionErrCode.Add("61", "Exceeds withdrawal limit ");
            voidTransactionErrCode.Add("62", "Restricted card ");
            voidTransactionErrCode.Add("63", "Security violation ");
            voidTransactionErrCode.Add("65", "Activity count exceeded ");
            voidTransactionErrCode.Add("91", "Issuer or switch inoperative ");
            voidTransactionErrCode.Add("96", "System malfunction");
            voidTransactionErrCode.Add("1001", "Merchant Code is empty ");
            voidTransactionErrCode.Add("1002", "Transaction ID is empty ");
            voidTransactionErrCode.Add("1003", "Amount is empty ");
            voidTransactionErrCode.Add("1004", "Currency is empty ");
            voidTransactionErrCode.Add("1005", "Signature is empty ");
            voidTransactionErrCode.Add("1006", "Signature not match ");
            voidTransactionErrCode.Add("1007", "Invalid Amount ");
            voidTransactionErrCode.Add("1008", "Invalid Currency ");
            voidTransactionErrCode.Add("1009", "Invalid Merchant Code ");
            voidTransactionErrCode.Add("1010", "This transaction is not eligible for voiding ");
            voidTransactionErrCode.Add("1011", "Transaction not found ");
            voidTransactionErrCode.Add("1012", "Connection error ");
            voidTransactionErrCode.Add("9999", "Transaction already voided ");
        }
    }
}
