﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace BCAPayments
{
    public class CommunicationPort
    {
        private readonly SerialPort _serialPort;

        public CommunicationPort(string portName, int baudRate, int DataBits)
        {
            if (string.IsNullOrWhiteSpace(portName))
                throw new ArgumentException("Port name cannot be empty.");

            if (baudRate <= 0)
                throw new ArgumentException("Baud rate must be a positive number.");

            if (DataBits < 5 || DataBits > 8)
                throw new ArgumentOutOfRangeException("Data bits must be between 5 and 8.");


            _serialPort = new SerialPort
            {
                PortName = portName,
                BaudRate = baudRate,
                Parity = Parity.None,
                DataBits = DataBits,
                StopBits = StopBits.One,
                Handshake = Handshake.None,
                //ReadTimeout = 100,
                //WriteTimeout = 5000
            };
        }

        public void Open()
        {
            try
            {
                if (!_serialPort.IsOpen)
                    _serialPort.Open();
            }
            catch (UnauthorizedAccessException ex)
            {
                // return;
                throw new InvalidOperationException("Access to the port is denied.", ex);
            }
            catch (IOException ex)
            {
                throw new InvalidOperationException("The port is not available.", ex);
            }
        }
        public void Close()
        {
            if (_serialPort.IsOpen)
                _serialPort.Close();
        }

        public void Read(byte[] buffer, int offset, int count)
        {
            _serialPort.Read(buffer, offset, count);
        }

        //public byte[] SendAndReceive(byte[] requestBytes)
        //{
        //    if (requestBytes == null || requestBytes.Length == 0)
        //        throw new ArgumentException("Request bytes cannot be null or empty.");

        //    try
        //    {
        //        _serialPort.Write(requestBytes, 0, requestBytes.Length);

        //        string acknowledgment = ReadFromEDC();
        //        if (!acknowledgment.Substring(0, 2).Equals("06"))
        //            throw new InvalidOperationException($"Expected ACK (0x06), but received: {acknowledgment}");

        //        // Step 2: Read the full response
        //        string responseHex = ReadFromEDC();

        //        byte[] responseBytes = Enumerable.Range(0, responseHex.Length / 2)
        //                     .Select(i => Convert.ToByte(responseHex.Substring(i * 2, 2), 16))
        //                     .ToArray();

        //        _serialPort.Write(new byte[] { 0x06 }, 0, 1);


        //        return responseBytes;

        //    }
        //    catch (Exception ex)
        //    {
        //        throw new InvalidOperationException("Error during communication.", ex);
        //    }
        //}


        public byte[] SendAndReceive(byte[] requestBytes)
        {
            if (requestBytes == null || requestBytes.Length == 0)
                throw new ArgumentException("Request bytes cannot be null or empty.");

            const int maxRetries = 3;
            int attempt = 0;

            while (attempt < maxRetries)
            {
                try
                {
                    attempt++;
                    Console.WriteLine($"Attempt {attempt} to send data...");

                    // Send the request
                    _serialPort.Write(requestBytes, 0, requestBytes.Length);

                    // Wait for ACK or NAK within 2 seconds
                    string acknowledgment = ReadFromEDC();

                    if (acknowledgment.Substring(0, 2).Equals("06"))
                    {
                        Console.WriteLine("ACK received!");

                        // Step 2: Read the full response
                        string responseHex = ReadFromEDC();

                        byte[] responseBytes = Enumerable.Range(0, responseHex.Length / 2)
                                         .Select(i => Convert.ToByte(responseHex.Substring(i * 2, 2), 16))
                                         .ToArray();

                        // Send ACK to confirm reception
                        _serialPort.Write(new byte[] { 0x06 }, 0, 1);

                        return responseBytes;
                    }
                    else if (acknowledgment.Substring(0, 2).Equals("15"))
                    {
                        Console.WriteLine("NAK received! Retrying...");
                    }
                    else
                    {
                        Console.WriteLine($"Unexpected response: {acknowledgment}. Retrying...");
                    }
                }
                catch (TimeoutException)
                {
                    Console.WriteLine($"Timeout on attempt {attempt}. Retrying...");
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException("Error during communication.", ex);
                }
            }

            throw new InvalidOperationException("Failed to communicate with the EDC after maximum retries.");
        }



        private string ReadFromEDC()
        {
            int offset = 0;
            while (_serialPort.BytesToRead == 0)
            {
                Thread.Sleep(500);
            }

            byte[] buffer = new byte[_serialPort.BytesToRead];
            try
            {

                int bytesRead;
                do
                {
                    bytesRead = _serialPort.Read(buffer, offset, buffer.Length - offset);
                    offset += bytesRead;
                    _serialPort.ReadTimeout = 100; // Adjust timeout for subsequent reads
                }
                while (bytesRead > 0);

                string a = BitConverter.ToString(buffer).Replace("-", "");
                // Convert the buffer to a hexadecimal string
                return BitConverter.ToString(buffer).Replace("-", "");
            }
            catch (TimeoutException ex)
            {
                if (offset > 0)
                    return BitConverter.ToString(buffer).Replace("-", "");
                throw;
            }
        }
    }

}
