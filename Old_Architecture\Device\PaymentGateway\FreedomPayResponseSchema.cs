﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:2.0.50727.8838
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Xml.Serialization;

// 
// This source code was auto-generated by xsd, Version=2.0.50727.3038.
// 


/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("xsd", "2.0.50727.3038")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
[System.Xml.Serialization.XmlRootAttribute("POSResponse", Namespace = "", IsNullable = false)]
public partial class POSResponse
{

    private string requestGuidField;

    private string transactionIdField;

    private string requestIdField;

    private string approvedAmountField;

    private string approvalCodeField;

    private string maskedCardNumberField;

    private string cardTypeField;

    private string nameOnCardField;

    private string issuerNameField;

    private string expiryDateField;

    private string merchantReferenceCodeField;

    private string dCCAcceptedField;

    private string entryModeField;

    private string receiptTextField;

    private string pinVerifiedField;

    private string deviceVerifiedField;

    private string signatureRequiredField;

    private string cashBackAmountField;

    private string decisionField;

    private string errorCodeField;

    private string messageField;

    private string emvAIDField;
    private string emvIADField;
    private string emvTVRField;
    private string emvTSIField;
    private string emvARCField;
    
        
        
        
        
    /// <remarks/>
    public string RequestGuid
    {
        get
        {
            return this.requestGuidField;
        }
        set
        {
            this.requestGuidField = value;
        }
    }

    /// <remarks/>
    public string TransactionId
    {
        get
        {
            return this.transactionIdField;
        }
        set
        {
            this.transactionIdField = value;
        }
    }

    /// <remarks/>
    public string RequestId
    {
        get
        {
            return this.requestIdField;
        }
        set
        {
            this.requestIdField = value;
        }
    }

    /// <remarks/>
    public string ApprovedAmount
    {
        get
        {
            return this.approvedAmountField;
        }
        set
        {
            this.approvedAmountField = value;
        }
    }

    /// <remarks/>
    public string ApprovalCode
    {
        get
        {
            return this.approvalCodeField;
        }
        set
        {
            this.approvalCodeField = value;
        }
    }

    /// <remarks/>
    public string MaskedCardNumber
    {
        get
        {
            return this.maskedCardNumberField;
        }
        set
        {
            this.maskedCardNumberField = value;
        }
    }

    /// <remarks/>
    public string CardType
    {
        get
        {
            return this.cardTypeField;
        }
        set
        {
            this.cardTypeField = value;
        }
    }

    /// <remarks/>
    public string NameOnCard
    {
        get
        {
            return this.nameOnCardField;
        }
        set
        {
            this.nameOnCardField = value;
        }
    }

    /// <remarks/>
    public string IssuerName
    {
        get
        {
            return this.issuerNameField;
        }
        set
        {
            this.issuerNameField = value;
        }
    }

    /// <remarks/>
    public string ExpiryDate
    {
        get
        {
            return this.expiryDateField;
        }
        set
        {
            this.expiryDateField = value;
        }
    }

    /// <remarks/>
    public string MerchantReferenceCode
    {
        get
        {
            return this.merchantReferenceCodeField;
        }
        set
        {
            this.merchantReferenceCodeField = value;
        }
    }

    /// <remarks/>
    public string DCCAccepted
    {
        get
        {
            return this.dCCAcceptedField;
        }
        set
        {
            this.dCCAcceptedField = value;
        }
    }

    /// <remarks/>
    public string EntryMode
    {
        get
        {
            return this.entryModeField;
        }
        set
        {
            this.entryModeField = value;
        }
    }

    /// <remarks/>
    public string ReceiptText
    {
        get
        {
            return this.receiptTextField;
        }
        set
        {
            this.receiptTextField = value;
        }
    }

    /// <remarks/>
    public string PinVerified
    {
        get
        {
            return this.pinVerifiedField;
        }
        set
        {
            this.pinVerifiedField = value;
        }
    }

    /// <remarks/>
    public string DeviceVerified
    {
        get
        {
            return this.deviceVerifiedField;
        }
        set
        {
            this.deviceVerifiedField = value;
        }
    }

    /// <remarks/>
    public string SignatureRequired
    {
        get
        {
            return this.signatureRequiredField;
        }
        set
        {
            this.signatureRequiredField = value;
        }
    }

    /// <remarks/>
    public string CashBackAmount
    {
        get
        {
            return this.cashBackAmountField;
        }
        set
        {
            this.cashBackAmountField = value;
        }
    }

    /// <remarks/>
    public string Decision
    {
        get
        {
            return this.decisionField;
        }
        set
        {
            this.decisionField = value;
        }
    }

    /// <remarks/>
    public string ErrorCode
    {
        get
        {
            return this.errorCodeField;
        }
        set
        {
            this.errorCodeField = value;
        }
    }

    /// <remarks/>
    public string Message
    {
        get
        {
            return this.messageField;
        }
        set
        {
            this.messageField = value;
        }
    }

    public string EmvAID
    {
        get
        {
            return this.emvAIDField;
        }
        set
        {
            this.emvAIDField = value;
        }
    }

    public string EmvIAD
    {
        get
        {
            return this.emvIADField;
        }
        set
        {
            this.emvIADField = value;
        }
    }

    public string EmvTVR
    {
        get
        {
            return this.emvTVRField;
        }
        set
        {
            this.emvTVRField = value;
        }
    }

    public string EmvTSI
    {
        get
        {
            return this.emvTSIField;
        }
        set
        {
            this.emvTSIField = value;
        }
    }

    public string EmvARC
    {
        get
        {
            return this.emvARCField;
        }
        set
        {
            this.emvARCField = value;
        }
    }
}
