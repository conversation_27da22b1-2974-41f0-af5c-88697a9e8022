﻿/********************************************************************************************
* Project Name - Web Payments
 * Description - Web Payment Helper classes
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Semnox.Core.GenericUtilities;
using Semnox.Core.Utilities;
using Semnox.Parafait.Customer;
using Semnox.Parafait.Languages;
using Semnox.Parafait.PaymentGateway;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.POS;
using Semnox.Parafait.Site;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.User;
using Semnox.Parafait.ViewContainer;

namespace Semnox.Parafait.WebPayments
{
    public class WebPaymentGatewayHelper
    {
        private readonly Semnox.Parafait.logging.Logger log;
        private ExecutionContext executionContext;

        internal WebPaymentGatewayHelper(ExecutionContext executionContext)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            this.executionContext = executionContext;
        }

        internal async Task<HostedPaymentRequestDTO> CreateHostedPaymentRequestDTO(TransactionDTO transactionDTO, string paymentSystemReference)
        {
            log.LogMethodEntry(transactionDTO, paymentSystemReference);

            TransactionPaymentDTO transactionPaymentDTOApplied =
                        transactionDTO.TransactionPaymentDTOList.FirstOrDefault(x => x.SystemReference == paymentSystemReference);
            if (transactionPaymentDTOApplied == null)
            {
                log.Error($"Transaction payment not found for SystemReference: {paymentSystemReference}. TransactionId: {transactionDTO.TransactionId}");
                throw new WebPaymentException(MessageViewContainerList.GetMessage(executionContext, "Payment Not Applied"));
            }
            log.Debug("Transaction payment " + transactionPaymentDTOApplied.PaymentId);

            PaymentModeContainerDTO selectedPaymentModesContainerDTO =
                            PaymentModeContainerList.GetPaymentModeContainerDTO(executionContext.GetSiteId(), transactionPaymentDTOApplied.PaymentModeId);

            CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO = new CreateHostedPaymentRequestDTO();
            createHostedPaymentRequestDTO.TransactionId = transactionDTO.TransactionId;
            createHostedPaymentRequestDTO.TransactionGuid = transactionDTO.Guid;
            createHostedPaymentRequestDTO.TransactionPaymentId = transactionPaymentDTOApplied.PaymentId;
            createHostedPaymentRequestDTO.TransactionPaymentGuid = transactionPaymentDTOApplied.Guid;
            createHostedPaymentRequestDTO.SiteId = transactionDTO.SiteId;
            createHostedPaymentRequestDTO.POSMachine = transactionDTO.POSMachine;
            createHostedPaymentRequestDTO.PaymentModeId = selectedPaymentModesContainerDTO.PaymentModeId;
            createHostedPaymentRequestDTO.PaymentMode = selectedPaymentModesContainerDTO.PaymentMode;
            createHostedPaymentRequestDTO.CurrencyCode = !String.IsNullOrWhiteSpace(transactionPaymentDTOApplied.CurrencyCode) ?
                transactionPaymentDTOApplied.CurrencyCode :
                ParafaitDefaultContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE");
            createHostedPaymentRequestDTO.Amount = transactionPaymentDTOApplied.Amount;
            createHostedPaymentRequestDTO.SubscriptionAuthorizationMode = 
                Enum.GetName(typeof(SubscriptionAuthorizationMode), transactionPaymentDTOApplied.SubscriptionAuthorizationMode);

            List<PaymentGatewayProduct> pgProducts = new List<PaymentGatewayProduct>();
            foreach (TransactionLineDTO transactionLineDTO in transactionDTO.TransactionLineDTOList)
            {
                log.Debug("Building product details: " + transactionLineDTO.ProductName);
                PaymentGatewayProduct pgProduct = new PaymentGatewayProduct();
                pgProduct.ProductName = transactionLineDTO.ProductName;
                pgProduct.ProductAmount = transactionLineDTO.NetAmount;
                pgProduct.ProductQuantity = (int)transactionLineDTO.Quantity;
                pgProducts.Add(pgProduct);
            }
            createHostedPaymentRequestDTO.Products = pgProducts;

            PaymentGatewayCustomer paymentGatewayCustomer = null;
            if (transactionDTO.CustomerId > -1)
            {
                log.Debug("Building customer details for CustomerId: " + transactionDTO.CustomerId);
                CustomerBL customerBL = new CustomerBL(executionContext, transactionDTO.CustomerId, true);
                CustomerDTO customerDTO = customerBL.CustomerDTO;
                paymentGatewayCustomer = new PaymentGatewayCustomer();
                paymentGatewayCustomer.CustomerIdentifier = customerDTO.ProfileId.ToString();
                paymentGatewayCustomer.CustomerName =
                    (!String.IsNullOrWhiteSpace(customerDTO.FirstName) ? customerDTO.FirstName + " " : "") +
                    (!String.IsNullOrWhiteSpace(customerDTO.MiddleName) ? customerDTO.MiddleName + " " : "") +
                    (!String.IsNullOrWhiteSpace(customerDTO.LastName) ? customerDTO.LastName + " " : "");
                paymentGatewayCustomer.CustomerEmail = customerDTO.Email;
                paymentGatewayCustomer.CustomerPhone = customerDTO.PhoneNumber;
                if (customerDTO.LatestAddressDTO != null)
                {
                    log.Debug("Adding customer address details.");
                    paymentGatewayCustomer.AddressLine1 = customerDTO.LatestAddressDTO.Line1;
                    paymentGatewayCustomer.AddressLine2 = customerDTO.LatestAddressDTO.Line2;
                    paymentGatewayCustomer.AddressLine3 = customerDTO.LatestAddressDTO.Line3;
                    paymentGatewayCustomer.City = customerDTO.LatestAddressDTO.City;
                    paymentGatewayCustomer.State = customerDTO.LatestAddressDTO.StateCode;
                    paymentGatewayCustomer.Country = customerDTO.LatestAddressDTO.CountryName;
                    paymentGatewayCustomer.PinCode = customerDTO.LatestAddressDTO.PostalCode;
                }
            }

            createHostedPaymentRequestDTO.PaymentGatewayCustomer = paymentGatewayCustomer;
            log.Debug("Initializing PaymentGatewayFactory and creating web payment gateway instance.");
            System.Threading.CancellationTokenSource cancellationTokenSource = new System.Threading.CancellationTokenSource();
            PaymentGatewayFactory.GetInstance().Initialize(true, cancellationTokenSource.Token);
            Semnox.Parafait.PaymentGateway.ParafaitWebPaymentGateway webPaymentGateway =
                (ParafaitWebPaymentGateway)PaymentGatewayFactory.GetInstance().GetPaymentGateway(selectedPaymentModesContainerDTO, executionContext);

            PaymentRequestDTO paymentRequestDTO = MapPaymentRequestDTO(createHostedPaymentRequestDTO);

            using (UnitOfWork unitOfWork = new UnitOfWork())
            {
                log.Debug("Creating payment session DTO using webPaymentGateway.");
                PaymentSessionDTO paymentSessionDTO = await webPaymentGateway.CreatePaymentSessionDTO(
                    paymentRequestDTO,
                    transactionPaymentDTOApplied,
                    unitOfWork
                );

                HostedPaymentRequestDTO hostedPaymentRequestDTO = MapHostedPaymentRequestDTO(paymentSessionDTO, createHostedPaymentRequestDTO);
                log.LogMethodExit(hostedPaymentRequestDTO);
                return hostedPaymentRequestDTO;
            }
        }

        internal PaymentRequestDTO MapPaymentRequestDTO(CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO)
        {
            log.Debug($"Mapping CreateHostedPaymentRequestDTO to PaymentRequestDTO ${createHostedPaymentRequestDTO}");
            return new PaymentRequestDTO(
                amount: createHostedPaymentRequestDTO.Amount,
                requestIdentifier: createHostedPaymentRequestDTO.TransactionPaymentGuid,
                intRequestIdentifier: createHostedPaymentRequestDTO.TransactionPaymentId,
                paymentResponseHistory: new List<PaymentResponseDTO>(),
                isManual: false,
                requestDate: SiteContainerList.CurrentDateTime(this.executionContext),
                requestUTCDate: ServerDateTime.Now,
                subscriptionAuthorizationMode: createHostedPaymentRequestDTO.SubscriptionAuthorizationMode,
                transactionNetAmount: -1
            );
        }

        internal HostedPaymentRequestDTO MapHostedPaymentRequestDTO(PaymentSessionDTO paymentSessionDTO, CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO)
        {
            log.Debug("Building HostedPaymentRequestDTO from PaymentSessionDTO and CreateHostedPaymentRequestDTO");
            HostedPaymentRequestDTO hostedPaymentRequestDTO = new HostedPaymentRequestDTO();
            hostedPaymentRequestDTO.TransactionId = createHostedPaymentRequestDTO.TransactionId;
            hostedPaymentRequestDTO.TransactionPaymentId = createHostedPaymentRequestDTO.TransactionPaymentId;
            hostedPaymentRequestDTO.TransactionPaymentGuid = createHostedPaymentRequestDTO.TransactionPaymentGuid;
            hostedPaymentRequestDTO.TransactionGuid = createHostedPaymentRequestDTO.TransactionGuid;
            hostedPaymentRequestDTO.SiteId = createHostedPaymentRequestDTO.SiteId;
            hostedPaymentRequestDTO.POSMachine = createHostedPaymentRequestDTO.POSMachine;
            hostedPaymentRequestDTO.PaymentModeId = createHostedPaymentRequestDTO.PaymentModeId;
            hostedPaymentRequestDTO.PaymentMode = createHostedPaymentRequestDTO.PaymentMode;
            hostedPaymentRequestDTO.CurrencyCode = createHostedPaymentRequestDTO.CurrencyCode;
            hostedPaymentRequestDTO.Amount = createHostedPaymentRequestDTO.Amount;
            hostedPaymentRequestDTO.Products = createHostedPaymentRequestDTO.Products;
            hostedPaymentRequestDTO.PaymentGatewayCustomer = createHostedPaymentRequestDTO.PaymentGatewayCustomer;

            LookupsContainerDTO lookupValuesList = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            List<LookupValuesContainerDTO> lookupValuesDTOlist = lookupValuesList.LookupValuesContainerDTOList;

            string paymentPageUrl = "";
            String apiSite = "";
            string webSite = "";
            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").Count() > 0)
            {
                apiSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").First().Description;
            }
            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").Count() > 0)
            {
                webSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").First().Description;
            }
            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").Count() > 0)
            {
                String linkPage = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").First().Description;
                linkPage = linkPage.Replace("@gateway", paymentSessionDTO.PaymentGatewayName);
                paymentPageUrl = webSite + linkPage;
            }
            else
            {
                paymentPageUrl = webSite + $"/payment/paymentGatewayLanding?PaymentGatewayName={paymentSessionDTO.PaymentGatewayName}&payload=@payLoad&siteId=@siteId&posMachine=@posMachine";
            }


            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Count() > 0)
            {
                hostedPaymentRequestDTO.SuccessURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentSessionDTO.PaymentGatewayName);
                log.Debug("SUCCESS_RESPONSE_API_URL " + hostedPaymentRequestDTO.SuccessURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").Count() > 0)
            {
                hostedPaymentRequestDTO.FailedURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentSessionDTO.PaymentGatewayName);
                log.Debug("FAILURE_RESPONSE_API_URL " + hostedPaymentRequestDTO.FailedURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").Count() > 0)
            {
                hostedPaymentRequestDTO.CancelURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentSessionDTO.PaymentGatewayName);
                log.Debug("CANCEL_RESPONSE_API_URL " + hostedPaymentRequestDTO.CancelURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").Count() > 0)
            {
                hostedPaymentRequestDTO.CallbackURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentSessionDTO.PaymentGatewayName);
                log.Debug("CALLBACK_RESPONSE_API_URL " + hostedPaymentRequestDTO.CallbackURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").Count() == 1)
            {
                hostedPaymentRequestDTO.PaymentSucceededURL = lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").First().Description;
                log.Debug("SUCCESS_REDIRECT_URL " + hostedPaymentRequestDTO.PaymentSucceededURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").Count() == 1)
            {
                hostedPaymentRequestDTO.PaymentFailedURL = lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").First().Description;
                log.Debug("FAILURE_REDIRECT_URL " + hostedPaymentRequestDTO.PaymentFailedURL);
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").Count() == 1)
            {
                hostedPaymentRequestDTO.PaymentCancelledURL = lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").First().Description;
                log.Debug("FAILURE_REDIRECT_URL " + hostedPaymentRequestDTO.PaymentCancelledURL);
            }

            if (!string.IsNullOrWhiteSpace(paymentSessionDTO.MountedPaymentSessionJSON))
            {
                try
                {
                    log.Debug("Removing 3DS data from Session Json...");
                    log.Debug("MountedPaymentSessionJSON: " + paymentSessionDTO.MountedPaymentSessionJSON);
                    var postParams = JsonConvert.DeserializeObject<Dictionary<string, string>>(paymentSessionDTO.MountedPaymentSessionJSON);
                    var keysToRemove = new List<string>
                    {
                        "isPaayEnabled",
                        "threedsPaayUrl",
                        "threedsPaayApiKey",
                        "tokenizePublicKey",
                        "dataPublicKey",
                        "showPAAYChallenge"
                    };

                    // Remove if key exists
                    foreach (var key in keysToRemove)
                    {
                        if (postParams.ContainsKey(key))
                        {
                            postParams.Remove(key);
                            log.Debug($"Removed {key} from payment session JSON");
                        }
                    }

                    // Serialize cleaned dictionary back to JSON string
                    paymentSessionDTO.MountedPaymentSessionJSON = JsonConvert.SerializeObject(postParams);
                }
                catch (Exception ex)
                {
                    log.Error("Failed to deserialize or remove PAAY fields from payment session JSON", ex);
                }
            }

            string payload = string.IsNullOrWhiteSpace(paymentSessionDTO.HostedPaymentSessionForm) ? paymentSessionDTO.MountedPaymentSessionJSON : paymentSessionDTO.HostedPaymentSessionForm;

            String base64String = Base64UrlEncoder.Encode(Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(payload)));
            
            hostedPaymentRequestDTO.WebsitePaymentPageLink = paymentPageUrl
                                                                .Replace("@payLoad", base64String)
                                                                .Replace("@siteId", hostedPaymentRequestDTO.SiteId.ToString())
                                                                .Replace("@posMachine", hostedPaymentRequestDTO.POSMachine.ToString());
            return hostedPaymentRequestDTO;
        }

        internal PaymentModeContainerDTO GetPaymentMode(String paymentGateway, String gatewayResponseString)
        {
            log.LogMethodEntry(paymentGateway, gatewayResponseString);
            PaymentModeContainerDTO paymentModeContainerDTO = null;

            string paymentGatewayName = string.Empty;
            int siteId = SiteContainerList.GetMasterSiteId();
            ExecutionContext initialExecutionContext = executionContext;

            if (paymentGateway.Contains("-"))
            {
                string[] tempArray = paymentGateway.Split(new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries);
                if (tempArray != null && tempArray.Length == 2)
                {
                    log.Debug("Site is part of the response: " + paymentGateway);
                    int.TryParse(tempArray[tempArray.Length - 1], out siteId);
                    paymentGatewayName = tempArray[0];
                }
            }
            else
            {
                log.Debug("Site id not found is return param, going with master");
                paymentGatewayName = paymentGateway;
            }

            log.Debug("SiteId: " + siteId + ", PaymentGateway: " + paymentGateway);

            if (siteId != initialExecutionContext.SiteId)
            {
                initialExecutionContext = new ExecutionContext(initialExecutionContext.UserId, siteId, -1, -1, initialExecutionContext.IsCorporate, -1, -1);
            }

            LookupsContainerDTO paymentGatewayLookup = LookupsContainerList.GetLookupsContainerDTO(siteId, "PAYMENT_GATEWAY");
            if (paymentGatewayLookup != null)
            {
                log.Debug("Got PAYMENT_GATEWAY lookup " + paymentGatewayLookup.LookupId);
                LookupValuesContainerDTO valueDTO = paymentGatewayLookup.LookupValuesContainerDTOList.FirstOrDefault(
                                                        x => x.LookupValue.Equals(paymentGatewayName, StringComparison.InvariantCultureIgnoreCase));
                if (valueDTO != null)
                {
                    log.Debug("Got Payment gateway lookupvalue " + valueDTO.LookupValueId);
                    PaymentModeContainerDTOCollection paymentModes = PaymentModeContainerList.GetPaymentModeContainerDTOCollection(siteId);
                    paymentModeContainerDTO = paymentModes.PaymentModeContainerDTOList.FirstOrDefault(x => x.Gateway == valueDTO.LookupValueId);
                }
                else
                {
                    log.Debug($"No lookup value found with the payment gateway name: {paymentGatewayName}");
                }
            }
            else
            {
                log.Debug($"lookup PAYMENT_GATEWAY not found for site ID {siteId}.");
            }

            log.LogMethodExit(paymentModeContainerDTO);
            return paymentModeContainerDTO;
        }

        internal Dictionary<string, string> GetPaymentLookupsAndString()
        {
            log.LogMethodEntry();
            Dictionary<string, string> paymentLookupsAndStrings = new Dictionary<string, string>();

            String failureMessage = "Your payment for order (reference @TrxId) could not be processed. Reason: @ReturnCode @Reason";
            String successMessage = "Your payment for order (reference @TrxId) was processed successfully. <br>Card Type: @CardType <br>Card Number: @CardNumber <br>Transaction Time: @PaymentTime";
            String pendingMessage = "Your payment for order (reference @TrxId) is currently in pending status. You will receive a confirmation message after the payment is processed.";
            String orderFailureMessage = "There was an issue processing your order (reference @TrxId). A refund has been issued.";
            String websiteURL = "";
            String defaultRedirectURL = "";
            String successRedirectURL = "";
            String failureRedirectURL = "";
            String cancelRedirectURL = "";

            // Get the default messages and redirection URL
            String temp = MessageContainerList.GetMessage(executionContext, 5112);
            if (!string.IsNullOrWhiteSpace(temp))
            {
                successMessage = temp;
                //log.Debug("Success Message " + successMessage);
            }
            temp = MessageContainerList.GetMessage(executionContext, 5113);
            if (!string.IsNullOrWhiteSpace(temp))
            {
                failureMessage = temp;
                //log.Debug("Failure Message " + failureMessage);
            }
            temp = MessageContainerList.GetMessage(executionContext, 5785);
            if (!string.IsNullOrWhiteSpace(temp))
            {
                pendingMessage = temp;
                //log.Debug("Pending Message " + pendingMessage);
            }

            LookupsContainerDTO LookupsViewContainerDTO = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            if (LookupsViewContainerDTO != null && LookupsViewContainerDTO.LookupValuesContainerDTOList != null)
            {
                if (LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").Count() == 1)
                {
                    websiteURL = LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").First().Description;
                    log.Debug("websiteURL " + websiteURL);
                }

                if (LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "DEFAULT_REDIRECT_URL").Count() == 1)
                {
                    defaultRedirectURL = websiteURL + LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "DEFAULT_REDIRECT_URL").First().Description;
                    log.Debug("defaultRedirectURL " + defaultRedirectURL);
                }

                if (LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").Count() == 1)
                {
                    successRedirectURL = websiteURL + LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").First().Description;
                    log.Debug("SUCCESS_REDIRECT_URL " + successRedirectURL);
                }

                if (LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").Count() == 1)
                {
                    failureRedirectURL = websiteURL + LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").First().Description;
                    log.Debug("FAILURE_REDIRECT_URL " + failureRedirectURL);
                }

                if (LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").Count() == 1)
                {
                    cancelRedirectURL = websiteURL + LookupsViewContainerDTO.LookupValuesContainerDTOList.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").First().Description;
                    log.Debug("CANCEL_REDIRECT_URL " + cancelRedirectURL);
                }
            }

            paymentLookupsAndStrings.Add("WEBSITE_URL", websiteURL);
            paymentLookupsAndStrings.Add("FAILURE_MESSAGE", failureMessage);
            paymentLookupsAndStrings.Add("PENDING_MESSAGE", pendingMessage);
            paymentLookupsAndStrings.Add("SUCCESS_MESSAGE", successMessage);
            paymentLookupsAndStrings.Add("ORDER_FAILURE_MESSAGE", orderFailureMessage);
            paymentLookupsAndStrings.Add("DEFAULT_REDIRECT_URL", defaultRedirectURL);
            paymentLookupsAndStrings.Add("SUCCESS_REDIRECT_URL", successRedirectURL);
            paymentLookupsAndStrings.Add("FAILURE_REDIRECT_URL", failureRedirectURL);
            paymentLookupsAndStrings.Add("CANCEL_REDIRECT_URL", cancelRedirectURL);

            log.LogMethodExit(paymentLookupsAndStrings);
            return paymentLookupsAndStrings;
        }

        internal string BuildMessage(String template, int TransactionId, PaymentTransactionDTO PaymentTransactionDTO)
        {
            log.LogMethodEntry();
            String message = template;
            message = message.Replace("@TrxId", TransactionId.ToString());
            message = message.Replace("@TransactionId", TransactionId.ToString());
            message = message.Replace("@TrxNo", TransactionId.ToString());

            if (PaymentTransactionDTO != null)
            {
                log.Debug("Building message from CCTransaction");
                if (!String.IsNullOrEmpty(PaymentTransactionDTO.TextResponse))
                {
                    message = message.Replace("@Reason", PaymentTransactionDTO.TextResponse);
                }

                if (message.Contains("@PaymentTime"))
                {
                    DateTime paymentDate = PaymentTransactionDTO.TransactionDatetime;
                    //paymentDate = SiteContainerList.FromSiteDateTime(PaymentTransactionDTO.SiteId, paymentDate);
                    String dateTimeFormat = "dd-MMM-yyyy h:mm tt";
                    String tempFormat = ParafaitDefaultContainerList.GetParafaitDefault(ExecutionContext.GetExecutionContext(), "DATETIME_FORMAT");
                    if (!String.IsNullOrEmpty(tempFormat))
                    {
                        dateTimeFormat = tempFormat;
                    }
                    message = message.Replace("@PaymentTime", paymentDate.ToString(dateTimeFormat));
                }

                message = message.Replace("@CardType", PaymentTransactionDTO.CardType);
                message = message.Replace("@CardNumber", PaymentTransactionDTO.AcctNo);
                message = message.Replace("@ReturnCode", PaymentTransactionDTO.DSIXReturnCode);
                message = message.Replace("@Reason", PaymentTransactionDTO.TextResponse);
            }
            else
            {
                message = message.Replace("@PaymentTime", "");
                message = message.Replace("@CardType", "");
                message = message.Replace("@CardNumber", "");
                message = message.Replace("@ReturnCode", "");
                message = message.Replace("@Reason", "");
            }
            log.LogMethodExit(message);
            return message;
        }

        internal ExecutionContext BuildPaymentExecutionContext(int siteId, String CreatedBy, String PosMachine)
        {
            log.LogMethodEntry();
            ExecutionContext transactionPaymentContext = null;

            log.Debug("Rebuilding execution context. Getting user, pos for site " + siteId + ":" + CreatedBy + ":" + PosMachine);
            UserContainerDTO user = UserContainerList.GetUserContainerDTO(siteId, CreatedBy); //Trx_header created by has id
            bool gotUser = user != null ? true : false;
            POSMachineContainerDTO posMachine = POSMachineContainerList.GetPOSMachineContainerDTOOrDefault(siteId, PosMachine, string.Empty, -1);
            bool gotPOS = posMachine != null ? true : false;
            log.Debug("gotUser " + gotUser + " gotPOS " + gotPOS);
            SiteContainerDTO siteContainerDTO = null;
            if(SiteContainerList.IsCorporate())
            {
                siteContainerDTO = SiteContainerList.GetSiteContainerDTOCollection()
                                                            .SiteContainerDTOList.Where(x => x.SiteId == siteId).FirstOrDefault();
            }
            else
            {
                siteContainerDTO = SiteContainerList.GetSiteContainerDTOCollection()
                                                            .SiteContainerDTOList.FirstOrDefault();
            }
            if (siteContainerDTO == null)
            {
                log.Error($"siteContainerDTO not found for siteId: {siteId}. Cannot build execution context.");
                throw new SiteNotFoundException(MessageViewContainerList.GetMessage(executionContext.SiteId, executionContext.LanguageId, 5982));
            }

            int languageId = -1;
            if (!string.IsNullOrWhiteSpace(executionContext.LanguageCode))
            {
                LanguageContainerDTO languageContainerDTO = LanguageContainerList.GetLanguageContainerDTOCollection(siteId)
                                                                            .LanguageContainerDTOList.Where(x => x.LanguageCode == executionContext.LanguageCode).FirstOrDefault();
                if (languageContainerDTO != null)
                {
                    languageId = languageContainerDTO.LanguageId;
                }
            }
            log.Debug("Building the transaction payment context");
            transactionPaymentContext = new ExecutionContext(user.LoginId, SiteContainerList.IsCorporate()? siteContainerDTO.SiteId : -1, siteContainerDTO.SiteId, posMachine.POSMachineId,
                                                                       user.UserId, user.RoleId, executionContext.IsCorporate, languageId, executionContext.WebApiToken,
                                                                       posMachine.Guid, posMachine.POSName, executionContext.LanguageCode);

            log.LogMethodExit(transactionPaymentContext);
            return transactionPaymentContext;
        }

        /// <summary>
        /// Processes payment response and handles additional response for complex flows
        /// </summary>
        /// <param name="paymentGatewayResponseDTO">Payment gateway response</param>
        /// <returns>HostedPaymentResponseDTO with additional response handling</returns>
        internal async Task<HostedPaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO)
        {
            log.LogMethodEntry(paymentGatewayResponseDTO);

            HostedPaymentResponseDTO hostedPaymentResponseDTO = new HostedPaymentResponseDTO();

            try
            {
                // Get payment gateway instance
                PaymentModeContainerDTO paymentModeContainerDTO = PaymentModeContainerList.GetPaymentModeContainerDTO(
                    executionContext.GetSiteId(), paymentGatewayResponseDTO.PaymentModeId);

                IPaymentGateway paymentGateway = PaymentGatewayFactory.GetPaymentGateway(
                    executionContext, paymentModeContainerDTO);

                // Check if this gateway supports additional response handling
                if (paymentGateway.IsPaymentHistoryListRequired)
                {
                    log.Debug("Payment gateway requires payment history list for processing");

                    // Get payment identifier first
                    string paymentIdentifier = paymentGateway.GetPaymentIdentifier(paymentGatewayResponseDTO.GatewayResponse);

                    if (!string.IsNullOrWhiteSpace(paymentIdentifier))
                    {
                        // Fetch payment transaction DTO and convert to PaymentResponseDTO list
                        List<PaymentResponseDTO> paymentHistoryList = await GetPaymentHistoryList(paymentIdentifier);

                        // Process payment response using simplified architecture
                        log.Info("Processing payment response using simplified architecture");
                        PaymentResponseDTO paymentResponseDTO = await paymentGateway.ProcessPaymentResponse(paymentGatewayResponseDTO);
                        log.Debug($"Payment response processed - Status: {paymentResponseDTO.Status}, Amount: {paymentResponseDTO.Amount}");

                        // Check if additional response data is present (for 3DS authentication, etc.)
                        // Note: Using ProcessData field since PaymentTransactionDTO doesn't have AdditionalData property
                        if (!string.IsNullOrWhiteSpace(paymentResponseDTO.ProcessData))
                        {
                            log.Debug("Additional response data found in ProcessData, attempting to parse");
                            try
                            {
                                // Parse additional response data from JSON stored in ProcessData field
                                var additionalResponseDTO = JsonConvert.DeserializeObject<AdditionalResponseDTO>(paymentResponseDTO.ProcessData);

                                if (additionalResponseDTO != null)
                                {
                                    log.Debug("Payment requires additional actions (PRE_AUTH)");
                                    log.Info($"Additional action required - Type: {additionalResponseDTO.Type}, URL: {additionalResponseDTO.Url}");
                                    hostedPaymentResponseDTO.AdditionalResponseDTO = additionalResponseDTO;
                                    hostedPaymentResponseDTO.RedirectResponseToWebsite = false; // Don't redirect, return additional response
                                }
                                else
                                {
                                    log.Debug("Payment completed without additional actions");
                                    hostedPaymentResponseDTO.RedirectURL = GetSuccessRedirectUrl(paymentResponseDTO);
                                    hostedPaymentResponseDTO.RedirectResponseToWebsite = true;
                                    log.Info($"Standard payment flow - Redirecting to: {hostedPaymentResponseDTO.RedirectURL}");
                                }
                            }
                            catch (Exception ex)
                            {
                                log.Error("Error parsing additional response data", ex);
                                // Fallback to standard redirect response
                                hostedPaymentResponseDTO.RedirectURL = GetSuccessRedirectUrl(paymentResponseDTO);
                                hostedPaymentResponseDTO.RedirectResponseToWebsite = true;
                                log.Warning($"Fallback to standard redirect due to parsing error: {hostedPaymentResponseDTO.RedirectURL}");
                            }
                        }
                        else
                        {
                            log.Debug("Payment completed without additional actions");
                            hostedPaymentResponseDTO.RedirectURL = GetSuccessRedirectUrl(paymentResponseDTO);
                            hostedPaymentResponseDTO.RedirectResponseToWebsite = true;
                            log.Info($"Standard payment flow - Redirecting to: {hostedPaymentResponseDTO.RedirectURL}");
                        }

                        // Save the payment response
                        log.Debug("Saving payment response to database");
                        await SavePaymentResponse(paymentResponseDTO);
                        log.Debug("Payment response saved successfully");
                    }
                }
                else
                {
                    // Standard payment processing without additional response handling
                    PaymentResponseDTO paymentResponseDTO = await paymentGateway.ProcessPaymentResponse(paymentGatewayResponseDTO);

                    hostedPaymentResponseDTO.RedirectURL = GetSuccessRedirectUrl(paymentResponseDTO);
                    hostedPaymentResponseDTO.RedirectResponseToWebsite = true;

                    // Save the payment response
                    await SavePaymentResponse(paymentResponseDTO);
                }
            }
            catch (Exception ex)
            {
                log.Error("Error processing payment response", ex);
                hostedPaymentResponseDTO.RedirectURL = GetErrorRedirectUrl();
                hostedPaymentResponseDTO.RedirectResponseToWebsite = true;
                throw;
            }

            log.LogMethodExit(hostedPaymentResponseDTO);
            return hostedPaymentResponseDTO;
        }

        private async Task<List<PaymentResponseDTO>> GetPaymentHistoryList(string paymentIdentifier)
        {
            log.LogMethodEntry(paymentIdentifier);

            // This would typically fetch from database or payment gateway
            // For now, return empty list as placeholder
            List<PaymentResponseDTO> paymentHistoryList = new List<PaymentResponseDTO>();

            log.LogMethodExit(paymentHistoryList);
            return await Task.FromResult(paymentHistoryList);
        }

        private async Task SavePaymentResponse(PaymentResponseDTO paymentResponseDTO)
        {
            log.LogMethodEntry(paymentResponseDTO);

            // Implementation to save payment response to database
            // This would typically update the transaction payment status

            log.LogMethodExit();
            await Task.CompletedTask;
        }

        private string GetSuccessRedirectUrl(PaymentResponseDTO paymentResponseDTO)
        {
            // Implementation to build success redirect URL
            return "/payment/success";
        }

        private string GetErrorRedirectUrl()
        {
            // Implementation to build error redirect URL
            return "/payment/error";
        }
    }
}
