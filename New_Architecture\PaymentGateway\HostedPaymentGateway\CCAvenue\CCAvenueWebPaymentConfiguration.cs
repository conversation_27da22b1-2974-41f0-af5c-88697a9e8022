﻿using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGateway
{
    class CCAvenueWebPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public CCAvenueWebPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            SetConfiguration("CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID"));
            SetConfiguration("CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE"));
            SetConfiguration("CCAVENUE_HOSTED_PAYMENT_WORKING_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CCAVENUE_HOSTED_PAYMENT_WORKING_KEY"));
            SetConfiguration("CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL"));
            SetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_API_URL"));

            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
            SetConfiguration("SITE_ID", Convert.ToString(executionContext.GetSiteId()));

            LookupsContainerDTO lookupsContainerDTO = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            List<LookupValuesContainerDTO> lookupValuesContainerDTOList = lookupsContainerDTO?.LookupValuesContainerDTOList ?? new List<LookupValuesContainerDTO>();

            string API_URL = string.Empty;

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "ANGULAR_PAYMENT_API"))
            {
                API_URL = lookupValuesContainerDTOList.First(x => x.LookupValue == "ANGULAR_PAYMENT_API").Description;
                SetConfiguration("ANGULAR_PAYMENT_API", API_URL);
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL"))
            {
                SetConfiguration("SUCCESS_RESPONSE_API_URL", API_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Description?.Replace("@gateway", $"{PaymentGateways.CCAvenueCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }
            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "FAILURE_RESPONSE_API_URL"))
            {
                SetConfiguration("FAILURE_RESPONSE_API_URL", API_URL + lookupValuesContainerDTOList.First(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").Description?.Replace("@gateway", $"{PaymentGateways.CCAvenueCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }
        }
    }
}
