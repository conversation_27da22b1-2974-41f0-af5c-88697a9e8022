/********************************************************************************************
* Project Name - Web Payments
 * Description - Local Implementation of the use cases
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Semnox.Core.GenericUtilities;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGateway;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.ViewContainer;

namespace Semnox.Parafait.WebPayments
{
    public class LocalWebPaymentsUseCases : LocalUseCases, IWebPaymentsUseCases
    {
        private Semnox.Parafait.logging.Logger log;

        /// <summary>
        /// LocalWebPaymentsUseCases
        /// </summary>
        /// <param name="executionContext"></param>
        public LocalWebPaymentsUseCases(ExecutionContext executionContext, string requestGuid) : base(executionContext, requestGuid)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, requestGuid);
            log.LogMethodExit();
        }

        public async Task<WebPaymentDTO> StartWebPayment(int transactionId, TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry(transactionId, transactionPaymentDTO);
            WebPaymentDTO result = null;
            using (UnitOfWork unitOfWork = new UnitOfWork())
            {
                PaymentModeContainerDTO selectedPaymentModesContainerDTO =
                        PaymentModeContainerList.GetPaymentModeContainerDTO(executionContext.GetSiteId(), transactionPaymentDTO.PaymentModeId);

                log.Debug("Identified the payment gateway. PaymentModeId: " + selectedPaymentModesContainerDTO.PaymentModeId + ", PaymentMode:" + selectedPaymentModesContainerDTO.PaymentMode);
                if (selectedPaymentModesContainerDTO.PaymentReferenceMandatory && string.IsNullOrWhiteSpace(transactionPaymentDTO.Reference))
                {
                    log.Debug("Payment Reference is not sent");
                    throw new MissingPaymentInformationException(MessageViewContainerList.GetMessage(executionContext, 1767));
                }
                if (selectedPaymentModesContainerDTO.IsCash)
                {
                    log.Debug("Cash Payment modes are not supported in web");
                    throw new MissingPaymentInformationException(MessageViewContainerList.GetMessage(executionContext, "Payment Method not implemented"));
                }
                if (transactionPaymentDTO != null && string.IsNullOrWhiteSpace(transactionPaymentDTO.SystemReference))
                {
                    log.Debug("System Reference is not sent");
                    throw new MissingPaymentInformationException(MessageViewContainerList.GetMessage(executionContext, "System Reference cannot be empty"));
                }

                TransactionBL transactionBL = null;
                if (IsDuplicateRequest())
                {
                    try
                    {
                        log.Debug($"Duplicate request: {requestGuid}");
                        log.Debug($"Transaction Id: {transactionId}");
                        transactionBL = new TransactionBL(executionContext, transactionId, unitOfWork);
                    }
                    catch (Exception ex)
                    {
                        log.Error($"Failed to retrieve existing transaction {transactionId} for duplicate StarteWebPayment request {requestGuid}.");
                        throw new TransactionNotFoundException("Existing Transaction not found for duplicate StartWebPayment request", ex);
                    }
                }
                else
                {
                    log.Debug("Not a Duplicate request. RequestGuid: " + requestGuid);
                    unitOfWork.Begin();
                    transactionBL = new TransactionBL(executionContext, transactionId, unitOfWork);
                    log.Debug("Calling Apply Payments on transactionId: " + transactionId);
                    transactionBL.ApplyPayments(new List<TransactionPaymentDTO>() { transactionPaymentDTO });
                    log.Debug("Apply Payments Completed");
                    CreateApplicationRequestLog("Transaction", "InitiateWebPayment", transactionBL.TransactionDTO.Guid, unitOfWork.SQLTrx);
                    unitOfWork.Commit();
                    log.Debug("Finished calling apply payment.");
                }

                result = new WebPaymentDTO();
                result.TransactionDTO = transactionBL.TransactionDTO;
                if (selectedPaymentModesContainerDTO.IsCreditCard)
                {
                    log.Debug("Building web payment request");
                    WebPaymentGatewayHelper webPaymentGatewayHelper = new WebPaymentGatewayHelper(executionContext);
                    result.HostedPaymentRequestDTO =
                                await webPaymentGatewayHelper.CreateHostedPaymentRequestDTO(transactionBL.TransactionDTO, transactionPaymentDTO.SystemReference);
                    log.Debug("Completed calling CreateGatewayPaymentRequest");
                }
                else
                {
                    log.Debug("Not a hosted payment request");
                }
                log.LogMethodExit(result);
                return result;
            }
        }

        public async Task<WebPaymentDTO> CompleteWebPayment(int transactionId, CompleteWebPaymentDTO completeWebPaymentDTO)
        {
            return await Task<WebPaymentDTO>.Factory.StartNew(() =>
            {
                log.LogMethodEntry(transactionId, completeWebPaymentDTO);
                WebPaymentDTO result = new WebPaymentDTO();
                TransactionBL transactionBL = null;

                using (UnitOfWork unitOfWork = new UnitOfWork())
                {
                    if (IsDuplicateRequest())
                    {
                        log.Debug($"Duplicate request with GUID: {requestGuid} for TransactionId: {transactionId}");
                        transactionBL = new TransactionBL(executionContext, transactionId, unitOfWork);
                        result.TransactionDTO = transactionBL.TransactionDTO;
                        log.LogMethodExit(result);
                        return result;
                    }

                    log.Debug("This is not a duplicate request");
                    unitOfWork.Begin();
                    PaymentTransactionDTO paymentTransactionDTO = completeWebPaymentDTO.PaymentTransactionDTO;
                    transactionBL = new TransactionBL(executionContext, transactionId, unitOfWork);
                    //Get the current status from DB object
                    TransactionPaymentDTO transactionPaymentDTO =
                        transactionBL.TransactionDTO.TransactionPaymentDTOList.FirstOrDefault(x => x.Guid.Equals(paymentTransactionDTO.TransactionPaymentGuid, StringComparison.InvariantCultureIgnoreCase));
                    if (transactionPaymentDTO == null)
                    {
                        log.Debug("Payment DTO not found");
                        log.Debug($"TransactionPaymentGuid: {paymentTransactionDTO.TransactionPaymentGuid}");
                        throw new MissingPaymentInformationException(MessageViewContainerList.GetMessage(executionContext, "Payment DTO not found"));
                    }

                    PaymentStatuses paymentStatuses;
                    Enum.TryParse(transactionPaymentDTO.PaymentStatus, out paymentStatuses);
                    log.Debug("Payment Status " + transactionPaymentDTO.PaymentStatus);
                    switch (paymentStatuses)
                    {
                        case PaymentStatuses.AUTHORIZATION_INITIATED:
                            {
                                log.Debug("Calling Authorize Payment");
                                if (string.IsNullOrWhiteSpace(completeWebPaymentDTO.OTPId) || string.IsNullOrWhiteSpace(completeWebPaymentDTO.OTP))
                                {
                                    log.Debug("OTPID or OTP not found.");
                                    log.Debug($"CompleteWebPaymentDTO: {completeWebPaymentDTO}");
                                    throw new DebitPaymentMissingOTPException(MessageViewContainerList.GetMessage(executionContext, "Payment OTP not found"));
                                }

                                PaymentActionDTO paymentActionDTO = new PaymentActionDTO(new PaymentTransactionDTO(), null);
                                paymentActionDTO.OTP = completeWebPaymentDTO.OTP;
                                paymentActionDTO.OTPId = completeWebPaymentDTO.OTPId;
                                transactionBL.AuthorizePayment(transactionPaymentDTO.PaymentId, paymentActionDTO);
                            }
                            break;
                        case PaymentStatuses.SALE_INITIATED:
                            {
                                log.Debug("Calling Sale Payment");
                                //transactionBL.SalePayment(transactionPaymentDTO.PaymentId, paymentTransactionDTO);
                            }
                            break;
                    }
                    unitOfWork.Commit();
                    log.Debug("Finished payment processing");
                    result.TransactionDTO = transactionBL.TransactionDTO;
                }

                using (UnitOfWork unitOfWorkCloseOrder = new UnitOfWork())
                {
                    log.Debug("Calling Try Close Transaction");
                    unitOfWorkCloseOrder.Begin();
                    OrderBL orderBL = new OrderBL(executionContext, transactionBL.TransactionDTO.OrderId, unitOfWorkCloseOrder);
                    transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);
                    log.Debug($"TryCloseTransaction for transactionId: {transactionBL.TransactionDTO.TransactionId}");
                    if (transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any()
                                        && transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.DoImmediatePost)
                                        && transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.OptionItemCode == POS.POSPrinterOverrideOptionItemCode.SHOWERROR))
                    {
                        orderBL.TryCloseTransaction(transactionBL.TransactionDTO.TransactionId, null);
                        transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);
                        transactionBL.Fiscalize();
                    }
                    else
                    {
                        orderBL.TryCloseTransaction(transactionBL.TransactionDTO.TransactionId, null);
                    }
                    transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);
                    log.Debug("Assigning the transaction DTO");
                    result.TransactionDTO = transactionBL.TransactionDTO;
                    CreateApplicationRequestLog("Transaction", "CompleteWebPayment", transactionBL.TransactionDTO.Guid, unitOfWorkCloseOrder.SQLTrx);
                    unitOfWorkCloseOrder.Commit();
                }

                using (UnitOfWork unitOfWorkCloseOrder = new UnitOfWork())
                {
                    if (transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any()
                                && transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.DoImmediatePost)
                                && !transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.OptionItemCode == POS.POSPrinterOverrideOptionItemCode.SHOWERROR))
                    {
                        try
                        {
                            log.Debug("Calling Fiscalize Transaction");
                            unitOfWorkCloseOrder.Begin();
                            OrderBL orderBL = new OrderBL(executionContext, transactionBL.TransactionDTO.OrderId, unitOfWorkCloseOrder);
                            transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);
                            transactionBL.Fiscalize();
                            log.Debug("Assigning the transaction DTO");
                            result.TransactionDTO = transactionBL.TransactionDTO;
                            unitOfWorkCloseOrder.Commit();
                        }
                        catch (Exception ex)
                        {
                            log.Error("fiscalization failed.");
                            log.Error(ex);
                            // throw new NotYetFiscalizedException(MessageContainerList.GetMessage(executionContext, 5577)); //Fiscalization Failed.Please check the logs for Errors.
                        }
                    }
                }

                log.LogMethodExit(result);
                return result;
            });
        }

        public async Task<WebPaymentDTO> ReverseWebPayment(int transactionId, ReverseWebPaymentDTO reverseWebPaymentDTO)
        {
            log.LogMethodEntry(transactionId, reverseWebPaymentDTO);
            WebPaymentDTO result = new WebPaymentDTO();
            TransactionBL transactionBL = null;

            using (UnitOfWork unitOfWork = new UnitOfWork())
            {
                if (IsDuplicateRequest())
                {
                    log.Debug($"Duplicate request with GUID: {requestGuid} for TransactionId: {transactionId}");
                    transactionBL = new TransactionBL(executionContext, transactionId, unitOfWork);
                    result.TransactionDTO = transactionBL.TransactionDTO;
                    log.LogMethodExit(result);
                    return result;
                }

                log.Debug("This is not a duplicate request");
                unitOfWork.Begin();
                System.Threading.CancellationTokenSource cancellationTokenSource = new System.Threading.CancellationTokenSource();

                transactionBL = new TransactionBL(executionContext, transactionId, unitOfWork);
                log.Debug($"Get transaction: {transactionId}");
                ApprovalDTO approvalDTO = new ApprovalDTO(executionContext.GetUserPKId(), "");
                TransactionReverseDTO transactionReverseDTO = new TransactionReverseDTO(transactionId, reverseWebPaymentDTO.ReasonId, reverseWebPaymentDTO.ReversalRemark, true, true, null, approvalDTO, null);
                OrderBL orderBL = new OrderBL(executionContext, transactionBL.TransactionDTO.OrderId, unitOfWork);
                TransactionBL reverseTransactionBL = null;
                try
                {
                    reverseTransactionBL = orderBL.ReverseTransaction(transactionBL.TransactionDTO.TransactionId, transactionReverseDTO);
                    TransactionDTO reversedTransactionDTO = reverseTransactionBL.TransactionDTO;
                    log.Debug($"Created a reversed transaction: {reversedTransactionDTO.TransactionId}");

                    transactionBL = unitOfWork.GetCachedEntity<TransactionBL>(typeof(TransactionBL), transactionId);
                    TransactionDTO originalTransactionDTO = transactionBL.TransactionDTO;

                    if (reversedTransactionDTO.TransactionStatus == TransactionStatuses.REVERSE_INITIATED.ToString() && reversedTransactionDTO.TransactionPaymentDTOList.Exists(x => x.PaymentStatus == PaymentStatuses.REVERSE_INITIATED.ToString()))
                    {
                        log.Debug("Initiating payment reversals");
                        foreach (TransactionPaymentDTO transactionPaymentDTO in reversedTransactionDTO.TransactionPaymentDTOList.Where(x => x.PaymentStatus == PaymentStatuses.REVERSE_INITIATED.ToString()))
                        {

                            log.Debug("Initiate payment reversals for " + transactionPaymentDTO.PaymentId);

                            PaymentTransactionDTO response = null;
                            string message = MessageViewContainerList.GetMessage(executionContext, 4551);
                            PaymentModeContainerDTO selectedPaymentModesContainerDTO = PaymentModeViewContainerList.GetPaymentModeContainerDTO(executionContext, transactionPaymentDTO.PaymentModeId);
                            Semnox.Parafait.PaymentGateway.PaymentGateway paymentGateway = PaymentGatewayFactory.GetInstance().GetPaymentGateway(selectedPaymentModesContainerDTO, executionContext);
                            TransactionPaymentDTO parentTransactionPaymentDTO = originalTransactionDTO.TransactionPaymentDTOList.FirstOrDefault(x => x.PaymentId == transactionPaymentDTO.ParentPaymentId);
                            PaymentTransactionDTO originalPaymentTransactionDTO = parentTransactionPaymentDTO.PaymentTransactionDTOList.FirstOrDefault(x => x.Status == PaymentTransactionStatuses.SUCCESS.ToString());
                            response = await paymentGateway.RefundAmount(transactionPaymentDTO, originalPaymentTransactionDTO,null, null, cancellationTokenSource.Token);
                            reverseTransactionBL.ReversePayment(transactionPaymentDTO.PaymentId, response);
                        }
                        log.Debug("Completed payment reversals, initiating close of reversed transaction ");

                        if (reversedTransactionDTO.TransactionPaymentDTOList.Any(x => x.PaymentStatus == PaymentStatuses.REVERSE_INITIATED.ToString()) == false)
                        {
                            OrderBL reversedOrderBL = new OrderBL(executionContext, reversedTransactionDTO.OrderId, unitOfWork);
                            reversedOrderBL.CloseTransaction(reverseTransactionBL.TransactionDTO.TransactionId, approvalDTO);
                            reverseTransactionBL = reversedOrderBL.GetTransactionBL(reverseTransactionBL.TransactionDTO.TransactionId);
                            log.Debug("Completed close of reversed transaction ");
                        }
                        log.Debug($"Completed processing. TransactionStatus: {reversedTransactionDTO.TransactionStatus}");
                    }

                    CreateApplicationRequestLog("Transaction", "ReverseWebPayment", transactionBL.TransactionDTO.Guid, unitOfWork.SQLTrx);
                    unitOfWork.Commit();
                }
                catch(Exception ex)
                {
                    unitOfWork.RollBack();
                    log.Error("Got an error while reversing transaction " + ex.Message);
                    throw new WebPaymentException(MessageViewContainerList.GetMessage(executionContext, "Could not reversed payment and transaction."));
                }

                log.Debug($"Finished payment processing transaction: {transactionId}");
                result.TransactionDTO = reverseTransactionBL.TransactionDTO;
            }
            log.LogMethodExit(result);
            return result;
        }

        public async Task<HostedPaymentResponseDTO> ProcessGatewayResponse(string paymentGateway, string paymentGatewayResponse, string caller)
        {
            log.LogMethodEntry(paymentGateway, paymentGatewayResponse, caller);

            ParafaitWebPaymentGateway webPaymentGateway = null;
            PaymentTransactionDTO processedPaymentTransactionDTO = null;
            HostedPaymentResponseDTO hostedPaymentResponseDTO = new HostedPaymentResponseDTO();
            TransactionPaymentDTO transactionPaymentDTO = null;

            TransactionBL transactionBL = null;
            TransactionDTO transactionDTO = null;
            ExecutionContext transactionPaymentContext = null;
            string transactionPaymentIdentifier = "";
            Dictionary<string, string> paymentLookupsAndStrings = null;
            bool paymentReceived = false;
            PaymentTransactionDTO paymentTransactionDTOForRefund = null;
            bool checkForRefund = false;
            PaymentTransactionStatuses paymentTrxStatuses = PaymentTransactionStatuses.ERROR;
            string paymentMessage = "";

            WebPaymentGatewayHelper webPaymentGatewayHelper = new WebPaymentGatewayHelper(executionContext);
            PaymentModeContainerDTO paymentModeContainerDTO = webPaymentGatewayHelper.GetPaymentMode(paymentGateway, paymentGatewayResponse);
            if (paymentModeContainerDTO == null)
            {
                log.Error("Payment mode not found. Cannot proceed. Gateway: " + paymentGateway + ", Response: " + paymentGatewayResponse);
                // Create a default payment dto.
                //throw new PaymentModeNotFoundException(MessageViewContainerList.GetMessage(executionContext, 6086, paymentGateway, executionContext.GetSiteId())); // Payment mode not found for gateway: &1 siteId: &2

                paymentMessage = MessageViewContainerList.GetMessage(executionContext, 6086, paymentGateway, executionContext.GetSiteId());
                paymentLookupsAndStrings = webPaymentGatewayHelper.GetPaymentLookupsAndString();
                string defaultRedirectURL = "";
                string defaultRedirect = "";
                string websiteURL = "";
                if (paymentLookupsAndStrings.TryGetValue("DEFAULT_REDIRECT_URL", out defaultRedirect) && !string.IsNullOrWhiteSpace(defaultRedirect))
                {
                    defaultRedirectURL = defaultRedirect
                                            .Replace("@trxGuid", transactionPaymentIdentifier)
                                            .Replace("@message", paymentMessage)
                                            .Replace("@transactionId", "");
                }
                else if (paymentLookupsAndStrings.TryGetValue("WEBSITE_URL", out websiteURL) && !string.IsNullOrWhiteSpace(websiteURL))
                {
                    defaultRedirectURL = paymentLookupsAndStrings["WEBSITE_URL"];
                }

                HostedPaymentResponseDTO defaultResponseDTO = new HostedPaymentResponseDTO
                {
                    RedirectURL = defaultRedirectURL,
                };

                log.Debug("Returning default HostedPaymentResponseDTO due to missing payment mode. RedirectURL: " + defaultRedirectURL);
                return defaultResponseDTO;
            }
            log.Debug("Created payment mode container, PaymentMode:" + paymentModeContainerDTO.PaymentMode + ", PaymentModeId:" + paymentModeContainerDTO.PaymentModeId);

            using (UnitOfWork unitOfWork = new UnitOfWork())
            {
                // There will be no duplicate check for processing the response as it can be triggered multiple times.
                // This will be handled using the status.
                log.Debug("Creating payment gateway");
                System.Threading.CancellationTokenSource cancellationTokenSource = new System.Threading.CancellationTokenSource();

                try
                {
                    log.Debug("Initializing PaymentGatewayFactory.");
                    PaymentGatewayFactory.GetInstance().Initialize(true, cancellationTokenSource.Token);
                    log.Debug("PaymentGatewayFactory initialized. Creating payment gateway instance.");
                    webPaymentGateway = (ParafaitWebPaymentGateway)PaymentGatewayFactory.GetInstance().GetPaymentGateway(paymentModeContainerDTO, executionContext);
                    transactionPaymentIdentifier = await webPaymentGateway.GetPaymentIdentifier(paymentGatewayResponse);
                    log.Debug("Got the payment identifier " + transactionPaymentIdentifier);
                    Guid trxGuid;
                    int paymentId;
                    TransactionPaymentListBL transactionPaymentListBL = new TransactionPaymentListBL(executionContext, unitOfWork);
                    SearchParameterList<TransactionPaymentDTO.SearchByParameters> searchParameters = new SearchParameterList<TransactionPaymentDTO.SearchByParameters>();
                    if (Guid.TryParse(transactionPaymentIdentifier, out trxGuid))
                    {
                        log.Debug("Payment identifier parsed as GUID: " + trxGuid);
                        searchParameters.Add(TransactionPaymentDTO.SearchByParameters.PAYMENT_GUID, transactionPaymentIdentifier);
                    }
                    else if (int.TryParse(transactionPaymentIdentifier, out paymentId))
                    {
                        log.Debug("Payment identifier parsed as INT: " + paymentId);
                        searchParameters.Add(TransactionPaymentDTO.SearchByParameters.PAYMENT_ID, transactionPaymentIdentifier);
                    }
                    else
                    {
                        log.Error("Cannot parse transaction Payment Identifier from response: " + transactionPaymentIdentifier);
                        throw new CannotParseIdentifierException(MessageViewContainerList.GetMessage(executionContext, 6087)); // Cannot parse transaction Payment Identifier from response
                    }

                    log.Debug("Searching for TransactionPaymentDTO with provided identifier.");
                    List<TransactionPaymentDTO> transactionPaymentsList = transactionPaymentListBL.GetTransactionPaymentDTOList(searchParameters);
                    if (transactionPaymentsList == null || !transactionPaymentsList.Any() || transactionPaymentsList.Count > 1)
                    {
                        log.Debug("Payment Identifier Not found");
                        throw new PaymentIdentifierNotFoundException(MessageViewContainerList.GetMessage(executionContext, "Payment Identifier Not found"));
                    }
                    transactionPaymentDTO = transactionPaymentsList[0];
                    log.Debug("Got the payment identifier object " + transactionPaymentDTO.PaymentId + ":" + transactionPaymentDTO.TransactionId);

                    transactionPaymentIdentifier = transactionPaymentDTO.Guid; // Here we get paymentGuid, if PaymentId is used as parafait trxId

                    transactionPaymentContext = webPaymentGatewayHelper.BuildPaymentExecutionContext(transactionPaymentDTO.SiteId, transactionPaymentDTO.CreatedBy, transactionPaymentDTO.PosMachine);
                    log.Debug("Built the transaction payment context. Logging will shift to new context " + transactionPaymentContext.POSMachineName);

                    log = LogManager.GetLogger(transactionPaymentContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                    webPaymentGatewayHelper = new WebPaymentGatewayHelper(transactionPaymentContext);
                    paymentLookupsAndStrings = webPaymentGatewayHelper.GetPaymentLookupsAndString();
                    log.Debug("Getting payment mode object for the correct site");
                    PaymentModeContainerDTO selectedPaymentModesContainerDTO =
                            PaymentModeContainerList.GetPaymentModeContainerDTO(transactionPaymentContext.GetSiteId(), transactionPaymentDTO.PaymentModeId);
                    log.Debug("Identified the payment gateway " + selectedPaymentModesContainerDTO.PaymentModeId + ":" + selectedPaymentModesContainerDTO.PaymentMode);

                    if (webPaymentGateway == null)
                    {
                        webPaymentGateway = (ParafaitWebPaymentGateway)PaymentGatewayFactory.GetInstance().GetPaymentGateway(selectedPaymentModesContainerDTO, transactionPaymentContext);
                        log.Debug("Created new payment gateway instance");
                    }
                    else
                    {
                        log.Debug("Reusing existing payment gateway instance");
                    }

                    hostedPaymentResponseDTO.CallbackResponseMessage = webPaymentGateway.CallbackResponseMessage();
                    hostedPaymentResponseDTO.RedirectResponseToWebsite = webPaymentGateway.RedirectResponseToWebsite();

                    if (transactionPaymentDTO.PaymentStatus == PaymentStatuses.SALE_INITIATED.ToString())
                    {
                        log.Debug("Payment is in SALE_INITIATED state. Proceed with the processing.");
                        log.Debug("Calling ProcessGatewayResponse");

                        // Check if gateway supports additional response like payment history
                        if (webPaymentGateway.IsPaymentHistoryListRequired)
                        {
                            log.Debug("Gateway requires payment history list - fetching payment history");

                            // Get payment identifier from the gateway response
                            string paymentIdentifier = await webPaymentGateway.GetPaymentIdentifier(paymentGatewayResponse);

                            if (!string.IsNullOrWhiteSpace(paymentIdentifier))
                            {
                                // Fetch payment transaction DTOs and convert to PaymentResponseDTO list
                                List<PaymentResponseDTO> paymentHistoryList = await GetPaymentHistoryList(paymentIdentifier);
                                log.Debug($"Fetched {paymentHistoryList?.Count ?? 0} payment history records");

                                // Call ProcessPaymentResponse with payment history
                                processedPaymentTransactionDTO = await webPaymentGateway.ProcessPaymentResponse(paymentGatewayResponse, paymentHistoryList);
                            }
                            else
                            {
                                log.Warning("Payment identifier is empty - proceeding without payment history");
                                processedPaymentTransactionDTO = await webPaymentGateway.ProcessPaymentResponse(paymentGatewayResponse);
                            }
                        }
                        else
                        {
                            log.Debug("Gateway does not require payment history - using standard processing");
                            processedPaymentTransactionDTO = await webPaymentGateway.ProcessPaymentResponse(paymentGatewayResponse);
                        }

                        log.Debug("Completed calling ProcessGatewayResponse");

                        if (processedPaymentTransactionDTO == null)
                        {
                            log.Debug("Payment Response DTO is null, redirect to default error path");
                            throw new WebPaymentException("Hosted DTO is null, redirect to default error path");
                        }
                        log.Debug("Payment Response is not null");
                        processedPaymentTransactionDTO.InvoiceNo = transactionPaymentIdentifier;
                        processedPaymentTransactionDTO.TransactionId = transactionPaymentDTO.TransactionId;

                        // Check if additional response data is present (for 3DS authentication, etc.)
                        if (processedPaymentTransactionDTO.AdditionalResponseDTO != null)
                        {
                            log.Debug("Payment requires additional actions - AdditionalResponseDTO found");

                            if (processedPaymentTransactionDTO.AdditionalResponseDTO is AdditionalResponseDTO additionalResponseDTO)
                            {
                                // Check shouldRedirect flag for client compatibility
                                bool shouldRedirect = false;
                                if (paymentLookupsAndStrings.ContainsKey("shouldRedirect"))
                                {
                                    bool.TryParse(paymentLookupsAndStrings["shouldRedirect"], out shouldRedirect);
                                }

                                if (shouldRedirect)
                                {
                                    log.Debug("Legacy client detected (shouldRedirect=true) - using redirect flow");
                                    hostedPaymentResponseDTO.RedirectURL = additionalResponseDTO.Url;
                                    hostedPaymentResponseDTO.RedirectResponseToWebsite = true;
                                }
                                else
                                {
                                    log.Debug("Modern client detected (shouldRedirect=false) - returning AdditionalResponseDTO");
                                    hostedPaymentResponseDTO.AdditionalResponseDTO = additionalResponseDTO;
                                    hostedPaymentResponseDTO.RedirectResponseToWebsite = false;
                                    hostedPaymentResponseDTO.CallbackResponseMessage = "[accepted]";
                                }

                                // Payment remains in PENDING state until additional actions complete
                                paymentTrxStatuses = PaymentTransactionStatuses.PENDING;
                                log.Debug("Payment status set to PENDING due to additional actions requirement");

                                log.LogMethodExit(hostedPaymentResponseDTO);
                                return hostedPaymentResponseDTO;
                            }
                        }

                        if (processedPaymentTransactionDTO.Status == PaymentTransactionStatuses.SUCCESS.ToString())
                        {
                            log.Debug("Transaction is successful");
                            paymentReceived = true;
                            paymentTransactionDTOForRefund = processedPaymentTransactionDTO;
                            paymentTrxStatuses = PaymentTransactionStatuses.SUCCESS;
                        }
                    }
                    else
                    {
                        log.Debug("Payment is not in SALE_INITIATED state. " + transactionPaymentDTO.PaymentStatus + " Invoking thread delay");
                        // Nitin Check - Do we need a thread delay here?
                        if (transactionPaymentDTO.PaymentTransactionDTOList != null && transactionPaymentDTO.PaymentTransactionDTOList.Any())
                        {
                            log.Debug("Details of parallel payment: Response ID: "
                                        + transactionPaymentDTO.PaymentTransactionDTOList[0].ResponseID
                                        + ", Status: " + transactionPaymentDTO.PaymentTransactionDTOList[0].Status.ToString());
                            Enum.TryParse(transactionPaymentDTO.PaymentTransactionDTOList[0].Status, out paymentTrxStatuses);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.Debug("Error caught while processing response " + ex);
                    if (hostedPaymentResponseDTO == null)
                    {
                        hostedPaymentResponseDTO = new HostedPaymentResponseDTO();
                        if (webPaymentGateway != null)
                        {
                            hostedPaymentResponseDTO.CallbackResponseMessage = webPaymentGateway.CallbackResponseMessage();
                            hostedPaymentResponseDTO.RedirectResponseToWebsite = webPaymentGateway.RedirectResponseToWebsite();
                            log.Debug("HostedPaymentResponseDTO re-initialized in catch block. Callback message: " + hostedPaymentResponseDTO.CallbackResponseMessage + ", Redirect: " + hostedPaymentResponseDTO.RedirectResponseToWebsite);
                        }
                    }

                    paymentTrxStatuses = PaymentTransactionStatuses.ERROR;
                    paymentMessage = ex.Message;

                    processedPaymentTransactionDTO = new PaymentTransactionDTO();
                    processedPaymentTransactionDTO.RecordNo = "C";
                    processedPaymentTransactionDTO.Status = PaymentTransactionStatuses.ERROR.ToString();
                    processedPaymentTransactionDTO.Purchase = "0";
                    processedPaymentTransactionDTO.Authorize = "0";
                    processedPaymentTransactionDTO.Amount = 0.0M;
                    processedPaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                    processedPaymentTransactionDTO.DSIXReturnCode = ex.Message;
                    processedPaymentTransactionDTO.TextResponse = ex.Message;
                    processedPaymentTransactionDTO.TransactionPaymentGuid = transactionPaymentDTO.Guid;
                    processedPaymentTransactionDTO.InvoiceNo = transactionPaymentDTO.Guid;
                }

                log.Debug("Redirect URL " + hostedPaymentResponseDTO.RedirectURL + " : message :" + paymentMessage);
            }

            // Sale Payment Region
            using (UnitOfWork unitOfWorkSaleTrx = new UnitOfWork())
            {
                try
                {
                    unitOfWorkSaleTrx.Begin();
                    transactionBL = new TransactionBL(transactionPaymentContext, transactionPaymentDTO.TransactionId, unitOfWorkSaleTrx);
                    transactionDTO = transactionBL.TransactionDTO;
                    log.Debug("Transaction DTO loaded. Finding TransactionPaymentDTO by GUID: " + transactionPaymentIdentifier);
                    transactionPaymentDTO = transactionDTO.TransactionPaymentDTOList.FirstOrDefault(x => x.Guid == transactionPaymentIdentifier);

                    if (transactionPaymentDTO == null)
                    {
                        log.Error("TransactionPaymentDTO not found in TransactionDTOList after loading transaction for GUID: " + transactionPaymentIdentifier);
                        throw new Semnox.Parafait.PaymentGatewayInterface.TransactionPaymentNotFoundException("TransactionPaymentDTO not found in transaction's payment list.");
                    }

                    if (transactionPaymentDTO.PaymentStatus == PaymentStatuses.SALE_INITIATED.ToString())
                    {
                        log.Debug("Payment is still in SALE_INITIATED status, so go ahead with the sale payment call.");
                        log.Debug($"Calling SalePayment for PaymentId: {transactionPaymentDTO.PaymentId}");
                        transactionBL.SalePayment(transactionPaymentDTO.PaymentId, processedPaymentTransactionDTO);
                    }
                    else
                    {
                        log.Debug("Payment is not in sale initiatiated state. " + transactionPaymentDTO.PaymentStatus.ToString());
                    }
                    CreateApplicationRequestLog("Transaction", "ProcessWebPayment", transactionDTO.Guid, unitOfWorkSaleTrx.SQLTrx);
                    unitOfWorkSaleTrx.Commit();
                }
                catch (Exception ex)
                {
                    log.Debug("Error caught will doing salepayment. The payment might have to be refunded." + ex);
                    // An exception has occured. A Payment transaction DTO is not created. So the payment might have to be refunded.
                    checkForRefund = true;
                }
            }

            using (UnitOfWork unitOfWorkValidatePayment = new UnitOfWork())
            {
                try
                {
                    log.Debug("Initiate post processing of payments.");
                    TransactionPaymentListBL transactionPaymentListBL = new TransactionPaymentListBL(transactionPaymentContext, unitOfWorkValidatePayment);
                    SearchParameterList<TransactionPaymentDTO.SearchByParameters> searchParameters = new SearchParameterList<TransactionPaymentDTO.SearchByParameters>();
                    searchParameters.Add(TransactionPaymentDTO.SearchByParameters.PAYMENT_GUID, transactionPaymentIdentifier);
                    log.Debug("Searching for TransactionPaymentDTO by GUID for post-processing: " + transactionPaymentIdentifier);
                    List<TransactionPaymentDTO> transactionPaymentsList = transactionPaymentListBL.GetTransactionPaymentDTOList(searchParameters);
                    if (transactionPaymentsList == null || !transactionPaymentsList.Any() || transactionPaymentsList.Count > 1)
                    {
                        log.Debug("Payment Identifier Not found");
                        checkForRefund = true;
                        paymentMessage = "Something went wrong. Not able to locate the payment";
                        hostedPaymentResponseDTO.RedirectURL = paymentLookupsAndStrings["DEFAULT_REDIRECT_URL"].Replace("@trxGuid", transactionPaymentIdentifier)
                                                                                        .Replace("@message", paymentMessage)
                                                                                        .Replace("@transactionId", transactionPaymentDTO.TransactionId.ToString());
                    }
                    else
                    {
                        transactionPaymentDTO = transactionPaymentsList[0];
                        log.Debug("Begin processing ");
                        PaymentStatuses paymentStatuses;
                        Enum.TryParse(transactionPaymentDTO.PaymentStatus, out paymentStatuses);
                        if (paymentStatuses != PaymentStatuses.SETTLED)
                        {
                            log.Debug("Payment is not in settled status. Refund might have to be invoked");
                            checkForRefund = true;
                        }
                        else
                        {
                            log.Debug("Payment is in settled status.");
                        }

                        if (transactionPaymentDTO.PaymentTransactionDTOList != null && transactionPaymentDTO.PaymentTransactionDTOList.Any())
                        {
                            PaymentTransactionDTO paymentTransactionDTO = transactionPaymentDTO.PaymentTransactionDTOList[0];

                            log.Debug("Building response objects based on status of Payment transaction DTO " + paymentTransactionDTO.ResponseID);
                            if (paymentTransactionDTO.Status != PaymentTransactionStatuses.SUCCESS.ToString() &&
                                             paymentTransactionDTO.Status != PaymentTransactionStatuses.CANCELLED.ToString())
                            {
                                if (paymentTransactionDTO.Status == PaymentTransactionStatuses.PENDING.ToString())
                                {
                                    log.Debug("Payment status is pending");
                                    paymentMessage = webPaymentGatewayHelper.BuildMessage(paymentLookupsAndStrings["PENDING_MESSAGE"], transactionPaymentDTO.TransactionId, paymentTransactionDTO);
                                }
                                else
                                {
                                    log.Debug($"Payment was not successful: {paymentTransactionDTO.Status}");
                                    paymentMessage = webPaymentGatewayHelper.BuildMessage(paymentLookupsAndStrings["FAILURE_MESSAGE"], transactionPaymentDTO.TransactionId, paymentTransactionDTO);
                                }
                                hostedPaymentResponseDTO.RedirectURL = paymentLookupsAndStrings["FAILURE_REDIRECT_URL"].Replace("@trxGuid", transactionPaymentIdentifier)
                                                                                                .Replace("@message", paymentMessage)
                                                                                                .Replace("@transactionId", transactionPaymentDTO.TransactionId.ToString());
                            }
                            else if (paymentTrxStatuses == PaymentTransactionStatuses.CANCELLED)
                            {
                                log.Debug("Payment was cancelled");
                                paymentMessage = webPaymentGatewayHelper.BuildMessage(paymentLookupsAndStrings["FAILURE_MESSAGE"], transactionPaymentDTO.TransactionId, paymentTransactionDTO);
                                hostedPaymentResponseDTO.RedirectURL = paymentLookupsAndStrings["CANCEL_REDIRECT_URL"].Replace("@trxGuid", transactionPaymentIdentifier)
                                                                                               .Replace("@message", paymentMessage)
                                                                                               .Replace("@transactionId", transactionPaymentDTO.TransactionId.ToString());
                            }
                            else
                            {
                                log.Debug("Payment was successful ");
                                paymentMessage = webPaymentGatewayHelper.BuildMessage(paymentLookupsAndStrings["SUCCESS_MESSAGE"], transactionPaymentDTO.TransactionId, paymentTransactionDTO);
                                hostedPaymentResponseDTO.RedirectURL = paymentLookupsAndStrings["SUCCESS_REDIRECT_URL"].Replace("@trxGuid", transactionPaymentIdentifier)
                                                                                                .Replace("@message", paymentMessage)
                                                                                                .Replace("@transactionId", transactionPaymentDTO.TransactionId.ToString());
                            }
                        }
                        else
                        {
                            log.Debug("Payment transaction DTO not found. Return to default URL");
                            checkForRefund = true;
                            paymentMessage = "Something went wrong. Not able to locate the payment transaction";
                            hostedPaymentResponseDTO.RedirectURL = paymentLookupsAndStrings["DEFAULT_REDIRECT_URL"].Replace("@trxGuid", transactionPaymentIdentifier)
                                                                                            .Replace("@message", paymentMessage)
                                                                                            .Replace("@transactionId", transactionPaymentDTO.TransactionId.ToString());
                            log.Debug("Redirect URL: " + hostedPaymentResponseDTO.RedirectURL);
                        }
                    }
                }
                catch (Exception ex)
                {
                    log.Error("Caught an error during payment validation: " + ex.Message);
                }
            }

            // How to determine if a refund needs to be initiated. 
            // Specific scenario - Payment received. Sale Transaction Failed. There is no entry in DB for the payment received. Payment details are in memory only.
            // This scenario can only be refunded from here.
            if (checkForRefund && paymentReceived && paymentTransactionDTOForRefund != null)
            {
                log.Debug("A payment has been received but no PaymentTransactionDTO is found. A refund needs to be initiated.");
                // Call a void transaction with the transaction id here
                paymentTrxStatuses = PaymentTransactionStatuses.CANCELLED;
            }

            // Try Close Transaction Region
            if (paymentTrxStatuses == PaymentTransactionStatuses.SUCCESS)
            {
                // Create a new unit of work to avoid rollback of the initiate response received
                using (UnitOfWork unitOfWorkTryCloseTrx = new UnitOfWork())
                {
                    try
                    {
                        unitOfWorkTryCloseTrx.Begin();
                        log.Debug("Begin Try Close the order for transaction ID: " + transactionBL?.TransactionDTO?.TransactionId);
                        // Payment has been processed. Now the transaction has to be processed
                        OrderBL orderBL = new OrderBL(transactionPaymentContext, transactionBL.TransactionDTO.OrderId, unitOfWorkTryCloseTrx);
                        transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);

                        if (transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any()
                                            && transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.DoImmediatePost)
                                            && transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.OptionItemCode == POS.POSPrinterOverrideOptionItemCode.SHOWERROR))
                        {
                            log.Debug("POS Printer Override Rules: ImmediatePost and ShowError are present. Calling TryCloseTransaction then Fiscalize.");
                            orderBL.TryCloseTransaction(transactionBL.TransactionDTO.TransactionId, null);
                            transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);
                            transactionBL.Fiscalize();
                        }
                        else
                        {
                            log.Debug("POS Printer Override Rules: No ImmediatePost with ShowError. Calling TryCloseTransaction.");
                            orderBL.TryCloseTransaction(transactionBL.TransactionDTO.TransactionId, null);
                        }

                        transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);
                        CreateApplicationRequestLog("Transaction", "CloseWebPayment", transactionDTO.Guid, unitOfWorkTryCloseTrx.SQLTrx);
                        unitOfWorkTryCloseTrx.Commit();
                        log.Debug("Building web payment request");
                    }
                    catch (Exception ex)
                    {
                        log.Debug("Got an error while trying to close the order " + ex.Message);
                        // Payment has been accepted and applied. Should Order closing result in a rollback?
                    }
                }

                using (UnitOfWork unitOfWorkCloseOrder = new UnitOfWork())
                {
                    if (transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any()
                                && transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.DoImmediatePost)
                                && !transactionBL.TransactionDTO.TrxPOSPrinterOverrideRulesDTOList.Any(x => x.OptionItemCode == POS.POSPrinterOverrideOptionItemCode.SHOWERROR))
                    {
                        try
                        {
                            log.Debug("Calling Fiscalize Transaction");
                            unitOfWorkCloseOrder.Begin();
                            OrderBL orderBL = new OrderBL(transactionPaymentContext, transactionBL.TransactionDTO.OrderId, unitOfWorkCloseOrder);
                            transactionBL = orderBL.GetTransactionBL(transactionBL.TransactionDTO.TransactionId);
                            transactionBL.Fiscalize();
                            unitOfWorkCloseOrder.Commit();
                            log.Debug("Fiscalization completed.");
                        }
                        catch (Exception ex)
                        {
                            log.Error("Fiscalization failed for transationId:" + transactionBL.TransactionDTO.TransactionId);
                            log.Error(ex);
                            // throw new NotYetFiscalizedException(MessageContainerList.GetMessage(executionContext, 5577)); //Fiscalization Failed.Please check the logs for Errors.
                        }
                    }
                }
            }
            log.LogMethodExit(hostedPaymentResponseDTO);
            return hostedPaymentResponseDTO;
        }

        public async Task<PaymentSessionDTO> GetPaymentSession(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);
            using (UnitOfWork unitOfWork = new UnitOfWork())
            {
                TransactionPaymentListBL transactionPaymentListBL = new TransactionPaymentListBL(executionContext, unitOfWork);
                List<KeyValuePair<TransactionPaymentDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<TransactionPaymentDTO.SearchByParameters, string>>();
                searchParameters.Add(new KeyValuePair<TransactionPaymentDTO.SearchByParameters, string>(TransactionPaymentDTO.SearchByParameters.PAYMENT_GUID, paymentRequestDTO.RequestIdentifier));
                log.Debug($"Searching for TransactionPaymentDTO with PaymentGuid: {paymentRequestDTO.RequestIdentifier}");
                List<TransactionPaymentDTO> transactionPaymentDTOList = transactionPaymentListBL.GetTransactionPaymentDTOList(searchParameters);

                if (transactionPaymentDTOList == null || transactionPaymentDTOList.Count != 1)
                {
                    log.Error("Transaction payment not found for requestGuid: " + paymentRequestDTO.RequestIdentifier);
                    throw new Semnox.Parafait.PaymentGatewayInterface.TransactionPaymentNotFoundException(MessageViewContainerList.GetMessage(executionContext.SiteId, executionContext.LanguageId, 5980)); // TransactionPayment record not found
                }
                TransactionPaymentDTO transactionPaymentDTO = transactionPaymentDTOList[0];
                log.Debug($"Found transaction payment: PaymentId = {transactionPaymentDTO.PaymentId}, TransactionId = {transactionPaymentDTO.TransactionId}, Amount = {transactionPaymentDTO.Amount}");

                WebPaymentGatewayHelper webPaymentGatewayHelper = new WebPaymentGatewayHelper(executionContext);
                ExecutionContext transactionPaymentContext = webPaymentGatewayHelper.BuildPaymentExecutionContext(transactionPaymentDTO.SiteId, transactionPaymentDTO.CreatedBy, transactionPaymentDTO.PosMachine);
                log.Debug("Built the transaction payment context. Logging will shift to new context " + transactionPaymentContext.POSMachineName);
                log = LogManager.GetLogger(transactionPaymentContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

                PaymentModeContainerDTO selectedPaymentModesContainerDTO =
                        PaymentModeContainerList.GetPaymentModeContainerDTO(transactionPaymentContext.GetSiteId(), transactionPaymentDTO.PaymentModeId);

                paymentRequestDTO.IntRequestIdentifier = transactionPaymentDTO.PaymentId;
                paymentRequestDTO.Amount = transactionPaymentDTO.Amount;
                paymentRequestDTO.SubscriptionAuthorizationMode = transactionPaymentDTO.SubscriptionAuthorizationMode.ToString();

                System.Threading.CancellationTokenSource cancellationTokenSource = new System.Threading.CancellationTokenSource();
                PaymentGatewayFactory.GetInstance().Initialize(true, cancellationTokenSource.Token);
                ParafaitWebPaymentGateway webPaymentGateway =
                    (ParafaitWebPaymentGateway)PaymentGatewayFactory.GetInstance().GetPaymentGateway(selectedPaymentModesContainerDTO, transactionPaymentContext);
                webPaymentGateway.ValidateConfiguration();
                PaymentSessionDTO paymentSessionDTO = await webPaymentGateway.CreatePaymentSessionDTO(paymentRequestDTO, transactionPaymentDTO, unitOfWork);
                log.Debug($"PaymentSessionDTO created. PaymentSession: {paymentSessionDTO.ToString()}");

                log.LogMethodExit(paymentSessionDTO);
                return paymentSessionDTO;
            }
        }
        /// <summary>
        /// Fetches payment history list for gateways that require it
        /// </summary>
        /// <param name="paymentIdentifier">Payment identifier to fetch history for</param>
        /// <returns>List of PaymentResponseDTO representing payment history</returns>
        private async Task<List<PaymentResponseDTO>> GetPaymentHistoryList(string paymentIdentifier)
        {
            log.LogMethodEntry(paymentIdentifier);
            List<PaymentResponseDTO> paymentHistoryList = new List<PaymentResponseDTO>();

            try
            {
                using (UnitOfWork unitOfWork = new UnitOfWork())
                {
                    // Search for payment transactions by payment identifier
                    TransactionPaymentListBL transactionPaymentListBL = new TransactionPaymentListBL(executionContext, unitOfWork);

                    // Use SearchParameterList for consistency with other methods in this class
                    SearchParameterList<TransactionPaymentDTO.SearchByParameters> searchParameters = new SearchParameterList<TransactionPaymentDTO.SearchByParameters>();

                    // Try to determine if the identifier is a GUID or ID and search accordingly
                    Guid trxGuid;
                    int paymentId;

                    if (Guid.TryParse(paymentIdentifier, out trxGuid))
                    {
                        log.Debug($"Payment identifier parsed as GUID: {trxGuid}");
                        searchParameters.Add(TransactionPaymentDTO.SearchByParameters.PAYMENT_GUID, paymentIdentifier);
                    }
                    else if (int.TryParse(paymentIdentifier, out paymentId))
                    {
                        log.Debug($"Payment identifier parsed as INT: {paymentId}");
                        searchParameters.Add(TransactionPaymentDTO.SearchByParameters.PAYMENT_ID, paymentIdentifier);
                    }
                    else
                    {
                        log.Debug($"Payment identifier as string: {paymentIdentifier}");
                        // For string identifiers, try PAYMENT_IDENTIFIER parameter if available
                        // Otherwise fall back to PAYMENT_GUID as string
                        searchParameters.Add(TransactionPaymentDTO.SearchByParameters.PAYMENT_GUID, paymentIdentifier);
                    }

                    List<TransactionPaymentDTO> transactionPaymentDTOList = transactionPaymentListBL.GetTransactionPaymentDTOList(searchParameters);
                    log.Debug($"Found {transactionPaymentDTOList?.Count ?? 0} transaction payments for identifier: {paymentIdentifier}");

                    if (transactionPaymentDTOList != null && transactionPaymentDTOList.Count > 0)
                    {
                        // Convert TransactionPaymentDTO to PaymentResponseDTO
                        foreach (TransactionPaymentDTO transactionPaymentDTO in transactionPaymentDTOList)
                        {
                            PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO
                            {
                                InvoiceNo = transactionPaymentDTO.PaymentIdentifier,
                                Amount = transactionPaymentDTO.Amount,
                                Status = transactionPaymentDTO.PaymentStatus,
                                RefNo = transactionPaymentDTO.PaymentIdentifier,
                                TransactionDatetime = transactionPaymentDTO.CreationDate,
                                Purchase = transactionPaymentDTO.Amount.ToString("0.00"),
                                Authorize = transactionPaymentDTO.Amount.ToString("0.00"),
                                RecordNo = transactionPaymentDTO.PaymentStatus == PaymentStatuses.SUCCESS.ToString() ? "A" : "C"
                            };
                            paymentHistoryList.Add(paymentResponseDTO);
                        }

                        // Sort by creation date to provide chronological order
                        paymentHistoryList = paymentHistoryList.OrderBy(p => p.TransactionDatetime).ToList();
                        log.Debug($"Payment history sorted chronologically - oldest to newest");
                    }
                    else
                    {
                        log.Debug($"No payment transactions found for identifier: {paymentIdentifier}");
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error($"Error fetching payment history for identifier {paymentIdentifier}: {ex.Message}", ex);
                // Return empty list on error - gateway can handle missing history gracefully
                paymentHistoryList = new List<PaymentResponseDTO>();
            }

            log.Debug($"Returning {paymentHistoryList.Count} payment history records");
            log.LogMethodExit(paymentHistoryList);
            return paymentHistoryList;
        }
    }
}
