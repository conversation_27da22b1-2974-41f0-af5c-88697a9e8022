﻿/********************************************************************************************
* Project Name - Web Payments
 * Description - web payment factory
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using System.Configuration;
using Semnox.Core.Utilities;

namespace Semnox.Parafait.WebPayments
{
    public class WebPaymentsUseCaseFactory
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// GetSubscriptionHeaderUseCases
        /// </summary>
        /// <param name="executionContext"></param>
        /// <returns></returns>
        public static IWebPaymentsUseCases GetWebPaymentsUseCases(ExecutionContext executionContext, string requestGuid)
        {
            log.LogMethodEntry(executionContext, requestGuid);
            IWebPaymentsUseCases result;
            if (ConfigurationManager.AppSettings["EXECUTION_MODE"] == "Remote")
            {
                result = new RemoteWebPaymentsUseCases(executionContext, requestGuid);
            }
            else
            {
                result = new LocalWebPaymentsUseCases(executionContext, requestGuid);
            }

            log.LogMethodExit(result);
            return result;
        }
    }
}
