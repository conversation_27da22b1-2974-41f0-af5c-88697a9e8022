﻿
/********************************************************************************************
 * Project Name - Payment GatewayInterface
 * Description  - PaymentGatewayException Class 
 * 
 **************
 **Version Log
 **************
 *Version     Date              Modified By                    Remarks          
 *********************************************************************************************
 * 2.190.0    24-Sep-2024       Amrutha                        Created
 *******************************************************************************************/



using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Security.Permissions;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{

    public enum PaymentErrorCodes
    {
        /// <summary> 
        /// Transaction Failed.No response received.
        /// </summary>
        PG_00001,
        /// <summary> 
        /// Last transaction check failed. Txn not found.
        /// </summary>
        PG_00002,
        /// <summary>
        /// Failed to communicate with the terminal.
        /// </summary>
        PG_00003,
        /// <summary>
        /// Transaction is in progress.
        /// </summary>
        PG_00004,
        /// <summary>
        /// Donation is in progress.
        /// </summary>
        PG_00005,
        /// <summary>
        ///  Donation Cancelled.
        PG_00006,
        /// <summary>
        /// Pre-Auth amount value should be minimum 1.
        /// </summary>
        PG_00007,
        /// <summary>
        /// Refund Failed.
        /// </summary>
        PG_00008,
        /// <summary>
        /// Transaction Failed.
        /// </summary>
        PG_00009,
        /// <summary>
        /// Refund failed due to Invalid amount passed.
        /// </summary>
        PG_00010,
        /// <summary>
        /// Settlement Failed.
        /// </summary>
        PG_00011,
        /// <summary>
        /// Last Transaction check not allowed for the Transaction.
        /// </summary>
        PG_00012,
        /// <summary>
        /// Deserialization of response failed
        /// </summary>
        PG_00013,
        /// <summary>
        /// Donation Failed. No response received.
        /// </summary>
        PG_00014,
        /// <summary>
        /// Payment failed: could not receive response.
        /// </summary>
        PG_00015,
        /// <summary>
        /// Response Conversion Failed Exception
        /// </summary>
        PG_00016,
        /// <summary>
        /// Invalid Transaction Type Exception
        /// </summary>
        PG_00017,
        /// <summary>
        /// Invalid Response Exception
        /// </summary>
        PG_00018,
        /// <summary>
        /// PreAuthorization Not Supported Exception
        /// </summary>
        PG_00019,
        /// <summary>
        /// Authorization not supported Exception
        /// </summary>
        PG_00020,
        /// <summary>
        /// Sale not Supported Exception
        /// </summary>
        PG_00021,
        /// <summary>
        /// Sale Transaction Failed.
        /// </summary>
        PG_00022,
        /// <summary>
        /// Authorization Failed
        /// </summary>
        PG_00023,
        /// <summary>
        /// PreAuthorization Failed
        /// </summary>
        PG_00024,
        /// <summary>
        /// Donation not Supported
        /// </summary>
        PG_00025,
        /// <summary>
        /// Customer copy printing not Supported
        /// </summary>
        PG_00026,
        /// <summary>
        /// Merchant copy printing not Supported
        /// </summary>
        PG_00027,
        /// <summary>
        /// Settlement not Supported
        /// </summary>
        PG_00028,
        /// <summary>
        /// Refund not Supported
        /// </summary>
        PG_00029,
        /// <summary>
        /// StatusCheck not Supported
        /// </summary>
        PG_00030,
        /// <summary>
        /// Tip option not supported
        /// </summary>
        PG_00031,
        /// <summary>
        /// Unable to perform transaction
        /// </summary>
        PG_00032,
        // <summary>
        /// DeviceNotConnectedException
        /// </summary>
        PG_00033,
        /// <summary>
        /// ConfigurationDataNotValid Exception
        /// </summary>
        PG_00034,
        /// <summary>
        /// Configuration Not Set Exception
        /// </summary>
        PG_00035,
        /// <summary>
        /// PaymentResponseDTOCreationException
        /// </summary>
        PG_00036,
        /// <summary>
        /// AdditionalResponseDataAccessException
        /// </summary>
        PG_00037,
        // <summary>
        /// ErrorFetchingPreAuthPaymentId
        /// </summary>
        PG_00038,
        /// <summary>
        /// AuthTransactionExistException
        /// </summary>
        PG_00039,
        /// <summary>
        /// IndependentRefundFailedException
        /// </summary>
        PG_00040,
        /// <summary>
        /// VerificationFailedException
        /// </summary>
        PG_00041,
        /// <summary>
        /// TransactionIdNullException
        /// </summary>
        PG_00042,
        /// <summary>
        /// Payment Session Not Supported
        /// </summary>
        PG_00043,
        /// <summary>
        /// Transaction Payment Not found for given requestId
        /// </summary>
        PG_00044,
        /// <summary>
        /// ProcessPaymentResponse Not Supported
        /// </summary>
        PG_00045,
        /// <summary>
        /// Site Not found
        /// </summary>
        PG_00046,
        /// <summary>
        /// GetPaymentIdentifier Not Supported
        /// </summary>
        PG_00047,
        /// <summary>
        /// PaymentResponseNullException
        /// </summary>
        PG_00048,
        /// <summary>
        /// PaymentResponseProcessingException
        /// </summary>
        PG_00049,
        /// <summary>
        /// StatusCheckResponseNullException
        /// </summary>
        PG_00050,
        /// <summary>
        /// ReadCardRequest Exception
        /// </summary>
        PG_00051,
        /// <summary>
        /// DeviceConnectionFailedException
        /// </summary>
        PG_00052,
        /// <summary>
        /// SessionKeyGenerationException
        /// </summary>
        PG_00053,
        /// <summary>
        /// DisplayInDeviceException
        /// </summary>
        PG_00054,
        /// <summary>
        /// ReadCardCommandException
        /// </summary>
        PG_00055,
        /// <summary>
        /// InvalidZipCodeException
        /// </summary>
        PG_00056,
        /// <summary>
        /// SaleCommandFailedException
        /// </summary>
        PG_00057,
        /// <summary>
        /// TransactionAdjustmentNotAllowedException
        /// </summary>
        PG_00058,
        /// <summary>
        /// InquireResponseException
        /// </summary>
        PG_00059,
        /// <summary>
        /// RefundResponseFailedException
        /// </summary>
        PG_00060,
        /// <summary>
        /// PaymentNotCompleteException
        /// </summary>
        PG_00061,
        /// <summary>
        /// ResponseParsingFailedException
        /// </summary>
        PG_00062,
        /// <summary>
        /// BinCommandFailedException
        /// </summary>
        PG_00063,
        /// <summary>
        /// PaymentDeclinedException
        /// </summary>
        PG_00064,
        /// <summary>
        /// InvalidSessionException
        /// </summary>
        PG_00065,
        /// <summary>
        ///PaymentCancelledException
        /// </summary>
        PG_00066,
        /// <summary>
        ///InsufficientRequestParamasException
        /// </summary>
        PG_00067,
        /// <summary>
        ///DatetimeConversionFailedException
        /// </summary>
        PG_00068,
        /// <summary>
        /// SignatureValidationFailureException
        /// </summary>
        PG_00069,
        /// <summary>
        /// PaymentModeNotFoundException
        /// </summary>
        PG_00070,
        /// <summary>
        /// CannotParseIdentifierException
        /// </summary>
        PG_00071,
        /// <summary>
        /// GatewayResponseBodyEmptyException
        /// </summary>
        PG_00072,
        /// <summary>
        /// GatewayResponseEmptyException
        /// </summary>
        PG_00073,
        /// <summary>
        /// RefundResponseNullException
        /// </summary>
        PG_00074,
        /// <summary>
        /// StatusAPIResponseNullException
        /// </summary>
        PG_00075,
        /// <summary>
        /// RefundRequestNullException
        /// </summary>
        PG_00076,
        /// <summary>
        /// BytoToStringConversionFailedException
        /// </summary>
        PG_00077,
        /// <summary>
        /// QRGenerationFailedException
        /// </summary>
        PG_00078,
        /// <summary>
        /// InternetNotAvailableException
        /// </summary>
        PG_00079,
        /// <summary>
        /// InvalidCardDetailsException
        /// </summary>
        PG_00080,
        /// <summary>
        /// PaymentTimeoutException
        /// </summary>
        PG_00081,
        /// <summary>
        /// CaptchaValidationException
        /// </summary>
        PG_00082,
        /// <summary>
        /// PaymentTransactionNotFoundException
        /// </summary>
        PG_00083,
        /// <summary>
        /// StoreNameEmptyException
        /// </summary>
        PG_00084,
        /// <summary>
        /// StoreNameMismatchException
        /// </summary>
        PG_00085

    };

    /// <summary>
    /// Represents PaymentGatewayException error that occur during parafait application execution. 
    /// </summary>
    [Serializable]
    public class PaymentGatewayException : Exception, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public PaymentGatewayException(string message) : base(message)
        {
        }

        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public PaymentGatewayException(Enum errorCode, string message) : base(errorCode.ToString() + " - " + message)
        {
        }


        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public PaymentGatewayException(string message, Exception innerException) : base(message, innerException)
        {

        }

        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public PaymentGatewayException(Enum errorCode, string message, Exception innerException) : base(errorCode.ToString() + " - " + message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentGatewayException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// Represents NonRetryableException  error that occur during gateway class execution. 
    /// </summary>
    [Serializable]
    public class NonRetryableException : Exception, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public NonRetryableException(string message) : base(message)
        {
        }

        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public NonRetryableException(Enum errorCode, string message) : base(errorCode.ToString() + " - " + message)
        {
        }


        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public NonRetryableException(string message, Exception innerException) : base(message, innerException)
        {

        }

        /// <summary>
        /// Parameterized constructor of PaymentGatewayException.
        /// </summary>
        public NonRetryableException(Enum errorCode, string message, Exception innerException) : base(errorCode.ToString() + " - " + message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected NonRetryableException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0001 : Represents Transaction Failed.No response received.
    /// </summary>
    [Serializable]
    public class ResponseNotReceivedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ResponseNotReceivedException(string message) : base(PaymentErrorCodes.PG_00001, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ResponseNotReceivedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00001, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ResponseNotReceivedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0002 : Represents Last transaction check failed. Txn not found.
    /// </summary>
    [Serializable]
    public class LastTransactionCheckFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.PG_00002
        /// </summary>
        public LastTransactionCheckFailedException(string message) : base(PaymentErrorCodes.PG_00002, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public LastTransactionCheckFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00002, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected LastTransactionCheckFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0003 : Represents Failed to communicate with the terminal.
    /// </summary>
    [Serializable]
    public class FailedToCommunicateException : NonRetryableException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.PG_00002
        /// </summary>
        public FailedToCommunicateException(string message) : base(PaymentErrorCodes.PG_00003, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public FailedToCommunicateException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00003, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected FailedToCommunicateException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0004 : Represents Transaction is in progress.
    /// </summary>
    [Serializable]
    public class TransactionInProgressException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.PG_00002
        /// </summary>
        public TransactionInProgressException(string message) : base(PaymentErrorCodes.PG_00004, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public TransactionInProgressException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00004, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected TransactionInProgressException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0005 : Represents Donation is in progress..
    /// </summary>
    [Serializable]
    public class DonationInProgressException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public DonationInProgressException(string message) : base(PaymentErrorCodes.PG_00005, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public DonationInProgressException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00005, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DonationInProgressException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0006 : Represents Donation Cancelled.
    /// </summary>
    [Serializable]
    public class DonationCancelledException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public DonationCancelledException(string message) : base(PaymentErrorCodes.PG_00006, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public DonationCancelledException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00006, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DonationCancelledException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0007 : Represents Pre-Auth amount value should be minimum 1.
    /// </summary>
    [Serializable]
    public class PreAuthMinValueException : NonRetryableException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public PreAuthMinValueException(string message) : base(PaymentErrorCodes.PG_00007, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public PreAuthMinValueException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00007, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PreAuthMinValueException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0008 : RepresentsRefund Failed.
    /// </summary>
    [Serializable]
    public class RefundFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public RefundFailedException(string message) : base(PaymentErrorCodes.PG_00008, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public RefundFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00008, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected RefundFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_0009 : Represents Transaction Failed.
    /// </summary>
    [Serializable]
    public class TransactionFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public TransactionFailedException(string message) : base(PaymentErrorCodes.PG_00009, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public TransactionFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00009, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected TransactionFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00010 : Represents Refund failed due to Invalid amount passed.
    /// </summary>
    [Serializable]
    public class InvalidAmountException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public InvalidAmountException(string message) : base(PaymentErrorCodes.PG_00010, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public InvalidAmountException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00010, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InvalidAmountException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00011 : Represents Settlement Failed.
    /// </summary>
    [Serializable]
    public class SettlementFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public SettlementFailedException(string message) : base(PaymentErrorCodes.PG_00011, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public SettlementFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00011, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SettlementFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00012 : Represents Last Transaction check not allowed for the Transaction.
    /// </summary>
    [Serializable]
    public class LastTransactionNotAllowedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public LastTransactionNotAllowedException(string message) : base(PaymentErrorCodes.PG_00012, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of DailyTransactionProductSaleModifiedException.
        /// </summary>
        public LastTransactionNotAllowedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00012, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected LastTransactionNotAllowedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00013 : Represents Deserialization of response failed
    /// </summary>
    [Serializable]
    public class DeserializationFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DeserializationFailedException(string message) : base(PaymentErrorCodes.PG_00013, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DeserializationFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00013, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DeserializationFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00014 : Represents Donation Failed. No response received.
    /// </summary>
    [Serializable]
    public class DonationFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DonationFailedException(string message) : base(PaymentErrorCodes.PG_00014, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DonationFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00014, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DonationFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00015 : Payment failed: could not receive response
    /// </summary>
    [Serializable]
    public class PaymentFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentFailedException(string message) : base(PaymentErrorCodes.PG_00015, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00015, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }


    }

    /// <summary>
    /// PG_00017 : Invalid Transaction Type
    /// </summary>
    [Serializable]
    public class InvalidTransactionTypeException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidTransactionTypeException(string message) : base(PaymentErrorCodes.PG_00017, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidTransactionTypeException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00017, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InvalidTransactionTypeException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00016 : Response Conversion Failed Exception
    /// </summary>
    [Serializable]
    public class ResponseConversionFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ResponseConversionFailedException(string message) : base(PaymentErrorCodes.PG_00016, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ResponseConversionFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00016, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ResponseConversionFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00018 : Represents Transaction Failed.No response received.
    /// </summary>
    [Serializable]
    public class InvalidResponseException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidResponseException(string message) : base(PaymentErrorCodes.PG_00018, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidResponseException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00018, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InvalidResponseException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00019 : PreAuthorization Not Supported Exception
    /// </summary>
    [Serializable]
    public class PreAuthorizationNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PreAuthorizationNotSupportedException(string message) : base(PaymentErrorCodes.PG_00019, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PreAuthorizationNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00019, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PreAuthorizationNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00020 : Authorization not supported Exception
    /// </summary>
    [Serializable]
    public class AuthorizationNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AuthorizationNotSupportedException(string message) : base(PaymentErrorCodes.PG_00020, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AuthorizationNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00020, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected AuthorizationNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00021 : Sale not Supported Exception
    /// </summary>
    [Serializable]
    public class SaleNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SaleNotSupportedException(string message) : base(PaymentErrorCodes.PG_00021, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SaleNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00021, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SaleNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00022 : Sale Transaction Failed.
    /// </summary>
    [Serializable]
    public class SaleTransactionFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SaleTransactionFailedException(string message) : base(PaymentErrorCodes.PG_00022, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SaleTransactionFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00022, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SaleTransactionFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00023 : Authorization Failed
    /// </summary>
    [Serializable]
    public class AuthorizationFailed : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AuthorizationFailed(string message) : base(PaymentErrorCodes.PG_00023, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AuthorizationFailed(string message, Exception innerException) : base(PaymentErrorCodes.PG_00023, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected AuthorizationFailed(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00024 : Represents Transaction Failed.No response received.
    /// </summary>
    [Serializable]
    public class PreAuthorizationFailed : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PreAuthorizationFailed(string message) : base(PaymentErrorCodes.PG_00024, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PreAuthorizationFailed(string message, Exception innerException) : base(PaymentErrorCodes.PG_00024, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PreAuthorizationFailed(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00025 : Represents Transaction Failed.No response received.
    /// </summary>
    [Serializable]
    public class DonationNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DonationNotSupportedException(string message) : base(PaymentErrorCodes.PG_00025, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DonationNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00025, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DonationNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00026 : Customer copy printing not Supported
    /// </summary>
    [Serializable]
    public class CustomerCopyNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public CustomerCopyNotSupportedException(string message) : base(PaymentErrorCodes.PG_00026, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public CustomerCopyNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00026, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected CustomerCopyNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00027 : Merchant copy printing not Supported
    /// </summary>
    [Serializable]
    public class MerchantCopyNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public MerchantCopyNotSupportedException(string message) : base(PaymentErrorCodes.PG_00027, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public MerchantCopyNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00027, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected MerchantCopyNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00028 : Settlement not Supported
    /// </summary>
    [Serializable]
    public class SettlementNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SettlementNotSupportedException(string message) : base(PaymentErrorCodes.PG_00028, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SettlementNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00028, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SettlementNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00029 : Refund not Supported
    /// </summary>
    [Serializable]
    public class RefundNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public RefundNotSupportedException(string message) : base(PaymentErrorCodes.PG_00029, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public RefundNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00029, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected RefundNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00030 : StatusCheck not Supported
    /// </summary>
    [Serializable]
    public class StatusCheckNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public StatusCheckNotSupportedException(string message) : base(PaymentErrorCodes.PG_00030, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public StatusCheckNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00030, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected StatusCheckNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00031 : Tip option not supported
    /// </summary>
    [Serializable]
    public class TipOPtionNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TipOPtionNotSupportedException(string message) : base(PaymentErrorCodes.PG_00031, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TipOPtionNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00031, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected TipOPtionNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00032 : Unable to perform transaction 
    /// </summary>
    [Serializable]
    public class UnabletoPerformTransactionException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public UnabletoPerformTransactionException(string message) : base(PaymentErrorCodes.PG_00032, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public UnabletoPerformTransactionException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00032, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected UnabletoPerformTransactionException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00033 : DeviceNotConnectedException
    /// </summary>
    [Serializable]
    public class DeviceNotConnectedException : NonRetryableException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DeviceNotConnectedException(string message) : base(PaymentErrorCodes.PG_00033, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DeviceNotConnectedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00033, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DeviceNotConnectedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00034 : ConfigurationDataNotValid Exception
    /// </summary>
    [Serializable]
    public class ConfigurationDataNotValidException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ConfigurationDataNotValidException(string message) : base(PaymentErrorCodes.PG_00034, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ConfigurationDataNotValidException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00034, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ConfigurationDataNotValidException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00035 : Configuration Not Set Exception
    /// </summary>
    [Serializable]
    public class ConfigurationNotSetException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ConfigurationNotSetException(string message) : base(PaymentErrorCodes.PG_00035, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ConfigurationNotSetException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00035, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ConfigurationNotSetException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00036 : PaymentResponseDTOCreationException
    /// </summary>
    [Serializable]
    public class PaymentResponseDTOCreationException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentResponseDTOCreationException(string message) : base(PaymentErrorCodes.PG_00036, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentResponseDTOCreationException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00036, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentResponseDTOCreationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00037 : AdditionalResponseDataAccessException
    /// </summary>
    [Serializable]
    public class AdditionalResponseDataAccessException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AdditionalResponseDataAccessException(string message) : base(PaymentErrorCodes.PG_00037, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AdditionalResponseDataAccessException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00037, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected AdditionalResponseDataAccessException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00038 : ErrorFetchingPreAuthPaymentId
    /// </summary>
    [Serializable]
    public class ErrorFetchingPreAuthPaymentId : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ErrorFetchingPreAuthPaymentId(string message) : base(PaymentErrorCodes.PG_00038, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ErrorFetchingPreAuthPaymentId(string message, Exception innerException) : base(PaymentErrorCodes.PG_00038, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ErrorFetchingPreAuthPaymentId(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00039 : AuthTransactionExistException
    /// </summary>
    [Serializable]
    public class AuthTransactionExistException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AuthTransactionExistException(string message) : base(PaymentErrorCodes.PG_00039, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public AuthTransactionExistException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00039, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected AuthTransactionExistException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00040 : IndependentRefundFailedException
    /// </summary>
    [Serializable]
    public class IndependentRefundFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public IndependentRefundFailedException(string message) : base(PaymentErrorCodes.PG_00040, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public IndependentRefundFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00040, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected IndependentRefundFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00041 : VerificationFailedException
    /// </summary>
    [Serializable]
    public class VerificationFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public VerificationFailedException(string message) : base(PaymentErrorCodes.PG_00041, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public VerificationFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00041, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected VerificationFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00042 : TransactionIdNullException
    /// </summary>
    [Serializable]
    public class TransactionIdNullException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TransactionIdNullException(string message) : base(PaymentErrorCodes.PG_00042, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TransactionIdNullException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00042, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected TransactionIdNullException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00043 : PaymentSessionNotSupportedException
    /// </summary>
    [Serializable]
    public class PaymentSessionNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentSessionNotSupportedException(string message) : base(PaymentErrorCodes.PG_00043, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentSessionNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00043, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentSessionNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00044 : TransactionPaymentNotFoundException
    /// </summary>
    [Serializable]
    public class TransactionPaymentNotFoundException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TransactionPaymentNotFoundException(string message) : base(PaymentErrorCodes.PG_00044, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TransactionPaymentNotFoundException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00044, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected TransactionPaymentNotFoundException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00045 : ProcessPaymentResponseNotSupportedException
    /// </summary>
    [Serializable]
    public class ProcessPaymentResponseNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ProcessPaymentResponseNotSupportedException(string message) : base(PaymentErrorCodes.PG_00045, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ProcessPaymentResponseNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00045, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ProcessPaymentResponseNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00046 : SiteNotFoundException
    /// </summary>
    [Serializable]
    public class SiteNotFoundException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SiteNotFoundException(string message) : base(PaymentErrorCodes.PG_00046, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SiteNotFoundException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00046, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SiteNotFoundException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }
    
    /// <summary>
    /// PG_00047 : GetPaymentIdentifierNotSupportedException
    /// </summary>
    [Serializable]
    public class GetPaymentIdentifierNotSupportedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public GetPaymentIdentifierNotSupportedException(string message) : base(PaymentErrorCodes.PG_00047, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public GetPaymentIdentifierNotSupportedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00047, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected GetPaymentIdentifierNotSupportedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00048 : PaymentResponseNullException
    /// </summary>
    [Serializable]
    public class PaymentResponseNullException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentResponseNullException(string message) : base(PaymentErrorCodes.PG_00048, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentResponseNullException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00048, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentResponseNullException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00049 : PaymentResponseProcessingException
    /// </summary>
    [Serializable]
    public class PaymentResponseProcessingException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentResponseProcessingException(string message) : base(PaymentErrorCodes.PG_00049, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentResponseProcessingException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00049, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentResponseProcessingException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00050 : StatusCheckResponseNullException
    /// </summary>
    [Serializable]
    public class StatusCheckResponseNullException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public StatusCheckResponseNullException(string message) : base(PaymentErrorCodes.PG_00050, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public StatusCheckResponseNullException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00050, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected StatusCheckResponseNullException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00051 : ReadCardRequestException
    /// </summary>
    [Serializable]
    public class ReadCardRequestException : NonRetryableException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ReadCardRequestException(string message) : base(PaymentErrorCodes.PG_00051, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ReadCardRequestException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00051, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ReadCardRequestException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00052 : DeviceConnectionFailedException
    /// </summary>
    [Serializable]
    public class DeviceConnectionFailedException : NonRetryableException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DeviceConnectionFailedException(string message) : base(PaymentErrorCodes.PG_00052, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DeviceConnectionFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00052, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DeviceConnectionFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00053 : SessionKeyGenerationException
    /// </summary>
    [Serializable]
    public class SessionKeyGenerationException : NonRetryableException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SessionKeyGenerationException(string message) : base(PaymentErrorCodes.PG_00053, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SessionKeyGenerationException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00053, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SessionKeyGenerationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00054 : DisplayInDeviceException
    /// </summary>
    [Serializable]
    public class DisplayInDeviceException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DisplayInDeviceException(string message) : base(PaymentErrorCodes.PG_00054, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DisplayInDeviceException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00054, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DisplayInDeviceException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00055 : DisplayInDeviceException
    /// </summary>
    [Serializable]
    public class ReadCardCommandException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ReadCardCommandException(string message) : base(PaymentErrorCodes.PG_00055, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ReadCardCommandException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00055, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ReadCardCommandException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00056 : InvalidZipCodeException
    /// </summary>
    [Serializable]
    public class InvalidZipCodeException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidZipCodeException(string message) : base(PaymentErrorCodes.PG_00056, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidZipCodeException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00056, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InvalidZipCodeException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00057 : SaleCommandFailedException
    /// </summary>
    [Serializable]
    public class SaleCommandFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SaleCommandFailedException(string message) : base(PaymentErrorCodes.PG_00057, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public SaleCommandFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00057, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SaleCommandFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00058 : TransactionAdjustmentNotAllowedException
    /// </summary>
    [Serializable]
    public class TransactionAdjustmentNotAllowedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TransactionAdjustmentNotAllowedException(string message) : base(PaymentErrorCodes.PG_00058, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public TransactionAdjustmentNotAllowedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00058, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected TransactionAdjustmentNotAllowedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00059 : InquireResponseException
    /// </summary>
    [Serializable]
    public class InquireResponseException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InquireResponseException(string message) : base(PaymentErrorCodes.PG_00059, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InquireResponseException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00059, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InquireResponseException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00060 : InquireResponseException
    /// </summary>
    [Serializable]
    public class RefundResponseFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public RefundResponseFailedException(string message) : base(PaymentErrorCodes.PG_00060, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public RefundResponseFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00060, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected RefundResponseFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00061 : PaymentNotCompleteException
    /// </summary>
    [Serializable]
    public class PaymentNotCompleteException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentNotCompleteException(string message) : base(PaymentErrorCodes.PG_00061, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentNotCompleteException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00061, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentNotCompleteException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00062 : ResponseParsingFailedException
    /// </summary>
    [Serializable]
    public class ResponseParsingFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ResponseParsingFailedException(string message) : base(PaymentErrorCodes.PG_00062, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public ResponseParsingFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00062, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected ResponseParsingFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00063 : BinCommandFailedException
    /// </summary>
    [Serializable]
    public class BinCommandFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public BinCommandFailedException(string message) : base(PaymentErrorCodes.PG_00063, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public BinCommandFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00063, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected BinCommandFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00064 : PaymentDeclinedException
    /// </summary>
    [Serializable]
    public class PaymentDeclinedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentDeclinedException(string message) : base(PaymentErrorCodes.PG_00064, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentDeclinedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00064, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentDeclinedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00065 : InvalidSessionException
    /// </summary>
    [Serializable]
    public class InvalidSessionException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidSessionException(string message) : base(PaymentErrorCodes.PG_00065, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InvalidSessionException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00065, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InvalidSessionException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00066 : PaymentCancelledException
    /// </summary>
    [Serializable]
    public class PaymentCancelledException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentCancelledException(string message) : base(PaymentErrorCodes.PG_00066, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentCancelledException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00066, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentCancelledException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00067 : InsufficientRequestParamasException
    /// </summary>
    [Serializable]
    public class InsufficientRequestParamasException : NonRetryableException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InsufficientRequestParamasException(string message) : base(PaymentErrorCodes.PG_00067, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public InsufficientRequestParamasException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00067, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InsufficientRequestParamasException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00068 : DatetimeConversionFailedException
    /// </summary>
    [Serializable]
    public class DatetimeConversionFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DatetimeConversionFailedException(string message) : base(PaymentErrorCodes.PG_00068, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DatetimeConversionFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00068, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DatetimeConversionFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00069 : SignatureValidationFailureException
    /// </summary>
    [Serializable]
    public class SignatureValidationFailureException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of SignatureValidationFailureException.
        /// </summary>
        public SignatureValidationFailureException(string message) : base(PaymentErrorCodes.PG_00069, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of SignatureValidationFailureException.
        /// </summary>
        public SignatureValidationFailureException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00069, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected SignatureValidationFailureException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }
    
    /// <summary>
    /// PG_00070 : PaymentModeNotFoundException
    /// </summary>
    [Serializable]
    public class PaymentModeNotFoundException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of PaymentModeNotFoundException.
        /// </summary>
        public PaymentModeNotFoundException(string message) : base(PaymentErrorCodes.PG_00070, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of PaymentModeNotFoundException.
        /// </summary>
        public PaymentModeNotFoundException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00070, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentModeNotFoundException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00071 : CannotParseIdentifierException
    /// </summary>
    [Serializable]
    public class CannotParseIdentifierException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of CannotParseIdentifierException.
        /// </summary>
        public CannotParseIdentifierException(string message) : base(PaymentErrorCodes.PG_00071, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of CannotParseIdentifierException.
        /// </summary>
        public CannotParseIdentifierException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00071, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected CannotParseIdentifierException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00072 : GatewayResponseBodyEmptyException
    /// </summary>
    [Serializable]
    public class GatewayResponseBodyEmptyException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of GatewayResponseBodyEmptyException.
        /// </summary>
        public GatewayResponseBodyEmptyException(string message) : base(PaymentErrorCodes.PG_00072, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of GatewayResponseBodyEmptyException.
        /// </summary>
        public GatewayResponseBodyEmptyException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00072, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected GatewayResponseBodyEmptyException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00073 : GatewayResponseEmptyException
    /// </summary>
    [Serializable]
    public class GatewayResponseEmptyException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of GatewayResponseEmptyException.
        /// </summary>
        public GatewayResponseEmptyException(string message) : base(PaymentErrorCodes.PG_00073, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of GatewayResponseEmptyException.
        /// </summary>
        public GatewayResponseEmptyException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00073, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected GatewayResponseEmptyException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00074 : RefundResponseNullException
    /// </summary>
    [Serializable]
    public class RefundResponseNullException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of RefundResponseNullException.
        /// </summary>
        public RefundResponseNullException(string message) : base(PaymentErrorCodes.PG_00074, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of RefundResponseNullException.
        /// </summary>
        public RefundResponseNullException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00074, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected RefundResponseNullException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00075 : StatusAPIResponseNullException
    /// </summary>
    [Serializable]
    public class StatusAPIResponseNullException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of StatusAPIResponseNullException.
        /// </summary>
        public StatusAPIResponseNullException(string message) : base(PaymentErrorCodes.PG_00075, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of StatusAPIResponseNullException.
        /// </summary>
        public StatusAPIResponseNullException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00075, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected StatusAPIResponseNullException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00076 : RefundRequestNullException
    /// </summary>
    [Serializable]
    public class RefundRequestNullException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of RefundRequestNullException.
        /// </summary>
        public RefundRequestNullException(string message) : base(PaymentErrorCodes.PG_00076, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of RefundRequestNullException.
        /// </summary>
        public RefundRequestNullException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00076, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected RefundRequestNullException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00077 : BytoToStringConversionFailedException
    /// </summary>
    [Serializable]
    public class BytoToStringConversionFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of RefundRequestNullException.
        /// </summary>
        public BytoToStringConversionFailedException(string message) : base(PaymentErrorCodes.PG_00077, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of RefundRequestNullException.
        /// </summary>
        public BytoToStringConversionFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00077, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected BytoToStringConversionFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00078 : QRGenerationFailedException
    /// </summary>
    [Serializable]
    public class QRGenerationFailedException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of QRGenerationFailedException.
        /// </summary>
        public QRGenerationFailedException(string message) : base(PaymentErrorCodes.PG_00078, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of QRGenerationFailedException.
        /// </summary>
        public QRGenerationFailedException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00078, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected QRGenerationFailedException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00079 : InternetNotAvailableException
    /// </summary>
    [Serializable]
    public class InternetNotAvailableException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of InternetNotAvailableException.
        /// </summary>
        public InternetNotAvailableException(string message) : base(PaymentErrorCodes.PG_00079, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of InternetNotAvailableException.
        /// </summary>
        public InternetNotAvailableException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00079, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InternetNotAvailableException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00080 : InvalidCardDetailsException
    /// </summary>
    [Serializable]
    public class InvalidCardDetailsException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of InvalidCardDetailsException.
        /// </summary>
        public InvalidCardDetailsException(string message) : base(PaymentErrorCodes.PG_00080, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of InvalidCardDetailsException.
        /// </summary>
        public InvalidCardDetailsException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00080, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected InvalidCardDetailsException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00081 : PaymentTimeoutException
    /// </summary>
    [Serializable]
    public class PaymentTimeoutException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of PaymentTimeoutException.
        /// </summary>
        public PaymentTimeoutException(string message) : base(PaymentErrorCodes.PG_00081, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of PaymentTimeoutException.
        /// </summary>
        public PaymentTimeoutException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00081, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentTimeoutException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00082 : CaptchaValidationException
    /// </summary>
    [Serializable]
    public class CaptchaValidationException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of CaptchaValidationException.
        /// </summary>
        public CaptchaValidationException(string message) : base(PaymentErrorCodes.PG_00082, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of CaptchaValidationException.
        /// </summary>
        public CaptchaValidationException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00079, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected CaptchaValidationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00083 : PaymentTransactionNotFoundException
    /// </summary>
    [Serializable]
    public class PaymentTransactionNotFoundException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of PaymentTransactionNotFoundException.
        /// </summary>
        public PaymentTransactionNotFoundException(string message) : base(PaymentErrorCodes.PG_00083, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of PaymentTransactionNotFoundException.
        /// </summary>
        public PaymentTransactionNotFoundException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00083, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentTransactionNotFoundException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00084 : StoreNameEmptyException
    /// </summary>
    [Serializable]
    public class StoreNameEmptyException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of StoreNameEmptyException.
        /// </summary>
        public StoreNameEmptyException(string message) : base(PaymentErrorCodes.PG_00084, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of StoreNameEmptyException.
        /// </summary>
        public StoreNameEmptyException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00084, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected StoreNameEmptyException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    /// <summary>
    /// PG_00085 : StoreNameMismatchException
    /// </summary>
    [Serializable]
    public class StoreNameMismatchException : PaymentGatewayException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of StoreNameMismatchException.
        /// </summary>
        public StoreNameMismatchException(string message) : base(PaymentErrorCodes.PG_00085, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of StoreNameMismatchException.
        /// </summary>
        public StoreNameMismatchException(string message, Exception innerException) : base(PaymentErrorCodes.PG_00085, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected StoreNameMismatchException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }
}



