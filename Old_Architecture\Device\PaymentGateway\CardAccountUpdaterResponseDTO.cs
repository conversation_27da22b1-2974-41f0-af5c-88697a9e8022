﻿/********************************************************************************************
 * Project Name - Device
 * Description  - Data object of CardAccountUpdaterResponse
 * 
 **************
 **Version Log
 **************
 *Version     Date          Modified By             Remarks          
 *********************************************************************************************
 *2.152.2    27-Aug-2024   Vignesh Bhat      Modified: Enable Card Account Updater Service in Subscription Billing Program
 ********************************************************************************************/
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
    /// <summary>
    /// This is the CardAccountUpdater data object class. This acts as data holder for the CardAccountUpdater business object
    /// </summary>
    public class CardAccountUpdaterResponseDTO
    {
        public class UpdateDTO
        {
            [JsonProperty("pageNumber")]
            public int PageNumber { get; set; }

            [JsonProperty("perPageSize")]
            public int PerPageSize { get; set; }

            [JsonProperty("currentPageSize")]
            public int CurrentPageSize { get; set; }

            [JsonProperty("totalPages")]
            public int TotalPages { get; set; }

            [JsonProperty("totalUpdates")]
            public int TotalUpdates { get; set; }

            [JsonProperty("updatesStartDate")]
            public string UpdatesStartDate { get; set; }

            [JsonProperty("updatesEndDate")]
            public string UpdatesEndDate { get; set; }

            [JsonProperty("updates")]
            public List<Update> Updates { get; set; }
        }

        public class Update
        {
            [JsonProperty("dateupdated")]
            public string DateUpdated { get; set; }

            [JsonProperty("newtoken")]
            public string NewToken { get; set; }

            [JsonProperty("oldtoken")]
            public string OldToken { get; set; }

            [JsonProperty("newexpiry")]
            public string NewExpiry { get; set; }

            [JsonProperty("status")]
            public string Status { get; set; }

            [JsonProperty("binInfo")]
            public BinInfo BinInfo { get; set; }

            [JsonProperty("profiles")]
            public List<ProfileDTO> Profiles { get; set; }
        }

        public class BinInfo
        {
            [JsonProperty("country")]
            public string Country { get; set; }

            [JsonProperty("product")]
            public string Product { get; set; }

            [JsonProperty("bin")]
            public string Bin { get; set; }

            [JsonProperty("purchase")]
            public bool Purchase { get; set; }

            [JsonProperty("prepaid")]
            public bool Prepaid { get; set; }

            [JsonProperty("issuer")]
            public string Issuer { get; set; }

            [JsonProperty("cardusestring")]
            public string CardUseString { get; set; }

            [JsonProperty("gsa")]
            public bool Gsa { get; set; }

            [JsonProperty("corporate")]
            public bool Corporate { get; set; }

            [JsonProperty("fsa")]
            public bool Fsa { get; set; }

            [JsonProperty("subtype")]
            public string Subtype { get; set; }

            [JsonProperty("binlo")]
            public string BinLo { get; set; }

            [JsonProperty("binhi")]
            public string BinHi { get; set; }
        }
        public class ProfileDTO
        {
            [JsonProperty("profileid")]
            public string ProfileId { get; set; }

            [JsonProperty("acctid")]
            public string Acctid { get; set; }
        }
    }
}
