﻿using BCAPayments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.BCA
{
    class BCAResponse : Response
    {
        public string TransactionType { get; }
        public BCAResponse(byte[] responseBytes) : base(responseBytes)
        {
            TransactionType = GetField(4, 2);
        }


        // Field Extraction Methods
        public string GetTransactionType() => GetField(4, 2);
        public string GetTransactionAmount() => GetField(6, 12);
        public string GetOtherAmount() => GetField(18, 12);
        public string GetPAN() => GetField(30, 19);
        public string GetResponseCode() => GetField(53, 2);
        public string GetRRN() => GetField(55, 12);
        public string GetApprovalCode() => GetField(67, 6);
        public string GetDate() => GetField(73, 8);
        public string GetTime() => GetField(81, 6);
        public string GetMerchantId() => GetField(87, 15);
        public string GetTerminalId() => GetField(102, 8);
        //public string GetCardholderName() => GetField(108, 26); 
        //public string GetPANCashierCard() => GetField(134, 16); 
        public string GetInvoiceNumber() => GetField(153, 6);


    }
}
