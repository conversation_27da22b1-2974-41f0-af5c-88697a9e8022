﻿/********************************************************************************************
 * Project Name -  Adyen Hosted Payment Gateway                                                                     
 * Description  -  Class to handle the payment of Adyen Hosted Payment Gateway
 *
 **************
 **Version Log
  *Version     Date             Modified By                Remarks          
 *********************************************************************************************
 *2.190.0     27-Nov-2024      Prajwal Hegde             Created for Website
 ********************************************************************************************/

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Remoting.Channels;
using System.Text;
using System.Web;

namespace Semnox.Parafait.PaymentGatewayInterface.WebPaymentGateway.Adyen
{
    public class AdyenWebCommandHandler
    {
        private readonly Semnox.Parafait.logging.Logger log;
        private string _api_url, _api_key, _api_version, _channel, _currency_code, _country_code, _merchant_account, _adyenWebPostUrl, _store_name, _business_day_start_time, _pos_supplier_name, _service_integrator_name, _merchant_application_name, _merchant_os, _pincode, _merchant_os_version, _merchant_device_os_version;
        private int _currencyConversionFactor;

        private string[] blockedPaymentMethods = {
            //"wechatpayWeb",
            "wechatpayMiniProgram"
        };

        public AdyenWebCommandHandler(PaymentConfiguration paymentConfiguration, Semnox.Parafait.logging.Logger log)
        {
            this.log = log;
            log.LogMethodEntry(paymentConfiguration);
            this._api_url = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_GATEWAY_API_URL");
            this._api_key = paymentConfiguration.GetConfiguration("ADYEN_HOSTED_PAYMENT_API_KEY");
            this._api_version = paymentConfiguration.GetConfiguration("API_VERSION");
            this._channel = paymentConfiguration.GetConfiguration("CHANNEL");
            this._currency_code = paymentConfiguration.GetConfiguration("CURRENCY_CODE");
            this._country_code = paymentConfiguration.GetConfiguration("COUNTRY_CODE");
            this._merchant_account = paymentConfiguration.GetConfiguration("ADYEN_HOSTED_PAYMENT_MERCHANT_ID");
            string strCurrencyConversionFactor = paymentConfiguration.GetConfiguration("CURRENCY_CONVERSION_FACTOR");
            this._currencyConversionFactor = int.TryParse(strCurrencyConversionFactor, out this._currencyConversionFactor) ? this._currencyConversionFactor : 2;
            this._adyenWebPostUrl = paymentConfiguration.GetConfiguration("SUCCESS_RESPONSE_API_URL");
            this._store_name = paymentConfiguration.GetConfiguration("STORE_NAME");
            this._business_day_start_time = paymentConfiguration.GetConfiguration("BUSINESS_DAY_START_TIME");
            this._pos_supplier_name = paymentConfiguration.GetConfiguration("POS_SUPPLIER_NAME");
            this._service_integrator_name = paymentConfiguration.GetConfiguration("SERVICE_INTEGRATOR_NAME");
            this._merchant_application_name = paymentConfiguration.GetConfiguration("MERCHANT_APPLICATION_NAME");
            this._merchant_os = paymentConfiguration.GetConfiguration("MERCHANT_OS");
            this._pincode = paymentConfiguration.GetConfiguration("PINCODE");
            this._merchant_os_version = paymentConfiguration.GetConfiguration("MERCHANT_OS_VERSION");
            this._merchant_device_os_version = paymentConfiguration.GetConfiguration("MERCHANT_DEVICE_OS_VERSION");
            log.LogMethodExit();
        }

        public double GetFormattedAmount(double amount, bool convertToMinorUnit)
        {
            log.LogMethodEntry(amount, convertToMinorUnit);
            double formattedAmount = 0;
            try
            {
                if (amount <= 0 || _currencyConversionFactor < 0)
                {
                    log.Error("Invalid params provided to GetFormattedAmount()");
                    throw new Exception("Payment Request Failed");
                }

                switch (_currencyConversionFactor)
                {
                    case 0:
                        formattedAmount = convertToMinorUnit ? amount : amount;
                        break;
                    case 1:
                        formattedAmount = convertToMinorUnit ? amount * 10 : amount * 0.1;
                        break;
                    case 2:
                        formattedAmount = convertToMinorUnit ? amount * 100 : amount * 0.01;
                        break;
                    case 3:
                        formattedAmount = convertToMinorUnit ? amount * 1000 : amount * 0.001;
                        break;
                    default:
                        break;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(formattedAmount);
            return formattedAmount;
        }

        public string GetPaymentMethods(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);
            string responseFromServer;
            try
            {
                if (paymentRequestDTO == null)
                {
                    log.Error($"Failed to proceed with Get payment method call. Reason: No payment methods request present.");
                    throw new Exception("Payment request failed");
                }
                GetPaymentMethodsRequestDto requestDto = new GetPaymentMethodsRequestDto
                {
                    merchantAccount = _merchant_account,
                    blockedPaymentMethods = blockedPaymentMethods,
                    countryCode = _country_code,
                    channel = _channel,
                    shopperReference = !string.IsNullOrWhiteSpace(paymentRequestDTO.PaymentGatewayCustomerDTO.PGIdentifier) ? paymentRequestDTO.PaymentGatewayCustomerDTO.PGIdentifier : "", //paymentRequestDTO.PaymentGatewayCustomerDTO.PGIdentifier contains CustomerGuid detail passed
                    store = _store_name
                };

                string API_URL = $"{_api_url}/{_api_version}/paymentMethods";
                log.Debug($"GetPaymentMethods API_URL= {API_URL}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";

                string json = JsonConvert.SerializeObject(requestDto, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                log.Debug($"Final request for GetPaymentMethods request= {json}");

                byte[] data = Encoding.UTF8.GetBytes(json);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("X-API-Key", _api_key);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Info($"Response from GetPaymentMethods api call, responseFromServer= {responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();
            }
            catch (WebException webEx)
            {
                string errorMsg = GetErrorMessage(webEx);
                log.Error("GetPaymentMethods Failed. Reason: " + errorMsg);
                throw new Exception(errorMsg, webEx);
            }
            catch (Exception ex)
            {
                log.Error("GetPaymentMethods Failed. Reason: " + ex);
                throw;
            }
            log.LogMethodExit(responseFromServer);
            return responseFromServer;
        }

        public MakePaymentResponseDto MakePayment(string gatewayResponse, PaymentResponseDTO paymentResponseDTO)
        {
            log.LogMethodEntry(gatewayResponse, paymentResponseDTO);

            MakePaymentRequestDto makePaymentRequestDto = null;
            MakePaymentResponseDto makePaymentResponseDto = null;
            string responseFromServer;

            try
            {
                //Build payment request DTO
                makePaymentRequestDto = BuildPaymentRequestDTO(gatewayResponse, paymentResponseDTO);

                if (makePaymentRequestDto == null)
                {
                    log.Error($"Payment request DTO was empty");
                    throw new Exception("Payment failed.");
                }

                string API_URL = $"{_api_url}/{_api_version}/payments";
                log.Debug($"MakePayment API_URL={API_URL}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";

                string json = JsonConvert.SerializeObject(makePaymentRequestDto, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                log.Debug($"Final Request for MakePayment API Call= {json}");

                log.Debug("Begin to make /payments api call");
                byte[] data = Encoding.UTF8.GetBytes(json);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", _api_key);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"/Payments api call completed. responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();

                if (string.IsNullOrWhiteSpace(responseFromServer))
                {
                    log.Error("MakePayment(): responseFromServer was null");
                    throw new Exception("Error: Payment failed");
                }
                // deserialize the response received
                makePaymentResponseDto = JsonConvert.DeserializeObject<MakePaymentResponseDto>(responseFromServer);
                log.Debug("/Payments response after deserializing. makePaymentResponseDto: " + makePaymentResponseDto.ToString());
            }
            catch (WebException webEx)
            {
                string errorMsg = GetErrorMessage(webEx);
                log.Error(errorMsg);
                throw new Exception(errorMsg, webEx);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(makePaymentResponseDto);
            return makePaymentResponseDto;
        }

        public MakePaymentResponseDto MakePaymentDetails(dynamic tempObj)
        {
            log.LogMethodEntry(tempObj);
            PaymentDetailsRequestDto paymentDetailsRequestDto = null;
            MakePaymentResponseDto makePaymentDetailsResponse = null;
            string responseFromServer;

            try
            {
                paymentDetailsRequestDto = BuildPaymentDetailsRequestDTO(tempObj);

                if (paymentDetailsRequestDto == null)
                {
                    log.Error($"MakePaymentDetails(): makePaymentRequestDto was empty.");
                    throw new Exception("Payment failed.");
                }

                string API_URL = $"{_api_url}/{_api_version}/payments/details";
                log.Debug($"MakePaymentDetails API_URL={API_URL}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";

                string json = JsonConvert.SerializeObject(paymentDetailsRequestDto, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                log.Debug($"MakePaymentDetails(): request json={json}");

                log.Debug("Begin /payment/details API call...");
                byte[] data = Encoding.UTF8.GetBytes(json);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", _api_key);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"MakePaymentDetails(): responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();

                if (string.IsNullOrWhiteSpace(responseFromServer))
                {
                    log.Error("MakePaymentDetails(): responseFromServer was null");
                    throw new Exception("Error: Payment failed");
                }

                // deserialize the response received
                makePaymentDetailsResponse = JsonConvert.DeserializeObject<MakePaymentResponseDto>(responseFromServer);
                log.Debug("Payment details response after deserializing. makePaymentDetailsResponse:" + makePaymentDetailsResponse.ToString());
            }
            catch (WebException webEx)
            {
                string errorMsg = GetErrorMessage(webEx);
                log.Error(errorMsg);
                throw new Exception(errorMsg, webEx);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(makePaymentDetailsResponse);
            return makePaymentDetailsResponse;
        }

        private MakePaymentRequestDto BuildPaymentRequestDTO(string gatewayResponse, PaymentResponseDTO paymentResponseDTO)
        {
            log.LogMethodEntry(gatewayResponse, paymentResponseDTO);
            MakePaymentRequestDto makePaymentRequestDto = null;

            try
            {
                dynamic tempObj = JsonConvert.DeserializeObject(gatewayResponse);
                if (tempObj == null)
                {
                    log.Error("BuildPaymentRequestDTO(): tempObj was null");
                    throw new Exception("Payment Failed");
                }

                makePaymentRequestDto = new MakePaymentRequestDto();

                // Set basic payment information
                makePaymentRequestDto.merchantAccount = _merchant_account;
                makePaymentRequestDto.reference = paymentResponseDTO.InvoiceNo;

                // Set amount
                if (tempObj.amount != null)
                {
                    makePaymentRequestDto.amount = new Amount
                    {
                        currency = tempObj.amount.currency?.ToString() ?? _currency_code,
                        value = Convert.ToInt64(tempObj.amount.value)
                    };
                }

                // Set payment method
                if (tempObj.paymentMethod != null)
                {
                    makePaymentRequestDto.paymentMethod = tempObj.paymentMethod;
                }

                // Set return URL for callback flow
                if (tempObj.returnUrl != null)
                {
                    makePaymentRequestDto.returnUrl = tempObj.returnUrl.ToString();
                }

                // Set shopper information
                if (tempObj.shopperReference != null)
                {
                    makePaymentRequestDto.shopperReference = tempObj.shopperReference.ToString();
                }

                if (tempObj.shopperEmail != null)
                {
                    makePaymentRequestDto.shopperEmail = tempObj.shopperEmail.ToString();
                }

                // Set browser info for web payments
                if (tempObj.browserInfo != null)
                {
                    makePaymentRequestDto.browserInfo = tempObj.browserInfo;
                }

                // Set origin for web payments
                if (tempObj.origin != null)
                {
                    makePaymentRequestDto.origin = tempObj.origin.ToString();
                }

                log.Debug($"Built payment request DTO: {JsonConvert.SerializeObject(makePaymentRequestDto)}");
            }
            catch (Exception ex)
            {
                log.Error("Error building payment request DTO", ex);
                throw;
            }

            log.LogMethodExit(makePaymentRequestDto);
            return makePaymentRequestDto;
        }

        /// <summary>
        /// Processes refund request through Adyen API
        /// </summary>
        /// <param name="originalPspReference">Original payment PSP reference</param>
        /// <param name="refundRequest">Refund request details</param>
        /// <returns>RefundResponseDto</returns>
        public RefundResponseDto ProcessRefund(string originalPspReference, RefundRequestDto refundRequest)
        {
            log.LogMethodEntry(originalPspReference, refundRequest);
            RefundResponseDto refundResponse = null;

            try
            {
                // Validate input parameters
                if (string.IsNullOrWhiteSpace(originalPspReference))
                {
                    log.Error("ProcessRefund(): originalPspReference is null or empty");
                    throw new ArgumentException("Original PSP reference is required for refund processing");
                }

                if (refundRequest == null)
                {
                    log.Error("ProcessRefund(): refundRequest is null");
                    throw new ArgumentException("Refund request is required for refund processing");
                }

                // Construct the refund URL dynamically using the original PSP reference
                // For full refunds, use reversals endpoint; for partial refunds, use refunds endpoint
                string refundUrl = $"{_api_url}/{_api_version}/payments/{originalPspReference}/reversals";
                log.Debug($"ProcessRefund(): Constructed refund URL={refundUrl}");

                string json = JsonConvert.SerializeObject(refundRequest, Formatting.None,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                log.Debug($"ProcessRefund(): json={json}");

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)WebRequest.Create(refundUrl);
                myHttpWebRequest.Method = "POST";
                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", _api_key);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());

                byte[] data = Encoding.UTF8.GetBytes(json);
                myHttpWebRequest.ContentLength = data.Length;

                using (Stream requestStream = myHttpWebRequest.GetRequestStream())
                {
                    requestStream.Write(data, 0, data.Length);
                }

                using (HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse())
                using (Stream responseStream = myHttpWebResponse.GetResponseStream())
                using (StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default))
                {
                    string responseFromServer = myStreamReader.ReadToEnd();
                    log.Debug($"ProcessRefund(): responseFromServer={responseFromServer}");

                    if (string.IsNullOrWhiteSpace(responseFromServer))
                    {
                        log.Error("ProcessRefund(): responseFromServer was null");
                        throw new Exception("Error: Refund failed - no response received");
                    }

                    refundResponse = JsonConvert.DeserializeObject<RefundResponseDto>(responseFromServer);
                    log.Debug("Refund response after deserializing: " + refundResponse?.ToString());
                }
            }
            catch (WebException webEx)
            {
                string errorMsg = GetErrorMessage(webEx);
                log.Error(errorMsg);
                throw new Exception(errorMsg, webEx);
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }

            log.LogMethodExit(refundResponse);
            return refundResponse;
        }

        /// <summary>
        /// Gets the merchant account from configuration
        /// </summary>
        /// <returns>Merchant account string</returns>
        public string GetMerchantAccount()
        {
            return _merchant_account;
        }

        /// <summary>
        /// Formats amount for Adyen API (converts to minor units if needed)
        /// </summary>
        /// <param name="amount">Amount to format</param>
        /// <param name="convertToMinorUnit">Whether to convert to minor units (cents)</param>
        /// <returns>Formatted amount</returns>
        public long GetFormattedAmount(decimal amount, bool convertToMinorUnit = true)
        {
            if (convertToMinorUnit)
            {
                return (long)(amount * 100); // Convert to cents
            }
            return (long)amount;
        }

        /// <summary>
        /// Maps Adyen result code to payment status
        /// </summary>
        /// <param name="paymentResponse">Adyen payment response</param>
        /// <returns>Payment status string</returns>
        public string GetPaymentStatusFromResultCode(MakePaymentResponseDto paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);
            string txStatus = string.Empty;
            try
            {
                if (paymentResponse != null && !string.IsNullOrWhiteSpace(paymentResponse.resultCode))
                {
                    string resultCode = paymentResponse.resultCode.ToUpper();
                    log.Debug($"Mapping Adyen result code: '{resultCode}' to payment status");

                    switch (resultCode)
                    {
                        case "AUTHORISED":
                            txStatus = AdyenWebTxStatus.SUCCESS.ToString();
                            log.Info($"Payment authorised successfully - Status: {txStatus}");
                            break;
                        case "PENDING":
                        case "RECEIVED":
                            txStatus = AdyenWebTxStatus.PENDING.ToString();
                            log.Info($"Payment pending - Result code: {resultCode}, Status: {txStatus}");
                            break;
                        case "REFUSED":
                        case "CANCELLED":
                        case "ERROR":
                            txStatus = AdyenWebTxStatus.FAILED.ToString();
                            log.Info($"Payment failed - Result code: {resultCode}, Status: {txStatus}");
                            break;
                        case "CHALLENGESHOPPER":
                        case "IDENTIFYSHOPPER":
                        case "PRESENTTOSHOPPER":
                        case "REDIRECTSHOPPER":
                            txStatus = AdyenWebTxStatus.PRE_AUTH.ToString();
                            log.Info($"Payment requires additional authentication - Result code: {resultCode}, Status: {txStatus}");
                            break;
                        default:
                            log.Debug($"Unknown result code: {resultCode}, defaulting to PENDING status");
                            txStatus = AdyenWebTxStatus.PENDING.ToString();
                            break;
                    }
                }
                else
                {
                    log.Error("Payment response or result code was null/empty");
                    txStatus = AdyenWebTxStatus.FAILED.ToString();
                }
            }
            catch (Exception ex)
            {
                log.Error("Error mapping payment status from result code", ex);
                txStatus = AdyenWebTxStatus.FAILED.ToString();
            }
            log.LogMethodExit(txStatus);
            return txStatus;
        }

        private string GetErrorMessage(WebException ex)
        {
            log.LogMethodEntry(ex);
            try
            {
                using (var stream = ex?.Response?.GetResponseStream())
                    if (stream != null)
                        using (var reader = new StreamReader(stream))
                        {
                            string webException = reader.ReadToEnd();
                            log.Error(webException);
                            dynamic errorObj = JsonConvert.DeserializeObject(webException);
                            if (errorObj != null)
                            {
                                log.LogMethodExit($"{errorObj.errorCode} | {errorObj.message}");
                                return $"{errorObj.errorCode} | {errorObj.message}";
                            }
                            else
                            {
                                log.LogMethodExit(ex.Message);
                                return ex.Message;
                            }
                        }
                log.LogMethodExit(ex.Message);
                return ex.Message;

            }
            catch (Exception exp)
            {
                log.Error(exp);
                throw;
            }
        }

        /// <summary>
        /// Makes a partial refund request to Adyen
        /// </summary>
        /// <param name="refundRequestDto">Partial refund request details</param>
        /// <returns>Refund response from Adyen</returns>
        public RefundResponseDto MakePartialRefund(PartialRefundRequestDto refundRequestDto)
        {
            log.LogMethodEntry(refundRequestDto);
            log.Info($"Processing partial refund request - Amount: {refundRequestDto?.amount?.value}, Currency: {refundRequestDto?.amount?.currency}");
            RefundResponseDto refundResponse = null;
            try
            {
                if (refundRequestDto == null)
                {
                    log.Error($"MakePartialRefund(): refundRequestDto was empty.");
                    throw new Exception("Refund failed.");
                }

                if (string.IsNullOrWhiteSpace(refundRequestDto.merchantAccount) ||
                      string.IsNullOrWhiteSpace(refundRequestDto.reference) ||
                      string.IsNullOrWhiteSpace(refundRequestDto.paymentId))
                {
                    log.Error($"MakePartialRefund(): Insufficient params provided - MerchantAccount: {!string.IsNullOrWhiteSpace(refundRequestDto.merchantAccount)}, Reference: {!string.IsNullOrWhiteSpace(refundRequestDto.reference)}, PaymentId: {!string.IsNullOrWhiteSpace(refundRequestDto.paymentId)}");
                    throw new Exception("Refund failed.");
                }

                string API_URL = $"{_api_url}/{_api_version}/payments/{refundRequestDto.paymentId}/refunds";
                log.Debug($"API_URL={API_URL}");
                log.Info($"Making partial refund API call to Adyen for payment: {refundRequestDto.paymentId}");
                string responseFromServer;

                refundRequestDto.paymentId = null; // clear the paymentId from refundRequestDto

                HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(API_URL);
                myHttpWebRequest.Method = "POST";

                string json = JsonConvert.SerializeObject(refundRequestDto, Formatting.None, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });
                log.Debug($"MakePartialRefund(): request json={json}");

                byte[] data = Encoding.UTF8.GetBytes(json);

                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Headers.Add("x-API-key", _api_key);
                myHttpWebRequest.Headers.Add("Idempotency-Key", Guid.NewGuid().ToString());
                myHttpWebRequest.ContentLength = data.Length;

                log.Debug("Sending partial refund request to Adyen");
                Stream requestStream = myHttpWebRequest.GetRequestStream();
                requestStream.Write(data, 0, data.Length);
                requestStream.Close();
                HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse();
                Stream responseStream = myHttpWebResponse.GetResponseStream();
                StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default);
                responseFromServer = myStreamReader.ReadToEnd();
                log.Debug($"MakePartialRefund(): responseFromServer={responseFromServer}");
                myStreamReader.Close();
                responseStream.Close();
                myHttpWebResponse.Close();

                if (string.IsNullOrWhiteSpace(responseFromServer))
                {
                    log.Error("MakePartialRefund(): responseFromServer was null");
                    throw new Exception("Error: Partial refund failed");
                }

                // deserialize the response received
                refundResponse = JsonConvert.DeserializeObject<RefundResponseDto>(responseFromServer);
                log.Info($"Partial refund response received - Status: {refundResponse?.status}, PSP Reference: {refundResponse?.pspReference}");
            }
            catch (WebException webEx)
            {
                string errorMsg = GetErrorMessage(webEx);
                log.Error($"WebException during partial refund: {errorMsg}");
                throw new Exception(errorMsg, webEx);
            }
            catch (Exception ex)
            {
                log.Error($"Exception during partial refund: {ex.Message}", ex);
                throw;
            }
            log.LogMethodExit(refundResponse);
            return refundResponse;
        }

        public PaymentDetailsRequestDto BuildPaymentDetailsRequestDTO(dynamic tempObj)
        {
            log.LogMethodEntry(tempObj);
            // DOCS: https://docs.adyen.com/api-explorer/Checkout/latest/post/payments/details
            PaymentDetailsRequestDto paymentDetailsRequestDto = null;
            string paymentData = null;
            Details details = null;

            try
            {
                string requestDtoStr = JsonConvert.SerializeObject(tempObj);
                log.Debug($"MakePaymentDetailsCall(): requestStr={requestDtoStr}");
                if (tempObj == null)
                {
                    log.Error("MakePaymentDetailsCall(): tempObj was null");
                    throw new Exception("Payment Failed");
                }

                if (tempObj.redirectResult == null)
                {
                    log.Error("MakePaymentDetailsCall(): tempObj.redirectResult was null");
                    throw new Exception("Payment Failed");
                }

                log.Debug("Getting paymentData");
                if (tempObj.paymentData != null || !string.IsNullOrWhiteSpace(Convert.ToString(tempObj.paymentData)))
                {
                    log.Debug("paymentData present");
                    paymentData = Convert.ToString(tempObj.paymentData);
                }
                else
                {
                    log.Debug("no paymentData present");
                }

                log.Debug("Checking whether tempObj.redirectResult is in JSON or Plain text format...");
                if (Convert.ToString(tempObj.redirectResult).Substring(0, 2) == "{\"")
                {
                    // json
                    RedirectResult redirectResultObj = JsonConvert.DeserializeObject<RedirectResult>(Convert.ToString(tempObj.redirectResult));
                    log.Debug($"redirectResult in json format= {redirectResultObj.ToString()}");
                    if (redirectResultObj != null)
                    {
                        details = redirectResultObj.details;
                        log.Debug($"details= {details.ToString()}");
                    }
                }
                else
                {
                    // plain text
                    log.Debug($"redirectResult in plain text format= {Convert.ToString(tempObj.redirectResult)}");
                    details = new Details
                    {
                        redirectResult = Convert.ToString(tempObj.redirectResult)
                    };
                    log.Debug($"details= {details.ToString()}");
                }

                //Build paymentDetailsRequestDto
                paymentDetailsRequestDto = new PaymentDetailsRequestDto
                {
                    details = details,
                    paymentData = paymentData
                };
                log.Debug("Request DTO for /Payment/details= " + paymentDetailsRequestDto);
            }
            catch (WebException webEx)
            {
                string errorMsg = GetErrorMessage(webEx);
                log.Error(errorMsg);
                throw;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(paymentDetailsRequestDto);
            return paymentDetailsRequestDto;
        }

    }
}
