﻿/******************************************************************************************************************************
 * Project Name - PaymentGatewayInterface
 * Description  - PaymentResponseDTO
 * 
 **************
 **Version Log
 **************
 *Version     Date          Modified By             Remarks          
 *******************************************************************************************************************************
 *2.190.0     24-Sep-2024   Amrutha                   Created
 ******************************************************************************************************************************/

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
   
    public class PaymentResponseDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string invoiceNo;
        private string tokenID;
        private string recordNo;
        private string dSIXReturnCode;
        private string textResponse;
        private string acctNo;
        private string cardType;
        private string tranCode;
        private string refNo;
        private string purchase;
        private string authorize;
        private DateTime transactionDatetime;
        private string authCode;
        private string processData;
        private string responseOrigin;
        private string userTraceData;
        private string captureStatus;
        private string acqRefData;
        private string tipAmount;
        private string customerCopy;
        private string merchantCopy;
        private string customerCardProfileId;
        private string status;
        private string creditCardName;
        private string nameOnCreditCard;
        private string creditCardExpiry;
        private decimal amount;

        public PaymentResponseDTO()
        {
            log.LogMethodEntry();
            log.LogMethodExit();
        }


        /// <summary>
        /// Constructor with Required data fields
        /// </summary>
        public PaymentResponseDTO( string invoiceNo, string tokenID, string recordNo, string dSIXReturnCode,
                                    string textResponse, string acctNo, string cardType, string tranCode, string refNo, string purchase,
                                    string authorize, DateTime transactionDatetime, string authCode, string processData, string responseOrigin,
                                    string userTraceData, string captureStatus, string acqRefData, string tipAmount, string customerCopy,
                                    string merchantCopy, string customerCardProfileId, string status, string creditCardName,
                                    string nameOnCreditCard, string creditCardExpiry, decimal amount)
            : this()
        {
            log.LogMethodEntry(invoiceNo, tokenID, recordNo, dSIXReturnCode, 
                                textResponse, acctNo, cardType, tranCode, refNo, purchase,
                                authorize, transactionDatetime, authCode, processData, responseOrigin, userTraceData, captureStatus, "acqRefData",
                                tipAmount, customerCopy, merchantCopy, customerCardProfileId, status, "creditCardName", "nameOnCreditCard", "creditCardExpiry", amount);

            this.invoiceNo = invoiceNo;
            this.tokenID = tokenID;
            this.recordNo = recordNo;
            this.dSIXReturnCode = dSIXReturnCode;
            this.textResponse = textResponse;
            this.acctNo = acctNo;
            this.cardType = cardType;
            this.tranCode = tranCode;
            this.refNo = refNo;
            this.purchase = purchase;
            this.authorize = authorize;
            this.transactionDatetime = transactionDatetime;
            this.authCode = authCode;
            this.processData = processData;
            this.responseOrigin = responseOrigin;
            this.userTraceData = userTraceData;
            this.captureStatus = captureStatus;
            this.acqRefData = acqRefData;
            this.tipAmount = tipAmount;
            this.customerCopy = customerCopy;
            this.merchantCopy = merchantCopy;
            this.customerCardProfileId = customerCardProfileId;
            this.status = status;
            this.creditCardName = creditCardName;
            this.nameOnCreditCard = nameOnCreditCard;
            this.creditCardExpiry = creditCardExpiry;
            this.amount = amount;
            log.LogMethodEntry();
        }


        /// <summary>
        /// Get/Set method of the InvoiceNo field
        /// </summary>
        public string InvoiceNo
        {
            get
            {
                return invoiceNo;
            }

            set
            {
                //IsChanged = true;
                invoiceNo = value;
            }
        }

        /// <summary>
        /// Get/Set method of the TokenID field
        /// </summary>
        public string TokenID
        {
            get
            {
                return tokenID;
            }

            set
            {
                //IsChanged = true;
                tokenID = value;
            }
        }

        /// <summary>
        /// Get/Set method of the RecordNo field
        /// </summary>
        public string RecordNo
        {
            get
            {
                return recordNo;
            }

            set
            {
                //IsChanged = true;
                recordNo = value;
            }
        }

        /// <summary>
        /// Get/Set method of the DSIXReturnCode field
        /// </summary>
        public string DSIXReturnCode
        {
            get
            {
                return dSIXReturnCode;
            }

            set
            {
                //IsChanged = true;
                dSIXReturnCode = value;
            }
        }

        /// <summary>
        /// Get/Set method of the TextResponse field
        /// </summary>
        public string TextResponse
        {
            get
            {
                return textResponse;
            }

            set
            {
                //IsChanged = true;
                textResponse = value;
            }
        }

        /// <summary>
        /// Get/Set method of the AcctNo field
        /// </summary>
        public string AcctNo
        {
            get
            {
                return acctNo;
            }

            set
            {
                //IsChanged = true;
                acctNo = value;
            }
        }

        /// <summary>
        /// Get/Set method of the CardType field
        /// </summary>
        public string CardType
        {
            get
            {
                return cardType;
            }

            set
            {
                //IsChanged = true;
                cardType = value;
            }
        }

        /// <summary>
        /// Get/Set method of the TranCode field
        /// </summary>
        public string TranCode
        {
            get
            {
                return tranCode;
            }

            set
            {
                //IsChanged = true;
                tranCode = value;
            }
        }

        /// <summary>
        /// Get/Set method of the RefNo field
        /// </summary>
        public string RefNo
        {
            get
            {
                return refNo;
            }

            set
            {
                //IsChanged = true;
                refNo = value;
            }
        }

        /// <summary>
        /// Get/Set method of the Purchase field
        /// </summary>
        public string Purchase
        {
            get
            {
                return purchase;
            }

            set
            {
                //IsChanged = true;
                purchase = value;
            }
        }

        /// <summary>
        /// Get/Set method of the Authorize field
        /// </summary>
        public string Authorize
        {
            get
            {
                return authorize;
            }

            set
            {
                //IsChanged = true;
                authorize = value;
            }
        }

        /// <summary>
        /// Get/Set method of the TransactionDatetime field
        /// </summary>
        public DateTime TransactionDatetime
        {
            get
            {
                return transactionDatetime;
            }

            set
            {
                //IsChanged = true;
                transactionDatetime = value;
            }
        }

        /// <summary>
        /// Get/Set method of the AuthCode field
        /// </summary>
        public string AuthCode
        {
            get
            {
                return authCode;
            }

            set
            {
                //IsChanged = true;
                authCode = value;
            }
        }

        /// <summary>
        /// Get/Set method of the ProcessData field
        /// </summary>
        public string ProcessData
        {
            get
            {
                return processData;
            }

            set
            {
                //IsChanged = true;
                processData = value;
            }
        }

        /// <summary>
        /// Get/Set method of the ResponseOrigin field
        /// </summary>
        public string ResponseOrigin
        {
            get
            {
                return responseOrigin;
            }

            set
            {
                //IsChanged = true;
                responseOrigin = value;
            }
        }

        /// <summary>
        /// Get/Set method of the UserTraceData field
        /// </summary>
        public string UserTraceData
        {
            get
            {
                return userTraceData;
            }

            set
            {
                //IsChanged = true;
                userTraceData = value;
            }
        }

        /// <summary>
        /// Get/Set method of the CaptureStatus field
        /// </summary>
        public string CaptureStatus
        {
            get
            {
                return captureStatus;
            }

            set
            {
                //IsChanged = true;
                captureStatus = value;
            }
        }

        /// <summary>
        /// Get/Set method of the AcqRefData field
        /// </summary>
        public string AcqRefData
        {
            get
            {
                return acqRefData;
            }

            set
            {
                //IsChanged = true;
                acqRefData = value;
            }
        }
        public string CreditCardName
        {
            get
            {
                return creditCardName;
            }
            set
            {
                //IsChanged = true;
                creditCardName = value;
            }
        }
        public string NameOnCreditCard
        {
            get
            {
                return nameOnCreditCard;
            }
            set
            {
                //IsChanged = true;
                nameOnCreditCard = value;
            }
        }
        public string CreditCardExpiry
        {
            get
            {
                return creditCardExpiry;
            }
            set
            {
                //IsChanged = true;
                creditCardExpiry = value;
            }
        }
        public decimal Amount
        {
            get
            {
                return amount;
            }
            set
            {
                //IsChanged = true;
                amount = value;
            }
        }

        /// <summary>
        /// Get method of the TipAmount field
        /// </summary>
        public string TipAmount
        {
            get
            {
                return tipAmount;
            }

            set
            {
                //IsChanged = true;
                tipAmount = value;
            }
        }
     

        /// <summary>
        ///  Get/Set method of the CustomerCopy field
        /// </summary>
        public string CustomerCopy
        {
            get { return customerCopy; }
            set { customerCopy = value; }
        }
        /// <summary>
        ///  Get/Set method of the MerchantCopy field
        /// </summary>
        public string MerchantCopy
        {
            get { return merchantCopy; }
            set { merchantCopy = value; }
        }
        /// <summary>
        ///  Get/Set method of the CustomerCardProfileId field
        /// </summary>
        public string CustomerCardProfileId
        {
            get { return customerCardProfileId; }
            set { customerCardProfileId = value; } //IsChanged = true; }
        }

        /// <summary>
        ///  Get/Set method of the Status field
        /// </summary>
        public string Status
        {
            get { return status; }
            set { status = value; } //IsChanged = true; }
        }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }

    }

    /// <summary>
    /// Additional response data for complex payment flows (e.g., 3DS authentication, redirects)
    /// When not null, indicates additional actions are required before completing the payment
    /// </summary>
    public class AdditionalResponseDTO
    {
        /// <summary>
        /// Type of additional action required (e.g., "redirect", "threeDS2", "blik")
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// URL for redirect-based actions
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// HTTP method for the action (GET, POST)
        /// </summary>
        public string Method { get; set; }

        /// <summary>
        /// Payment data to be included in the action
        /// </summary>
        public string PaymentData { get; set; }

        /// <summary>
        /// Additional data required for the action
        /// </summary>
        public Dictionary<string, object> Data { get; set; }

        /// <summary>
        /// Indicates whether this response requires additional user action
        /// </summary>
        public bool RequiresAction => !string.IsNullOrWhiteSpace(Type);

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
