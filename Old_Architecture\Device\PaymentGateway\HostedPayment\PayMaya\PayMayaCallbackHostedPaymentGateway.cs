﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net;
using System.Text;
using System.Web;
using System.Collections.Specialized;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PayMaya
{
    public class PayMayaCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string PUBLIC_KEY;
        private string SECRET_KEY;
        private string POST_URL;
        private string BASE_URL;
        private string CURRENCY;

        const string SUCCESS = "PAYMENT_SUCCESS";
        const string FAILED = "PAYMENT_FAILED";
        const string PAYMENT_EXPIRED = "PAYMENT_EXPIRED";
        const string PAYMENT_CANCELLED = "PAYMENT_CANCELLED";
        const string PENDING = "PENDING_PAYMENT";
        const string PROCESSING = "PAYMENT_PROCESSING";
        const string PENDING_TOKEN = "PENDING_TOKEN";
        const string AUTH_FAILED = "AUTH_FAILED";
        const string AUTH_SUCCESS = "AUTH_SUCCESS";
        const string AUTHENTICATING = "AUTHENTICATING";
        const string FOR_AUTHENTICATION = "FOR_AUTHENTICATION";
        const string REFUNDED = "REFUNDED";
        const string VOIDED = "VOIDED";
        private string paymentPageLink;
        string objectGuid;

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";

        PayMayaHostedCommandHandler payMayaHostedCommandHandler;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "PAYMENT_SUCCESS", PaymentStatusType.SUCCESS },
            { "PAYMENT_PROCESSING", PaymentStatusType.PENDING },
            { "PENDING_TOKEN", PaymentStatusType.PENDING },
            { "PENDING_PAYMENT", PaymentStatusType.PENDING },
            { "FOR_AUTHENTICATION", PaymentStatusType.PENDING },
            { "AUTHENTICATING", PaymentStatusType.PENDING },
            { "AUTH_SUCCESS", PaymentStatusType.PENDING },
            { "AUTH_FAILED", PaymentStatusType.FAILED },
            { "PAYMENT_EXPIRED", PaymentStatusType.FAILED },
            { "PAYMENT_FAILED", PaymentStatusType.FAILED },
            { "PAYMENT_CANCELLED", PaymentStatusType.FAILED },
            { "VOIDED", PaymentStatusType.FAILED },
            { "REFUNDED", PaymentStatusType.FAILED }
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Success", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
            { "Processing", PaymentStatusType.FAILED }
        };

        public PayMayaCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            BuildTransactions = false;
            Initialize();
            log.LogMethodExit(null);
        }
        public override void Initialize()
        {
            log.LogMethodEntry();

            PUBLIC_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            SECRET_KEY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");

            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            POST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            CURRENCY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");
            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }

            payMayaHostedCommandHandler = new PayMayaHostedCommandHandler(PUBLIC_KEY, SECRET_KEY, BASE_URL, POST_URL);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;


            if (string.IsNullOrWhiteSpace(PUBLIC_KEY))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            }
            if (string.IsNullOrWhiteSpace(SECRET_KEY))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            string apiSite = "";
            string webSite = "";
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API") != null)
            {
                apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;
            }
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB") != null)
            {
                webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;
            }
            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.PayMayaCallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    System.Collections.Specialized.NameValueCollection queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.PayMayaCallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PayMayaCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PayMayaCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PayMayaCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.PayMayaCallbackHostedPayment.ToString());
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description;
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description;
            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description;
            }
            hostedGatewayDTO.PGSuccessResponseMessage = "OK";
            hostedGatewayDTO.PGFailedResponseMessage = "OK";

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;

            log.LogMethodExit();
        }

        /// <summary>
        /// Initiates the payment processing based on the provided gateway response.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the initialized payment details.
        /// </returns>
        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            if (hostedGatewayDTO == null)
            {
                hostedGatewayDTO = new HostedGatewayDTO();
            }
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();

            // proceed with processing
            PayMayaHostedPaymentResponseDto responseObj = GetResposeObj(gatewayResponse);
            log.Debug("Response after deserializing: " + responseObj.ToString());
            if (responseObj != null)
            {
                log.Debug(responseObj.requestReferenceNumber);
                hostedGatewayDTO.TrxId = Convert.ToInt32(responseObj.requestReferenceNumber);
                //hostedGatewayDTO.GatewayReferenceNumber = responseObj.trxref.ToString();
                //log.Debug(hostedGatewayDTO.TrxId + ":" + hostedGatewayDTO.GatewayReferenceNumber);
            }

            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }
        private PayMayaHostedPaymentResponseDto GetResposeObj(string gatewayResponse)
        {
            //log.LogMethodEntry(gatewayResponse);
            PayMayaHostedPaymentResponseDto responseObj = null;


            string jsonString = ConvertQueryStringToJson(gatewayResponse);

            //log.Debug("Converted JSON " + jsonString.ToString());
            responseObj = JsonConvert.DeserializeObject<PayMayaHostedPaymentResponseDto>(jsonString);
            log.LogMethodExit(responseObj);
            return responseObj;
        }

        private string ConvertQueryStringToJson(string gatewayResponse)
        {
            log.LogMethodEntry();
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(gatewayResponse);

            Dictionary<string, string> responseDictionary = new Dictionary<string, string>();

            foreach (string key in responseCollection.AllKeys)
            {
                responseDictionary.Add(key, responseCollection[key]);
            }

            string responseJson = JsonConvert.SerializeObject(responseDictionary);

            log.LogMethodExit(responseJson);
            return responseJson;
        }


        /// <summary>
        /// Creates a initial gateway request.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="paymentToken"></param>
        /// <returns>HostedGatewayDTO</returns>
        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            try
            {
                log.LogMethodEntry();
                log.LogMethodEntry("CCRequestSite:" + utilities.ExecutionContext.GetSiteId());
                objectGuid = transactionPaymentsDTO.Reference;
                transactionPaymentsDTO.Reference = "PaymentModeId:" + transactionPaymentsDTO.PaymentModeId + "|CurrencyCode:" + transactionPaymentsDTO.CurrencyCode;
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
                requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
                requestParamsDict.Add("paymentToken", paymentToken);

                hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
                hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(hostedGatewayDTO.GatewayRequestString, paymentPageLink);

                log.Debug("Request string:" + hostedGatewayDTO.GatewayRequestString);
                log.Debug("Direct request link:" + hostedGatewayDTO.PaymentRequestLink);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());

            return hostedGatewayDTO;

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(string URL, string FormName, string submitMethod = "POST", string id = null)
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();

            builder.Append("<html>");

            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));
            builder.AppendFormat("<input name=\"id\" type=\"hidden\" value=\"{0}\" />", id);

            //foreach (KeyValuePair<string, string> param in postparamslist)
            //{
            //    builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            //}

            builder.Append("</form>");
            builder.Append("</body></html>");
            return builder.ToString();
        }


        /// <summary>
        /// Creates a payment request through the PayStack gateway and prepares redirection information.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The details of the transaction to be processed.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the URL and request string for redirecting users to complete the payment.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error during payment request creation or processing.</exception>

        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            PayMayaHostedCheckoutResponseDto payMayaCreatePaymentResponse = null;
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);

                if (transactionPaymentsDTO == null)
                {
                    log.Error("TransactionPaymentsDTO is null");
                    throw new ArgumentNullException(nameof(transactionPaymentsDTO));
                }

                string trxId = Convert.ToString(transactionPaymentsDTO.TransactionId) ?? "";
                string checkoutUrl = string.Empty;
                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardName) 
                    || string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute5) 
                    || string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute4) 
                    || string.IsNullOrWhiteSpace(transactionPaymentsDTO.NameOnCreditCard) 
                    || string.IsNullOrWhiteSpace(transactionPaymentsDTO.CardEntitlementType) 
                    || string.IsNullOrWhiteSpace(transactionPaymentsDTO.Memo))
                {
                    Dictionary<string, string> errorParams = new Dictionary<string, string>
                                {
                                    { "PaymentFailure", "1" },
                                    { "ErrorMessage", "Payment has been declined! Please enter all the mandatory customer details" },
                                    { "TrxId", transactionPaymentsDTO.TransactionId.ToString() },
                                    {"Date", utilities.getServerTime().ToString() }
                                };
                    hostedGatewayDTO.GatewayRequestString = payMayaHostedCommandHandler.ErrorForm(errorParams);
                    CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                    List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                    searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, transactionPaymentsDTO.TransactionId.ToString()));
                    CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                    TransactionSiteId = cCRequestsPGWDTO.SiteId;

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;

                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.INVOICE_NUMBER, cCRequestsPGWDTO.RequestID.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                    if (cCTransactionsPGWDTOList == null)
                    {
                        CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO
                        {
                            TranCode = PaymentGatewayTransactionType.SALE.ToString(),
                            InvoiceNo = cCRequestsPGWDTO.RequestID.ToString(),
                            TransactionDatetime = utilities.getServerTime(),
                            DSIXReturnCode = "Payment has been declined! Please enter all the mandatory customer details",
                            TextResponse = "FAILED",
                            RecordNo = transactionPaymentsDTO.TransactionId.ToString()
                        };
                        hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                        CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                        cCTransactionsPGWBL.Save();
                    }
                }
                else
                {
                    PayMayaHostedPaymentRequestDto payMayaRequestDTO = new PayMayaHostedPaymentRequestDto
                    {
                        totalAmount = new Amount
                        {
                            value = transactionPaymentsDTO.Amount,
                            currency = CURRENCY,
                            details = new AmountDetails
                            {
                                discount = 0,
                                serviceCharge = 0,
                                shippingFee = 0,
                                tax = 0, //todo: how to map these details
                                subtotal = transactionPaymentsDTO.Amount
                            }
                        },
                        buyer = new Buyer
                        {
                            firstName = transactionPaymentsDTO.CreditCardName,
                            //middleName = "Paul",
                            lastName = transactionPaymentsDTO.Memo,
                            contact = new Contact
                            {
                                phone = transactionPaymentsDTO.CardEntitlementType,
                                email = transactionPaymentsDTO.NameOnCreditCard
                            },
                            shippingAddress = new Address
                            {
                                firstName = transactionPaymentsDTO.CreditCardName,
                                //middleName = "Paul",
                                lastName = transactionPaymentsDTO.Memo,
                                phone = transactionPaymentsDTO.CardEntitlementType,
                                email = transactionPaymentsDTO.NameOnCreditCard,
                                line1 = transactionPaymentsDTO.Attribute5,
                                //line2 = "Reliance Street",
                                city = transactionPaymentsDTO.Attribute4,
                                state = transactionPaymentsDTO.CreditCardNumber,
                                zipCode = transactionPaymentsDTO.PaymentCardNumber,
                                countryCode = "PH", // since PayMaya is for Philippines only
                                //shippingType = "ST"
                            },
                            billingAddress = new Address
                            {
                                line1 = transactionPaymentsDTO.Attribute5,
                                //line2 = "Reliance Street",
                                city = transactionPaymentsDTO.Attribute4,
                                state = transactionPaymentsDTO.CreditCardNumber,
                                zipCode = transactionPaymentsDTO.PaymentCardNumber,
                                countryCode = "PH",
                            }
                        },
                        redirectUrl = new RedirectUrl
                        {
                            success = successResponseAPIURL + "?requestReferenceNumber=" + trxId,
                            failure = failureResponseAPIURL + "?requestReferenceNumber=" + trxId,
                            cancel = cancelResponseAPIURL + "?requestReferenceNumber=" + trxId,
                        },
                        requestReferenceNumber = trxId,
                        metadata = new MetaData
                        {
                            paymentModeId = transactionPaymentsDTO.PaymentModeId.ToString(),
                        }
                    };

                    payMayaCreatePaymentResponse = payMayaHostedCommandHandler.CreateCheckout(payMayaRequestDTO);

                    if (payMayaCreatePaymentResponse == null)
                    {
                        log.Error("CreateGatewayPaymentRequest(): Checkout Transaction Response was empty");
                        throw new Exception("Error: could not create payment session");
                    }

                    if (string.IsNullOrWhiteSpace(payMayaCreatePaymentResponse.redirectUrl))
                    {
                        log.Error("GatewayPageURL was null");
                        throw new Exception("Error creating the payment request");
                    }
                    checkoutUrl = payMayaCreatePaymentResponse.redirectUrl;
                    string id = payMayaCreatePaymentResponse.checkoutId;
                    log.Debug($"CreateGatewayPaymentRequest(): Payment ResponseDto: {payMayaCreatePaymentResponse}");
                    log.Debug($"CreateGatewayPaymentRequest(): Payment request is created, redirecting to Checkout URL: {checkoutUrl}");

                    hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(checkoutUrl, "fromPayMayaForm", "GET", id);

                    log.Info("request form string:" + hostedGatewayDTO.GatewayRequestFormString);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }

        /// <summary>
        /// Processes the response received from the payment gateway and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the updated payment details and status.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment or updating the payment status.</exception>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            PayMayaHostedPaymentResponseDto payMayaResponse = null;
            //bool isStatusUpdated;
            try
            {
                log.Debug("Entering GetResposeObj. ");
                PayMayaHostedPaymentResponseDto jsonResponse = GetResposeObj(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + jsonResponse.ToString());
                payMayaResponse = jsonResponse;


                if (payMayaResponse.requestReferenceNumber != null)
                {
                    log.Debug("Transaction id: " + payMayaResponse.requestReferenceNumber);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(payMayaResponse.requestReferenceNumber);
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                PayMayaHostedPaymentResponseDto trxSearchResponse = payMayaHostedCommandHandler.CreateTrxSearch(payMayaResponse.requestReferenceNumber);

                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("Error processing your payment");
                }

                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(trxSearchResponse.amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = trxSearchResponse.currency;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.id.ToString();//paymaya transaction id
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(trxSearchResponse.metadata.paymentModeId);//paymentmode id
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.description; // cardnumber : **** **** **** 2346
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.details.scheme; //cardtype : master-card : Others
                hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.details.issuer; //issuername

                PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.status, PaymentGatewayTransactionType.SALE);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());
                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Payment status is success");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                }
                else if (salePaymentStatus == PaymentStatusType.PENDING)
                {
                    log.Debug("Payment status is pending");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                }
                else if (salePaymentStatus == PaymentStatusType.FAILED)
                {
                    log.Debug("Payment status is failed");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }
                else
                {
                    log.Error("Payment status is unknown. Considering status as: " + salePaymentStatus.ToString());
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }

                hostedGatewayDTO.PaymentStatus = salePaymentStatus;
                log.Debug("Final hostedGatewayDTO.PaymentStatus: " + hostedGatewayDTO.PaymentStatus);

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;

                log.Debug("Trying to update the CC request to payment processing status");
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);

                int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(PaymentProcessStatusType.PAYMENT_PROCESSING.ToString(), hostedGatewayDTO.PaymentProcessStatus.ToString());

                if (rowsUpdated == 0)
                {
                    log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                }
                else
                {
                    log.Debug("CC request updated to " + hostedGatewayDTO.PaymentProcessStatus.ToString());
                }

                //check if ccTransactionPGW updated
                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Error("No reference id/Transaction present in PayMaya receipt response");
                    cCTransactionsPGWDTOList = null;
                }

                if (cCTransactionsPGWDTOList == null)
                {  // update the CCTransactionsPGWDTO
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.TextResponse = trxSearchResponse.status;
                    cCTransactionsPGWDTO.DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(trxSearchResponse.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.CardType = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName;
                    cCTransactionsPGWDTO.AcctNo = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber;
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();
                    cCTransactionsPGWDTO.AuthCode = trxSearchResponse.fundSource == null ? "" : trxSearchResponse.fundSource.details.last4;
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse.createdAt);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        /// <summary>
        /// Retrieves the status of a transaction based on the provided transaction ID.
        /// </summary>
        /// <param name="trxId">The ID of the transaction to retrieve status for.</param>
        /// <returns>
        /// Returns a JSON string containing the status information of the transaction, including success or failure status, transaction amount, reference number, account number, and any relevant messages.
        /// </returns>
        /// <exception cref="Exception">Thrown when there are insufficient parameters passed to the request or when an error occurs during the processing of the transaction.</exception>
        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]
        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;

            try
            {
                if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                PayMayaHostedPaymentResponseDto txSearchResponseDTO = payMayaHostedCommandHandler.CreateTrxSearch(trxId);
                if (txSearchResponseDTO == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }

                log.Debug($"TxSearch Response for TrxId: {trxId}: " + txSearchResponseDTO);

                if (txSearchResponseDTO != null)
                {

                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.amount));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", Convert.ToDouble(txSearchResponseDTO.amount));
                    cCTransactionsPGWDTO.RefNo = txSearchResponseDTO.id.ToString(); //paymentId
                    cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
                    cCTransactionsPGWDTO.TextResponse = txSearchResponseDTO.status;
                    cCTransactionsPGWDTO.DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(txSearchResponseDTO.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                    cCTransactionsPGWDTO.CardType = txSearchResponseDTO.fundSource == null ? "" : txSearchResponseDTO.fundSource.type;
                    cCTransactionsPGWDTO.AuthCode = txSearchResponseDTO.fundSource == null ? "" : txSearchResponseDTO.fundSource.details.last4;
                    cCTransactionsPGWDTO.AcctNo = txSearchResponseDTO.fundSource == null ? "" : txSearchResponseDTO.fundSource.description;
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(txSearchResponseDTO.createdAt);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    if (txSearchResponseDTO.status == SUCCESS)
                    {
                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else if (txSearchResponseDTO.status == PENDING || txSearchResponseDTO.status == PROCESSING || txSearchResponseDTO.status == PENDING_TOKEN || txSearchResponseDTO.status == FOR_AUTHENTICATION || txSearchResponseDTO.status == AUTHENTICATING || txSearchResponseDTO.status == AUTH_SUCCESS)
                    {
                        log.Error("GetTransactionStatus(): Error updating the payment status");

                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (txSearchResponseDTO.status));
                        dict.Add("orderId", trxId);
                        //throw new Exception("redirect checkoutmessage");
                    }

                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (txSearchResponseDTO.status));
                        dict.Add("orderId", trxId);
                    }

                }
                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        private DateTime GetPaymentDate(string updatedAt)
        {
            log.LogMethodEntry(updatedAt);
            DateTime paymentDate = new DateTime();

            if (updatedAt != null)
            {
                log.Debug("Payment Date from response: " + updatedAt);
                if (DateTime.TryParseExact(updatedAt, "yyyyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }

        /// <summary>
        /// Initiates a refund process for a transaction based on the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details for initiating the refund.</param>
        /// <returns>
        /// Returns the updated TransactionPaymentsDTO after processing the refund.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefund = false;
            refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
            PaymentStatusType refundPaymentStatus;
            CCRequestPGWDTO cCRequestPGWDTO = null;
            PayMayaHostedRefundVoidResponseDto refundResponseDTO = null;

            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }
                PayMayaHostedPaymentResponseDto trxSearchResponse = payMayaHostedCommandHandler.CreateTrxSearch(refundTrxId);

                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("Error processing your payment");
                }

                log.Debug("Refund processing started");
                if (trxSearchResponse.canRefund)
                {
                    cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                    PayMayaHostedRefundRequestDto requestDto = new PayMayaHostedRefundRequestDto
                    {
                        totalAmount = new RefundAmount
                        {
                            amount = transactionPaymentsDTO.Amount,
                            currency = CURRENCY,
                        },
                        reason = "Customer request",
                    };

                    log.Debug("PayMaya Refund Request has been created, RequestDTO: " + requestDto);

                    refundResponseDTO = payMayaHostedCommandHandler.CreateRefund(refundTrxId, requestDto);
                }
                else if (trxSearchResponse.canVoid)
                {
                    cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_VOID);

                    PayMayaHostedVoidRequestDto requestDto = new PayMayaHostedVoidRequestDto
                    {
                        reason = "Customer request",
                    };

                    log.Debug("Paystack Refund Request has been created, RequestDTO: " + requestDto);

                    refundResponseDTO = payMayaHostedCommandHandler.CreateVoid(refundTrxId, requestDto);
                }

                log.Debug("PayMaya Void/Refund Response refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }


                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.RecordNo = refundTrxId; //parafait TrxId
                ccTransactionsPGWDTO.DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(refundResponseDTO.status);
                ccTransactionsPGWDTO.RefNo = refundResponseDTO.id.ToString(); //paymaya paymentId
                ccTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(refundResponseDTO.updatedAt);

                refundPaymentStatus = MapPaymentStatus(refundResponseDTO.status, PaymentGatewayTransactionType.REFUND);
                log.Debug("Refund Status: " + refundPaymentStatus.ToString());
                if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Refund Success for trxId: " + refundTrxId);
                    isRefund = true;
                    ccTransactionsPGWDTO.TextResponse = refundResponseDTO.status;
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.reason;
                    ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                    ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                    ccTransactionsPGWDTO.AcctNo = refundResponseDTO.reason;
                    ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();

                }
                else
                {
                    //refund failed
                    isRefund = false;
                    string errorMessage = refundResponseDTO.status;
                    log.Error($"Refund Failed. Error Message received: {errorMessage}");
                    ccTransactionsPGWDTO.TextResponse = "FAILED";
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.reason;
                    ccTransactionsPGWDTO.PaymentStatus = PaymentStatusType.FAILED.ToString();

                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }


            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        /// <summary>
        /// Maps the raw payment gateway status to a standardized internal payment status type
        /// based on the transaction type. Handles errors and logs the process.
        /// Defaults to a PENDING status if the raw status is not recognized.
        /// </summary>
        /// <param name="rawPaymentGatewayStatus">The status string from the payment gateway.</param>
        /// <param name="pgwTrxType">The type of payment gateway transaction.</param>
        /// <returns>A PaymentStatusType enumeration value representing the mapped payment status.</returns>
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

        /// <summary>
        /// Retrieves the payment status of a transaction by its ID from PayMaya.
        /// Logs the process and handles errors. If the transaction ID is not provided,
        /// it returns a default response indicating no payment found.
        /// </summary>
        /// <param name="transactionPaymentsDTO">Data Transfer Object containing transaction details.</param>
        /// <param name="paymentGatewayTransactionType">Optional transaction type, default is STATUSCHECK.</param>
        /// <returns>A HostedGatewayDTO object containing the payment status details.</returns>
        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            PayMayaHostedPaymentResponseDto orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = payMayaHostedCommandHandler.CreateTrxSearch(trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error($"Order status for trxId: {trxIdString} failed.");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                PaymentStatusType txSearchPaymentStatus = MapPaymentStatus(orderStatusResult.status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of txSearchPaymentStatus: " + txSearchPaymentStatus.ToString());

                if (orderStatusResult.fundSource == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = orderStatusResult.status.ToLower(),
                        PaymentStatus = txSearchPaymentStatus.ToString(),
                        TransactionDatetime = GetPaymentDate(orderStatusResult.createdAt),
                        DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(orderStatusResult.status)
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO != null ? cCRequestsPGWDTO.RequestID.ToString() : trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.Amount = Convert.ToDouble(orderStatusResult.amount);
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.RefNo = orderStatusResult.id;
                cCTransactionsPGWDTO.RecordNo = orderStatusResult.requestReferenceNumber;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.status.ToUpper();
                cCTransactionsPGWDTO.DSIXReturnCode = payMayaHostedCommandHandler.GetPaymentStatusDescription(orderStatusResult.status);
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.CardType = orderStatusResult.fundSource == null ? "" : orderStatusResult.fundSource.details.scheme;
                cCTransactionsPGWDTO.PaymentStatus = txSearchPaymentStatus.ToString();
                cCTransactionsPGWDTO.AcctNo = orderStatusResult.fundSource == null ? "" : orderStatusResult.fundSource.description;
                cCTransactionsPGWDTO.AuthCode = orderStatusResult.fundSource == null ? "" : orderStatusResult.fundSource.details.last4;
                cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(orderStatusResult.createdAt);

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }

    }
}
