﻿using System;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using Semnox.Parafait.PaymentGatewayInterface;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;

namespace Semnox.Parafait.PaymentGateway
{
    class CardConnectWebPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public CardConnectWebPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            log.LogMethodEntry();
            log.Debug("Initializing CardConnect configuration");

            // Basic configurations
            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID"));
            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_USER_NAME", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CARD_CONNECT_HOSTED_PAYMENT_USER_NAME"));
            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_PASSWORD", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CARD_CONNECT_HOSTED_PAYMENT_PASSWORD"));
            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_BASE_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CARD_CONNECT_HOSTED_PAYMENT_BASE_URL"));
            SetConfiguration("CURRENCY_CODE", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "CURRENCY_CODE"));
            SetConfiguration("PAYMENT_MODE_ID", paymentModeContainerDTO?.PaymentModeId.ToString());
            SetConfiguration("SITE_ID", Convert.ToString(executionContext.GetSiteId()));

            // PAAY details if PAAY is enabled
            SetConfiguration("IS_PAAY_ENABLED", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "PAYMENT_GATEWAY_CHANNEL_NAME")?.ToUpper() == "Y" ? "Y" : "N");
            string strEnablePAAYChallenge = ConfigurationManager.AppSettings["ENABLE_PAAY_CHALLENGE"];
            bool isPAAYEnabled = !string.IsNullOrWhiteSpace(strEnablePAAYChallenge) &&
                                 strEnablePAAYChallenge.Trim().ToUpper() == "TRUE";
            SetConfiguration("SHOW_PAAY_CHALLENGE", isPAAYEnabled ? "Y" : "N");
            SetConfiguration("THREEDS_PAAY_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_API_URL"));
            SetConfiguration("THREEDS_PAAY_API_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY"));

            // System Options
            SetConfiguration("RSA_TOKENIZE_PUBLIC_KEY", SystemOptionViewContainerList.GetSystemOption(executionContext, "Hosted Payment keys", "CardConnect tokenize public key"));
            SetConfiguration("RSA_DATA_PUBLIC_KEY", SystemOptionViewContainerList.GetSystemOption(executionContext, "Hosted Payment keys", "CardConnect data public key"));
            SetConfiguration("RSA_DATA_PRIVATE_KEY", SystemOptionViewContainerList.GetSystemOption(executionContext, "Hosted Payment keys", "CardConnect data private key"));

            // Optional boolean config
            SetConfiguration("ENABLE_ADDRESS_VALIDATION", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "ENABLE_ADDRESS_VALIDATION")?.ToUpper() == "Y" ? "Y" : "N");
            SetConfiguration("ENABLE_GOOGLE_RECAPTCHA", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "ENABLE_GOOGLE_RECAPTCHA")?.ToUpper() == "Y" ? "Y" : "N");

            // Recaptcha details if enabled
            SetConfiguration("GOOGLE_RECAPTCHA_SECRET_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "GOOGLE_RECAPTCHA_SECRET_KEY"));
            SetConfiguration("GOOGLE_RECAPTCHA_CLIENT_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "GOOGLE_RECAPTCHA_CLIENT_ID"));
            SetConfiguration("GOOGLE_RECAPTCHA_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "GOOGLE_RECAPTCHA_URL"));

            // Transaction time limit fallback
            SetConfiguration("HOSTED_PAYMENT_TRX_WINDOW_TIME", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "HOSTED_PAYMENT_TRX_WINDOW_TIME"));

            // Lookup Values
            LookupsContainerDTO lookupsContainerDTO = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            List<LookupValuesContainerDTO> lookupValuesContainerDTOList = lookupsContainerDTO?.LookupValuesContainerDTOList ?? new List<LookupValuesContainerDTO>();

            String apiSite = "";
            if (lookupValuesContainerDTOList.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").Count() > 0)
            {
                apiSite = lookupValuesContainerDTOList.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").First().Description;
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL"))
            {
                SetConfiguration("SUCCESS_RESPONSE_API_URL", apiSite + lookupValuesContainerDTOList.First(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", $"{PaymentGateways.CardConnectCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL"))
            {
                SetConfiguration("CALLBACK_RESPONSE_API_URL", apiSite + lookupValuesContainerDTOList.First(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", $"{PaymentGateways.CardConnectCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }

            log.LogMethodExit();
        }
    }
}
