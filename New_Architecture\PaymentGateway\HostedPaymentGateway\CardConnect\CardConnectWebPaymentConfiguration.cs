﻿using System;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentMode;
using Semnox.Parafait.ViewContainer;
using Semnox.Parafait.PaymentGatewayInterface;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;

namespace Semnox.Parafait.PaymentGateway
{
    class CardConnectWebPaymentConfiguration : PaymentConfiguration
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public CardConnectWebPaymentConfiguration(ExecutionContext executionContext, PaymentModeContainerDTO paymentModeContainerDTO)
        {
            log.LogMethodEntry();
            log.Debug("Initializing CardConnect configuration");

            PaymentModeAttributesContainerDTO paymentModeAttributes = paymentModeContainerDTO.PaymentModeAttributesContainerDTO;

            if (paymentModeAttributes == null)
            {
                log.Error($"PaymentModeAttributesContainerDTO is null for PaymentModeId {paymentModeContainerDTO.PaymentModeId}, site_id: {Convert.ToString(executionContext.GetSiteId())}. Cannot initialize CardConnectWebPaymentConfiguration.");
                log.Error("PaymentModeDTO: " + paymentModeContainerDTO.ToString());
                throw new PaymentModeAttributesNotFoundException("PaymentMode attributes not found.");
            }

            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayMerchantId));
            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_USER_NAME", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayMerchantKey));
            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_PASSWORD", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewaySecretKey));
            SetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_BASE_URL", paymentModeAttributes.PaymentGatewayBaseURL);
            SetConfiguration("CURRENCY_CODE", paymentModeAttributes.CurrencyCode);

            SetConfiguration("IS_PAAY_ENABLED", paymentModeAttributes.EnablePaay?.ToUpper() == "Y" ? "Y" : "N");
            SetConfiguration("SHOW_PAAY_CHALLENGE", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayAttribute1)?.ToUpper() == "Y" ? "Y" : "N");
            SetConfiguration("THREEDS_PAAY_URL", paymentModeAttributes.PaymentGatewayAPIURL);
            SetConfiguration("THREEDS_PAAY_API_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayPublishableKey));

            SetConfiguration("RSA_TOKENIZE_PUBLIC_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedRSATokenizePublicKey));
            SetConfiguration("RSA_DATA_PUBLIC_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedRSADataPublicKey));
            SetConfiguration("RSA_DATA_PRIVATE_KEY", Encryption.Decrypt(paymentModeAttributes.EncryptedRSADataPrivateKey));

            SetConfiguration("ENABLE_ADDRESS_VALIDATION", paymentModeAttributes.EnableAddressValidation?.ToUpper() == "Y" ? "Y" : "N");
            SetConfiguration("ENABLE_GOOGLE_RECAPTCHA", paymentModeAttributes.EnableGoogleRecaptcha?.ToUpper() == "Y" ? "Y" : "N");
            
            SetConfiguration("HOSTED_PAYMENT_TRX_WINDOW_TIME", Encryption.Decrypt(paymentModeAttributes.EncryptedPaymentGatewayAttribute2));

            // For AngularPaymentWeb URL
            SetConfiguration("ANGULAR_PAYMENT_WEB", paymentModeAttributes.AngularPaymentWeb);
            SetConfiguration("PAYMENT_MODE_ID", paymentModeContainerDTO?.PaymentModeId.ToString());
            SetConfiguration("SITE_ID", Convert.ToString(executionContext.GetSiteId()));
            
            // Recaptcha details if enabled
            SetConfiguration("GOOGLE_RECAPTCHA_SECRET_KEY", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "GOOGLE_RECAPTCHA_SECRET_KEY"));
            SetConfiguration("GOOGLE_RECAPTCHA_CLIENT_ID", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "GOOGLE_RECAPTCHA_CLIENT_ID"));
            SetConfiguration("GOOGLE_RECAPTCHA_URL", ParafaitDefaultViewContainerList.GetParafaitDefault(executionContext, "GOOGLE_RECAPTCHA_URL"));

            // Lookup Values
            LookupsContainerDTO lookupsContainerDTO = LookupsViewContainerList.GetLookupsContainerDTO(executionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            List<LookupValuesContainerDTO> lookupValuesContainerDTOList = lookupsContainerDTO?.LookupValuesContainerDTOList ?? new List<LookupValuesContainerDTO>();

            String apiSite = "";
            if (lookupValuesContainerDTOList.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").Count() > 0)
            {
                apiSite = lookupValuesContainerDTOList.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").First().Description;
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL"))
            {
                SetConfiguration("SUCCESS_RESPONSE_API_URL", apiSite + lookupValuesContainerDTOList.First(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", $"{PaymentGateways.CardConnectCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }

            if (lookupValuesContainerDTOList.Any(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL"))
            {
                SetConfiguration("CALLBACK_RESPONSE_API_URL", apiSite + lookupValuesContainerDTOList.First(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", $"{PaymentGateways.CardConnectCallbackHostedPayment.ToString()}-{executionContext.GetSiteId()}"));
            }

            log.LogMethodExit();
        }
    }
}
