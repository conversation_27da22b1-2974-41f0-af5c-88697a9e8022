﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Serialization;

// 
// This source code was auto-generated by wsdl, Version=4.0.30319.33440.
// 


/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Web.Services.WebServiceBindingAttribute(Name = "BasicHttpBinding_IGatewayService", Namespace = "https://www.mobile88.com")]
public partial class BasicHttpBinding_IGatewayService : System.Web.Services.Protocols.SoapHttpClientProtocol
{

    private System.Threading.SendOrPostCallback EntryPageFunctionalityOperationCompleted;

    /// <remarks/>
    public BasicHttpBinding_IGatewayService()
    {
        //this.Url = "http://www.mobile.com/ePayment/WebService/MHGatewayService/GatewayService.svc";
    }

    /// <remarks/>
    public event EntryPageFunctionalityCompletedEventHandler EntryPageFunctionalityCompleted;

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/IGatewayService/EntryPageFunctionality", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    [return: System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public ClientResponseModel EntryPageFunctionality([System.Xml.Serialization.XmlElementAttribute(IsNullable = true)] ClientRequestModel requestModelObj)
    {
        object[] results = this.Invoke("EntryPageFunctionality", new object[] {
                    requestModelObj});
        return ((ClientResponseModel)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginEntryPageFunctionality(ClientRequestModel requestModelObj, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("EntryPageFunctionality", new object[] {
                    requestModelObj}, callback, asyncState);
    }

    /// <remarks/>
    public ClientResponseModel EndEntryPageFunctionality(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((ClientResponseModel)(results[0]));
    }

    /// <remarks/>
    public void EntryPageFunctionalityAsync(ClientRequestModel requestModelObj)
    {
        this.EntryPageFunctionalityAsync(requestModelObj, null);
    }

    /// <remarks/>
    public void EntryPageFunctionalityAsync(ClientRequestModel requestModelObj, object userState)
    {
        if ((this.EntryPageFunctionalityOperationCompleted == null))
        {
            this.EntryPageFunctionalityOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEntryPageFunctionalityOperationCompleted);
        }
        this.InvokeAsync("EntryPageFunctionality", new object[] {
                    requestModelObj}, this.EntryPageFunctionalityOperationCompleted, userState);
    }

    private void OnEntryPageFunctionalityOperationCompleted(object arg)
    {
        if ((this.EntryPageFunctionalityCompleted != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.EntryPageFunctionalityCompleted(this, new EntryPageFunctionalityCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    public new void CancelAsync(object userState)
    {
        base.CancelAsync(userState);
    }
}

/// <remarks/>
// CODEGEN: The optional WSDL extension element 'PolicyReference' from namespace 'http://schemas.xmlsoap.org/ws/2004/09/policy' was not handled.
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Web.Services.WebServiceBindingAttribute(Name = "BasicHttpsBinding_IGatewayService", Namespace = "https://www.mobile88.com")]
public partial class BasicHttpsBinding_IGatewayService : System.Web.Services.Protocols.SoapHttpClientProtocol
{

    private System.Threading.SendOrPostCallback EntryPageFunctionalityOperationCompleted;

    /// <remarks/>
    public BasicHttpsBinding_IGatewayService()
    {
        //this.Url = "https://payment.ipay88.com.my/ePayment/WebService/MHGatewayService/GatewayService" +
        //   ".svc";
    }

    /// <remarks/>
    public event EntryPageFunctionalityCompletedEventHandler EntryPageFunctionalityCompleted;

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/IGatewayService/EntryPageFunctionality", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    [return: System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public ClientResponseModel EntryPageFunctionality([System.Xml.Serialization.XmlElementAttribute(IsNullable = true)] ClientRequestModel requestModelObj)
    {
        object[] results = this.Invoke("EntryPageFunctionality", new object[] {
                    requestModelObj});
        return ((ClientResponseModel)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginEntryPageFunctionality(ClientRequestModel requestModelObj, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("EntryPageFunctionality", new object[] {
                    requestModelObj}, callback, asyncState);
    }

    /// <remarks/>
    public ClientResponseModel EndEntryPageFunctionality(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((ClientResponseModel)(results[0]));
    }

    /// <remarks/>
    public void EntryPageFunctionalityAsync(ClientRequestModel requestModelObj)
    {
        this.EntryPageFunctionalityAsync(requestModelObj, null);
    }

    /// <remarks/>
    public void EntryPageFunctionalityAsync(ClientRequestModel requestModelObj, object userState)
    {
        if ((this.EntryPageFunctionalityOperationCompleted == null))
        {
            this.EntryPageFunctionalityOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEntryPageFunctionalityOperationCompleted);
        }
        this.InvokeAsync("EntryPageFunctionality", new object[] {
                    requestModelObj}, this.EntryPageFunctionalityOperationCompleted, userState);
    }

    private void OnEntryPageFunctionalityOperationCompleted(object arg)
    {
        if ((this.EntryPageFunctionalityCompleted != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.EntryPageFunctionalityCompleted(this, new EntryPageFunctionalityCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    public new void CancelAsync(object userState)
    {
        base.CancelAsync(userState);
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://schemas.datacontract.org/2004/07/MHPHGatewayService.Model")]
public partial class ClientRequestModel
{

    private string actionTypeField;

    private string amountField;

    private string backendURLField;

    private string barcodeNoField;

    private string cCCIdField;

    private string cCCOriTokenIdField;

    private string cCMonthField;

    private string cCNameField;

    private string cCNoField;

    private string cCYearField;

    private string cVV2Field;

    private string currencyField;

    private string discountedAmountField;

    private string mTLogIdField;

    private string mTVersionField;

    private string merchantCodeField;

    private int paymentIdField;

    private bool paymentIdFieldSpecified;

    private string prodDescField;

    private string promoCodeField;

    private string refNoField;

    private string remarkField;

    private string signatureField;

    private string signatureTypeField;

    private string terminalIDField;

    private string tokenIdField;

    private string userContactField;

    private string userEmailField;

    private string userNameField;

    private string forexRateField;

    private string langField;

    private string xfield1Field;

    private string xfield2Field;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string ActionType
    {
        get
        {
            return this.actionTypeField;
        }
        set
        {
            this.actionTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Amount
    {
        get
        {
            return this.amountField;
        }
        set
        {
            this.amountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string BackendURL
    {
        get
        {
            return this.backendURLField;
        }
        set
        {
            this.backendURLField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string BarcodeNo
    {
        get
        {
            return this.barcodeNoField;
        }
        set
        {
            this.barcodeNoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCCId
    {
        get
        {
            return this.cCCIdField;
        }
        set
        {
            this.cCCIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCCOriTokenId
    {
        get
        {
            return this.cCCOriTokenIdField;
        }
        set
        {
            this.cCCOriTokenIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCMonth
    {
        get
        {
            return this.cCMonthField;
        }
        set
        {
            this.cCMonthField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCName
    {
        get
        {
            return this.cCNameField;
        }
        set
        {
            this.cCNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCNo
    {
        get
        {
            return this.cCNoField;
        }
        set
        {
            this.cCNoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCYear
    {
        get
        {
            return this.cCYearField;
        }
        set
        {
            this.cCYearField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CVV2
    {
        get
        {
            return this.cVV2Field;
        }
        set
        {
            this.cVV2Field = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Currency
    {
        get
        {
            return this.currencyField;
        }
        set
        {
            this.currencyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string DiscountedAmount
    {
        get
        {
            return this.discountedAmountField;
        }
        set
        {
            this.discountedAmountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string MTLogId
    {
        get
        {
            return this.mTLogIdField;
        }
        set
        {
            this.mTLogIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string MTVersion
    {
        get
        {
            return this.mTVersionField;
        }
        set
        {
            this.mTVersionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string MerchantCode
    {
        get
        {
            return this.merchantCodeField;
        }
        set
        {
            this.merchantCodeField = value;
        }
    }

    /// <remarks/>
    public int PaymentId
    {
        get
        {
            return this.paymentIdField;
        }
        set
        {
            this.paymentIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PaymentIdSpecified
    {
        get
        {
            return this.paymentIdFieldSpecified;
        }
        set
        {
            this.paymentIdFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string ProdDesc
    {
        get
        {
            return this.prodDescField;
        }
        set
        {
            this.prodDescField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string PromoCode
    {
        get
        {
            return this.promoCodeField;
        }
        set
        {
            this.promoCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string RefNo
    {
        get
        {
            return this.refNoField;
        }
        set
        {
            this.refNoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Remark
    {
        get
        {
            return this.remarkField;
        }
        set
        {
            this.remarkField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Signature
    {
        get
        {
            return this.signatureField;
        }
        set
        {
            this.signatureField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string SignatureType
    {
        get
        {
            return this.signatureTypeField;
        }
        set
        {
            this.signatureTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string TerminalID
    {
        get
        {
            return this.terminalIDField;
        }
        set
        {
            this.terminalIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string TokenId
    {
        get
        {
            return this.tokenIdField;
        }
        set
        {
            this.tokenIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string UserContact
    {
        get
        {
            return this.userContactField;
        }
        set
        {
            this.userContactField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string UserEmail
    {
        get
        {
            return this.userEmailField;
        }
        set
        {
            this.userEmailField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string UserName
    {
        get
        {
            return this.userNameField;
        }
        set
        {
            this.userNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string forexRate
    {
        get
        {
            return this.forexRateField;
        }
        set
        {
            this.forexRateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string lang
    {
        get
        {
            return this.langField;
        }
        set
        {
            this.langField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string xfield1
    {
        get
        {
            return this.xfield1Field;
        }
        set
        {
            this.xfield1Field = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string xfield2
    {
        get
        {
            return this.xfield2Field;
        }
        set
        {
            this.xfield2Field = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://schemas.datacontract.org/2004/07/MHPHGatewayService.Model")]
public partial class ClientResponseModel
{

    private string actionTypeField;

    private string amountField;

    private string amountBeforeDiscountField;

    private string authCodeField;

    private string bankMIDField;

    private string bindCardErrDesccField;

    private string cCNameField;

    private string cCNoField;

    private string cardTypeField;

    private string currencyField;

    private string dCCConversionRateField;

    private int dCCStatusField;

    private bool dCCStatusFieldSpecified;

    private string discountField;

    private string errDescField;

    private string langField;

    private string merchantCodeField;

    private string originalAmountField;

    private string originalCurrencyField;

    private int paymentIdField;

    private bool paymentIdFieldSpecified;

    private string paymentTypeField;

    private string qRCodeField;

    private string qRValueField;

    private string refNoField;

    private string remarkField;

    private string requeryField;

    private string s_banknameField;

    private string s_countryField;

    private string settlementAmountField;

    private string settlementCurrencyField;

    private string signatureField;

    private int statusField;

    private bool statusFieldSpecified;

    private string tokenIdField;

    private string transIdField;

    private string xfield1Field;

    private string xfield2Field;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string ActionType
    {
        get
        {
            return this.actionTypeField;
        }
        set
        {
            this.actionTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Amount
    {
        get
        {
            return this.amountField;
        }
        set
        {
            this.amountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string AmountBeforeDiscount
    {
        get
        {
            return this.amountBeforeDiscountField;
        }
        set
        {
            this.amountBeforeDiscountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string AuthCode
    {
        get
        {
            return this.authCodeField;
        }
        set
        {
            this.authCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string BankMID
    {
        get
        {
            return this.bankMIDField;
        }
        set
        {
            this.bankMIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string BindCardErrDescc
    {
        get
        {
            return this.bindCardErrDesccField;
        }
        set
        {
            this.bindCardErrDesccField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCName
    {
        get
        {
            return this.cCNameField;
        }
        set
        {
            this.cCNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CCNo
    {
        get
        {
            return this.cCNoField;
        }
        set
        {
            this.cCNoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string CardType
    {
        get
        {
            return this.cardTypeField;
        }
        set
        {
            this.cardTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Currency
    {
        get
        {
            return this.currencyField;
        }
        set
        {
            this.currencyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string DCCConversionRate
    {
        get
        {
            return this.dCCConversionRateField;
        }
        set
        {
            this.dCCConversionRateField = value;
        }
    }

    /// <remarks/>
    public int DCCStatus
    {
        get
        {
            return this.dCCStatusField;
        }
        set
        {
            this.dCCStatusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DCCStatusSpecified
    {
        get
        {
            return this.dCCStatusFieldSpecified;
        }
        set
        {
            this.dCCStatusFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Discount
    {
        get
        {
            return this.discountField;
        }
        set
        {
            this.discountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string ErrDesc
    {
        get
        {
            return this.errDescField;
        }
        set
        {
            this.errDescField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Lang
    {
        get
        {
            return this.langField;
        }
        set
        {
            this.langField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string MerchantCode
    {
        get
        {
            return this.merchantCodeField;
        }
        set
        {
            this.merchantCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string OriginalAmount
    {
        get
        {
            return this.originalAmountField;
        }
        set
        {
            this.originalAmountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string OriginalCurrency
    {
        get
        {
            return this.originalCurrencyField;
        }
        set
        {
            this.originalCurrencyField = value;
        }
    }

    /// <remarks/>
    public int PaymentId
    {
        get
        {
            return this.paymentIdField;
        }
        set
        {
            this.paymentIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PaymentIdSpecified
    {
        get
        {
            return this.paymentIdFieldSpecified;
        }
        set
        {
            this.paymentIdFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string PaymentType
    {
        get
        {
            return this.paymentTypeField;
        }
        set
        {
            this.paymentTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string QRCode
    {
        get
        {
            return this.qRCodeField;
        }
        set
        {
            this.qRCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string QRValue
    {
        get
        {
            return this.qRValueField;
        }
        set
        {
            this.qRValueField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string RefNo
    {
        get
        {
            return this.refNoField;
        }
        set
        {
            this.refNoField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Remark
    {
        get
        {
            return this.remarkField;
        }
        set
        {
            this.remarkField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Requery
    {
        get
        {
            return this.requeryField;
        }
        set
        {
            this.requeryField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string S_bankname
    {
        get
        {
            return this.s_banknameField;
        }
        set
        {
            this.s_banknameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string S_country
    {
        get
        {
            return this.s_countryField;
        }
        set
        {
            this.s_countryField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string SettlementAmount
    {
        get
        {
            return this.settlementAmountField;
        }
        set
        {
            this.settlementAmountField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string SettlementCurrency
    {
        get
        {
            return this.settlementCurrencyField;
        }
        set
        {
            this.settlementCurrencyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Signature
    {
        get
        {
            return this.signatureField;
        }
        set
        {
            this.signatureField = value;
        }
    }

    /// <remarks/>
    public int Status
    {
        get
        {
            return this.statusField;
        }
        set
        {
            this.statusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool StatusSpecified
    {
        get
        {
            return this.statusFieldSpecified;
        }
        set
        {
            this.statusFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string TokenId
    {
        get
        {
            return this.tokenIdField;
        }
        set
        {
            this.tokenIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string TransId
    {
        get
        {
            return this.transIdField;
        }
        set
        {
            this.transIdField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Xfield1
    {
        get
        {
            return this.xfield1Field;
        }
        set
        {
            this.xfield1Field = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable = true)]
    public string Xfield2
    {
        get
        {
            return this.xfield2Field;
        }
        set
        {
            this.xfield2Field = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void EntryPageFunctionalityCompletedEventHandler(object sender, EntryPageFunctionalityCompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class EntryPageFunctionalityCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal EntryPageFunctionalityCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public ClientResponseModel Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((ClientResponseModel)(this.results[0]));
        }
    }
}
