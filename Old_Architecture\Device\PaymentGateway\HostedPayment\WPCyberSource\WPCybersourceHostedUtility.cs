﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.WPCyberSource
{
    public class WPCyberSourceHostedUtility : HostedPaymentGatewayUtility
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        public WPCyberSourceHostedUtility() : base()
        {
            log.LogMethodEntry();
            Initialize();
            log.LogMethodExit();
        }

        private Dictionary<string, string> configParameters = new Dictionary<string, string>();
        const string SCHEME = "https://";
        const string ALGORITHM = "HmacSHA256";
        private string HOST_URL;
        private string REST_SECRET_KEY;
        private string PUBLIC_KEY;
        private string MERCHANT_ID;
        private void Initialize()
        {
           
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_API_URL", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", "");
            PaymentCredentailsList.Add("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", "");
           
        }
        private void LoadConfigParams()
        {
            try
            {
                configParameters.Add("SCHEME", SCHEME);
                configParameters.Add("REST_SECRET_KEY", REST_SECRET_KEY);
                configParameters.Add("PUBLIC_KEY", PUBLIC_KEY);
                configParameters.Add("ALGORITHM", ALGORITHM);
                configParameters.Add("MERCHANT_ID", MERCHANT_ID);
                configParameters.Add("HOST_URL", HOST_URL);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// Initializes and returns an ChillpayHostedCommandHandler instance.
        /// </summary>
        /// <param name="paymentCredentialsList">The payment credentials list.</param>
        /// <returns>An instance of ChillpayHostedCommandHandler.</returns>
        public WPCyberSourceCommandHandler InitializeCommandHandler(Dictionary<string, string> paymentCredentialsList)
        {
            if (paymentCredentialsList == null)
            {
                throw new ArgumentNullException("paymentCredentialsList", "The payment credentials list cannot be null.");
            }
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_API_URL", out HOST_URL) || string.IsNullOrWhiteSpace(HOST_URL))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_API_URL'.");
            }
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_SECRET_KEY", out REST_SECRET_KEY) || string.IsNullOrWhiteSpace(REST_SECRET_KEY))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_SECRET_KEY'.");
            }
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", out PUBLIC_KEY) || string.IsNullOrWhiteSpace(PUBLIC_KEY))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY'.");
            }
            if (!paymentCredentialsList.TryGetValue("HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", out MERCHANT_ID) || string.IsNullOrWhiteSpace(MERCHANT_ID))
            {
                throw new ArgumentException("Please enter a valid 'HOSTED_PAYMENT_GATEWAY_MERCHANT_ID'.");
            }

            configParameters.Clear();
            LoadConfigParams();


            return new WPCyberSourceCommandHandler();
        }
        /// <summary>
        /// Retrieves the payment status search result for a transaction ID.
        /// </summary>
        /// <param name="trxId">The transaction ID to search for.</param>
        /// <returns>The payment status search result.</returns>
        public override TrxSearchUtilityDTO GetPaymentStatusSearch(string trxId)
        {
            log.LogMethodEntry(trxId);
            TrxSearchUtilityDTO trxSearchResult = new TrxSearchUtilityDTO
            {
                TransactionId = trxId
            };

            if (string.IsNullOrWhiteSpace(trxId))
            {
                log.Error("Invalid transaction ID.");
                trxSearchResult.ErrorMessage = "Invalid transaction ID.";
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.PaymentStatus = "Failed";
                trxSearchResult.TransactionId = "null";
                return trxSearchResult;
            }

            try
            {
                WPCyberSourceCommandHandler commandHandler = InitializeCommandHandler(PaymentCredentailsList);
                if (commandHandler == null)
                {
                    log.Error("CommandHandler instance is null");
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    return trxSearchResult;
                }

                TxSearchRequestDTO searchRequestDTO = commandHandler.GetTxSearchRequestDTO(trxId);
                log.Debug("GetTxSearchRequestDTO- searchRequestDTO: " + searchRequestDTO);
                TxSearchResponseDTO txSearchResponseDTO = commandHandler.CreateTxSearch(searchRequestDTO, configParameters);

                if (txSearchResponseDTO == null || txSearchResponseDTO.totalCount <= 0)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    trxSearchResult.ErrorMessage = "Error processing your search";
                    trxSearchResult.FormattedResponse = "null";
                    trxSearchResult.PaymentStatus = "Failed";
                    trxSearchResult.TransactionId = trxId;
                    return trxSearchResult;
                }
                log.Info("Total count of txSearchResponse: " + txSearchResponseDTO.totalCount.ToString());

                TxStatusDTO txStatus = commandHandler.GetTxStatusFromSearchResponse(txSearchResponseDTO);
                log.Debug("GetTxStatusFromSearchResponse- txStatus: " + txStatus);

                PaymentStatusType salePaymentStatus = MapPaymentStatus(txStatus.reasonCode.ToString(), PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());

                string formattedJson = JsonConvert.SerializeObject(txStatus, Formatting.Indented);
                trxSearchResult.FormattedResponse = formattedJson;
                trxSearchResult.PaymentStatus = salePaymentStatus.ToString();
            }
            catch (Exception ex)
            {
                log.Error("Error searching transaction details for trxId: " + trxId);
                trxSearchResult.FormattedResponse = "null";
                trxSearchResult.ErrorMessage = ex.Message;
                trxSearchResult.PaymentStatus = "Failed"; // Set payment status to "Failed"
            }
            log.LogMethodExit(trxSearchResult.ToString());
            return trxSearchResult;
        }

        /// <summary>
        /// Maps the payment status from raw response to Semnox payment status.
        /// </summary>
        /// <param name="rawPaymentGatewayStatus">The raw payment gateway status.</param>
        /// <param name="pgwTrxType">The type of payment gateway transaction.</param>
        /// <returns>The mapped payment status type.</returns>
        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType defaultStatus = PaymentStatusType.FAILED; //default status
            PaymentStatusType paymentStatusType = defaultStatus;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            {"100", PaymentStatusType.SUCCESS},
                            {"110", PaymentStatusType.FAILED}, //Partial Payment
                            {"104", PaymentStatusType.FAILED}, //Duplicate payment
                            {"102", PaymentStatusType.FAILED},
                            {"150", PaymentStatusType.FAILED},
                            {"151", PaymentStatusType.FAILED},
                            {"152", PaymentStatusType.FAILED},
                            {"200", PaymentStatusType.FAILED},
                            {"201", PaymentStatusType.FAILED},
                            {"202", PaymentStatusType.FAILED},
                            {"203", PaymentStatusType.FAILED},
                            {"204", PaymentStatusType.FAILED},
                            {"205", PaymentStatusType.FAILED},
                            {"207", PaymentStatusType.FAILED},
                            {"208", PaymentStatusType.FAILED},
                            {"210", PaymentStatusType.FAILED},
                            {"211", PaymentStatusType.FAILED},
                            {"221", PaymentStatusType.FAILED},
                            {"222", PaymentStatusType.FAILED},
                            {"230", PaymentStatusType.FAILED},
                            {"231", PaymentStatusType.FAILED},
                            {"232", PaymentStatusType.FAILED},
                            {"233", PaymentStatusType.FAILED},
                            {"234", PaymentStatusType.FAILED},
                            {"236", PaymentStatusType.FAILED},
                            {"240", PaymentStatusType.FAILED},
                            {"475", PaymentStatusType.FAILED},
                            {"476", PaymentStatusType.FAILED},
                            {"481", PaymentStatusType.FAILED},
                            {"520", PaymentStatusType.FAILED},
                            {"-1", PaymentStatusType.NONE},
                            {"-2", PaymentStatusType.NONE},
                        };
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            {"PENDING", PaymentStatusType.SUCCESS}, //Ref link: https://developer.cybersource.com/api-reference-assets/index.html#payments_refund_refund-a-payment_responsefielddescription_201_status
                        };
                        break;
                    case PaymentGatewayTransactionType.VOID:
                        pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
                        {
                            {"VOIDED", PaymentStatusType.SUCCESS},
                            {"CANCELLED", PaymentStatusType.FAILED}, //Ref link: https://developer.cybersource.com/api-reference-assets/index.html#payments_void_void-a-payment_responsefielddescription_201_status
                            {"FAILED", PaymentStatusType.FAILED},
                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to failed.");
                    paymentStatusType = defaultStatus;
                }
            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentStatusType = defaultStatus;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }

    }
}

