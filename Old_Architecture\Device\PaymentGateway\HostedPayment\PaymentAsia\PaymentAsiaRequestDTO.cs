﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Newtonsoft.Json;


namespace PaymentAsiaPOC.PaymentAsia
{
    public class PaymentAsiaRequestDTO
    {
        public string merchant_reference { get; set; }

        public string currency { get; set; }

        public string amount { get; set; }

        public string sign { get; set; }

        public string return_url { get; set; }

        public string customer_ip { get; set; }

        public string customer_first_name { get; set; }

        public string customer_last_name { get; set; }

        public string customer_address { get; set; }

        public string customer_phone { get; set; }

        public string customer_email { get; set; }

        public string customer_state { get; set; }

        public string customer_country { get; set; }

        public string customer_postal_code { get; set; }

        public string network { get; set; }

        public string subject { get; set; }

        public string notify_url { get; set; }

        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }

    }


    public class PaymentAsiaQueryTransactionsRequestDTO
    {
        public string merchant_reference { get; set; }

        public string sign { get; set; }

    }

    public class PaymentAsiaVoidRefundRequestDTO
    {
        public string merchant_reference { get; set; }
        public string amount { get; set; }
        public string sign { get; set; }
    }
    public class PaymentAsiaRefundStatusRequestDTO
    {
        public string refund_reference { get; set; }
        public string sign { get; set; }
    }

    public class PaymentAsiaSettlementDetailsRequestDTO
    {
        public string settlement_date { get; set; }
        public string network { get; set; }
        public string sign { get; set; }
    }
}