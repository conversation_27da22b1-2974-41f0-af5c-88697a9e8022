﻿/********************************************************************************************
 * Project Name - CommnonAPI
 * Description  - Transaction use case controller to apply Web Payment for transaction.
 * 
 **************
 **Version Log
 **************
 *Version     Date            Modified By                Remarks          
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin                  Created
 * ********************************************************************************************/

using System;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using Semnox.Core.Utilities;
using Semnox.Core.GenericUtilities;
using System.Threading.Tasks;
using Semnox.CommonAPI.Helpers;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.WebPayments;

namespace Semnox.CommonAPI.Controllers.WebPayment
{
    public class StartWebPaymentController : ApiController
    {
        private Semnox.Parafait.logging.Logger log;

        /// <summary>
        /// Start Hosted Payment
        /// </summary>
        [HttpPost]
        [Route("api/Transaction/WebPayment/{TransactionId}/StartWebPayment")]
        [Authorize]
        public async Task<HttpResponseMessage> Post([FromUri] int TransactionId, [FromBody]TransactionPaymentDTO transactionPaymentDTO)
        {
            ExecutionContext executionContext = null;
            try
            {
                executionContext = ExecutionContextBuilder.GetExecutionContext(Request);
                log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
                log.LogMethodEntry(TransactionId, transactionPaymentDTO);

                IWebPaymentsUseCases webPaymentUseCases = WebPaymentsUseCaseFactory.GetWebPaymentsUseCases(executionContext, RequestIdentifierHelper.GetRequestIdentifier(Request));
                WebPaymentDTO result = await webPaymentUseCases.StartWebPayment(TransactionId, transactionPaymentDTO);
                log.LogMethodExit(result);
                return Request.CreateResponse(HttpStatusCode.OK, new { data = result });
            }
            catch (ParafaitApplicationException ex)
            {
                string customException = GenericExceptionMessage.GetValidCustomExeptionMessage(ex, executionContext);
                log.Error(customException);
                return Request.CreateResponse(HttpStatusCode.BadRequest, new { data = customException, exception = ExceptionSerializer.Serialize(ex) });
            }
            catch (Exception ex)
            {
                string customException = GenericExceptionMessage.GetValidCustomExeptionMessage(ex, executionContext);
                log.Error(customException);
                return Request.CreateResponse(HttpStatusCode.InternalServerError, new { data = customException, exception = ExceptionSerializer.Serialize(ex) });
            }
        }

    }
}
