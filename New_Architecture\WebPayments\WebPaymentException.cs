﻿/********************************************************************************************
* Project Name - Web Payments
 * Description -Exception classes for web payments
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using Semnox.Core.Utilities;
using System;
using System.Runtime.Serialization;
using System.Security.Permissions;

namespace Semnox.Parafait.WebPayments
{
    public enum WebPaymentErrorCodes
    {
        /// <summary>
        /// MissingPaymentInformationException
        /// </summary>
        WP_00001,
        /// <summary>
        /// DebitPaymentMissingOTPException
        /// </summary>
        WP_00002,
        /// <summary>
        /// PaymentIdentifierNotFoundException
        /// </summary>
        WP_00003,
        WP_00004,
        WP_00005,
        WP_00006,
        WP_00007,
        WP_00008,
        WP_00009,
        /// <summary>
        /// WebPaymentException
        /// </summary>
        WP_00010,
    }

    [Serializable]
    /// <summary>
    /// Represents MissingPaymentInformationException error that occur during parafait application execution. 
    /// </summary>
    public class MissingPaymentInformationException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public MissingPaymentInformationException(string message) : base(WebPaymentErrorCodes.WP_00001, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public MissingPaymentInformationException(string message, Exception innerException) : base(WebPaymentErrorCodes.WP_00001, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected MissingPaymentInformationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    [Serializable]
    /// <summary>
    /// Represents DebitPaymentMissingOTPException error that occur during parafait application execution. 
    /// </summary>
    public class DebitPaymentMissingOTPException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DebitPaymentMissingOTPException(string message) : base(WebPaymentErrorCodes.WP_00002, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public DebitPaymentMissingOTPException(string message, Exception innerException) : base(WebPaymentErrorCodes.WP_00002, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected DebitPaymentMissingOTPException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }
    [Serializable]
    /// <summary>
    /// Represents PaymentIdentifierNotFoundException error that occur during parafait application execution. 
    /// </summary>
    public class PaymentIdentifierNotFoundException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentIdentifierNotFoundException(string message) : base(WebPaymentErrorCodes.WP_00002, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public PaymentIdentifierNotFoundException(string message, Exception innerException) : base(WebPaymentErrorCodes.WP_00002, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected PaymentIdentifierNotFoundException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }

    [Serializable]
    /// <summary>
    /// Represents MissingPaymentInformationException error that occur during parafait application execution. 
    /// </summary>
    public class WebPaymentException : ParafaitApplicationException, ISerializable
    {
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public WebPaymentException(string message) : base(WebPaymentErrorCodes.WP_00010, message)
        {
        }
        /// <summary>
        /// Parameterized constructor of ParafaitApplicationException.
        /// </summary>
        public WebPaymentException(string message, Exception innerException) : base(WebPaymentErrorCodes.WP_00010, message, innerException)
        {

        }

        /// <summary>
        /// Serialization Constructor
        /// </summary>
        /// <param name="info"></param>
        /// <param name="context"></param>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        protected WebPaymentException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
        /// <summary>
        /// ISerializable interface implementation
        /// </summary>
        [SecurityPermissionAttribute(SecurityAction.Demand, SerializationFormatter = true)]
        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            if (info == null)
            {
                throw new ArgumentNullException("info");
            }
            base.GetObjectData(info, context);
        }
    }
}
