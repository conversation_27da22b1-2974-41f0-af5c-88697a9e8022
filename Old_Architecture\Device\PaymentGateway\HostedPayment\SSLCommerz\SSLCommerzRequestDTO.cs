﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.SSLCommerz
{
    public class SSLCommerzRequestDTO
    {
        public string store_id { get; set; }
        public string store_passwd { get; set; }
        public double total_amount { get; set; }
        public string currency { get; set; }
        public string tran_id { get; set; }
        public string success_url { get; set; }
        public string fail_url { get; set; }
        public string cancel_url { get; set; }
        public string ipn_url { get; set; }

        public string cus_email { get; set; }
        public string cus_name { get; set; }
        public string cus_add1 { get; set; }
        public string cus_city { get; set; }
        public string cus_country { get; set; }
        public string cus_phone { get; set; }
        public string shipping_method { get; set; }
        public string product_name { get; set; }
        public string product_category { get; set; }
        public string product_profile { get; set; }
        public string value_a { get; set; }
        public string value_b { get; set; }


        public override string ToString()

        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
