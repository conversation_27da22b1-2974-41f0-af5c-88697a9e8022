﻿using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Semnox.Parafait.Discounts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Globalization;
using System.Web;
using System.Collections.Specialized;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.SSLCommerz
{
    class SSLCommerzCallbackHostedPaymentGateway : HostedPaymentGateway
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private HostedGatewayDTO hostedGatewayDTO;
        private string STORE_ID;
        private string STORE_PASSWD;

        private string POST_URL;
        private string BASE_URL;
        private string CURRENCY;

        private string trxInitializeUrl;
        private string verifyTrxApiUrl;
        private string refundApiUrl;
        private string paymentPageLink;


        const string VALID = "valid";
        const string FAILED = "failed";
        const string VALIDATED = "validated";
        const string REVERSED = "reversed";
        const string PENDING = "pending";
        const string PROCESSING = "processing";
        const string ONGOING = "ongoing";

        private string successResponseAPIURL;
        private string failureResponseAPIURL;
        private string cancelResponseAPIURL;
        private string callbackResponseAPIURL;
        const string WEBSITECONFIGURATION = "WEB_SITE_CONFIGURATION";

        SSLCommerzHostedCommandHandler sslCommerzCommandHandler;

        private static readonly Dictionary<string, PaymentStatusType> SaleStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Valid", PaymentStatusType.SUCCESS },
            { "Validated", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
            { "Unattempted", PaymentStatusType.FAILED },
            { "Expired", PaymentStatusType.FAILED },
            { "Pending", PaymentStatusType.PENDING },
            { "Processing", PaymentStatusType.PENDING }
        };
        private static readonly Dictionary<string, PaymentStatusType> RefundStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase)
        {
            { "Success", PaymentStatusType.SUCCESS },
            { "Failed", PaymentStatusType.FAILED },
            { "Processing", PaymentStatusType.FAILED }
        };

        public SSLCommerzCallbackHostedPaymentGateway(Utilities utilities, bool isUnattended, ShowMessageDelegate showMessageDelegate, WriteToLogDelegate writeToLogDelegate)
           : base(utilities, isUnattended, showMessageDelegate, writeToLogDelegate)
        {
            log.LogMethodEntry(utilities, isUnattended, showMessageDelegate, writeToLogDelegate);

            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            hostedGatewayDTO = new HostedGatewayDTO();
            Initialize();
            this.BuildTransactions = false;
            log.LogMethodExit(null);
        }
        public override void Initialize()
        {
            log.LogMethodEntry();


            STORE_ID = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            STORE_PASSWD = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");

            POST_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");

            BASE_URL = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            CURRENCY = ParafaitDefaultContainerList.GetParafaitDefault(utilities.ExecutionContext, "CURRENCY_CODE");
            if (BASE_URL.EndsWith("/"))
            {
                BASE_URL = BASE_URL.Remove(BASE_URL.Length - 1);
            }
            trxInitializeUrl = BASE_URL + $"/gwprocess/v4/api.php";

            verifyTrxApiUrl = BASE_URL + $"/validator/api/merchantTransIDvalidationAPI.php";
            refundApiUrl = BASE_URL + $"/validator/api/merchantTransIDvalidationAPI.php";

            sslCommerzCommandHandler = new SSLCommerzHostedCommandHandler(STORE_ID, STORE_PASSWD, BASE_URL, trxInitializeUrl, verifyTrxApiUrl, refundApiUrl);

            StringBuilder errMsgBuilder = new StringBuilder();
            string errMsgFormat = "Please enter {0} value in configuration. Site : " + utilities.ParafaitEnv.SiteId;


            if (string.IsNullOrWhiteSpace(STORE_ID))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(STORE_PASSWD))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            }
            if (string.IsNullOrWhiteSpace(POST_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_API_URL");
            }
            if (string.IsNullOrWhiteSpace(BASE_URL))
            {
                errMsgBuilder.AppendFormat(errMsgFormat, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            }
            string errMsg = errMsgBuilder.ToString();

            if (!string.IsNullOrWhiteSpace(errMsg))
            {
                log.Error(errMsg);
                throw new Exception(utilities.MessageUtils.getMessage(errMsg));
            }


            string apiSite = "";
            string webSite = "";

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API") != null)
            {
                apiSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_API").Description;

            }
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB") != null)
            {
                webSite = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB").Description;

            }

            if (string.IsNullOrWhiteSpace(apiSite) || string.IsNullOrWhiteSpace(webSite))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB." + apiSite + "\n" + webSite);
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_API/ANGULAR_PAYMENT_WEB."));
            }
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK") != null)
            {
                String linkPage = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "ANGULAR_PAYMENT_WEB_PAGE_LINK").Description;
                linkPage = linkPage.Replace("@gateway", PaymentGateways.SSLCommerzCallbackHostedPayment.ToString());
                paymentPageLink = webSite + linkPage;

                try
                {
                    Uri uri = new Uri(paymentPageLink);
                    UriBuilder uriBuilder = new UriBuilder(uri);
                    var queryParams = HttpUtility.ParseQueryString(uriBuilder.Query);

                    if (queryParams["payload"] == "@payload")
                    {
                        queryParams.Remove("payload");
                    }

                    if (queryParams["paymentSession"] == null)
                    {
                        queryParams.Add("paymentSession", "@paymentSession");
                    }

                    uriBuilder.Query = queryParams.ToString();
                    paymentPageLink = uriBuilder.Uri.ToString().Replace("%40paymentSession", "@paymentSession");
                }
                catch (Exception ex)
                {
                    log.Error("Error building paymentRequestLink " + ex.Message);
                    throw new Exception(utilities.MessageUtils.getMessage("Please check setup for WEB_SITE_CONFIGURATION LookUpValues description for  ANGULAR_PAYMENT_WEB/ANGULAR_PAYMENT_WEB_PAGE_LINK."));
                }
            }
            else
            {
                paymentPageLink = webSite + $"/payment/paymentGateway?paymentGatewayName={PaymentGateways.SSLCommerzCallbackHostedPayment.ToString()}&paymentSession=@paymentSession";
            }
            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL") != null)
            {
                successResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL") != null)
            {
                failureResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL") != null)
            {
                cancelResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL") != null)
            {
                callbackResponseAPIURL = LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CALLBACK_RESPONSE_API_URL").Description.Replace("@gateway", PaymentGateways.SSLCommerzCallbackHostedPayment.ToString());

            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.SuccessURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "SUCCESS_REDIRECT_URL").Description;


            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.FailureURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "FAILURE_REDIRECT_URL").Description;



            }

            if (LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL") != null)
            {
                hostedGatewayDTO.CancelURL = webSite + LookupsContainerList.GetLookupValuesContainerDTOOrDefault(utilities.ExecutionContext.GetSiteId(), WEBSITECONFIGURATION, "CANCEL_REDIRECT_URL").Description;



            }
            hostedGatewayDTO.PGSuccessResponseMessage = "OK";
            hostedGatewayDTO.PGFailedResponseMessage = "OK";

            if (string.IsNullOrWhiteSpace(successResponseAPIURL) || string.IsNullOrWhiteSpace(callbackResponseAPIURL) || string.IsNullOrWhiteSpace(failureResponseAPIURL))
            {
                log.Error("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL.");
                throw new Exception(utilities.MessageUtils.getMessage("Please enter WEBSITECONFIGURATION LookUpValues description for  SuccessURL/FailureURL/CallBackURL."));
            }

            successResponseAPIURL = apiSite + successResponseAPIURL;
            callbackResponseAPIURL = apiSite + callbackResponseAPIURL;
            failureResponseAPIURL = apiSite + failureResponseAPIURL;
            cancelResponseAPIURL = apiSite + cancelResponseAPIURL;

            log.LogMethodExit();
        }

        /// <summary>
        /// Creates a initial gateway request.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <param name="paymentToken"></param>
        /// <returns>HostedGatewayDTO</returns>
        public override HostedGatewayDTO CreateGatewayPaymentInitialRequest(TransactionPaymentsDTO transactionPaymentsDTO, string paymentToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentToken);
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("TransactionPaymentsDTO is null");
                    throw new ArgumentNullException(nameof(transactionPaymentsDTO));
                }
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, TransactionType.SALE.ToString());

                IDictionary<string, string> requestParamsDict = new Dictionary<string, string>();
                requestParamsDict.Add("paymentSession", cCRequestPGWDTO.Guid);
                requestParamsDict.Add("paymentToken", paymentToken);

                this.hostedGatewayDTO.GatewayRequestString = JsonConvert.SerializeObject(requestParamsDict);
                this.hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(this.hostedGatewayDTO.GatewayRequestString, paymentPageLink);
                //this.hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(requestParamsDict, paymentPageLink, "authForm");

                log.Debug("Request string:" + this.hostedGatewayDTO.GatewayRequestString);
                log.Debug("Direct request link:" + this.hostedGatewayDTO.PaymentRequestLink);
                //log.Debug("GatewayRequestFormString:" + this.hostedGatewayDTO.GatewayRequestFormString);

            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }


        public override HostedGatewayDTO CreateGatewayPaymentSession(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            SSLCommerzResponseDTO sslCommerzCreatePaymentResponse = null;
            try
            {
                log.LogMethodEntry(transactionPaymentsDTO);
                string checkoutUrl = string.Empty;
                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.CreditCardName) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute5) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute4) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.NameOnCreditCard) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.Attribute3) ||
    string.IsNullOrWhiteSpace(transactionPaymentsDTO.CardEntitlementType))
                {
                    //Dictionary<string, string> errorParams = new Dictionary<string, string>
                    //                        {
                    //                            { "PaymentFailure", "1" },
                    //                            { "ErrorMessage", "Payment has been declined! Please enter all the mandatory customer details" },
                    //                            { "TrxId", transactionPaymentsDTO.TransactionId.ToString() },
                    //                            {"Date", utilities.getServerTime().ToString() }
                    //                        };
                    //hostedGatewayDTO.GatewayRequestString = sslCommerzCommandHandler.ErrorForm(errorParams);
                    //CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                    //List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                    //searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, transactionPaymentsDTO.TransactionId.ToString()));
                    //CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                    //TransactionSiteId = cCRequestsPGWDTO.SiteId;

                    //List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;

                    //CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    //List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    //searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.INVOICE_NUMBER, cCRequestsPGWDTO.RequestID.ToString()));
                    //cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                    //if (cCTransactionsPGWDTOList == null)
                    //{
                    //    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    //    {
                    //        TranCode = PaymentGatewayTransactionType.SALE.ToString(),
                    //        InvoiceNo = cCRequestsPGWDTO.RequestID.ToString(),
                    //        TransactionDatetime = utilities.getServerTime(),
                    //        DSIXReturnCode = "Payment has been declined! Please enter all the mandatory customer details",
                    //        TextResponse = "FAILED",
                    //        RecordNo = transactionPaymentsDTO.TransactionId.ToString()
                    //    };
                    //    this.hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;
                    //    CCTransactionsPGWBL cCTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO);
                    //    cCTransactionsPGWBL.Save();
                    //}
                    log.Error("Payment has been declined! Please enter all the mandatory customer details");
                    throw new Exception("redirect checkoutmessage");
                }
                else
                {

                    // error page for missing customer details - build error page when it fails - wpcybersource
                    SSLCommerzRequestDTO sslCommerzRequestDTO = new SSLCommerzRequestDTO
                    {
                        store_id = STORE_ID,
                        store_passwd = STORE_PASSWD,
                        tran_id = Convert.ToString(transactionPaymentsDTO.TransactionId) ?? "",
                        currency = transactionPaymentsDTO.CurrencyCode ?? "",
                        total_amount = transactionPaymentsDTO.Amount,
                        success_url = string.IsNullOrWhiteSpace(successResponseAPIURL) ? "" : successResponseAPIURL,
                        fail_url = string.IsNullOrWhiteSpace(failureResponseAPIURL) ? "" : failureResponseAPIURL,
                        cancel_url = string.IsNullOrWhiteSpace(cancelResponseAPIURL) ? "" : cancelResponseAPIURL,
                        cus_name = transactionPaymentsDTO.CreditCardName,
                        cus_add1 = transactionPaymentsDTO.Attribute5,
                        cus_city = transactionPaymentsDTO.Attribute4,
                        cus_email = transactionPaymentsDTO.NameOnCreditCard,
                        cus_country = transactionPaymentsDTO.Attribute3,
                        cus_phone = transactionPaymentsDTO.CardEntitlementType,
                        shipping_method = "NO",
                        value_a = Convert.ToString(transactionPaymentsDTO.PaymentModeId) ?? ""
                    };

                    if (transactionPaymentsDTO.paymentModeDTO != null)
                    {
                        foreach (DiscountCouponsDTO dcDTO in transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList)
                        {
                            sslCommerzRequestDTO.product_name = dcDTO.FromNumber ?? "";
                            sslCommerzRequestDTO.product_category = dcDTO.FromNumber ?? "";
                            sslCommerzRequestDTO.product_profile = dcDTO.FromNumber ?? "";

                        }
                    }
                    else
                    {
                        sslCommerzRequestDTO.product_name = " ";
                        sslCommerzRequestDTO.product_category = " ";
                        sslCommerzRequestDTO.product_profile = " ";
                    }

                    log.Debug("sslCommerzRequestDTO" + sslCommerzRequestDTO.ToString());
                    sslCommerzCreatePaymentResponse = sslCommerzCommandHandler.CreateCheckout(sslCommerzRequestDTO);

                    if (sslCommerzCreatePaymentResponse == null)
                    {
                        log.Error("CreateGatewayPaymentRequest(): Checkout Transaction Response was empty");
                        throw new Exception("Error: could not create payment session");
                    }

                    if (string.IsNullOrWhiteSpace(sslCommerzCreatePaymentResponse.GatewayPageURL))
                    {
                        log.Error("GatewayPageURL was null");
                        throw new Exception("Error creating the payment request");
                    }
                    checkoutUrl = sslCommerzCreatePaymentResponse.GatewayPageURL;
                    log.Debug($"CreateGatewayPaymentRequest(): Payment ResponseDto: {sslCommerzCreatePaymentResponse}");
                    log.Debug($"CreateGatewayPaymentRequest(): Payment request is created, redirecting to Checkout URL: {checkoutUrl}");

                    hostedGatewayDTO.RequestURL = checkoutUrl;

                    hostedGatewayDTO.GatewayRequestFormString = GetSubmitFormKeyValueList(checkoutUrl, "fromSSLCommerzForm", "GET");
                    //hostedGatewayDTO.PaymentRequestLink = GeneratePaymentPageLink(hostedGatewayDTO.GatewayRequestFormString, paymentPageLink);

                    log.Info("request url:" + hostedGatewayDTO.RequestURL);
                    log.Info("request string:" + hostedGatewayDTO.GatewayRequestFormString);
                    //log.Debug("direct request link:" + hostedGatewayDTO.PaymentRequestLink);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit("gateway dto:" + hostedGatewayDTO.ToString());
            return hostedGatewayDTO;
        }


        /// <summary>
        /// GetSubmitFormKeyValueList
        /// </summary>
        /// <param name="postparamslist"></param>
        /// <param name="URL"></param>
        /// <param name="FormName"></param>
        /// <param name="submitMethod"></param>
        /// <returns></returns>
        private string GetSubmitFormKeyValueList(/*IDictionary<string, string> postparamslist, */string URL, string FormName, string submitMethod = "POST")
        {
            string Method = submitMethod;
            System.Text.StringBuilder builder = new System.Text.StringBuilder();
            builder.Clear();

            builder.Append("<html>");

            builder.Append(string.Format("<body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            //foreach (KeyValuePair<string, string> param in postparamslist)
            //{
            //    builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            //}

            builder.Append("</form>");
            builder.Append("</body></html>");
            return builder.ToString();
        }

        private SSLCommerzCallbackResponse GetResposeObj(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            SSLCommerzCallbackResponse responseObj = null;

            string jsonString = ConvertQueryStringToJson(gatewayResponse);
            log.Debug("Response as JSON: " + jsonString);

            responseObj = JsonConvert.DeserializeObject<SSLCommerzCallbackResponse>(jsonString);

            log.LogMethodExit(responseObj);
            return responseObj;
        }

        private string ConvertQueryStringToJson(string gatewayResponse)
        {
            log.LogMethodEntry();
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(gatewayResponse);

            Dictionary<string, string> responseDictionary = new Dictionary<string, string>();

            foreach (var key in responseCollection.AllKeys)
            {
                responseDictionary.Add(key, responseCollection[key]);
            }

            string responseJson = JsonConvert.SerializeObject(responseDictionary);

            log.LogMethodExit(responseJson);
            return responseJson;
        }

        /// <summary>
        /// Initiates the payment processing based on the provided gateway response.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the initialized payment details.
        /// </returns>
        public override HostedGatewayDTO InitiatePaymentProcessing(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);

            if (hostedGatewayDTO == null)
            {
                hostedGatewayDTO = new HostedGatewayDTO();
            }

            SSLCommerzCallbackResponse gatewayData = GetResposeObj(gatewayResponse);

            if (!string.IsNullOrEmpty(gatewayData.tran_id))
            {
                int trxId;
                if (int.TryParse(gatewayData.tran_id, out trxId))
                {
                    hostedGatewayDTO.TrxId = trxId;
                }
            }
            if (!string.IsNullOrEmpty(gatewayData.bank_tran_id))
            {
                hostedGatewayDTO.GatewayReferenceNumber = gatewayData.bank_tran_id;
            }
            log.Debug("TrxId:" +hostedGatewayDTO.TrxId + " :: GatewayReferenceNumber:" + hostedGatewayDTO.GatewayReferenceNumber);

            log.LogMethodExit(hostedGatewayDTO);
            return hostedGatewayDTO;
        }


        /// <summary>
        /// Processes the response received from the payment gateway and updates the payment status accordingly.
        /// </summary>
        /// <param name="gatewayResponse">The response received from the payment gateway.</param>
        /// <returns>
        /// Returns a HostedGatewayDTO containing the updated payment details and status.
        /// </returns>
        /// <exception cref="Exception">Thrown when there is an error processing the payment or updating the payment status.</exception>
        public override HostedGatewayDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            hostedGatewayDTO.CCTransactionsPGWDTO = null;
            hostedGatewayDTO.TransactionPaymentsDTO = new TransactionPaymentsDTO();
            SSLCommerzCallbackResponse sslCommerzResponse = null;
            try
            {
                sslCommerzResponse = GetResposeObj(gatewayResponse);
                log.Debug("gatewayResponseDTO: " + sslCommerzResponse.ToString());


                if (sslCommerzResponse.tran_id != null)
                {
                    log.Debug("Transaction id: " + sslCommerzResponse.tran_id);
                    hostedGatewayDTO.TransactionPaymentsDTO.TransactionId = Convert.ToInt32(sslCommerzResponse.tran_id);
                }
                else
                {
                    log.Error("Response for Sale Transaction doesn't contain TrxId.");
                    throw new Exception("Error processing your payment");
                }
                SSLCommerzTrxStatusElementDTO trxSearchResponse = sslCommerzCommandHandler.CreateTxSearch(sslCommerzResponse.tran_id);

                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("Error processing your payment");
                }

                hostedGatewayDTO.TransactionPaymentsDTO.Amount = Convert.ToDouble(trxSearchResponse.amount);
                hostedGatewayDTO.TransactionPaymentsDTO.CurrencyCode = trxSearchResponse.currency;
                hostedGatewayDTO.TransactionPaymentsDTO.Reference = trxSearchResponse.bank_tran_id.ToString();//sslcommerz transaction id
                hostedGatewayDTO.TransactionPaymentsDTO.PaymentModeId = Convert.ToInt32(trxSearchResponse.value_a);//paymentmode id
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardNumber = trxSearchResponse.card_no;
                hostedGatewayDTO.TransactionPaymentsDTO.CreditCardName = trxSearchResponse.card_type;
                hostedGatewayDTO.TransactionPaymentsDTO.NameOnCreditCard = trxSearchResponse.card_issuer;


                //check if ccTransactionPGW updated
                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString()));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                TransactionSiteId = cCRequestsPGWDTO.SiteId;

                List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList;
                if (!string.IsNullOrEmpty(hostedGatewayDTO.TransactionPaymentsDTO.Reference))
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.REF_NO, hostedGatewayDTO.TransactionPaymentsDTO.Reference.ToString()));
                    cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetNonReversedCCTransactionsPGWDTOList(searchParameters);
                }
                else
                {
                    log.Error("No reference id/Transaction present in PaymentAsia receipt response");
                    cCTransactionsPGWDTOList = null;
                }
                PaymentStatusType salePaymentStatus = MapPaymentStatus(trxSearchResponse.status, PaymentGatewayTransactionType.SALE);
                log.Debug("Value of salePaymentStatus: " + salePaymentStatus.ToString());
                if (salePaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Payment status is success");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_COMPLETED;
                }
                else if (salePaymentStatus == PaymentStatusType.PENDING)
                {
                    log.Debug("Payment status is pending");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_PENDING;
                }
                else if (salePaymentStatus == PaymentStatusType.FAILED)
                {
                    log.Debug("Payment status is failed");
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }
                else
                {
                    log.Error("Payment status is unknown. Considering status as failed Status: " + salePaymentStatus.ToString());
                    hostedGatewayDTO.PaymentStatus = PaymentStatusType.FAILED;
                    hostedGatewayDTO.PaymentProcessStatus = PaymentProcessStatusType.PAYMENT_FAILED;
                }

                log.Debug("Trying to update the CC request payment processing status " + hostedGatewayDTO.PaymentStatus + ":" + hostedGatewayDTO.PaymentProcessStatus);
                CCRequestPGWBL cCRequestPGWBL = new CCRequestPGWBL(utilities.ExecutionContext, cCRequestsPGWDTO.RequestID);
                int rowsUpdated = cCRequestPGWBL.ChangePaymentProcessingStatus(PaymentProcessStatusType.PAYMENT_PROCESSING.ToString(), hostedGatewayDTO.PaymentProcessStatus.ToString());

                if (rowsUpdated == 0)
                {
                    log.Debug("CC request could not be updated, indicates that a parallel thread might be processing this");
                }
                else
                {
                    log.Debug("CC request updated to payment processing status");
                }

                hostedGatewayDTO.PaymentStatus = salePaymentStatus;
                log.Debug("Final hostedGatewayDTO.PaymentStatus: " + hostedGatewayDTO.PaymentStatus);



                if (cCTransactionsPGWDTOList == null)
                {  
                    // update the CCTransactionsPGWDTO
                    log.Debug("No CC Transactions found");
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.RefNo = hostedGatewayDTO.TransactionPaymentsDTO.Reference;
                    cCTransactionsPGWDTO.RecordNo = hostedGatewayDTO.TransactionPaymentsDTO.TransactionId.ToString();
                    cCTransactionsPGWDTO.TextResponse = trxSearchResponse.status;
                    cCTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetStatusMessage(trxSearchResponse.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    cCTransactionsPGWDTO.CardType = trxSearchResponse.card_type;
                    cCTransactionsPGWDTO.CaptureStatus = "Risk level: " + trxSearchResponse.risk_level;
                    cCTransactionsPGWDTO.ResponseOrigin = "Risk title: " + trxSearchResponse.risk_title;
                    cCTransactionsPGWDTO.AcctNo = sslCommerzCommandHandler.GetMaskedCardNumber(trxSearchResponse.card_no);
                    cCTransactionsPGWDTO.PaymentStatus = salePaymentStatus.ToString();
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    hostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                }
            }
            catch (Exception ex)
            {
                log.Error("Payment processing failed", ex);
                throw;
            }

            log.Debug("Final hostedGatewayDTO " + hostedGatewayDTO.ToString());
            log.LogMethodExit(hostedGatewayDTO);

            return hostedGatewayDTO;
        }

        private DateTime GetPaymentDate(SSLCommerzTrxStatusElementDTO response)
        {
            log.LogMethodEntry(response);
            DateTime paymentDate = new DateTime();

            if (response != null)
            {
                log.Debug("Payment Date from response: " + response.tran_date);
                if (DateTime.TryParseExact(response.tran_date, "yyyyMMddhhmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out paymentDate))
                {
                    log.Debug("Payment date parse successfully");
                }
                else
                {
                    log.Error("Payment date parse failed! Assigning payment date to serverTime");
                    paymentDate = utilities.getServerTime();
                }
            }
            else
            {
                log.Error("No response present. Assigning payment date to serverTime");
                paymentDate = utilities.getServerTime();
            }

            log.Debug("Final Payment date: " + paymentDate);

            log.LogMethodEntry(paymentDate);
            return paymentDate;
        }

        /// <summary>
        /// Retrieves the status of a transaction based on the provided transaction ID.
        /// </summary>
        /// <param name="trxId">The ID of the transaction to retrieve status for.</param>
        /// <returns>
        /// Returns a JSON string containing the status information of the transaction, including success or failure status, transaction amount, reference number, account number, and any relevant messages.
        /// </returns>
        /// <exception cref="Exception">Thrown when there are insufficient parameters passed to the request or when an error occurs during the processing of the transaction.</exception>
        [Obsolete("GetTransactionStatus(string) is deprecated, please use GetPaymentStatusSearch(TransactionPaymentsDTO) instead.")]

        public override string GetTransactionStatus(string trxId)
        {
            log.LogMethodEntry(trxId);
            Dictionary<string, Object> dict = new Dictionary<string, Object>();
            dynamic resData;

            try
            {
                if (Convert.ToInt32(trxId) < 0 || string.IsNullOrWhiteSpace(trxId))
                {
                    log.Error("No Transaction id passed");
                    throw new Exception("Insufficient Params passed to the request");
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

                SSLCommerzTrxStatusElementDTO trxSearchResponse = sslCommerzCommandHandler.CreateTxSearch(trxId);
                if (trxSearchResponse == null)
                {
                    log.Error("No matching transaction found for the specified conditions.");
                    throw new Exception("No matching transaction found.");
                }
                log.Debug($"TxSearch Response for TrxId: {trxId}: " + trxSearchResponse);

                if (trxSearchResponse != null)
                {
                    // 14 - Purchase Success
                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                    //cCTransactionsPGWDTO.AuthCode = hostedGatewayDTO.TransactionPaymentsDTO.CreditCardAuthorization;
                    cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (trxSearchResponse.amount));
                    cCTransactionsPGWDTO.RefNo = trxSearchResponse.bank_tran_id;
                    cCTransactionsPGWDTO.RecordNo = trxSearchResponse.tran_id;
                    cCTransactionsPGWDTO.TextResponse = trxSearchResponse.status.ToUpper();
                    cCTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetStatusMessage(trxSearchResponse.status);
                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                    cCTransactionsPGWDTO.CardType = trxSearchResponse.card_type;
                    cCTransactionsPGWDTO.AcctNo = sslCommerzCommandHandler.GetMaskedCardNumber(trxSearchResponse.card_no);
                    cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(trxSearchResponse);

                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                    ccTransactionsPGWBL.Save();

                    string searchDataStatus = trxSearchResponse.status.ToLower();
                    if (searchDataStatus == VALID || searchDataStatus == VALIDATED)
                    {

                        //Update the CCTrxPGW
                        dict.Add("status", "1");
                        dict.Add("message", "success");
                        dict.Add("retref", cCTransactionsPGWDTO.RefNo);
                        dict.Add("amount", cCTransactionsPGWDTO.Authorize);
                        dict.Add("orderId", trxId);
                        dict.Add("acctNo", cCTransactionsPGWDTO.AcctNo);
                    }
                    else if (searchDataStatus == PROCESSING || searchDataStatus == PENDING || searchDataStatus == ONGOING)
                    {

                        log.Error("GetTransactionStatus(): Error updating the payment status");

                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (trxSearchResponse.status));
                        dict.Add("orderId", trxId);
                        throw new Exception("redirect checkoutmessage");
                    }
                    else
                    {
                        log.Error($"GetTransactionStatus: Payment failed for TrxId {trxId}");
                        //cancel the Tx in Parafait DB
                        dict.Add("status", "0");
                        dict.Add("message", (trxSearchResponse.status));
                        dict.Add("orderId", trxId);
                    }
                }
                else
                {
                    log.Error($"Could not find Payment for trxId: {trxId}.");
                    //cancel the Tx in Parafait DB
                    dict.Add("status", "0");
                    dict.Add("message", "no transaction found");
                    dict.Add("orderId", trxId);
                }

                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

                log.LogMethodExit(resData);
                return resData;
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
        }

        /// <summary>
        /// Initiates a refund process for a transaction based on the provided transaction details.
        /// </summary>
        /// <param name="transactionPaymentsDTO">The transaction details for initiating the refund.</param>
        /// <returns>
        /// Returns the updated TransactionPaymentsDTO after processing the refund.
        /// </returns>
        public override TransactionPaymentsDTO RefundAmount(TransactionPaymentsDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            string refundTrxId = string.Empty;
            CCTransactionsPGWDTO ccOrigTransactionsPGWDTO = null;
            bool isRefund = false;
            refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    log.Error("transactionPaymentsDTO was Empty");
                    throw new Exception("Error processing Refund");
                }

                if (transactionPaymentsDTO.CCResponseId > -1)
                {
                    CCTransactionsPGWListBL cCTransactionsPGWListBL = new CCTransactionsPGWListBL();
                    List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>> searchParameters1 = new List<KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>>();
                    searchParameters1.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.RESPONSE_ID, transactionPaymentsDTO.CCResponseId.ToString()));

                    List<CCTransactionsPGWDTO> cCTransactionsPGWDTOList = cCTransactionsPGWListBL.GetCCTransactionsPGWDTOList(searchParameters1);

                    //get transaction type of sale CCRequest record
                    ccOrigTransactionsPGWDTO = cCTransactionsPGWDTOList[0];
                    log.Debug("Original ccOrigTransactionsPGWDTO: " + ccOrigTransactionsPGWDTO.ToString());

                    // to get original TrxId  (in case of POS refund)
                    refundTrxId = ccOrigTransactionsPGWDTO.RecordNo;
                    log.Debug("Original TrxId for refund: " + refundTrxId);
                }
                else
                {
                    // refundTrxId = Convert.ToString(transactionPaymentsDTO.TransactionId);
                    log.Debug("Refund TrxId for refund: " + refundTrxId);
                }

                if (string.IsNullOrWhiteSpace(transactionPaymentsDTO.Reference))
                {
                    log.Error("transactionPaymentsDTO.Reference was null");
                    throw new Exception("Error processing Refund");
                }
                log.Debug("Refund processing started");
                CCRequestPGWDTO cCRequestPGWDTO = CreateCCRequestPGW(transactionPaymentsDTO, CREDIT_CARD_REFUND);

                if (string.IsNullOrEmpty(refundTrxId))
                {
                    log.Error("Error processing Refund");
                    throw new Exception("Error processing Refund");
                }


                SSLCommerzRefundResponseDTO refundResponseDTO = sslCommerzCommandHandler.CreateRefund(transactionPaymentsDTO.Reference, transactionPaymentsDTO.Amount);
                log.Debug("SSLCommerz Refund Response refundResponseDTO: " + refundResponseDTO);

                if (refundResponseDTO == null)
                {
                    log.Error("Refund Response was null");
                    throw new Exception("Refund Failed:We did not receive Response from Payment Gateway.");
                }
                PaymentStatusType refundPaymentStatus = MapPaymentStatus(refundResponseDTO.status, PaymentGatewayTransactionType.REFUND);
                log.Debug("Value of txSearchPaymentStatus: " + refundPaymentStatus.ToString());

                CCTransactionsPGWDTO ccTransactionsPGWDTO = new CCTransactionsPGWDTO();
                ccTransactionsPGWDTO.InvoiceNo = cCRequestPGWDTO.RequestID > 0 ? cCRequestPGWDTO.RequestID.ToString() : refundTrxId;
                ccTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                ccTransactionsPGWDTO.ResponseOrigin = ccOrigTransactionsPGWDTO != null ? ccOrigTransactionsPGWDTO.ResponseID.ToString() : null;
                ccTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                ccTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", transactionPaymentsDTO.Amount);
                ccTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
                ccTransactionsPGWDTO.RecordNo = transactionPaymentsDTO.TransactionId.ToString(); //parafait TrxId
                ccTransactionsPGWDTO.TextResponse = refundResponseDTO.status.ToUpper();
                ccTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetRefundStatusMessage(refundResponseDTO.status);
                ccTransactionsPGWDTO.RefNo = refundResponseDTO.bank_tran_id; //sslcommerz paymentId

                ccTransactionsPGWDTO.PaymentStatus = refundPaymentStatus.ToString();


                if (refundPaymentStatus == PaymentStatusType.SUCCESS)
                {
                    log.Debug("Refund Success for trxId: " + refundTrxId);
                    isRefund = true;
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.status;
                }
                else
                {
                    //refund failed
                    isRefund = false;
                    string errorMessage = refundResponseDTO.status;
                    log.Error($"Refund Failed. Error Message received: {errorMessage}");
                    ccTransactionsPGWDTO.AcqRefData = refundResponseDTO.status;
                }

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(ccTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
                transactionPaymentsDTO.CCResponseId = ccTransactionsPGWBL.CCTransactionsPGWDTO.ResponseID;

                if (!isRefund)
                {
                    throw new Exception("Refund failed");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }


            log.LogMethodExit(transactionPaymentsDTO);
            return transactionPaymentsDTO;
        }

        internal override PaymentStatusType MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentStatusType paymentStatusType = PaymentStatusType.FAILED;
            try
            {
                Dictionary<string, PaymentStatusType> pgwStatusMappingDict = new Dictionary<string, PaymentStatusType>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = SaleStatusMappingDict;
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = RefundStatusMappingDict;
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentStatusType);
                log.Debug("Value of transformed payment status: " + paymentStatusType.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to pending.");
                    paymentStatusType = PaymentStatusType.PENDING;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to pending." + ex);
                paymentStatusType = PaymentStatusType.PENDING;
            }

            log.LogMethodExit(paymentStatusType);
            return paymentStatusType;
        }
        public override HostedGatewayDTO GetPaymentStatusSearch(TransactionPaymentsDTO transactionPaymentsDTO, PaymentGatewayTransactionType paymentGatewayTransactionType = PaymentGatewayTransactionType.STATUSCHECK)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            HostedGatewayDTO paymentStatusSearchHostedGatewayDTO = new HostedGatewayDTO();
            SSLCommerzTrxStatusElementDTO orderStatusResult = null;
            string trxIdString = string.Empty;

            try
            {
                trxIdString = Convert.ToString(transactionPaymentsDTO.TransactionId);

                if (string.IsNullOrWhiteSpace(trxIdString))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxIdString));
                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);
                log.Info("Sale cCRequestsPGWDTO: " + cCRequestsPGWDTO);

                //Call TxSearch API
                orderStatusResult = sslCommerzCommandHandler.CreateTxSearch(trxIdString);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error($"Order status for trxId: {trxIdString} failed.");
                    CCTransactionsPGWDTO tempCCTransactionsPGWDTO = new CCTransactionsPGWDTO
                    {
                        Authorize = transactionPaymentsDTO.Amount.ToString(),
                        Purchase = transactionPaymentsDTO.Amount.ToString(),
                        RecordNo = transactionPaymentsDTO.TransactionId.ToString(),
                        TextResponse = "No payment found",
                        PaymentStatus = PaymentStatusType.NONE.ToString()
                    };
                    paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = tempCCTransactionsPGWDTO;
                    log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
                    return paymentStatusSearchHostedGatewayDTO;
                }

                PaymentStatusType txSearchPaymentStatus = MapPaymentStatus(orderStatusResult.status, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of txSearchPaymentStatus: " + txSearchPaymentStatus.ToString());

                CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
                cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO != null ? cCRequestsPGWDTO.RequestID.ToString() : trxIdString;
                cCTransactionsPGWDTO.Authorize = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.Purchase = string.Format("{0:0.00}", (orderStatusResult.amount));
                cCTransactionsPGWDTO.RefNo = orderStatusResult.bank_tran_id;
                cCTransactionsPGWDTO.RecordNo = orderStatusResult.tran_id;
                cCTransactionsPGWDTO.TextResponse = orderStatusResult.status.ToUpper();
                cCTransactionsPGWDTO.DSIXReturnCode = sslCommerzCommandHandler.GetStatusMessage(orderStatusResult.status);
                cCTransactionsPGWDTO.TranCode = paymentGatewayTransactionType.ToString();
                cCTransactionsPGWDTO.CardType = orderStatusResult.card_type;
                cCTransactionsPGWDTO.PaymentStatus = txSearchPaymentStatus.ToString();
                cCTransactionsPGWDTO.AcctNo = sslCommerzCommandHandler.GetMaskedCardNumber(orderStatusResult.card_no);
                cCTransactionsPGWDTO.TransactionDatetime = GetPaymentDate(orderStatusResult);

                paymentStatusSearchHostedGatewayDTO.CCTransactionsPGWDTO = cCTransactionsPGWDTO;

                CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
                ccTransactionsPGWBL.Save();
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatusSearch. Error message: " + ex.Message);
            }

            log.LogMethodExit(paymentStatusSearchHostedGatewayDTO);
            return paymentStatusSearchHostedGatewayDTO;
        }


    }


}


