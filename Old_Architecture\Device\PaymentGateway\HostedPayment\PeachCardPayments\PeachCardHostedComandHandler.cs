﻿using Newtonsoft.Json;
using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.PeachCardPayments
{
    public class PeachCardHostedCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        private readonly string ENTITY_ID;

        private readonly string SECRET_KEY;
        private string BASE_URL;
        private readonly string AUTH_URL;
        private readonly string POST_URL;
        private readonly string REFERER_URL;
        private readonly string SESSION_URL;
        private readonly string CHECKOUT_URL = "/v2/checkout";
        private readonly string VERIFY_URL = "/status";
        private readonly string REFUND_URL = "/v1/checkout/refund";

        //ENTITY_ID, SECRET_TOKEN, BASE_URL, POST_URL, SESSION_URL, AUTH_URL
        public PeachCardHostedCommandHandler(string ENTITY_ID, string SECRET_KEY, string BASE_URL, string POST_URL, string SESSION_URL, string REFERER_URL, string AUTH_URL)
        {
            this.ENTITY_ID = ENTITY_ID;
            this.SECRET_KEY = SECRET_KEY;
            this.BASE_URL = BASE_URL;
            this.POST_URL = POST_URL;
            this.REFERER_URL = REFERER_URL;
            this.SESSION_URL = SESSION_URL;
            this.AUTH_URL = AUTH_URL;
        }
        public static string EncodeToBase64(string input)
        {
            log.LogMethodEntry(input);
            byte[] bytesToEncode = System.Text.Encoding.UTF8.GetBytes(input);
            string result = Convert.ToBase64String(bytesToEncode);
            log.LogMethodExit(result);
            return result;
        }

        public string GenerateSignature(Dictionary<string, string> fields)
        {
            log.LogMethodEntry(fields.ToString());
            Dictionary<string, string> sortedFields = fields.OrderBy(kv => kv.Key)
                .ToDictionary(kv => kv.Key, kv => kv.Value);

            string concatenatedKeyValuePairs = string.Join("", sortedFields.Select(kv => $"{kv.Key}{kv.Value}"));

            byte[] secretKeyBytes = Encoding.UTF8.GetBytes(SECRET_KEY);

            byte[] concatenatedValueBytes = Encoding.UTF8.GetBytes(concatenatedKeyValuePairs);

            using (HMACSHA256 hmac = new HMACSHA256(secretKeyBytes))
            {
                byte[] hashBytes = hmac.ComputeHash(concatenatedValueBytes);
                string result = BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
                log.LogMethodExit(result);

                return result;
            }
        }
        public PeachPaymentsTrxSeachResponseDTO VerifyPayment(string reference)
        {
            log.LogMethodEntry(reference);
            PeachPaymentsTrxSeachResponseDTO checkoutResponseDto = null;
            try
            {
                if (reference == null)
                {
                    throw new Exception("Error occurred while processing your payment");
                }

                PeachPaymentsTrxSearchRequestDTO requestDto = new PeachPaymentsTrxSearchRequestDTO
                {
                    AuthenticationEntityId = ENTITY_ID,
                    MerchantTransactionId = reference
                };

                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("authentication.entityId", requestDto.AuthenticationEntityId));
                formData.Add(new KeyValuePair<string, string>("merchantTransactionId", requestDto.MerchantTransactionId));

                string signature = GenerateSignature(formData.ToDictionary(kv => kv.Key, kv => kv.Value));
                requestDto.Signature = signature;
                formData.Add(new KeyValuePair<string, string>("signature", requestDto.Signature));

                //{{baseUrl}}/status?authentication.entityId={{entityId}}&merchantTransactionId=OrderNo453432&signature=e8b78ffd9c1628dd81d3c331ce7d6429059e5c8d20fd99a85734fe8ff2eacb3d
                string API_URL = BASE_URL + VERIFY_URL + "?authentication.entityId=" + ENTITY_ID +
                "&merchantTransactionId=" + reference + "&signature=" + signature;


                string responseFromServer;

                WebRequestClient webRequestClient = new WebRequestClient(API_URL, HttpVerb.GET);
                webRequestClient.ContentType = "application/json";
                webRequestClient.IsBasicAuthentication = true;
                webRequestClient.Username = SECRET_KEY;

                responseFromServer = webRequestClient.GetResponse();
                log.Debug("Raw response: " + responseFromServer);
                PeachPaymentsTrxSeachResponseDTO checkoutResponseDtoList = JsonConvert.DeserializeObject<PeachPaymentsTrxSeachResponseDTO>(responseFromServer);
                checkoutResponseDto = checkoutResponseDtoList;
                log.Debug("Deseialized response: " + checkoutResponseDto.ToString());

            }
            catch (Exception)
            {
                throw;
            }
            log.LogMethodExit(checkoutResponseDto.ToString());
            return checkoutResponseDto;
        }

        //public PayMayaRefundDTO CreateVoid(PayMayaRefundVoidRequestDto requestDto, string reference)
        //{
        //    string refundUrl = BASE_URL + reference + VOID_URL;
        //    WebRequestClient webRequestClient = new WebRequestClient(refundUrl, HttpVerb.POST, requestDto);
        //    webRequestClient.ContentType = "application/json";
        //    webRequestClient.IsBasicAuthentication = true;
        //    webRequestClient.Username = SECRET_KEY;

        //    string responseFromServer = webRequestClient.MakeRequest();

        //    PayMayaRefundDTO checkoutResponseDto = JsonConvert.DeserializeObject<PayMayaRefundDTO>(responseFromServer);

        //    return checkoutResponseDto;

        //}

        public PeachPaymentsRefundResponseDTO CreateRefund(PeachPaymentsRefundRequestDTO peachPaymentsRequestDTO)
        {
            log.LogMethodEntry(peachPaymentsRequestDTO.ToString());
            PeachPaymentsRefundResponseDTO checkoutResponseDto;
            try
            {
                List<KeyValuePair<string, string>> formData = new List<KeyValuePair<string, string>>();

                formData.Add(new KeyValuePair<string, string>("authentication.entityId", peachPaymentsRequestDTO.AuthenticationEntityId));

                formData.Add(new KeyValuePair<string, string>("id", peachPaymentsRequestDTO.Id));

                formData.Add(new KeyValuePair<string, string>("amount", peachPaymentsRequestDTO.Amount));

                formData.Add(new KeyValuePair<string, string>("paymentType", peachPaymentsRequestDTO.PaymentType));

                formData.Add(new KeyValuePair<string, string>("currency", peachPaymentsRequestDTO.Currency));

                string signature = GenerateSignature(formData.ToDictionary(kv => kv.Key, kv => kv.Value));

                formData.Add(new KeyValuePair<string, string>("signature", signature));

                if (peachPaymentsRequestDTO == null)
                {
                    throw new Exception("Error creating payment request");
                }
                WebRequestClient webRequestClient = new WebRequestClient(POST_URL + REFUND_URL, HttpVerb.POST, formData);
                webRequestClient.ContentType = "application/x-www-form-urlencoded";
                webRequestClient.IsBasicAuthentication = false;
                webRequestClient.Referer = REFERER_URL;
                string responseFromServer = webRequestClient.MakeRequest();
                log.Debug("Raw response: " + responseFromServer);

                // Deserialize the response from the server
                checkoutResponseDto = JsonConvert.DeserializeObject<PeachPaymentsRefundResponseDTO>(responseFromServer);
                log.Debug("Deseialized response: " + checkoutResponseDto.ToString());
                log.LogMethodExit();
                return checkoutResponseDto;
            }
            catch (Exception)
            {
                throw;
            }
        }


        public PeachPaymentsAuthResponseDTO CreateAuth(PeachPaymentsAuthRequestDTO requestDto)
        {
            log.LogMethodEntry(requestDto.ToString());
            WebRequestClient webRequestClient = new WebRequestClient(AUTH_URL, HttpVerb.POST, requestDto);
            webRequestClient.ContentType = "application/json";
            webRequestClient.IsBasicAuthentication = true;

            string responseFromServer = webRequestClient.MakeRequest();
            log.Debug("Raw response: " + responseFromServer);
            PeachPaymentsAuthResponseDTO checkoutResponseDto = JsonConvert.DeserializeObject<PeachPaymentsAuthResponseDTO>(responseFromServer);
            log.Debug("Deseialized response: " + checkoutResponseDto.ToString());
            log.LogMethodExit();
            return checkoutResponseDto;

        }


        public PeachPaymentsCheckoutDTO GenerateCheckoutId(PeachPaymentsAuthResponseDTO authResponse, PeachPaymentsCheckoutRequestDTO requestDto)
        {
            log.LogMethodEntry();
            string fullUrl = BASE_URL + CHECKOUT_URL;
            HttpWebRequest myHttpWebRequest = (HttpWebRequest)HttpWebRequest.Create(fullUrl);
            myHttpWebRequest.Method = "POST";

            try
            {
                // Manually create the JSON string
                string json = $@"{{
            ""authentication"": {{
                ""entityId"": ""{requestDto.Authentication.EntityId}""
            }},
            ""amount"": {requestDto.Amount},
            ""currency"": ""{requestDto.Currency}"",
            ""shopperResultUrl"": ""{requestDto.ShopperResultUrl}"",
            ""merchantTransactionId"": ""{requestDto.MerchantTransactionId}"",
            ""nonce"": ""{requestDto.Nonce}"",
            ""notificationUrl"": ""{requestDto.NotificationUrl}"",
            ""cancelUrl"": ""{requestDto.CancelUrl}"",
            ""merchantInvoiceId"": ""{requestDto.MerchantInvoiceId}"",
            ""defaultPaymentMethod"": ""{requestDto.DefaultPaymentMethod}"",
            ""forceDefaultMethod"": {requestDto.ForceDefaultMethod.ToString().ToLower()},
            ""createRegistration"": {requestDto.CreateRegistration.ToString().ToLower()},
            ""allowStoringDetails"": {requestDto.AllowStoringDetails.ToString().ToLower()}
        }}";
                byte[] data = Encoding.UTF8.GetBytes(json);

                // Set the request headers
                myHttpWebRequest.ContentType = "application/json";
                myHttpWebRequest.Accept = "application/json";
                myHttpWebRequest.Headers.Add("Authorization", "Bearer " + authResponse.access_token);
                myHttpWebRequest.ContentLength = data.Length;
                myHttpWebRequest.Referer = REFERER_URL;
                myHttpWebRequest.Headers.Add("Origin", REFERER_URL);
                myHttpWebRequest.Host = new Uri(BASE_URL).Host;

                // Handle SSL certificates (insecure, only for testing purposes)
                ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

                // Write data to the request stream
                using (Stream requestStream = myHttpWebRequest.GetRequestStream())
                {
                    requestStream.Write(data, 0, data.Length);
                }

                // Get and process the response
                using (HttpWebResponse myHttpWebResponse = (HttpWebResponse)myHttpWebRequest.GetResponse())
                {
                    using (Stream responseStream = myHttpWebResponse.GetResponseStream())
                    {
                        using (StreamReader myStreamReader = new StreamReader(responseStream, Encoding.Default))
                        {
                            string responseFromServer = myStreamReader.ReadToEnd();
                            PeachPaymentsCheckoutDTO checkoutResponseDto = JsonConvert.DeserializeObject<PeachPaymentsCheckoutDTO>(responseFromServer);
                            log.LogMethodExit();
                            return checkoutResponseDto;
                        }
                    }
                }
            }
            catch (WebException webEx)
            {
                log.Error($"WebException occurred: {webEx.Message}");
                if (webEx.Response != null)
                {
                    using (var errorResponse = (HttpWebResponse)webEx.Response)
                    {
                        using (var reader = new StreamReader(errorResponse.GetResponseStream()))
                        {
                            string errorText = reader.ReadToEnd();
                            log.Error($"Error response: {errorText}");
                        }
                    }
                }
                throw; // Rethrow the exception if needed
            }
            catch (Exception ex)
            {
                log.Error($"An error occurred: {ex.Message}");
                throw; // Rethrow the exception if needed
            }
            finally
            {
                log.LogMethodExit();
            }
        }


    }
}
