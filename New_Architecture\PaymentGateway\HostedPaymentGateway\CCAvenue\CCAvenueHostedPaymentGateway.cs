﻿/********************************************************************************************
 * Project Name - CC Avenue Hosted Payment Gateway                                                                     
 * Description  - Class to handle the payment of CC Avenue Hosted Payment Gateway
 *
 **************
 **Version Log
  *Version     Date          Modified By          Remarks          
 *********************************************************************************************
 *2.70        15-April-2024   Yashodhara C H      Created
 ********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using CCA.Util;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Semnox.Core.Utilities;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.ViewContainer;
using System.Threading;
using Semnox.Parafait.PaymentGatewayInterface;
using Microsoft.IdentityModel.Tokens;

namespace Semnox.Parafait.PaymentGateway
{
    public class CCAvenueCallbackHostedPaymentGateway : CCAvenueHostedPaymentGateway
    {
        internal override string paymentGatewayName { get { return "CCAvenueCallbackHostedPayment"; } }

        public CCAvenueCallbackHostedPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, System.Threading.CancellationToken cancellationToken)
            : base(executionContext, isUnattended, cancellationToken)
        {
            log.LogMethodEntry(executionContext, writeToLogDelegate);
            log.Debug("Pagement Gateway " + paymentGatewayName);
            log.LogMethodExit(null);
        }
    }

    public class CCAvenueHostedPaymentGateway : WebHostedPaymentGateway
    {
        internal readonly Semnox.Parafait.logging.Logger log;
        internal virtual string paymentGatewayName { get { return "CCAvenueHostedPayment"; } }
        
        public CCAvenueHostedPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, System.Threading.CancellationToken cancellationToken)
            : base(executionContext, isUnattended, cancellationToken)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, writeToLogDelegate);
            this.InitConfigurations();
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12 | SecurityProtocolType.Ssl3;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            log.LogMethodExit(null);
        }


       

        private void InitConfigurations()
        {
            StringBuilder errMsg = new StringBuilder();
            this.WebPaymentGatewayConfiguration = new WebPaymentGatewayConfiguration();

            this.WebPaymentGatewayConfiguration.MerchantId = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.MerchantId))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "CCAVENUE_HOSTED_PAYMENT_MERCHANT_ID", ExecutionContext.GetSiteId().ToString() }));
            }

            this.WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "CCAVENUE_HOSTED_PAYMENT_ACCESS_CODE", ExecutionContext.GetSiteId().ToString() }));
            }

            this.WebPaymentGatewayConfiguration.MerchantKey = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CCAVENUE_HOSTED_PAYMENT_WORKING_KEY");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.MerchantKey))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "CCAVENUE_HOSTED_PAYMENT_WORKING_KEY", ExecutionContext.GetSiteId().ToString() }));
            }

            this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "CCAVENUE_HOSTED_PAYMENT_TRANSACTION_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            this.WebPaymentGatewayConfiguration.GatewayAPIURL = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayAPIURL))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }        

            log.LogVariableState("merchantId", WebPaymentGatewayConfiguration.MerchantId);
            log.LogVariableState("accessCode", WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey);
            log.LogVariableState("workingKey", WebPaymentGatewayConfiguration.MerchantKey);
            log.LogVariableState("post_url", WebPaymentGatewayConfiguration.WebsitePaymentPageURL);
            log.LogVariableState("ccavenue_api_url", WebPaymentGatewayConfiguration.GatewayAPIURL);

            log.Debug("Building lookups");
            LookupsContainerDTO lookupValuesList = LookupsViewContainerList.GetLookupsContainerDTO(ExecutionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            if (lookupValuesList == null)
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup. Site : &2", new string[] { "WEB_PAYMENT_CONFIGURATION", ExecutionContext.GetSiteId().ToString() }));
                log.Error(errMsg.ToString());
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, errMsg.ToString()));
            }
            List<LookupValuesContainerDTO> lookupValuesDTOlist = lookupValuesList.LookupValuesContainerDTOList;
            if (lookupValuesDTOlist == null || !lookupValuesDTOlist.Any())
            {
                log.Error("WEB_PAYMENT_CONFIGURATION lookup not found.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "WEB_PAYMENT_CONFIGURATION lookup not found."));
            }
            log.Debug("Built lookups");

            String apiSite = "";
            String webSite = "";
            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").Count() > 0)
            {
                apiSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").First().Description;
                log.Debug("ANGULAR_PAYMENT_API: " + apiSite + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "ANGULAR_PAYMENT_API", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").Count() > 0)
            {
                webSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").First().Description;
                log.Debug("ANGULAR_PAYMENT_WEB: " + webSite + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "ANGULAR_PAYMENT_WEB", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").Count() > 0)
            {
                String linkPage = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").First().Description;
                linkPage = linkPage.Replace("@gateway", paymentGatewayName);
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink = webSite + linkPage;
            }
            else
            {
                //this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink = webSite + "/payment/ccavenuehostedpayment?payload=@payLoad&siteId=@siteId&posMachine=@posMachine";
                String linkPage = "/payment/paymentGatewayLanding?PaymentGatewayName=@gateway&payload=@payLoad&siteId=@siteId&posMachine=@posMachine";
                linkPage = linkPage.Replace("@gateway", paymentGatewayName);
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink = webSite + linkPage;
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.SuccessURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentGatewayName);
                log.Debug("SUCCESS_RESPONSE_API_URL: " + this.WebPaymentGatewayConfiguration.SuccessURL + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATIONPlease enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "SUCCESS_RESPONSE_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.FailedURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentGatewayName);
                log.Debug("FAILURE_RESPONSE_API_URL: " + this.WebPaymentGatewayConfiguration.FailedURL + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "FAILURE_RESPONSE_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.CancelURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentGatewayName);
                log.Debug("CANCEL_RESPONSE_API_URL: " + this.WebPaymentGatewayConfiguration.CancelURL + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "CANCEL_RESPONSE_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.CallbackURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CALLBACK_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentGatewayName);
                log.Debug("CALLBACK_RESPONSE_API_URL: " + this.WebPaymentGatewayConfiguration.CancelURL + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "CALLBACK_RESPONSE_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.PaymentSucceededURL = webSite+ lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").First().Description.Replace("@gateway", paymentGatewayName);
                log.Debug("SUCCESS_REDIRECT_URL: " + this.WebPaymentGatewayConfiguration.PaymentSucceededURL + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "SUCCESS_REDIRECT_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.PaymentFailedURL = webSite + lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").First().Description.Replace("@gateway", paymentGatewayName);
                log.Debug("FAILURE_REDIRECT_URL: " + this.WebPaymentGatewayConfiguration.PaymentFailedURL + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "FAILURE_REDIRECT_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.PaymentCancelledURL = webSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").First().Description.Replace("@gateway", paymentGatewayName);
                log.Debug("CANCEL_REDIRECT_URL: " + this.WebPaymentGatewayConfiguration.PaymentCancelledURL + " for SiteId: " + ExecutionContext.GetSiteId());
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "CANCEL_REDIRECT_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            log.Debug("Error : " + errMsg.ToString());
            String error = errMsg.ToString();
            if (!string.IsNullOrWhiteSpace(error))
            {
                log.Error(errMsg);
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, error));
            }
            log.LogMethodExit(this.WebPaymentGatewayConfiguration);
        }


        /// <summary>
        /// This method is used initiate a payment request
        /// </summary>
        /// <param name="createHostedPaymentRequestDTO"></param>
        /// <returns></returns>
        public override HostedPaymentRequestDTO CreateGatewayPaymentRequest(CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO)
        {
            log.LogMethodEntry(createHostedPaymentRequestDTO);
            HostedPaymentRequestDTO HostedPaymentRequestDTO = GetHostedPaymentRequestDTO(createHostedPaymentRequestDTO, this.paymentGatewayName);
            try
            {
                HostedPaymentRequestDTO.GatewayRequestHTML = SubmitFormKeyValueList(SetPostParameters(HostedPaymentRequestDTO), this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL, "frmPayPost");
                HostedPaymentRequestDTO.WebsitePaymentPageURL = this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL;
                String base64String = Base64UrlEncoder.Encode(Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(HostedPaymentRequestDTO.GatewayRequestHTML)));
                HostedPaymentRequestDTO.WebsitePaymentPageLink = this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink
                                                                .Replace("@payLoad", base64String)
                                                                .Replace("@siteId", HostedPaymentRequestDTO.SiteId.ToString())
                                                                .Replace("@posMachine", HostedPaymentRequestDTO.POSMachine.ToString());
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit(HostedPaymentRequestDTO);
            return HostedPaymentRequestDTO;
        }


        /// <returns> returns  List<KeyValuePair<string, string>></returns>
        private List<KeyValuePair<string, string>> SetPostParameters(HostedPaymentRequestDTO hostedPaymentRequestDTO)
        {
            log.LogMethodEntry(hostedPaymentRequestDTO);
            List<KeyValuePair<string, string>> postparamslist = new List<KeyValuePair<string, string>>();
            postparamslist.Clear();
            postparamslist.Add(new KeyValuePair<string, string>("tid", ServerDateTime.Now.Ticks.ToString()));           
            postparamslist.Add(new KeyValuePair<string, string>("merchant_id", this.WebPaymentGatewayConfiguration.MerchantId));            
            postparamslist.Add(new KeyValuePair<string, string>("order_id", hostedPaymentRequestDTO.TransactionPaymentGuid));            
            postparamslist.Add(new KeyValuePair<string, string>("amount", hostedPaymentRequestDTO.Amount.ToString()));            
            postparamslist.Add(new KeyValuePair<string, string>("currency", hostedPaymentRequestDTO.CurrencyCode));           
            postparamslist.Add(new KeyValuePair<string, string>("redirect_url", hostedPaymentRequestDTO.SuccessURL));           
            postparamslist.Add(new KeyValuePair<string, string>("cancel_url", hostedPaymentRequestDTO.FailedURL));            
            postparamslist.Add(new KeyValuePair<string, string>("merchant_param1", PaymentGateways.CCAvenueHostedPayment.ToString()));           
            postparamslist.Add(new KeyValuePair<string, string>("merchant_param2", hostedPaymentRequestDTO.SiteId.ToString()));            
            postparamslist.Add(new KeyValuePair<string, string>("merchant_param3", hostedPaymentRequestDTO.TransactionId.ToString()));            
            //Customer Identifier - Customer Profile Id
            postparamslist.Add(new KeyValuePair<string, string>("customer_identifier", hostedPaymentRequestDTO.PaymentGatewayCustomer?.CustomerIdentifier ?? ""));

            // log.Info("Customer Identifier added to post : " + transactionPaymentsDTO.Reference);


            string HashData = "";
            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                HashData += param.Key + "=" + param.Value.ToString() + "&";
            }
            CCACrypto ccaCrypto = new CCACrypto();
            string strEncRequest = ccaCrypto.Encrypt(HashData, this.WebPaymentGatewayConfiguration.MerchantKey);

            List<KeyValuePair<string, string>> postparamslistOut = new List<KeyValuePair<string, string>>();
            postparamslistOut.Clear();
            postparamslistOut.Add(new KeyValuePair<string, string>("encRequest", strEncRequest));
            postparamslistOut.Add(new KeyValuePair<string, string>("access_code", this.WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey));

            log.LogMethodExit(postparamslist);
            return postparamslistOut;


        }

        /// Generate form
        /// </summary>
        /// <param name="postparamslist">postparamslist</param>
        /// <param name="URL">URL</param>   
        /// <param name="hashedvalue">hashedvalue</param>
        /// <returns> returns string</returns>
        private string SubmitFormKeyValueList(List<KeyValuePair<string, string>> postparamslist, string URL, string FormName, string submitMethod = "POST")
        {

            string Method = submitMethod;
            StringBuilder builder = new StringBuilder();
            builder.Clear();
            builder.Append("<html><head>");
            builder.Append("<META HTTP-EQUIV=\"CACHE-CONTROL\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append("<META HTTP-EQUIV=\"PRAGMA\" CONTENT=\"no-store, no-cache, must-revalidate\" />");
            builder.Append(string.Format("</head><body onload=\"document.{0}.submit()\">", FormName));
            builder.Append(string.Format("<form name=\"{0}\" method=\"{1}\" action=\"{2}\" >", FormName, Method, URL));

            foreach (KeyValuePair<string, string> param in postparamslist)
            {
                builder.Append(string.Format("<input name=\"{0}\" type=\"hidden\" value=\"{1}\" />", param.Key, param.Value));
            }
            builder.Append("</form>");
            builder.Append("</body></html>");
            return builder.ToString();
        }

        /// <summary>
        /// gets Payment Identifer from response
        /// </summary>
        /// <param name="paymentGatewayResponse"></param>
        /// <returns></returns>
        public override string GetPaymentIdentifier(String paymentGatewayResponse)
        {
            log.LogMethodEntry(paymentGatewayResponse);
            String paymentGatewayIdentifier = string.Empty;

            string payEncResp = "";
            string pgreference = "";
            String paymentReferneceNo = "";

            //encResp=4f4445cafaee83befeb056d236cf02f8d3b4566c3ab963d3d95&orderNo=155721&crossSellUrl=
            string[] responseList = paymentGatewayResponse.Split('&');
            for (int i = 0; i < responseList.Length; i++)
            {
                string response = responseList[i];
                if (response.Contains("encResp"))
                {
                    payEncResp = response.Substring(response.IndexOf("=") + 1);
                }
                else if (response.Contains("orderNo"))
                {
                    paymentGatewayIdentifier = response.Substring(response.IndexOf("=") + 1);
                }
                else if (response.Contains("order_id"))
                {
                    paymentGatewayIdentifier = response.Substring(response.IndexOf("=") + 1);
                }
            }


            //CCACrypto ccaCrypto = new CCACrypto();
            //string encResponse = ccaCrypto.Decrypt(payEncResp, WebPaymentGatewayConfiguration.MerchantKey);
            //log.Debug("Decrypted Response: " + encResponse);
            //string[] segments = encResponse.Split('&');
            //foreach (string seg in segments)
            //{
            //    string[] parts = seg.Split('=');
            //    if (parts.Length > 0)
            //    {
            //        string Key = parts[0].Trim();
            //        string Value = parts[1].Trim();
            //        if (Key == "bank_ref_no")
            //        {
            //            paymentReferneceNo = Value;
            //        }
            //    }
            //}
            log.Debug("Encrypted Response: " + payEncResp);
            log.Debug("PG Reference from response: " + pgreference);
            log.Debug("OrderNo from response: " + paymentGatewayIdentifier);
            
            log.LogMethodExit(paymentGatewayIdentifier);
            return paymentGatewayIdentifier;
        }

        /// <summary>
        /// Process the payment response
        /// </summary>
        /// <param name="gatewayResponse"></param>
        /// <returns></returns>
        public override HostedPaymentResponseDTO ProcessGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry(gatewayResponse);
            HostedPaymentResponseDTO hostedPaymentResponseDTO = GetHostedPaymentResponseDTO();
            PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO();
            string payEncResp = "";
            string respOrderNo = "";
            try
            {
                if(string.IsNullOrWhiteSpace(gatewayResponse))
                {
                    log.Error($"Payment failed failed.");
                    throw new PaymentGatewayProcessingException($"Transaction processing falied");
                }

                string[] responseList = gatewayResponse.Split('&');
                for (int i = 0; i < responseList.Length; i++)
                {
                    string response = responseList[i];
                    if (response.Contains("encResp"))
                    {
                        payEncResp = response.Substring(response.IndexOf("=") + 1);
                    }
                    else if (response.Contains("orderNo"))
                    {
                        respOrderNo = response.Substring(response.IndexOf("=") + 1);
                    }
                    else if (response.Contains("order_id"))
                    {
                        respOrderNo = response.Substring(response.IndexOf("=") + 1);
                    }
                }

                log.Debug("Encrypted Response: " + payEncResp);
                log.Debug("OrderNo from response: " + respOrderNo);

                CCACrypto ccaCrypto = new CCACrypto();
                string encResponse = ccaCrypto.Decrypt(payEncResp, WebPaymentGatewayConfiguration.MerchantKey);

                log.Debug("Decrypted Response: " + encResponse);

                string[] segments = encResponse.Split('&');

                string statusMessage = "";
                string failureMessage = "";

                foreach (string seg in segments)
                {
                    string[] parts = seg.Split('=');
                    if (parts.Length > 0)
                    {
                        string Key = parts[0].Trim();
                        string Value = parts[1].Trim();
                        if (Key == "failure_message")
                        {
                            failureMessage = Value.ToString();
                        }
                        else if (Key == "status_message")
                        {
                            statusMessage = Value.ToString();
                        }
                        else if (Key == "order_id")
                        {
                            paymentTransactionDTO.RecordNo = Value;
                        }
                        else if (Key == "tracking_id")
                        {
                            paymentTransactionDTO.RefNo = Value;
                        }
                        else if (Key == "bank_ref_no")
                        {
                            paymentTransactionDTO.AuthCode = Value;
                        }
                        else if (Key == "amount")
                        {
                            paymentTransactionDTO.Amount = Convert.ToDecimal(Value);
                            paymentTransactionDTO.Purchase = Value;
                        }
                        else if (Key == "currency")
                        {
                            paymentTransactionDTO.Authorize = Value;
                        }
                        else if (Key == "merchant_param2" && string.IsNullOrWhiteSpace(Value) == false)
                        {
                            paymentTransactionDTO.SiteId = Convert.ToInt32(Value);
                        }
                        else if (Key == "merchant_param3" && string.IsNullOrWhiteSpace(Value) == false)
                        {
                            paymentTransactionDTO.TransactionId = Convert.ToInt32(Value);
                            //paymentTransactionDTO.RecordNo = Value;
                        }
                    }
                }
                paymentTransactionDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                //Check whether response TrxID are matching
                if (paymentTransactionDTO.RecordNo != respOrderNo)
                {
                    log.Error("Payment Rejected - TrxId doesn't match with response orderId");
                    throw new PaymentGatewayProcessingException("Payment has been rejected!");
                }

                //Call Status API to reconfirm payment status
                log.Debug("Calling status api to confirm order status");
                OrderStatusResult orderStatusResult = GetOrderStatus(paymentTransactionDTO.RecordNo);

                if(orderStatusResult == null)
                {
                    log.Error($"Order status for trxId: {paymentTransactionDTO.RecordNo} failed.");
                    throw new PaymentGatewayProcessingException($"Transaction processing falied for trxId: {paymentTransactionDTO.RecordNo}!");
                }

                string OrderStatus = orderStatusResult.order_status;
                log.Debug("OrderStatus: "+ OrderStatus);

                if (!string.IsNullOrEmpty(OrderStatus) && (OrderStatus.ToLower() == "Shipped".ToLower() || OrderStatus.ToLower() == "Successful".ToLower()))
                {

                    //paymentTransactionDTO.Status = PaymentGatewayTransactionType.SALE.ToString();
                    hostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.SUCCESS;
                    paymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                    paymentTransactionDTO.DSIXReturnCode = "SUCCESS " + OrderStatus;
                    paymentTransactionDTO.TextResponse = OrderStatus;
                    paymentTransactionDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                    paymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                }
                else
                {
                    //paymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                    hostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                    paymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                    paymentTransactionDTO.DSIXReturnCode = failureMessage + OrderStatus;
                    paymentTransactionDTO.TextResponse = statusMessage +  OrderStatus;
                    paymentTransactionDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                    paymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();

                }
                
            }
            catch (Exception ex) when (ex is PaymentGatewayProcessingException
                                        || ex is PaymentGatewayTransactionTimeoutException
                                        || ex is PaymentGatewayInvalidCardException)
            {
                log.Debug("Caught a processing error " + ex);
                //paymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                hostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                paymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                paymentTransactionDTO.DSIXReturnCode = ex.Message;
                paymentTransactionDTO.TextResponse = ex.Message;
                paymentTransactionDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                paymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
            }
            catch (Exception ex)
            {
                log.Error("Error occurred while processing payment: " + ex.Message);
                //paymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                hostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                paymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                paymentTransactionDTO.DSIXReturnCode = ex.Message;
                paymentTransactionDTO.TextResponse = ex.Message;
                paymentTransactionDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                paymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
            }

            hostedPaymentResponseDTO.PaymentTransactionDTO = paymentTransactionDTO;
            log.Debug("Final hostedGatewayDTO " + hostedPaymentResponseDTO.ToString());

            log.LogMethodExit(hostedPaymentResponseDTO);
            return hostedPaymentResponseDTO;
        }

        /// <summary>
        /// Method to get the payment details for the a specific trxId
        /// </summary>
        /// <param name="trxId"></param>
        /// <returns></returns>
        public OrderStatusResult GetOrderStatus(string transactionPaymentGuid)
        {
            log.LogMethodEntry(transactionPaymentGuid);
            CCACrypto ccaCrypto = new CCACrypto();
            string orderStatusQueryJson = "";
            string strEncRequest = "";
            string authQueryUrlParam = "";
            string message = "";
            OrderStatusResponseDTO orderStatusResponseDTO = null;
            OrderStatusResult orderStatusResult = null;

            orderStatusQueryJson = "{ \"order_no\":\"" + transactionPaymentGuid + "\" }";
            log.Debug("Status API orderStatusQueryJson: " + orderStatusQueryJson);

            strEncRequest = ccaCrypto.Encrypt(orderStatusQueryJson, this.WebPaymentGatewayConfiguration.MerchantKey);
            log.Debug("Status API strEncRequest: " + strEncRequest);

            authQueryUrlParam = "enc_request=" + strEncRequest + "&access_code=" + this.WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey + "&command=orderStatusTracker&request_type=JSON&response_type=JSON";
            log.Debug("Status API authQueryUrlParam: " + authQueryUrlParam);

            try
            {
                message = PostPaymentRequestToGateway(this.WebPaymentGatewayConfiguration.GatewayAPIURL, authQueryUrlParam);
                log.Debug("Initial response from Status API: " + message);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            try
            {

                orderStatusResponseDTO = ExtractResponse<OrderStatusResponseDTO>(message, ccaCrypto);
                orderStatusResult = orderStatusResponseDTO.Order_Status_Result;

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(orderStatusResult);
            return orderStatusResult;
        }

        public string PostPaymentRequestToGateway(string queryUrl, string urlParam)
        {
            string message = "";
            try
            {
                StreamWriter myWriter = null;// it will open a http connection with provided url
                WebRequest objRequest = WebRequest.Create(queryUrl);//send data using objxmlhttp object
                objRequest.Method = "POST";
                objRequest.ContentType = "application/x-www-form-urlencoded";//to set content type
                myWriter = new System.IO.StreamWriter(objRequest.GetRequestStream());
                myWriter.Write(urlParam);//send data
                myWriter.Close();//closed the myWriter object

                // Getting Response
                System.Net.HttpWebResponse objResponse = (System.Net.HttpWebResponse)objRequest.GetResponse();//receive the responce from objxmlhttp object 
                using (System.IO.StreamReader sr = new System.IO.StreamReader(objResponse.GetResponseStream()))
                {
                    message = sr.ReadToEnd();
                }
            }
            catch (Exception ex)
            {
                log.Error("Exception occured while connection." + ex.Message);
                throw new Exception("Exception occured while connection." + ex.Message);
            }
            return message;

        }

        public NameValueCollection GetResponseMap(string message)
        {
            NameValueCollection Params = new NameValueCollection();
            if (message != null || !"".Equals(message))
            {
                string[] segments = message.Split('&');
                foreach (string seg in segments)
                {
                    string[] parts = seg.Split('=');
                    if (parts.Length > 0)
                    {
                        string Key = parts[0].Trim();
                        string Value = parts[1].Trim();
                        Params.Add(Key, Value);
                    }
                }
            }
            return Params;
        }


        /// <summary>
        /// Refunds the open transaction
        /// </summary>
        /// <param name="refundTransactionPaymentsDTO"></param>
        /// <param name="originalPaymentTransactionDTO"></param>
        /// <param name="progress"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public override async Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO refundTransactionPaymentsDTO, PaymentTransactionDTO originalPaymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(refundTransactionPaymentsDTO, originalPaymentTransactionDTO, progress);
            PaymentTransactionDTO refundPaymentTransactionDTO = new PaymentTransactionDTO();
            CCACrypto ccaCrypto = new CCACrypto();
            RefundRequestDTO refundRequestDTO = null;
            RefundResponseDTO refundResponseDTO = null;
            string strEncRefundReq;
            string refundRequestSerialize;
            string refundQueryUrlParam;
            string refundResposne;
            
            RefundOrderResult refundOrderResult = null;

            if (refundTransactionPaymentsDTO == null)
            {
                log.Error("transactionPaymentsDTO is Empty");
                //PaymentGatewayRefundProcessingException
                throw new Exception("Refund failed");
            }

            try
            {
                refundRequestDTO = new RefundRequestDTO
                {
                    reference_no = refundTransactionPaymentsDTO.Reference,
                    refund_amount = string.Format("{0:0.00}", refundTransactionPaymentsDTO.Amount),
                    refund_ref_no = refundTransactionPaymentsDTO.TransactionId.ToString()
                };

                refundRequestSerialize = JsonConvert.SerializeObject(refundRequestDTO);
                log.Debug("refundRequestDTO: " + refundRequestSerialize);

                strEncRefundReq = ccaCrypto.Encrypt(refundRequestSerialize, this.WebPaymentGatewayConfiguration.MerchantKey);
                log.Debug("Refund request refundRequestSerialize: " + strEncRefundReq);

                refundQueryUrlParam = "enc_request=" + strEncRefundReq + "&access_code=" + this.WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey + "&command=refundOrder&request_type=JSON&response_type=JSON";
                log.Debug("Final Refund request refundQueryUrlParam: " + refundQueryUrlParam);

                try
                {
                    refundResposne = PostPaymentRequestToGateway(this.WebPaymentGatewayConfiguration.GatewayAPIURL, refundQueryUrlParam);
                    log.Debug("Initial response from Refund API: " + refundResposne);
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw new Exception(ex.Message);
                }

                //Extract and decrypt the refund response
                refundResponseDTO = ExtractResponse<RefundResponseDTO>(refundResposne, ccaCrypto);
                refundOrderResult = refundResponseDTO.Refund_Order_Result;


                refundPaymentTransactionDTO.InvoiceNo = refundTransactionPaymentsDTO.Reference;
                refundPaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                refundPaymentTransactionDTO.AcctNo = refundTransactionPaymentsDTO.CreditCardNumber;
                refundPaymentTransactionDTO.ResponseOrigin = originalPaymentTransactionDTO != null ? originalPaymentTransactionDTO.TransactionId.ToString() : null;
                refundPaymentTransactionDTO.Authorize = refundTransactionPaymentsDTO.Amount.ToString();
                refundPaymentTransactionDTO.Purchase = refundTransactionPaymentsDTO.Amount.ToString();
                refundPaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                refundPaymentTransactionDTO.RecordNo = refundTransactionPaymentsDTO.TransactionId.ToString(); //parafait TrxId
                refundPaymentTransactionDTO.RefNo = refundTransactionPaymentsDTO.Reference; //paymentId

                if (refundOrderResult != null && refundOrderResult.refund_status.ToString() == "0")
                {
                    log.Debug("Refund Success");
                    refundPaymentTransactionDTO.TextResponse = "SUCCESS";
                    refundPaymentTransactionDTO.DSIXReturnCode = refundOrderResult.error_code;
                    refundPaymentTransactionDTO.AuthCode = refundOrderResult.refund_status.ToString();
                    refundPaymentTransactionDTO.AcqRefData = refundOrderResult.reason;
                    refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                }
                else
                {
                    log.Error("Refund Failed, Reason: " + refundOrderResult?.reason);
                    refundPaymentTransactionDTO.TextResponse = "FALIED";
                    refundPaymentTransactionDTO.DSIXReturnCode = refundOrderResult?.error_code;
                    refundPaymentTransactionDTO.AuthCode = refundOrderResult?.refund_status.ToString();
                    refundPaymentTransactionDTO.AcqRefData = refundOrderResult?.reason;
                    refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                }             
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(refundPaymentTransactionDTO);
            return refundPaymentTransactionDTO;
        }

        public override HostedPaymentResponseDTO GetPaymentStatus(string transactionPaymentGuid)
        {
            log.LogMethodEntry();
            HostedPaymentResponseDTO hostedPaymentResponseDTO = GetHostedPaymentResponseDTO();
            OrderStatusResult orderStatusResult = null;

            try
            {
                if (string.IsNullOrWhiteSpace(transactionPaymentGuid))
                {
                    log.Error("No trxId present. Unable to perform payment status search");
                    throw new PaymentGatewayProcessingException("Insufficient Params passed to the request!");
                }

                //Call TxSearch API
                orderStatusResult = GetOrderStatus(transactionPaymentGuid);
                log.Debug("Response for orderStatusResponseDTO: " + JsonConvert.SerializeObject(orderStatusResult));

                if (orderStatusResult == null)
                {
                    log.Error($"Order status for payment guid: {transactionPaymentGuid} failed.");
                    throw new PaymentGatewayProcessingException($"Transaction processing falied for payment guid: {transactionPaymentGuid}!");
                }

                string OrderStatus = orderStatusResult.order_status;
                log.Debug("Raw OrderStatus: " + OrderStatus);

                PaymentTransactionStatuses mappedPaymentStatus = MapPaymentStatus(OrderStatus, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of mappedPaymentStatus: " + mappedPaymentStatus.ToString());

                hostedPaymentResponseDTO.PaymentTransactionStatus = mappedPaymentStatus;

                PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO();
                paymentTransactionDTO.InvoiceNo = transactionPaymentGuid;
                paymentTransactionDTO.Authorize = orderStatusResult?.order_capt_amt.ToString();
                paymentTransactionDTO.Purchase = orderStatusResult?.order_amt.ToString();
                paymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                paymentTransactionDTO.AcctNo = orderStatusResult?.order_bank_ref_no.ToString(); // here card number is not received
                paymentTransactionDTO.RefNo = orderStatusResult?.reference_no.ToString();
                paymentTransactionDTO.RecordNo = orderStatusResult?.order_no.ToString();
                paymentTransactionDTO.TextResponse = orderStatusResult?.order_bank_response;
                paymentTransactionDTO.AuthCode = orderStatusResult?.status.ToString();
                paymentTransactionDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                paymentTransactionDTO.CardType = orderStatusResult?.order_card_name;
                paymentTransactionDTO.Status = mappedPaymentStatus.ToString(); //Based on payment status job decides whether payment os applied or not

                log.Debug("Result paymentTransactionDTO: " + paymentTransactionDTO.ToString());

                //Assign paymentTransactionDTO
                hostedPaymentResponseDTO.PaymentTransactionDTO = paymentTransactionDTO;
            }
            catch (Exception ex)
            {
                log.Error("Error performing GetPaymentStatus. Error message: " + ex);

                hostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO
                {
                    InvoiceNo = transactionPaymentGuid,
                    Purchase = "0",
                    Authorize = "0",
                    Amount = 0.0M,
                    TransactionDatetime = ServerDateTime.Now,
                    TextResponse = "Error performing GetPaymentStatus!",
                    DSIXReturnCode = ex.Message,
                    Status = PaymentTransactionStatuses.ERROR.ToString()
                };
                hostedPaymentResponseDTO.PaymentTransactionDTO = paymentTransactionDTO;
            }

            log.LogMethodExit(hostedPaymentResponseDTO);
            return hostedPaymentResponseDTO;
        }

        /// <summary>
        /// Reverse a 
        /// </summary>
        /// <param name="reversedTransactionPaymentDTO"></param>
        /// <param name="progress"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public override async Task<PaymentTransactionDTO> ReversePayment(TransactionPaymentDTO reversedTransactionPaymentDTO, IProgress<PaymentProgressReport> progress,
                                                   CancellationToken cancellationToken)
        {
            log.LogMethodEntry(reversedTransactionPaymentDTO);
            PaymentTransactionDTO originalPaymentTransactionDTO = BuildPaymentTransactionResponseDTO(reversedTransactionPaymentDTO);
            originalPaymentTransactionDTO.Status = PaymentTransactionStatuses.ERROR.ToString();
            originalPaymentTransactionDTO.TextResponse = MessageViewContainerList.GetMessage(ExecutionContext, "Payment Reverse is not supported.");
            log.LogMethodExit(originalPaymentTransactionDTO);
            return originalPaymentTransactionDTO;
        }

        private PaymentTransactionDTO BuildPaymentTransactionResponseDTO(TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO);
            PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO(-1, transactionPaymentDTO.TransactionId, transactionPaymentDTO.Guid, string.Empty,
                string.Empty, string.Empty,
                -1, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty,
                DateTime.Now, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, null, null, null, transactionPaymentDTO.Guid, true, PaymentTransactionStatuses.SUCCESS.ToString(), "", "", "", transactionPaymentDTO.Amount,-1);
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        public T ExtractResponse<T>(string resposne, CCACrypto ccaCrypto)
        {
            NameValueCollection param = GetResponseMap(resposne);
            string status = "";
            string encResJson = "";
            T deserializedDTO = default(T);

            if (param != null && param.Count == 2)
            {
                for (int i = 0; i < param.Count; i++)
                {
                    if ("status".Equals(param.Keys[i]))
                    {
                        status = param[i];
                    }
                    if ("enc_response".Equals(param.Keys[i]))
                    {
                        encResJson = param[i];
                    }
                }
                if (!string.IsNullOrEmpty(status) && status.Equals("0"))
                {
                    string ResJson = ccaCrypto.Decrypt(encResJson, this.WebPaymentGatewayConfiguration.MerchantKey);
                    log.Debug("Extracted Response: " + ResJson);

                    T obj = JsonConvert.DeserializeObject<T>(ResJson);
                    deserializedDTO = (T)obj;
                }
                else if (!string.IsNullOrEmpty(status) && status.Equals("1"))
                {
                    log.Error("ExtractResponse | failure response from ccAvenues: " + encResJson);
                    throw new Exception("ExtractResponse failed!");
                }
            }

            return deserializedDTO;
        }

        internal override PaymentTransactionStatuses MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentTransactionStatuses paymentTransactionStatus = PaymentTransactionStatuses.FAILED;
            try
            {
                Dictionary<string, PaymentTransactionStatuses> pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                        pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "Shipped", PaymentTransactionStatuses.SUCCESS },
                            { "Successful", PaymentTransactionStatuses.SUCCESS },

                            { "Refunded", PaymentTransactionStatuses.FAILED },
                            { "Systemrefund", PaymentTransactionStatuses.FAILED },
                            { "Aborted", PaymentTransactionStatuses.FAILED },
                            { "Auto-Cancelled", PaymentTransactionStatuses.FAILED },
                            { "Reversed", PaymentTransactionStatuses.FAILED }, // two identical transactions for same order number, both were successful at bank'send but we got response for only one of them, then next dayduring reconciliation we mark one of the transaction as auto reversed.
                            { "Cancelled", PaymentTransactionStatuses.FAILED },
                            { "Chargeback", PaymentTransactionStatuses.FAILED },
                            { "Invalid", PaymentTransactionStatuses.FAILED },
                            { "Fraud", PaymentTransactionStatuses.FAILED },
                            { "Unsuccessful", PaymentTransactionStatuses.FAILED },
                            { "Timeout", PaymentTransactionStatuses.FAILED },
                            { "Auto-Refunded", PaymentTransactionStatuses.FAILED },

                            { "Awaited", PaymentTransactionStatuses.PENDING },
                            { "Initiated", PaymentTransactionStatuses.PENDING }
                        };
                        break;
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "0", PaymentTransactionStatuses.SUCCESS },
                            { "1", PaymentTransactionStatuses.FAILED }
                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");

                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentTransactionStatus);
                log.Debug("Value of transformed payment status: " + paymentTransactionStatus.ToString());

                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in list of available payment gateway status. Defaulting payment status to FAILED.");
                    paymentTransactionStatus = PaymentTransactionStatuses.FAILED;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to FAILED." + ex);
                paymentTransactionStatus = PaymentTransactionStatuses.FAILED;
            }

            log.LogMethodExit(paymentTransactionStatus);
            return paymentTransactionStatus;
        }



        #region OrderStatusResponseDTO
        public class OrderStatusResult
        {
            public string order_gtw_id { get; set; }
            public int order_no { get; set; }
            public string order_ship_zip { get; set; }
            public string order_ship_address { get; set; }
            public string order_bill_email { get; set; }
            public double order_capt_amt { get; set; }
            public string order_ship_tel { get; set; }
            public string order_ship_name { get; set; }
            public string order_bill_country { get; set; }
            public string order_card_name { get; set; }
            public string order_status { get; set; }
            public string order_bill_state { get; set; }
            public double order_tax { get; set; }
            public string order_bill_city { get; set; }
            public string order_ship_state { get; set; }
            public double order_discount { get; set; }
            public double order_TDS { get; set; }
            public string order_date_time { get; set; }
            public string order_ship_country { get; set; }
            public string order_bill_address { get; set; }
            public double order_fee_perc_value { get; set; }
            public string order_ip { get; set; }
            public string order_option_type { get; set; }
            public string order_bank_ref_no { get; set; }
            public string order_currncy { get; set; }
            public double order_fee_flat { get; set; }
            public string order_ship_city { get; set; }
            public string order_bill_tel { get; set; }
            public string order_device_type { get; set; }
            public double order_gross_amt { get; set; }
            public double order_amt { get; set; }
            public string order_fraud_status { get; set; }
            public string order_bill_zip { get; set; }
            public string order_bill_name { get; set; }
            public string reference_no { get; set; }
            public string order_bank_response { get; set; }
            public string order_status_date_time { get; set; }
            public int status { get; set; }
        }

        public class OrderStatusResponseDTO
        {
            public OrderStatusResult Order_Status_Result { get; set; }
        }
        #endregion

        #region RefundRequestDTO

        public class RefundRequestDTO
        {
            public string reference_no { get; set; }
            public string refund_amount { get; set; }
            public string refund_ref_no { get; set; }

            public override string ToString()
            {
                StringBuilder returnValue = new StringBuilder("\n----------------------RefundRequestDTO-----------------------------\n");
                returnValue.Append(" Reference_no : " + reference_no.ToString());
                returnValue.Append(" Refund_amount : " + refund_amount.ToString());
                returnValue.Append(" Refund_ref_no : " + refund_ref_no.ToString());
                returnValue.Append("\n-------------------------------------------------------------\n");
                return returnValue.ToString();
            }
        }
        #endregion

        #region RefundResponseDTO

        public class RefundOrderResult
        {
            public string reason { get; set; }
            public int refund_status { get; set; }
            public string error_code { get; set; }
        }

        public class RefundResponseDTO
        {
            public RefundOrderResult Refund_Order_Result { get; set; }
        }
        #endregion



    }
}
