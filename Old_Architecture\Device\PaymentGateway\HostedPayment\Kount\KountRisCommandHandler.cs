﻿using Semnox.Core.HttpUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.Kount
{

    class KountRisCommandHandler
    {

        private static Dictionary<string, string> paymentStatusDescriptions = new Dictionary<string, string>
        {
            { "D", "Payment has been declined" },
        };

        public static string GetTextResponseDescriptionKount(string statusCode)
        {
            if (paymentStatusDescriptions.ContainsKey(statusCode))
            {
                return paymentStatusDescriptions[statusCode];
            }
            else
            {
                return "Unknown Status";
            }
        }

        public static string KhashMethod(string creditCardNumber, string KountConfigKey)
        {
            string allowedCharacters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            // Combine the credit card number with the config key
            string dataToHash = creditCardNumber + "." + KountConfigKey;

            // Calculate the SHA-1 hash
            byte[] hashBytes;
            using (SHA1 sha1 = new SHA1CryptoServiceProvider())
            {
                hashBytes = sha1.ComputeHash(Encoding.UTF8.GetBytes(dataToHash));
            }

            // Convert the hash into a string of alphanumeric characters
            StringBuilder kountHash = new StringBuilder();
            int len = 14;
            for (int i = 0; i < len; i++)
            {
                byte hashByte = hashBytes[i % hashBytes.Length];
                int index = hashByte % allowedCharacters.Length;
                kountHash.Append(allowedCharacters[index]);
            }

            return creditCardNumber.Substring(0, 6) + kountHash.ToString();
        }

        public static string MaskMethod(string creditCardNumber, MaskingPattern pattern)
        {
            // Ensure the credit card number has at least 10 digits
            if (creditCardNumber.Length < 10)
            {
                throw new ArgumentException("Credit card number must be at least 10 digits long.");
            }

            // Determine the number of visible digits at the beginning and end
            int visibleDigitsAtStart = 0;
            int visibleDigitsAtEnd = 0;

            switch (pattern)
            {
                case MaskingPattern.AllMaskedButLastFour:
                    visibleDigitsAtEnd = 4;
                    break;
                case MaskingPattern.First2Last6:
                    visibleDigitsAtStart = 2;
                    visibleDigitsAtEnd = 6;
                    break;
                case MaskingPattern.First4Last4:
                    visibleDigitsAtStart = 4;
                    visibleDigitsAtEnd = 4;
                    break;
                default:
                    throw new ArgumentException("Invalid masking pattern specified.");
            }

            // Extract visible digits at the beginning
            string visibleStart = creditCardNumber.Substring(0, visibleDigitsAtStart);

            // Calculate the number of 'X' characters needed to meet the expected format
            int xCount = Math.Max(0, creditCardNumber.Length - visibleDigitsAtStart - visibleDigitsAtEnd);

            // Create a string with the required number of capital "X" characters
            string maskedX = new string('X', xCount);

            // Extract visible digits at the end
            string visibleEnd = creditCardNumber.Substring(creditCardNumber.Length - visibleDigitsAtEnd);

            // Ensure that the final format matches the expected pattern
            string maskedCardNumber = $"{visibleStart}{maskedX}{visibleEnd}";

            // Validate the output format
            if (!Regex.IsMatch(maskedCardNumber, $"^{Regex.Escape(visibleStart)}X*{Regex.Escape(visibleEnd)}$"))
            {
                throw new ArgumentException("Generated masked card number does not match the expected format.");
            }

            return maskedCardNumber;
        }

        public static List<KeyValuePair<string, string>> PrepareRequestData(TransactionPaymentsDTO transactionPaymentsDTO, KountConfigDTO kountConfigDTO, string KountConfigKey, KountRisRequestDTO kountRisRequestDTO, string CCDetails = "", string Penc = "")
        {
            var requestData = new List<KeyValuePair<string, string>>();

            if (kountConfigDTO.Ptyp == "CARD")
            {
                if (Penc == "KHASH")
                {
                    // Calculate the Kount hash using KhashMethod and set it as PTOK
                    CCDetails = KountRisCommandHandler.KhashMethod(CCDetails, KountConfigKey);
                }
                else if (Penc == "MASK")
                {
                    // Generate the masked credit card number using MaskMethod and set it as PTOK
                    CCDetails = KountRisCommandHandler.MaskMethod(CCDetails, MaskingPattern.AllMaskedButLastFour);
                }
            }
            double totalInCents = transactionPaymentsDTO.Amount * 100;


            // Add common parameters to the request data
            requestData.AddRange(new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("MERC", kountRisRequestDTO.Merc),
                new KeyValuePair<string, string>("MODE", kountConfigDTO.Mode),
                new KeyValuePair<string, string>("VERS", kountRisRequestDTO.Vers),
                new KeyValuePair<string, string>("SESS", transactionPaymentsDTO.Attribute2),
                new KeyValuePair<string, string>("SITE", kountRisRequestDTO.SiteId),
                new KeyValuePair<string, string>("MACK", kountRisRequestDTO.Mack),
                new KeyValuePair<string, string>("IPAD", transactionPaymentsDTO.ExternalSourceReference),
                new KeyValuePair<string, string>("FRMT", kountRisRequestDTO.Formt),
                new KeyValuePair<string, string>("AUTH", kountConfigDTO.Auth),
                new KeyValuePair<string, string>("CURR", transactionPaymentsDTO.CurrencyCode),
                new KeyValuePair<string, string>("PTYP", kountConfigDTO.Ptyp),
                new KeyValuePair<string, string>("PTOK", CCDetails),
                new KeyValuePair<string, string>("PENC", Penc),
                new KeyValuePair<string, string>("TOTL", totalInCents.ToString()),
                new KeyValuePair<string, string>("EMAL", transactionPaymentsDTO.NameOnCreditCard),
                new KeyValuePair<string, string>("NAME", transactionPaymentsDTO.CreditCardName),
                new KeyValuePair<string, string>("ORDR", transactionPaymentsDTO.TransactionId.ToString()),

            });

            // Add product-specific parameters to the request data
            //for (int i = 0; i < request.Products.Count; i++)
            for (int i = 0; i < transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList.Count; i++)

            {
                double priceInCents = transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList[i].LineId * 100;
                requestData.Add(new KeyValuePair<string, string>($"PROD_PRICE[{i}]", priceInCents.ToString()));
                requestData.Add(new KeyValuePair<string, string>($"PROD_QUANT[{i}]", transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList[i].Count.ToString()));
                requestData.Add(new KeyValuePair<string, string>($"PROD_TYPE[{i}]", transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList[i].CreatedBy));
                requestData.Add(new KeyValuePair<string, string>($"PROD_ITEM[{i}]", transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList[i].FromNumber));
                requestData.Add(new KeyValuePair<string, string>($"PROD_DESC[{i}]", transactionPaymentsDTO.paymentModeDTO.DiscountCouponsDTOList[i].LastUpdatedBy));
            }

            return requestData;
        }

        public static List<KeyValuePair<string, string>> PrepareRequestDataUpdate(string sess, string auth, string tran, KountRisRequestDTO kountRisRequestDTO)
        {
            var updateRequestData = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("MERC", kountRisRequestDTO.Merc),
                new KeyValuePair<string, string>("MODE", "U"),
                new KeyValuePair<string, string>("VERS", kountRisRequestDTO.Vers),
                new KeyValuePair<string, string>("SITE", kountRisRequestDTO.SiteId),
                new KeyValuePair<string, string>("MACK", kountRisRequestDTO.Mack),
                new KeyValuePair<string, string>("FRMT", kountRisRequestDTO.Formt),
                new KeyValuePair<string, string>("SESS", sess),
                new KeyValuePair<string, string>("AUTH", auth),
                new KeyValuePair<string, string>("TRAN", tran)
            };
            return updateRequestData;
        }


    }
}
