<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="https://transaction.elementexpress.com" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="https://transaction.elementexpress.com" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="https://transaction.elementexpress.com">
      <s:element name="HealthCheck">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="Credentials">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="AccountID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AccountToken" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AcceptorID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="NewAccountToken" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Application">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ApplicationVersion" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="HealthCheckResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="Response">
        <s:complexContent mixed="false">
          <s:extension base="tns:ExpressResponse">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="HostResponseCode" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="HostResponseMessage" type="s:string" />
              <s:element minOccurs="0" maxOccurs="1" name="HostTransactionDateTime" type="s:string" />
              <s:element minOccurs="1" maxOccurs="1" name="Credentials" type="tns:Credentials" />
              <s:element minOccurs="1" maxOccurs="1" name="Batch" type="tns:Batch" />
              <s:element minOccurs="1" maxOccurs="1" name="Card" type="tns:Card" />
              <s:element minOccurs="1" maxOccurs="1" name="Transaction" type="tns:Transaction" />
              <s:element minOccurs="1" maxOccurs="1" name="PaymentAccount" type="tns:PaymentAccount" />
              <s:element minOccurs="1" maxOccurs="1" name="Address" type="tns:Address" />
              <s:element minOccurs="1" maxOccurs="1" name="ScheduledTask" type="tns:ScheduledTask" />
              <s:element minOccurs="1" maxOccurs="1" name="DemandDepositAccount" type="tns:DemandDepositAccount" />
              <s:element minOccurs="1" maxOccurs="1" name="TransactionSetup" type="tns:TransactionSetup" />
              <s:element minOccurs="1" maxOccurs="1" name="Terminal" type="tns:Terminal" />
              <s:element minOccurs="1" maxOccurs="1" name="AutoRental" type="tns:AutoRental" />
              <s:element minOccurs="1" maxOccurs="1" name="Healthcare" type="tns:Healthcare" />
              <s:element minOccurs="1" maxOccurs="1" name="Lodging" type="tns:Lodging" />
              <s:element minOccurs="1" maxOccurs="1" name="BIN" type="tns:BIN" />
              <s:element minOccurs="1" maxOccurs="1" name="EnhancedBIN" type="tns:EnhancedBIN" />
              <s:element minOccurs="1" maxOccurs="1" name="Token" type="tns:Token" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ExpressResponse" abstract="true">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ExpressResponseCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpressResponseMessage" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpressTransactionDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpressTransactionTime" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpressTransactionTimezone" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExtendedParameters" type="tns:ArrayOfExtendedParameters" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfExtendedParameters">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ExtendedParameters" type="tns:ExtendedParameters" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ExtendedParameters">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Key" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Value" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Batch">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="BatchCloseType" type="tns:BatchCloseType" />
          <s:element minOccurs="1" maxOccurs="1" name="BatchQueryType" type="tns:BatchQueryType" />
          <s:element minOccurs="0" maxOccurs="1" name="HostBatchID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostItemID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostBatchCount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostBatchAmount" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="BatchGroupingCode" type="tns:BatchGroupingCode" />
          <s:element minOccurs="0" maxOccurs="1" name="HostReversalQueueID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostCreditSaleCount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostCreditSaleAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostCreditReturnCount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostCreditReturnAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostDebitSaleCount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostDebitSaleAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostDebitReturnCount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostDebitReturnAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostBatchItems" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="BatchIndexCode" type="tns:BatchIndexCode" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="BatchCloseType">
        <s:restriction base="s:string">
          <s:enumeration value="Regular" />
          <s:enumeration value="Force" />
          <s:enumeration value="Clear" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="BatchQueryType">
        <s:restriction base="s:string">
          <s:enumeration value="Totals" />
          <s:enumeration value="Item" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="BatchGroupingCode">
        <s:restriction base="s:string">
          <s:enumeration value="FullBatch" />
          <s:enumeration value="SingleItem" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="BatchIndexCode">
        <s:restriction base="s:string">
          <s:enumeration value="Current" />
          <s:enumeration value="FirstPrevious" />
          <s:enumeration value="SecondPrevious" />
          <s:enumeration value="ThirdPrevious" />
          <s:enumeration value="FourthPrevious" />
          <s:enumeration value="FifthPrevious" />
          <s:enumeration value="SixthPrevious" />
          <s:enumeration value="SeventhPrevious" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Card">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Track1Data" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Track2Data" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Track3Data" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="MagneprintData" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CardNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TruncatedCardNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpirationMonth" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ExpirationYear" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CardholderName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CVV" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CAVV" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="XID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PINBlock" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="KeySerialNumber" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="EncryptedFormat" type="tns:EncryptionFormat" />
          <s:element minOccurs="0" maxOccurs="1" name="EncryptedTrack1Data" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EncryptedTrack2Data" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EncryptedCardData" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CardDataKeySerialNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AVSResponseCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CVVResponseCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CAVVResponseCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CardLogo" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GiftCardSecurityCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AlternateCardNumber1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AlternateCardNumber2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AlternateCardNumber3" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SecondaryCardNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BIN" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="EncryptionFormat">
        <s:restriction base="s:string">
          <s:enumeration value="Default" />
          <s:enumeration value="Format1" />
          <s:enumeration value="Format2" />
          <s:enumeration value="Format3" />
          <s:enumeration value="Format4" />
          <s:enumeration value="Format5" />
          <s:enumeration value="Format6" />
          <s:enumeration value="Format7" />
          <s:enumeration value="Format8" />
          <s:enumeration value="Format9" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Transaction">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="TransactionID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ClerkNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShiftID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="OriginalAuthorizedAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TotalAuthorizedAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SalesTaxAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TipAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ApprovalNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReferenceNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TicketNumber" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ReversalType" type="tns:ReversalType" />
          <s:element minOccurs="1" maxOccurs="1" name="MarketCode" type="tns:MarketCode" />
          <s:element minOccurs="0" maxOccurs="1" name="AcquirerData" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CashBackAmount" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="BillPaymentFlag" type="tns:BooleanType" />
          <s:element minOccurs="1" maxOccurs="1" name="DuplicateCheckDisableFlag" type="tns:BooleanType" />
          <s:element minOccurs="1" maxOccurs="1" name="DuplicateOverrideFlag" type="tns:BooleanType" />
          <s:element minOccurs="1" maxOccurs="1" name="RecurringFlag" type="tns:BooleanType" />
          <s:element minOccurs="0" maxOccurs="1" name="CommercialCardCustomerCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ProcessorName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionStatus" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionStatusCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HostTransactionID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="MerchantVerificationValue" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PartialApprovedFlag" type="tns:BooleanType" />
          <s:element minOccurs="0" maxOccurs="1" name="ApprovedAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CommercialCardResponseCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BalanceAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BalanceCurrencyCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ConvenienceFeeAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GiftCardStatusCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillPayerAccountNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GiftCardBalanceTransferCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="EMVEncryptionFormat" type="tns:EMVEncryptionFormat" />
          <s:element minOccurs="1" maxOccurs="1" name="ReversalReason" type="tns:ReversalReason" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ReversalType">
        <s:restriction base="s:string">
          <s:enumeration value="System" />
          <s:enumeration value="Full" />
          <s:enumeration value="Partial" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="MarketCode">
        <s:restriction base="s:string">
          <s:enumeration value="Default" />
          <s:enumeration value="AutoRental" />
          <s:enumeration value="DirectMarketing" />
          <s:enumeration value="ECommerce" />
          <s:enumeration value="FoodRestaurant" />
          <s:enumeration value="HotelLodging" />
          <s:enumeration value="Petroleum" />
          <s:enumeration value="Retail" />
          <s:enumeration value="QSR" />
          <s:enumeration value="Grocery" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="BooleanType">
        <s:restriction base="s:string">
          <s:enumeration value="False" />
          <s:enumeration value="True" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="EMVEncryptionFormat">
        <s:restriction base="s:string">
          <s:enumeration value="Default" />
          <s:enumeration value="Format1" />
          <s:enumeration value="Format2" />
          <s:enumeration value="Format3" />
          <s:enumeration value="Format4" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ReversalReason">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown" />
          <s:enumeration value="RejectedPartialApproval" />
          <s:enumeration value="Timeout" />
          <s:enumeration value="EditError" />
          <s:enumeration value="MACVerifyError" />
          <s:enumeration value="MACSyncError" />
          <s:enumeration value="EncryptionError" />
          <s:enumeration value="SystemError" />
          <s:enumeration value="PossibleFraud" />
          <s:enumeration value="CardRemoval" />
          <s:enumeration value="ChipDecline" />
          <s:enumeration value="TerminalError" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="PaymentAccount">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="PaymentAccountID" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PaymentAccountType" type="tns:PaymentAccountType" />
          <s:element minOccurs="0" maxOccurs="1" name="PaymentBrand" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PaymentAccountReferenceNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupID" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PASSUpdaterBatchStatus" type="tns:PASSUpdaterBatchStatus" />
          <s:element minOccurs="1" maxOccurs="1" name="PASSUpdaterOption" type="tns:PASSUpdaterOption" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="PaymentAccountType">
        <s:restriction base="s:string">
          <s:enumeration value="CreditCard" />
          <s:enumeration value="Checking" />
          <s:enumeration value="Savings" />
          <s:enumeration value="ACH" />
          <s:enumeration value="Other" />
          <s:enumeration value="OmniToken" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="PASSUpdaterBatchStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Null" />
          <s:enumeration value="IncludedInNextBatch" />
          <s:enumeration value="NotIncludedInNextBatch" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="PASSUpdaterOption">
        <s:restriction base="s:string">
          <s:enumeration value="Null" />
          <s:enumeration value="AutoUpdateEnabled" />
          <s:enumeration value="AutoUpdateDisabled" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Address">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="BillingName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillingAddress1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillingAddress2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillingCity" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillingState" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillingZipcode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillingEmail" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BillingPhone" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingAddress1" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingAddress2" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingCity" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingState" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingZipcode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingEmail" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ShippingPhone" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ScheduledTask">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ScheduledTaskID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ScheduledTaskRunLogID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ScheduledTaskGroupID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ScheduledTaskName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ScheduledTaskReferenceNumber" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="RunFrequency" type="tns:RunFrequency" />
          <s:element minOccurs="0" maxOccurs="1" name="RunStartDate" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="RunUntilCancelFlag" type="tns:BooleanType" />
          <s:element minOccurs="0" maxOccurs="1" name="RunCycles" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ScheduledTaskStatus" type="tns:StatusType" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="RunFrequency">
        <s:restriction base="s:string">
          <s:enumeration value="OneTimeFuture" />
          <s:enumeration value="Daily" />
          <s:enumeration value="Weekly" />
          <s:enumeration value="BiWeekly" />
          <s:enumeration value="Monthly" />
          <s:enumeration value="BiMonthly" />
          <s:enumeration value="Quarterly" />
          <s:enumeration value="SemiAnnually" />
          <s:enumeration value="Yearly" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="StatusType">
        <s:restriction base="s:string">
          <s:enumeration value="Active" />
          <s:enumeration value="Disabled" />
          <s:enumeration value="Removed" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="DemandDepositAccount">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DDAAccountType" type="tns:DDAAccountType" />
          <s:element minOccurs="0" maxOccurs="1" name="AccountNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="RoutingNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CheckNumber" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="CheckType" type="tns:CheckType" />
          <s:element minOccurs="0" maxOccurs="1" name="TruncatedAccountNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TruncatedRoutingNumber" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="DDAAccountType">
        <s:restriction base="s:string">
          <s:enumeration value="Checking" />
          <s:enumeration value="Savings" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="CheckType">
        <s:restriction base="s:string">
          <s:enumeration value="Personal" />
          <s:enumeration value="Business" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="TransactionSetup">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupAccountID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupAcceptorID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupApplicationID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupApplicationName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionSetupApplicationVersion" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="TransactionSetupMethod" type="tns:TransactionSetupMethod" />
          <s:element minOccurs="1" maxOccurs="1" name="Device" type="tns:Device" />
          <s:element minOccurs="1" maxOccurs="1" name="Embedded" type="tns:BooleanType" />
          <s:element minOccurs="1" maxOccurs="1" name="CVVRequired" type="tns:BooleanType" />
          <s:element minOccurs="1" maxOccurs="1" name="AutoReturn" type="tns:BooleanType" />
          <s:element minOccurs="0" maxOccurs="1" name="CompanyName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LogoURL" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Tagline" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="WelcomeMessage" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReturnURL" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ReturnURLTitle" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="OrderDetails" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ProcessTransactionTitle" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ValidationCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DeviceInputCode" type="tns:DeviceInputCode" />
          <s:element minOccurs="0" maxOccurs="1" name="CustomCss" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="TransactionSetupMethod">
        <s:restriction base="s:string">
          <s:enumeration value="Null" />
          <s:enumeration value="CreditCardSale" />
          <s:enumeration value="CreditCardAuthorization" />
          <s:enumeration value="CreditCardAVSOnly" />
          <s:enumeration value="CreditCardForce" />
          <s:enumeration value="DebitCardSale" />
          <s:enumeration value="CheckSale" />
          <s:enumeration value="PaymentAccountCreate" />
          <s:enumeration value="PaymentAccountUpdate" />
          <s:enumeration value="Sale" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="Device">
        <s:restriction base="s:string">
          <s:enumeration value="Null" />
          <s:enumeration value="MagtekEncryptedSwipe" />
          <s:enumeration value="EncryptedInputDevice" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="DeviceInputCode">
        <s:restriction base="s:string">
          <s:enumeration value="NotUsed" />
          <s:enumeration value="Unknown" />
          <s:enumeration value="Terminal" />
          <s:enumeration value="Keyboard" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Terminal">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="TerminalID" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="TerminalType" type="tns:TerminalType" />
          <s:element minOccurs="1" maxOccurs="1" name="CardPresentCode" type="tns:CardPresentCode" />
          <s:element minOccurs="1" maxOccurs="1" name="CardholderPresentCode" type="tns:CardholderPresentCode" />
          <s:element minOccurs="1" maxOccurs="1" name="CardInputCode" type="tns:CardInputCode" />
          <s:element minOccurs="1" maxOccurs="1" name="CVVPresenceCode" type="tns:CVVPresenceCode" />
          <s:element minOccurs="1" maxOccurs="1" name="TerminalCapabilityCode" type="tns:TerminalCapabilityCode" />
          <s:element minOccurs="1" maxOccurs="1" name="TerminalEnvironmentCode" type="tns:TerminalEnvironmentCode" />
          <s:element minOccurs="1" maxOccurs="1" name="MotoECICode" type="tns:MotoECICode" />
          <s:element minOccurs="1" maxOccurs="1" name="CVVResponseType" type="tns:CVVResponseType" />
          <s:element minOccurs="1" maxOccurs="1" name="ConsentCode" type="tns:ConsentCode" />
          <s:element minOccurs="0" maxOccurs="1" name="TerminalSerialNumber" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="TerminalEncryptionFormat" type="tns:EncryptionFormat" />
          <s:element minOccurs="0" maxOccurs="1" name="LaneNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Model" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EMVKernelVersion" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="StoreCardID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="StoreCardPassword" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="OperatorID" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="TerminalType">
        <s:restriction base="s:string">
          <s:enumeration value="Unknown" />
          <s:enumeration value="PointOfSale" />
          <s:enumeration value="ECommerce" />
          <s:enumeration value="MOTO" />
          <s:enumeration value="FuelPump" />
          <s:enumeration value="ATM" />
          <s:enumeration value="Voice" />
          <s:enumeration value="Mobile" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="CardPresentCode">
        <s:restriction base="s:string">
          <s:enumeration value="UseDefault" />
          <s:enumeration value="Unknown" />
          <s:enumeration value="Present" />
          <s:enumeration value="NotPresent" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="CardholderPresentCode">
        <s:restriction base="s:string">
          <s:enumeration value="UseDefault" />
          <s:enumeration value="Unknown" />
          <s:enumeration value="Present" />
          <s:enumeration value="NotPresent" />
          <s:enumeration value="MailOrder" />
          <s:enumeration value="PhoneOrder" />
          <s:enumeration value="StandingAuth" />
          <s:enumeration value="ECommerce" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="CardInputCode">
        <s:restriction base="s:string">
          <s:enumeration value="UseDefault" />
          <s:enumeration value="Unknown" />
          <s:enumeration value="MagstripeRead" />
          <s:enumeration value="ContactlessMagstripeRead" />
          <s:enumeration value="ManualKeyed" />
          <s:enumeration value="ManualKeyedMagstripeFailure" />
          <s:enumeration value="ChipRead" />
          <s:enumeration value="ContactlessChipRead" />
          <s:enumeration value="ManualKeyedChipReadFailure" />
          <s:enumeration value="MagstripeReadChipReadFailure" />
          <s:enumeration value="MagstripeReadNonTechnicalFallback" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="CVVPresenceCode">
        <s:restriction base="s:string">
          <s:enumeration value="UseDefault" />
          <s:enumeration value="NotProvided" />
          <s:enumeration value="Provided" />
          <s:enumeration value="Illegible" />
          <s:enumeration value="CustomerIllegible" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="TerminalCapabilityCode">
        <s:restriction base="s:string">
          <s:enumeration value="UseDefault" />
          <s:enumeration value="Unknown" />
          <s:enumeration value="NoTerminal" />
          <s:enumeration value="MagstripeReader" />
          <s:enumeration value="ContactlessMagstripeReader" />
          <s:enumeration value="KeyEntered" />
          <s:enumeration value="ChipReader" />
          <s:enumeration value="ContactlessChipReader" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="TerminalEnvironmentCode">
        <s:restriction base="s:string">
          <s:enumeration value="UseDefault" />
          <s:enumeration value="NoTerminal" />
          <s:enumeration value="LocalAttended" />
          <s:enumeration value="LocalUnattended" />
          <s:enumeration value="RemoteAttended" />
          <s:enumeration value="RemoteUnattended" />
          <s:enumeration value="ECommerce" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="MotoECICode">
        <s:restriction base="s:string">
          <s:enumeration value="UseDefault" />
          <s:enumeration value="NotUsed" />
          <s:enumeration value="Single" />
          <s:enumeration value="Recurring" />
          <s:enumeration value="Installment" />
          <s:enumeration value="SecureECommerce" />
          <s:enumeration value="NonAuthenticatedSecureTransaction" />
          <s:enumeration value="NonAuthenticatedSecureECommerceTransaction" />
          <s:enumeration value="NonSecureECommerceTransaction" />
          <s:enumeration value="AmericanExpressToken" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="CVVResponseType">
        <s:restriction base="s:string">
          <s:enumeration value="Regular" />
          <s:enumeration value="Extended" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ConsentCode">
        <s:restriction base="s:string">
          <s:enumeration value="NotUsed" />
          <s:enumeration value="FaceToFace" />
          <s:enumeration value="Phone" />
          <s:enumeration value="Internet" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="AutoRental">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalAgreementNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalNoShowIndicator" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalExtraChargesDetail" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalPickupDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalDropoffDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalCustomerName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalReturnCity" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalReturnState" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalReturnLocationID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalDuration" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalPickupLocation" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalPickupCity" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalPickupState" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalPickupCountryCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalPickupTime" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalReturnCountryCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalReturnDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalReturnTime" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="AutoRentalVehicleClassCode" type="tns:AutoRentalVehicleClassCode" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalDistance" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="AutoRentalDistanceUnit" type="tns:AutoRentalDistanceUnit" />
          <s:element minOccurs="1" maxOccurs="1" name="AutoRentalAuditAdjustmentCode" type="tns:AutoRentalAuditAdjustmentCode" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalAuditAdjustmentAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalWeeklyRentalRate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalDailyRentalRate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AutoRentalInsuranceCharges" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="AutoRentalVehicleClassCode">
        <s:restriction base="s:string">
          <s:enumeration value="Unused" />
          <s:enumeration value="Mini" />
          <s:enumeration value="Subcompact" />
          <s:enumeration value="Economy" />
          <s:enumeration value="Compact" />
          <s:enumeration value="Midsize" />
          <s:enumeration value="Intermediate" />
          <s:enumeration value="Standard" />
          <s:enumeration value="Fullsize" />
          <s:enumeration value="Luxury" />
          <s:enumeration value="Premium" />
          <s:enumeration value="MiniVan" />
          <s:enumeration value="Van12Passenger" />
          <s:enumeration value="MovingVan" />
          <s:enumeration value="Van15Passenger" />
          <s:enumeration value="CargoVan" />
          <s:enumeration value="Truck12Foot" />
          <s:enumeration value="Truck20Foot" />
          <s:enumeration value="Truck24Foot" />
          <s:enumeration value="Truck26Foot" />
          <s:enumeration value="Moped" />
          <s:enumeration value="Stretch" />
          <s:enumeration value="Regular" />
          <s:enumeration value="Unique" />
          <s:enumeration value="Exotic" />
          <s:enumeration value="TruckSmallMedium" />
          <s:enumeration value="TruckLarge" />
          <s:enumeration value="SUVSmall" />
          <s:enumeration value="SUVMedium" />
          <s:enumeration value="SUVLarge" />
          <s:enumeration value="SUVExotic" />
          <s:enumeration value="FourWheelDrive" />
          <s:enumeration value="Special" />
          <s:enumeration value="Miscellaneous" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="AutoRentalDistanceUnit">
        <s:restriction base="s:string">
          <s:enumeration value="Unused" />
          <s:enumeration value="Miles" />
          <s:enumeration value="Kilometers" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="AutoRentalAuditAdjustmentCode">
        <s:restriction base="s:string">
          <s:enumeration value="NoAdjustments" />
          <s:enumeration value="MultipleAdjustments" />
          <s:enumeration value="OneAdjustmentCardmemberNotified" />
          <s:enumeration value="OneAdjustmentCardmemberNotNotified" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Healthcare">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareFlag" type="tns:BooleanType" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareFirstAccountType" type="tns:HealthcareAccountType" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareFirstAmountType" type="tns:HealthcareAmountType" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareFirstCurrencyCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareFirstAmountSign" type="tns:AmountSign" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareFirstAmount" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareSecondAccountType" type="tns:HealthcareAccountType" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareSecondAmountType" type="tns:HealthcareAmountType" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareSecondCurrencyCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareSecondAmountSign" type="tns:AmountSign" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareSecondAmount" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareThirdAccountType" type="tns:HealthcareAccountType" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareThirdAmountType" type="tns:HealthcareAmountType" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareThirdCurrencyCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareThirdAmountSign" type="tns:AmountSign" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareThirdAmount" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareFourthAccountType" type="tns:HealthcareAccountType" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareFourthAmountType" type="tns:HealthcareAmountType" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareFourthCurrencyCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="HealthcareFourthAmountSign" type="tns:AmountSign" />
          <s:element minOccurs="0" maxOccurs="1" name="HealthcareFourthAmount" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="HealthcareAccountType">
        <s:restriction base="s:string">
          <s:enumeration value="NotSpecified" />
          <s:enumeration value="Savings" />
          <s:enumeration value="Checking" />
          <s:enumeration value="CreditCard" />
          <s:enumeration value="Universal" />
          <s:enumeration value="StoredValueAccount" />
          <s:enumeration value="CashBenefitsAccount" />
          <s:enumeration value="FoodStampsAccount" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="HealthcareAmountType">
        <s:restriction base="s:string">
          <s:enumeration value="LedgerBalance" />
          <s:enumeration value="AvailableBalance" />
          <s:enumeration value="Healthcare" />
          <s:enumeration value="Transit" />
          <s:enumeration value="Copayment" />
          <s:enumeration value="OriginalAmount" />
          <s:enumeration value="PartialAuthorizedAmount" />
          <s:enumeration value="Prescription" />
          <s:enumeration value="Vision" />
          <s:enumeration value="Clinic" />
          <s:enumeration value="Dental" />
          <s:enumeration value="CashOver" />
          <s:enumeration value="OriginalCashOver" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="AmountSign">
        <s:restriction base="s:string">
          <s:enumeration value="Positive" />
          <s:enumeration value="Negative" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Lodging">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="LodgingAgreementNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingCheckInDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingCheckOutDate" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingRoomAmount" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingRoomTax" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingNoShowIndicator" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingDuration" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingCustomerName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingClientCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingExtraChargesDetail" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="LodgingExtraChargesAmounts" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="LodgingPrestigiousPropertyCode" type="tns:LodgingPrestigiousPropertyCode" />
          <s:element minOccurs="1" maxOccurs="1" name="LodgingSpecialProgramCode" type="tns:LodgingSpecialProgramCode" />
          <s:element minOccurs="1" maxOccurs="1" name="LodgingChargeType" type="tns:LodgingChargeType" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="LodgingPrestigiousPropertyCode">
        <s:restriction base="s:string">
          <s:enumeration value="NonParticipant" />
          <s:enumeration value="DollarLimit500" />
          <s:enumeration value="DollarLimit1000" />
          <s:enumeration value="DollarLimit1500" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="LodgingSpecialProgramCode">
        <s:restriction base="s:string">
          <s:enumeration value="Default" />
          <s:enumeration value="Sale" />
          <s:enumeration value="NoShow" />
          <s:enumeration value="AdvanceDeposit" />
          <s:enumeration value="DelayedCharge" />
          <s:enumeration value="ExpressService" />
          <s:enumeration value="AssuredReservation" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="LodgingChargeType">
        <s:restriction base="s:string">
          <s:enumeration value="Default" />
          <s:enumeration value="Restaurant" />
          <s:enumeration value="GiftShop" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="BIN">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="BINTypeCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BINTypeValue" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BINDecorator" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="EnhancedBIN">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Status" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DebitCard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CheckCard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CreditCard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="GiftCard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CommercialCard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="FleetCard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PrepaidCard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="HSAFSACard" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PINLessBillPay" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="EBT" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="WIC" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="InternationalBIN" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DurbinBINRegulation" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Token">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="TokenID" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="TokenProvider" type="tns:TokenProvider" />
          <s:element minOccurs="0" maxOccurs="1" name="VaultID" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TokenOptions" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TAProviderID" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="TokenProvider">
        <s:restriction base="s:string">
          <s:enumeration value="Null" />
          <s:enumeration value="ExpressPASS" />
          <s:enumeration value="OmniToken" />
          <s:enumeration value="Paymetric" />
          <s:enumeration value="TransArmor" />
        </s:restriction>
      </s:simpleType>
      <s:element name="TimeCheck">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TimeCheckResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccountTokenCreate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccountTokenCreateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccountTokenActivate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccountTokenActivateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="MagneprintDataDecrypt">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="MagneprintDataDecryptResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransactionSetup">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="transactionSetup" type="tns:TransactionSetup" />
            <s:element minOccurs="1" maxOccurs="1" name="address" type="tns:Address" />
            <s:element minOccurs="1" maxOccurs="1" name="paymentAccount" type="tns:PaymentAccount" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransactionSetupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BINQuery">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BINQueryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnhancedBINQuery">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EnhancedBINQueryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransactionSetupExpire">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="transactionSetup" type="tns:TransactionSetup" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TransactionSetupExpireResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardSale">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="address" type="tns:Address" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardSaleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAuthorization">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="address" type="tns:Address" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAuthorizationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAuthorizationCompletion">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAuthorizationCompletionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardForce">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardForceResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardCredit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardCreditResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAdjustment">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAdjustmentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardReversal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardReversalResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardIncrementalAuthorization">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardIncrementalAuthorizationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardBalanceInquiry">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardBalanceInquiryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardVoid">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardVoidResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAVSOnly">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="address" type="tns:Address" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardAVSOnlyResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardReturn">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreditCardReturnResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardSale">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardSaleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardReturn">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardReturnResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardReversal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardReversalResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardPinlessSale">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardPinlessSaleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardPinlessReturn">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DebitCardPinlessReturnResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BatchClose">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="batch" type="tns:Batch" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BatchCloseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BatchItemQuery">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="batch" type="tns:Batch" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BatchItemQueryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BatchTotalsQuery">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="batch" type="tns:Batch" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BatchTotalsQueryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckSale">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="demandDepositAccount" type="tns:DemandDepositAccount" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="identification" type="tns:Identification" />
            <s:element minOccurs="1" maxOccurs="1" name="address" type="tns:Address" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="Identification">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="TaxIDNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DriversLicenseNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DriversLicenseState" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BirthDate" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="CheckSaleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckCredit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="demandDepositAccount" type="tns:DemandDepositAccount" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="identification" type="tns:Identification" />
            <s:element minOccurs="1" maxOccurs="1" name="address" type="tns:Address" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckCreditResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckVerification">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="demandDepositAccount" type="tns:DemandDepositAccount" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="identification" type="tns:Identification" />
            <s:element minOccurs="1" maxOccurs="1" name="address" type="tns:Address" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckVerificationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckReturn">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckReturnResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckVoid">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckVoidResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckReversal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckReversalResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTSale">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="ebt" type="tns:EBT" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="EBT">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="EBTTypeIndex" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="VoucherNumber" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="EBTSaleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTCredit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="ebt" type="tns:EBT" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTCreditResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTVoucher">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="ebt" type="tns:EBT" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTVoucherResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTBalanceInquiry">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="ebt" type="tns:EBT" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTBalanceInquiryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTReversal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="1" maxOccurs="1" name="ebt" type="tns:EBT" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EBTReversalResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardSystemCheck">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardSystemCheckResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardActivate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardActivateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardSale">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardSaleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReload">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReloadResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardCredit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardCreditResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReturn">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReturnResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardBalanceInquiry">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardBalanceInquiryResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardUnload">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardUnloadResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardClose">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardCloseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardAuthorization">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardAuthorizationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardAuthorizationCompletion">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardAuthorizationCompletionResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReversal">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReversalResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReport">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardReportResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardBalanceTransfer">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="credentials" type="tns:Credentials" />
            <s:element minOccurs="1" maxOccurs="1" name="application" type="tns:Application" />
            <s:element minOccurs="1" maxOccurs="1" name="terminal" type="tns:Terminal" />
            <s:element minOccurs="1" maxOccurs="1" name="card" type="tns:Card" />
            <s:element minOccurs="1" maxOccurs="1" name="transaction" type="tns:Transaction" />
            <s:element minOccurs="0" maxOccurs="1" name="extendedParameters" type="tns:ArrayOfExtendedParameters" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GiftCardBalanceTransferResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="response" type="tns:Response" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HealthCheckSoapIn">
    <wsdl:part name="parameters" element="tns:HealthCheck" />
  </wsdl:message>
  <wsdl:message name="HealthCheckSoapOut">
    <wsdl:part name="parameters" element="tns:HealthCheckResponse" />
  </wsdl:message>
  <wsdl:message name="TimeCheckSoapIn">
    <wsdl:part name="parameters" element="tns:TimeCheck" />
  </wsdl:message>
  <wsdl:message name="TimeCheckSoapOut">
    <wsdl:part name="parameters" element="tns:TimeCheckResponse" />
  </wsdl:message>
  <wsdl:message name="AccountTokenCreateSoapIn">
    <wsdl:part name="parameters" element="tns:AccountTokenCreate" />
  </wsdl:message>
  <wsdl:message name="AccountTokenCreateSoapOut">
    <wsdl:part name="parameters" element="tns:AccountTokenCreateResponse" />
  </wsdl:message>
  <wsdl:message name="AccountTokenActivateSoapIn">
    <wsdl:part name="parameters" element="tns:AccountTokenActivate" />
  </wsdl:message>
  <wsdl:message name="AccountTokenActivateSoapOut">
    <wsdl:part name="parameters" element="tns:AccountTokenActivateResponse" />
  </wsdl:message>
  <wsdl:message name="MagneprintDataDecryptSoapIn">
    <wsdl:part name="parameters" element="tns:MagneprintDataDecrypt" />
  </wsdl:message>
  <wsdl:message name="MagneprintDataDecryptSoapOut">
    <wsdl:part name="parameters" element="tns:MagneprintDataDecryptResponse" />
  </wsdl:message>
  <wsdl:message name="TransactionSetupSoapIn">
    <wsdl:part name="parameters" element="tns:TransactionSetup" />
  </wsdl:message>
  <wsdl:message name="TransactionSetupSoapOut">
    <wsdl:part name="parameters" element="tns:TransactionSetupResponse" />
  </wsdl:message>
  <wsdl:message name="BINQuerySoapIn">
    <wsdl:part name="parameters" element="tns:BINQuery" />
  </wsdl:message>
  <wsdl:message name="BINQuerySoapOut">
    <wsdl:part name="parameters" element="tns:BINQueryResponse" />
  </wsdl:message>
  <wsdl:message name="EnhancedBINQuerySoapIn">
    <wsdl:part name="parameters" element="tns:EnhancedBINQuery" />
  </wsdl:message>
  <wsdl:message name="EnhancedBINQuerySoapOut">
    <wsdl:part name="parameters" element="tns:EnhancedBINQueryResponse" />
  </wsdl:message>
  <wsdl:message name="TransactionSetupExpireSoapIn">
    <wsdl:part name="parameters" element="tns:TransactionSetupExpire" />
  </wsdl:message>
  <wsdl:message name="TransactionSetupExpireSoapOut">
    <wsdl:part name="parameters" element="tns:TransactionSetupExpireResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardSaleSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardSale" />
  </wsdl:message>
  <wsdl:message name="CreditCardSaleSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardSaleResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardAuthorizationSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardAuthorization" />
  </wsdl:message>
  <wsdl:message name="CreditCardAuthorizationSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardAuthorizationResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardAuthorizationCompletionSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardAuthorizationCompletion" />
  </wsdl:message>
  <wsdl:message name="CreditCardAuthorizationCompletionSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardAuthorizationCompletionResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardForceSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardForce" />
  </wsdl:message>
  <wsdl:message name="CreditCardForceSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardForceResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardCreditSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardCredit" />
  </wsdl:message>
  <wsdl:message name="CreditCardCreditSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardCreditResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardAdjustmentSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardAdjustment" />
  </wsdl:message>
  <wsdl:message name="CreditCardAdjustmentSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardAdjustmentResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardReversalSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardReversal" />
  </wsdl:message>
  <wsdl:message name="CreditCardReversalSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardReversalResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardIncrementalAuthorizationSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardIncrementalAuthorization" />
  </wsdl:message>
  <wsdl:message name="CreditCardIncrementalAuthorizationSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardIncrementalAuthorizationResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardBalanceInquirySoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardBalanceInquiry" />
  </wsdl:message>
  <wsdl:message name="CreditCardBalanceInquirySoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardBalanceInquiryResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardVoidSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardVoid" />
  </wsdl:message>
  <wsdl:message name="CreditCardVoidSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardVoidResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardAVSOnlySoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardAVSOnly" />
  </wsdl:message>
  <wsdl:message name="CreditCardAVSOnlySoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardAVSOnlyResponse" />
  </wsdl:message>
  <wsdl:message name="CreditCardReturnSoapIn">
    <wsdl:part name="parameters" element="tns:CreditCardReturn" />
  </wsdl:message>
  <wsdl:message name="CreditCardReturnSoapOut">
    <wsdl:part name="parameters" element="tns:CreditCardReturnResponse" />
  </wsdl:message>
  <wsdl:message name="DebitCardSaleSoapIn">
    <wsdl:part name="parameters" element="tns:DebitCardSale" />
  </wsdl:message>
  <wsdl:message name="DebitCardSaleSoapOut">
    <wsdl:part name="parameters" element="tns:DebitCardSaleResponse" />
  </wsdl:message>
  <wsdl:message name="DebitCardReturnSoapIn">
    <wsdl:part name="parameters" element="tns:DebitCardReturn" />
  </wsdl:message>
  <wsdl:message name="DebitCardReturnSoapOut">
    <wsdl:part name="parameters" element="tns:DebitCardReturnResponse" />
  </wsdl:message>
  <wsdl:message name="DebitCardReversalSoapIn">
    <wsdl:part name="parameters" element="tns:DebitCardReversal" />
  </wsdl:message>
  <wsdl:message name="DebitCardReversalSoapOut">
    <wsdl:part name="parameters" element="tns:DebitCardReversalResponse" />
  </wsdl:message>
  <wsdl:message name="DebitCardPinlessSaleSoapIn">
    <wsdl:part name="parameters" element="tns:DebitCardPinlessSale" />
  </wsdl:message>
  <wsdl:message name="DebitCardPinlessSaleSoapOut">
    <wsdl:part name="parameters" element="tns:DebitCardPinlessSaleResponse" />
  </wsdl:message>
  <wsdl:message name="DebitCardPinlessReturnSoapIn">
    <wsdl:part name="parameters" element="tns:DebitCardPinlessReturn" />
  </wsdl:message>
  <wsdl:message name="DebitCardPinlessReturnSoapOut">
    <wsdl:part name="parameters" element="tns:DebitCardPinlessReturnResponse" />
  </wsdl:message>
  <wsdl:message name="BatchCloseSoapIn">
    <wsdl:part name="parameters" element="tns:BatchClose" />
  </wsdl:message>
  <wsdl:message name="BatchCloseSoapOut">
    <wsdl:part name="parameters" element="tns:BatchCloseResponse" />
  </wsdl:message>
  <wsdl:message name="BatchItemQuerySoapIn">
    <wsdl:part name="parameters" element="tns:BatchItemQuery" />
  </wsdl:message>
  <wsdl:message name="BatchItemQuerySoapOut">
    <wsdl:part name="parameters" element="tns:BatchItemQueryResponse" />
  </wsdl:message>
  <wsdl:message name="BatchTotalsQuerySoapIn">
    <wsdl:part name="parameters" element="tns:BatchTotalsQuery" />
  </wsdl:message>
  <wsdl:message name="BatchTotalsQuerySoapOut">
    <wsdl:part name="parameters" element="tns:BatchTotalsQueryResponse" />
  </wsdl:message>
  <wsdl:message name="CheckSaleSoapIn">
    <wsdl:part name="parameters" element="tns:CheckSale" />
  </wsdl:message>
  <wsdl:message name="CheckSaleSoapOut">
    <wsdl:part name="parameters" element="tns:CheckSaleResponse" />
  </wsdl:message>
  <wsdl:message name="CheckCreditSoapIn">
    <wsdl:part name="parameters" element="tns:CheckCredit" />
  </wsdl:message>
  <wsdl:message name="CheckCreditSoapOut">
    <wsdl:part name="parameters" element="tns:CheckCreditResponse" />
  </wsdl:message>
  <wsdl:message name="CheckVerificationSoapIn">
    <wsdl:part name="parameters" element="tns:CheckVerification" />
  </wsdl:message>
  <wsdl:message name="CheckVerificationSoapOut">
    <wsdl:part name="parameters" element="tns:CheckVerificationResponse" />
  </wsdl:message>
  <wsdl:message name="CheckReturnSoapIn">
    <wsdl:part name="parameters" element="tns:CheckReturn" />
  </wsdl:message>
  <wsdl:message name="CheckReturnSoapOut">
    <wsdl:part name="parameters" element="tns:CheckReturnResponse" />
  </wsdl:message>
  <wsdl:message name="CheckVoidSoapIn">
    <wsdl:part name="parameters" element="tns:CheckVoid" />
  </wsdl:message>
  <wsdl:message name="CheckVoidSoapOut">
    <wsdl:part name="parameters" element="tns:CheckVoidResponse" />
  </wsdl:message>
  <wsdl:message name="CheckReversalSoapIn">
    <wsdl:part name="parameters" element="tns:CheckReversal" />
  </wsdl:message>
  <wsdl:message name="CheckReversalSoapOut">
    <wsdl:part name="parameters" element="tns:CheckReversalResponse" />
  </wsdl:message>
  <wsdl:message name="EBTSaleSoapIn">
    <wsdl:part name="parameters" element="tns:EBTSale" />
  </wsdl:message>
  <wsdl:message name="EBTSaleSoapOut">
    <wsdl:part name="parameters" element="tns:EBTSaleResponse" />
  </wsdl:message>
  <wsdl:message name="EBTCreditSoapIn">
    <wsdl:part name="parameters" element="tns:EBTCredit" />
  </wsdl:message>
  <wsdl:message name="EBTCreditSoapOut">
    <wsdl:part name="parameters" element="tns:EBTCreditResponse" />
  </wsdl:message>
  <wsdl:message name="EBTVoucherSoapIn">
    <wsdl:part name="parameters" element="tns:EBTVoucher" />
  </wsdl:message>
  <wsdl:message name="EBTVoucherSoapOut">
    <wsdl:part name="parameters" element="tns:EBTVoucherResponse" />
  </wsdl:message>
  <wsdl:message name="EBTBalanceInquirySoapIn">
    <wsdl:part name="parameters" element="tns:EBTBalanceInquiry" />
  </wsdl:message>
  <wsdl:message name="EBTBalanceInquirySoapOut">
    <wsdl:part name="parameters" element="tns:EBTBalanceInquiryResponse" />
  </wsdl:message>
  <wsdl:message name="EBTReversalSoapIn">
    <wsdl:part name="parameters" element="tns:EBTReversal" />
  </wsdl:message>
  <wsdl:message name="EBTReversalSoapOut">
    <wsdl:part name="parameters" element="tns:EBTReversalResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardSystemCheckSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardSystemCheck" />
  </wsdl:message>
  <wsdl:message name="GiftCardSystemCheckSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardSystemCheckResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardActivateSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardActivate" />
  </wsdl:message>
  <wsdl:message name="GiftCardActivateSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardActivateResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardSaleSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardSale" />
  </wsdl:message>
  <wsdl:message name="GiftCardSaleSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardSaleResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardReloadSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardReload" />
  </wsdl:message>
  <wsdl:message name="GiftCardReloadSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardReloadResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardCreditSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardCredit" />
  </wsdl:message>
  <wsdl:message name="GiftCardCreditSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardCreditResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardReturnSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardReturn" />
  </wsdl:message>
  <wsdl:message name="GiftCardReturnSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardReturnResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardBalanceInquirySoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardBalanceInquiry" />
  </wsdl:message>
  <wsdl:message name="GiftCardBalanceInquirySoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardBalanceInquiryResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardUnloadSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardUnload" />
  </wsdl:message>
  <wsdl:message name="GiftCardUnloadSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardUnloadResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardCloseSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardClose" />
  </wsdl:message>
  <wsdl:message name="GiftCardCloseSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardCloseResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardAuthorizationSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardAuthorization" />
  </wsdl:message>
  <wsdl:message name="GiftCardAuthorizationSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardAuthorizationResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardAuthorizationCompletionSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardAuthorizationCompletion" />
  </wsdl:message>
  <wsdl:message name="GiftCardAuthorizationCompletionSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardAuthorizationCompletionResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardReversalSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardReversal" />
  </wsdl:message>
  <wsdl:message name="GiftCardReversalSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardReversalResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardReportSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardReport" />
  </wsdl:message>
  <wsdl:message name="GiftCardReportSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardReportResponse" />
  </wsdl:message>
  <wsdl:message name="GiftCardBalanceTransferSoapIn">
    <wsdl:part name="parameters" element="tns:GiftCardBalanceTransfer" />
  </wsdl:message>
  <wsdl:message name="GiftCardBalanceTransferSoapOut">
    <wsdl:part name="parameters" element="tns:GiftCardBalanceTransferResponse" />
  </wsdl:message>
  <wsdl:portType name="ExpressSoap">
    <wsdl:operation name="HealthCheck">
      <wsdl:input message="tns:HealthCheckSoapIn" />
      <wsdl:output message="tns:HealthCheckSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TimeCheck">
      <wsdl:input message="tns:TimeCheckSoapIn" />
      <wsdl:output message="tns:TimeCheckSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AccountTokenCreate">
      <wsdl:input message="tns:AccountTokenCreateSoapIn" />
      <wsdl:output message="tns:AccountTokenCreateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AccountTokenActivate">
      <wsdl:input message="tns:AccountTokenActivateSoapIn" />
      <wsdl:output message="tns:AccountTokenActivateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="MagneprintDataDecrypt">
      <wsdl:input message="tns:MagneprintDataDecryptSoapIn" />
      <wsdl:output message="tns:MagneprintDataDecryptSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TransactionSetup">
      <wsdl:input message="tns:TransactionSetupSoapIn" />
      <wsdl:output message="tns:TransactionSetupSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="BINQuery">
      <wsdl:input message="tns:BINQuerySoapIn" />
      <wsdl:output message="tns:BINQuerySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EnhancedBINQuery">
      <wsdl:input message="tns:EnhancedBINQuerySoapIn" />
      <wsdl:output message="tns:EnhancedBINQuerySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TransactionSetupExpire">
      <wsdl:input message="tns:TransactionSetupExpireSoapIn" />
      <wsdl:output message="tns:TransactionSetupExpireSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardSale">
      <wsdl:input message="tns:CreditCardSaleSoapIn" />
      <wsdl:output message="tns:CreditCardSaleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardAuthorization">
      <wsdl:input message="tns:CreditCardAuthorizationSoapIn" />
      <wsdl:output message="tns:CreditCardAuthorizationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardAuthorizationCompletion">
      <wsdl:input message="tns:CreditCardAuthorizationCompletionSoapIn" />
      <wsdl:output message="tns:CreditCardAuthorizationCompletionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardForce">
      <wsdl:input message="tns:CreditCardForceSoapIn" />
      <wsdl:output message="tns:CreditCardForceSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardCredit">
      <wsdl:input message="tns:CreditCardCreditSoapIn" />
      <wsdl:output message="tns:CreditCardCreditSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardAdjustment">
      <wsdl:input message="tns:CreditCardAdjustmentSoapIn" />
      <wsdl:output message="tns:CreditCardAdjustmentSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardReversal">
      <wsdl:input message="tns:CreditCardReversalSoapIn" />
      <wsdl:output message="tns:CreditCardReversalSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardIncrementalAuthorization">
      <wsdl:input message="tns:CreditCardIncrementalAuthorizationSoapIn" />
      <wsdl:output message="tns:CreditCardIncrementalAuthorizationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardBalanceInquiry">
      <wsdl:input message="tns:CreditCardBalanceInquirySoapIn" />
      <wsdl:output message="tns:CreditCardBalanceInquirySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardVoid">
      <wsdl:input message="tns:CreditCardVoidSoapIn" />
      <wsdl:output message="tns:CreditCardVoidSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardAVSOnly">
      <wsdl:input message="tns:CreditCardAVSOnlySoapIn" />
      <wsdl:output message="tns:CreditCardAVSOnlySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreditCardReturn">
      <wsdl:input message="tns:CreditCardReturnSoapIn" />
      <wsdl:output message="tns:CreditCardReturnSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DebitCardSale">
      <wsdl:input message="tns:DebitCardSaleSoapIn" />
      <wsdl:output message="tns:DebitCardSaleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DebitCardReturn">
      <wsdl:input message="tns:DebitCardReturnSoapIn" />
      <wsdl:output message="tns:DebitCardReturnSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DebitCardReversal">
      <wsdl:input message="tns:DebitCardReversalSoapIn" />
      <wsdl:output message="tns:DebitCardReversalSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DebitCardPinlessSale">
      <wsdl:input message="tns:DebitCardPinlessSaleSoapIn" />
      <wsdl:output message="tns:DebitCardPinlessSaleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DebitCardPinlessReturn">
      <wsdl:input message="tns:DebitCardPinlessReturnSoapIn" />
      <wsdl:output message="tns:DebitCardPinlessReturnSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="BatchClose">
      <wsdl:input message="tns:BatchCloseSoapIn" />
      <wsdl:output message="tns:BatchCloseSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="BatchItemQuery">
      <wsdl:input message="tns:BatchItemQuerySoapIn" />
      <wsdl:output message="tns:BatchItemQuerySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="BatchTotalsQuery">
      <wsdl:input message="tns:BatchTotalsQuerySoapIn" />
      <wsdl:output message="tns:BatchTotalsQuerySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckSale">
      <wsdl:input message="tns:CheckSaleSoapIn" />
      <wsdl:output message="tns:CheckSaleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckCredit">
      <wsdl:input message="tns:CheckCreditSoapIn" />
      <wsdl:output message="tns:CheckCreditSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckVerification">
      <wsdl:input message="tns:CheckVerificationSoapIn" />
      <wsdl:output message="tns:CheckVerificationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckReturn">
      <wsdl:input message="tns:CheckReturnSoapIn" />
      <wsdl:output message="tns:CheckReturnSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckVoid">
      <wsdl:input message="tns:CheckVoidSoapIn" />
      <wsdl:output message="tns:CheckVoidSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckReversal">
      <wsdl:input message="tns:CheckReversalSoapIn" />
      <wsdl:output message="tns:CheckReversalSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EBTSale">
      <wsdl:input message="tns:EBTSaleSoapIn" />
      <wsdl:output message="tns:EBTSaleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EBTCredit">
      <wsdl:input message="tns:EBTCreditSoapIn" />
      <wsdl:output message="tns:EBTCreditSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EBTVoucher">
      <wsdl:input message="tns:EBTVoucherSoapIn" />
      <wsdl:output message="tns:EBTVoucherSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EBTBalanceInquiry">
      <wsdl:input message="tns:EBTBalanceInquirySoapIn" />
      <wsdl:output message="tns:EBTBalanceInquirySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EBTReversal">
      <wsdl:input message="tns:EBTReversalSoapIn" />
      <wsdl:output message="tns:EBTReversalSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardSystemCheck">
      <wsdl:input message="tns:GiftCardSystemCheckSoapIn" />
      <wsdl:output message="tns:GiftCardSystemCheckSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardActivate">
      <wsdl:input message="tns:GiftCardActivateSoapIn" />
      <wsdl:output message="tns:GiftCardActivateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardSale">
      <wsdl:input message="tns:GiftCardSaleSoapIn" />
      <wsdl:output message="tns:GiftCardSaleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardReload">
      <wsdl:input message="tns:GiftCardReloadSoapIn" />
      <wsdl:output message="tns:GiftCardReloadSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardCredit">
      <wsdl:input message="tns:GiftCardCreditSoapIn" />
      <wsdl:output message="tns:GiftCardCreditSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardReturn">
      <wsdl:input message="tns:GiftCardReturnSoapIn" />
      <wsdl:output message="tns:GiftCardReturnSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardBalanceInquiry">
      <wsdl:input message="tns:GiftCardBalanceInquirySoapIn" />
      <wsdl:output message="tns:GiftCardBalanceInquirySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardUnload">
      <wsdl:input message="tns:GiftCardUnloadSoapIn" />
      <wsdl:output message="tns:GiftCardUnloadSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardClose">
      <wsdl:input message="tns:GiftCardCloseSoapIn" />
      <wsdl:output message="tns:GiftCardCloseSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardAuthorization">
      <wsdl:input message="tns:GiftCardAuthorizationSoapIn" />
      <wsdl:output message="tns:GiftCardAuthorizationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardAuthorizationCompletion">
      <wsdl:input message="tns:GiftCardAuthorizationCompletionSoapIn" />
      <wsdl:output message="tns:GiftCardAuthorizationCompletionSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardReversal">
      <wsdl:input message="tns:GiftCardReversalSoapIn" />
      <wsdl:output message="tns:GiftCardReversalSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardReport">
      <wsdl:input message="tns:GiftCardReportSoapIn" />
      <wsdl:output message="tns:GiftCardReportSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GiftCardBalanceTransfer">
      <wsdl:input message="tns:GiftCardBalanceTransferSoapIn" />
      <wsdl:output message="tns:GiftCardBalanceTransferSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ExpressSoap" type="tns:ExpressSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HealthCheck">
      <soap:operation soapAction="https://transaction.elementexpress.com/HealthCheck" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TimeCheck">
      <soap:operation soapAction="https://transaction.elementexpress.com/TimeCheck" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccountTokenCreate">
      <soap:operation soapAction="https://transaction.elementexpress.com/AccountTokenCreate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccountTokenActivate">
      <soap:operation soapAction="https://transaction.elementexpress.com/AccountTokenActivate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MagneprintDataDecrypt">
      <soap:operation soapAction="https://transaction.elementexpress.com/MagneprintDataDecrypt" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransactionSetup">
      <soap:operation soapAction="https://transaction.elementexpress.com/TransactionSetup" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BINQuery">
      <soap:operation soapAction="https://transaction.elementexpress.com/BINQuery" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnhancedBINQuery">
      <soap:operation soapAction="https://transaction.elementexpress.com/EnhancedBINQuery" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransactionSetupExpire">
      <soap:operation soapAction="https://transaction.elementexpress.com/TransactionSetupExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardSale">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardSale" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAuthorization">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardAuthorization" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAuthorizationCompletion">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardAuthorizationCompletion" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardForce">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardForce" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardCredit">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardCredit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAdjustment">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardAdjustment" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardReversal">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardReversal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardIncrementalAuthorization">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardIncrementalAuthorization" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardBalanceInquiry">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardBalanceInquiry" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardVoid">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardVoid" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAVSOnly">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardAVSOnly" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardReturn">
      <soap:operation soapAction="https://transaction.elementexpress.com/CreditCardReturn" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardSale">
      <soap:operation soapAction="https://transaction.elementexpress.com/DebitCardSale" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardReturn">
      <soap:operation soapAction="https://transaction.elementexpress.com/DebitCardReturn" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardReversal">
      <soap:operation soapAction="https://transaction.elementexpress.com/DebitCardReversal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardPinlessSale">
      <soap:operation soapAction="https://transaction.elementexpress.com/DebitCardPinlessSale" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardPinlessReturn">
      <soap:operation soapAction="https://transaction.elementexpress.com/DebitCardPinlessReturn" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BatchClose">
      <soap:operation soapAction="https://transaction.elementexpress.com/BatchClose" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BatchItemQuery">
      <soap:operation soapAction="https://transaction.elementexpress.com/BatchItemQuery" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BatchTotalsQuery">
      <soap:operation soapAction="https://transaction.elementexpress.com/BatchTotalsQuery" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckSale">
      <soap:operation soapAction="https://transaction.elementexpress.com/CheckSale" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckCredit">
      <soap:operation soapAction="https://transaction.elementexpress.com/CheckCredit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckVerification">
      <soap:operation soapAction="https://transaction.elementexpress.com/CheckVerification" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckReturn">
      <soap:operation soapAction="https://transaction.elementexpress.com/CheckReturn" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckVoid">
      <soap:operation soapAction="https://transaction.elementexpress.com/CheckVoid" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckReversal">
      <soap:operation soapAction="https://transaction.elementexpress.com/CheckReversal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTSale">
      <soap:operation soapAction="https://transaction.elementexpress.com/EBTSale" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTCredit">
      <soap:operation soapAction="https://transaction.elementexpress.com/EBTCredit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTVoucher">
      <soap:operation soapAction="https://transaction.elementexpress.com/EBTVoucher" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTBalanceInquiry">
      <soap:operation soapAction="https://transaction.elementexpress.com/EBTBalanceInquiry" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTReversal">
      <soap:operation soapAction="https://transaction.elementexpress.com/EBTReversal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardSystemCheck">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardSystemCheck" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardActivate">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardActivate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardSale">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardSale" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReload">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardReload" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardCredit">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardCredit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReturn">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardReturn" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardBalanceInquiry">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardBalanceInquiry" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardUnload">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardUnload" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardClose">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardClose" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardAuthorization">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardAuthorization" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardAuthorizationCompletion">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardAuthorizationCompletion" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReversal">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardReversal" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReport">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardReport" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardBalanceTransfer">
      <soap:operation soapAction="https://transaction.elementexpress.com/GiftCardBalanceTransfer" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ExpressSoap12" type="tns:ExpressSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HealthCheck">
      <soap12:operation soapAction="https://transaction.elementexpress.com/HealthCheck" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TimeCheck">
      <soap12:operation soapAction="https://transaction.elementexpress.com/TimeCheck" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccountTokenCreate">
      <soap12:operation soapAction="https://transaction.elementexpress.com/AccountTokenCreate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccountTokenActivate">
      <soap12:operation soapAction="https://transaction.elementexpress.com/AccountTokenActivate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MagneprintDataDecrypt">
      <soap12:operation soapAction="https://transaction.elementexpress.com/MagneprintDataDecrypt" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransactionSetup">
      <soap12:operation soapAction="https://transaction.elementexpress.com/TransactionSetup" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BINQuery">
      <soap12:operation soapAction="https://transaction.elementexpress.com/BINQuery" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnhancedBINQuery">
      <soap12:operation soapAction="https://transaction.elementexpress.com/EnhancedBINQuery" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TransactionSetupExpire">
      <soap12:operation soapAction="https://transaction.elementexpress.com/TransactionSetupExpire" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardSale">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardSale" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAuthorization">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardAuthorization" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAuthorizationCompletion">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardAuthorizationCompletion" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardForce">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardForce" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardCredit">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardCredit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAdjustment">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardAdjustment" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardReversal">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardReversal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardIncrementalAuthorization">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardIncrementalAuthorization" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardBalanceInquiry">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardBalanceInquiry" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardVoid">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardVoid" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardAVSOnly">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardAVSOnly" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreditCardReturn">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CreditCardReturn" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardSale">
      <soap12:operation soapAction="https://transaction.elementexpress.com/DebitCardSale" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardReturn">
      <soap12:operation soapAction="https://transaction.elementexpress.com/DebitCardReturn" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardReversal">
      <soap12:operation soapAction="https://transaction.elementexpress.com/DebitCardReversal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardPinlessSale">
      <soap12:operation soapAction="https://transaction.elementexpress.com/DebitCardPinlessSale" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DebitCardPinlessReturn">
      <soap12:operation soapAction="https://transaction.elementexpress.com/DebitCardPinlessReturn" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BatchClose">
      <soap12:operation soapAction="https://transaction.elementexpress.com/BatchClose" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BatchItemQuery">
      <soap12:operation soapAction="https://transaction.elementexpress.com/BatchItemQuery" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BatchTotalsQuery">
      <soap12:operation soapAction="https://transaction.elementexpress.com/BatchTotalsQuery" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckSale">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CheckSale" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckCredit">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CheckCredit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckVerification">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CheckVerification" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckReturn">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CheckReturn" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckVoid">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CheckVoid" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckReversal">
      <soap12:operation soapAction="https://transaction.elementexpress.com/CheckReversal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTSale">
      <soap12:operation soapAction="https://transaction.elementexpress.com/EBTSale" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTCredit">
      <soap12:operation soapAction="https://transaction.elementexpress.com/EBTCredit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTVoucher">
      <soap12:operation soapAction="https://transaction.elementexpress.com/EBTVoucher" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTBalanceInquiry">
      <soap12:operation soapAction="https://transaction.elementexpress.com/EBTBalanceInquiry" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EBTReversal">
      <soap12:operation soapAction="https://transaction.elementexpress.com/EBTReversal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardSystemCheck">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardSystemCheck" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardActivate">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardActivate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardSale">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardSale" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReload">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardReload" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardCredit">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardCredit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReturn">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardReturn" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardBalanceInquiry">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardBalanceInquiry" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardUnload">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardUnload" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardClose">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardClose" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardAuthorization">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardAuthorization" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardAuthorizationCompletion">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardAuthorizationCompletion" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReversal">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardReversal" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardReport">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardReport" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GiftCardBalanceTransfer">
      <soap12:operation soapAction="https://transaction.elementexpress.com/GiftCardBalanceTransfer" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Express">
    <wsdl:port name="ExpressSoap" binding="tns:ExpressSoap">
      <soap:address location="https://certtransaction.elementexpress.com/express.asmx" />
    </wsdl:port>
    <wsdl:port name="ExpressSoap12" binding="tns:ExpressSoap12">
      <soap12:address location="https://certtransaction.elementexpress.com/express.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>