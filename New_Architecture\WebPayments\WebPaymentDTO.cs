﻿/********************************************************************************************
* Project Name - Web Payments
 * Description - DTO object for web payment
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Semnox.Parafait.PaymentGateway;
using Semnox.Parafait.Transaction.V2;

namespace Semnox.Parafait.WebPayments
{
    public class WebPaymentDTO
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private TransactionDTO transactionDTO;
        private HostedPaymentRequestDTO hostedPaymentRequestDTO;
        private HostedPaymentResponseDTO hostedPaymentResponseDTO;

        public WebPaymentDTO()
        {
            log.LogMethodEntry();
            log.LogMethodExit();
        }

        /// <summary>
        /// Get/Set method of the TransactionDTO field
        /// </summary>
        [Browsable(false)]
        public TransactionDTO TransactionDTO
        {
            get
            {
                return transactionDTO;
            }

            set
            {
                transactionDTO = value;
            }
        }

        /// <summary>
        /// Get/Set method of the hostedPaymentRequestDTO field
        /// </summary>
        [Browsable(false)]
        public HostedPaymentRequestDTO HostedPaymentRequestDTO
        {
            get
            {
                return hostedPaymentRequestDTO;
            }

            set
            {
                hostedPaymentRequestDTO = value;
            }
        }

        /// <summary>
        /// Get/Set method of the hostedPaymentResponseDTO field
        /// </summary>
        [Browsable(false)]
        public HostedPaymentResponseDTO HostedPaymentResponseDTO
        {
            get
            {
                return hostedPaymentResponseDTO;
            }

            set
            {
                hostedPaymentResponseDTO = value;
            }
        }
    }
}
