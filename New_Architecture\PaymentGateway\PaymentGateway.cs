﻿/*
 *  * Project Name - PaymentGateway
 * Description  - PaymentGateway
 * 
 **************
 **Version Log
 **************
 *Version     Date             Modified By      Remarks          
 *****************************************************************************************************************
*2.90.0       23-Jun-2020      Raghuveera       Variable refund to check variable redund is allowed or not
*2.100.0      01-Sep-2020      Guru S A         Payment link changes
*2.110.0       30-Dec-2020      Girish Kundar   Modified : Added delete method = Payment link changes
*2.150.0      08-Feb-2022      Mathew Ninan     Re-designed to separate out device and payment operation
* 2.190.0     22-11-2024       Amrutha          Modified- as a part of Payment gateway Redesign
******************************************************************************************************************/
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using utility = Semnox.Core.Utilities;
using Semnox.Parafait.ViewContainer;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.Site;
using Semnox.Parafait.PaymentMode;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;

namespace Semnox.Parafait.PaymentGateway
{
    /// <summary>
    /// Delegate that invokes to write the Log to a File
    /// </summary>
    public delegate void WriteToLogDelegate(int KioskTrxId, string Activity, int TrxId, int Value, string Message, int POSMachineId, string POSMachine);

    /// <summary>
    /// Represents a payment gateway used for accepting credit/debit card payments.
    /// </summary>
    public class PaymentGateway
    {
        private readonly Semnox.Parafait.logging.Logger log;

        /// <summary>
        /// Parafait utilities.
        /// </summary>
        // protected Utilities utilities;
        protected utility.ExecutionContext ExecutionContext;

        /// <summary>
        /// Whether the payment process is supervised by an attendant.
        /// </summary>
        protected bool isUnattended;

        /// <summary>
        /// Whether the gateway requires the PaymentHistory
        /// </summary>
        public virtual bool IsPaymentHistoryListRequired
        {
            get
            {
                return false;
            }
        }
        /// <summary>
        /// Whether to print receipt
        /// </summary>
        protected bool printReceipt = true;

        /// <summary>
        /// Over all transaction amount
        /// </summary>
        protected decimal overallTransactionAmount = 0;

        /// <summary>
        /// Get/Set Methods for printReceipt
        /// </summary>
        public bool PrintReceipt
        {
            get
            {
                return printReceipt;
            }

            set
            {
                printReceipt = value;
            }
        }
        protected bool paymentLinkEnabled = false;
        protected DateTime lastTransactionCompleteTime;
        protected const string PAYMENTLINKSETUP = "PAYMENT_LINK_SETUP";
        protected const string TRANSACTIONPAYMENTLINKURL = "ONLINE_PAYMENT_LINK_URL";
        protected CancellationToken cancellationToken;
        protected WriteToLogDelegate writeToLogDelegate;
        protected bool isHostedPayment;
        public enum TRX_STATUS_CHECK_RESPONSE
        {
            //No action required
            NO_ACTION,
            //Wait
            WAIT,
            //Send Last Trx check Request
            SEND_STATUS_CHECK
        }
        /// <summary>
        /// Constructor of payment gateway class.
        /// </summary>
        /// <param name="executionContext">Environment Execution Context.</param>
        /// <param name="isUnattended">Whether the payment process is supervised by an attendant.</param>
        /// <param name="showMessageDelegate"> Delegate instance to display message.</param>
        /// <param name="writeToLogDelegate">Delegate instance for writing the Log to File</param>
        public PaymentGateway(utility.ExecutionContext executionContext, bool isUnattended, CancellationToken cancellationToken)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, isUnattended);
            this.ExecutionContext = executionContext;
            this.isUnattended = isUnattended;
            //this.showMessageDelegate = showMessageDelegate;
            //this.writeToLogDelegate = writeToLogDelegate;
            lastTransactionCompleteTime = DateTime.Now;
            paymentLinkEnabled = IsPaymentLinkEnbled();
            log.LogMethodExit(null);
        }

        /// <summary>
        /// Initializes the payment gateway.
        /// </summary>
        public virtual void Initialize(int paymentModeId)
        {
            log.LogMethodEntry(paymentModeId);
            log.LogMethodExit(null);
        }
        public virtual void ValidateConfiguration()
        {
            log.LogMethodEntry();
            log.LogMethodExit(null);
        }
        /// <summary>
        /// This method is to call after each transaction complete and clear all the objects which are created/set during the transaction.
        /// </summary>
        public virtual void CleanUp()
        {
            log.LogMethodEntry();
            log.LogMethodExit();
        }
        public async virtual Task<PaymentTransactionDTO> CheckLastTransactionStatus(TransactionPaymentDTO transactionPaymentsDTO, IProgress<PaymentProgressReport> progress, System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            List<PaymentTransactionDTO> paymentTransactionDTOList = new List<PaymentTransactionDTO>();
            try
            {
                List<PaymentModeContainerDTO> paymentModeContainerDTOList = PaymentModeViewContainerList.GetPaymentModeContainerDTOList(ExecutionContext);
                if (transactionPaymentsDTO.TransactionId == -1
                    ||
                    (transactionPaymentsDTO.TransactionId > -1
                     && paymentModeContainerDTOList != null
                     && paymentModeContainerDTOList.Exists(x => x.PaymentModeId == transactionPaymentsDTO.PaymentModeId && x.IsCreditCard == false)
                    )
                   )
                {
                    log.LogMethodExit("There is no credit card transaction done from this POS");
                    //TBC
                    //return true;
                    return null;
                }
                //PaymentTransactionDTO paymentTransaction = transactionPaymentsDTO.PaymentTransactionDTOList.Where(x => x.Status == PaymentTransactionStatuses.SUCCESS.ToString()).OrderByDescending(x => x.CreationDate).FirstOrDefault();
                //foreach (PaymentModeContainerDTO paymentModeContainerDTO in paymentModeContainerDTOList)
                //{
                //    LookupValuesContainerDTO lookupValuesContainerDTO = LookupsViewContainerList.GetLookupValuesContainerDTO(ExecutionContext.SiteId, paymentModeContainerDTO.Gateway);
                //    Enum.TryParse(lookupValuesContainerDTO.LookupValue, out gateWay);
                //}
                //if(gateWay == PaymentGateways.AdyenPayment)
                //{
                //    if (paymentTransaction.ParentResponseId != null && (int)paymentTransaction.ParentResponseId > -1)
                //    {
                //        paymentTransactionDTOList = await ITransactionUseCases.GetPaymentTransactionDTOList(parentResponseId: (int)paymentTransaction.ParentResponseId);
                //    }
                //}

                PaymentTransactionDTO paymentTransactionDTO = await SendLastTransactionStatusCheckRequestAsync(transactionPaymentsDTO, paymentTransactionDTOList, progress, cancellationToken);
                log.LogMethodExit(paymentTransactionDTO);
                return paymentTransactionDTO;
            }
            catch (Exception ex)
            {
                log.Error("Error in last transaction check", ex);
                log.LogMethodExit(false);
                //TBC
                //return false;
                return null;
            }
        }
        /// <summary>
        /// ValidateLastTransactionStatus
        /// Payment record for which status needs to be checked should be passed. 
        /// Also, last payment transaction (ccTransactionsPGW)
        /// for this payment should be shared. Can be null as well.
        /// </summary>
        /// <param name="paymentTransactionDTO">Payment Transaction DTO corresponding to ccTransactionsPGW</param>
        /// <param name="transactionPaymentsDTO">Tranction payment record for which status has to be checked</param>
        /// <returns></returns>
        public virtual KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>> ValidateLastTransactionStatus(TransactionPaymentDTO transactionPaymentsDTO)
        {
            //Any changes here make sure to review CheckLastTransactionStatus() as well
            log.LogMethodEntry(transactionPaymentsDTO);
            List<utility.ValidationError> validationErrorsList = new List<utility.ValidationError>();
            KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>> keyValuePair;
            keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>>(TRX_STATUS_CHECK_RESPONSE.NO_ACTION, validationErrorsList);
            try
            {
                if (transactionPaymentsDTO == null)
                {
                    keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>>(TRX_STATUS_CHECK_RESPONSE.NO_ACTION, validationErrorsList);
                    log.Info("There is no credit card transaction done for this transaction");
                }
                else
                {
                    //if (paymentTransactionDTO == null)
                    if (transactionPaymentsDTO.PaymentTransactionDTOList == null)
                    {
                        ////ToDo: In case of capture if the response is not received then last transaction check should use the original Authorization request's request ID instead of capture request requestid for checking the last transaction status. Currently there is no link between original authorization request and capture request of that authorization.
                        ////TransactionPaymentsListBL transactionPaymentsListBL = new TransactionPaymentsListBL(utilities.ExecutionContext);
                        ////transactionsPaymentsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.INVOICE_NUMBER, cCRequestPGWDTO.RequestID.ToString()));
                        ////ccTransactionsSearchParams.Add(new KeyValuePair<CCTransactionsPGWDTO.SearchByParameters, string>(CCTransactionsPGWDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                        //SendLastTransactionStatusCheckRequest(cCRequestPGWDTO, null); 

                        int waitTimeLimit = ParafaitDefaultViewContainerList.GetParafaitDefault<int>(ExecutionContext, "WAIT_TIME_FOR_LAST_PAYMENT_REQ_STATUS_CHECK", 15);
                        if (waitTimeLimit < 15)
                        {
                            waitTimeLimit = 15;
                            log.Info("Wait time should be minimum 15 minutes");
                        }
                        TimeSpan timeDiff = (transactionPaymentsDTO.PaymentDate.AddMinutes(waitTimeLimit) - SiteDateTime.GetSiteDateTime(ExecutionContext));
                        int waitMinutes = 0;
                        waitMinutes = (int)timeDiff.TotalMinutes;
                        if (transactionPaymentsDTO.PaymentDate.AddMinutes(waitMinutes) > SiteDateTime.GetSiteDateTime(ExecutionContext))
                        {
                            string errorMsg = MessageViewContainerList.GetMessage(ExecutionContext, 2765, waitMinutes);
                            //Payment request is pending for more that &1 minutes. Sending status check request...
                            validationErrorsList.Add(new utility.ValidationError("cCRequestPGWDTO", "RequestDatetime", errorMsg));
                            keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>>(TRX_STATUS_CHECK_RESPONSE.SEND_STATUS_CHECK, validationErrorsList);
                            log.Info("Wait time for last payment has crossed, need to send status check request");
                        }
                        else
                        {
                            string errorMsg = MessageViewContainerList.GetMessage(ExecutionContext, 2766, waitMinutes);
                            //Waiting for last card transaction response. Please attempt new payment after &1 minutes
                            validationErrorsList.Add(new utility.ValidationError("cCRequestPGWDTO", "RequestDatetime", errorMsg));
                            keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>>(TRX_STATUS_CHECK_RESPONSE.WAIT, validationErrorsList);
                            log.Info("Wait for last card transaction response. " + waitMinutes.ToString());
                        }
                    }
                    else
                    {
                        //transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.CCRESPONSE_ID, cCTransactionsPGWDTOList[0].ResponseID.ToString()));
                        //transactionsPaymentsSearchParams.Add(new KeyValuePair<TransactionPaymentsDTO.SearchByParameters, string>(TransactionPaymentsDTO.SearchByParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));
                        //List<TransactionPaymentsDTO> transactionPaymentsDTOs = transactionPaymentsListBL.GetTransactionPaymentsDTOList(transactionsPaymentsSearchParams);
                        if (transactionPaymentsDTO.PaymentTransactionDTOList != null
                           /* && transactionPaymentsDTO.PaymentTransactionDTOList.Exists(x => x.ResponseID == paymentTransactionDTO.ResponseID)*/)
                        {
                            keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>>(TRX_STATUS_CHECK_RESPONSE.NO_ACTION, validationErrorsList);
                            log.Info("credit card transaction done from this POS is up to date.");
                        }
                        else
                        {
                            keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>>(TRX_STATUS_CHECK_RESPONSE.SEND_STATUS_CHECK, validationErrorsList);
                            log.Info("Havent rececived payment, need to send status check request");
                        }
                        //if (transactionPaymentsDTOs == null || transactionPaymentsDTOs.Count == 0)
                        //{
                        //    // SendLastTransactionStatusCheckRequest(cCRequestPGWDTO, cCTransactionsPGWDTOList[0]);
                        //    keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<ValidationError>>(TRX_STATUS_CHECK_RESPONSE.SEND_STATUS_CHECK, validationErrorsList);
                        //    log.Info("Havent rececived payment, need to send status check request");
                        //}
                        //else
                        //{
                        //    keyValuePair = new KeyValuePair<TRX_STATUS_CHECK_RESPONSE, List<ValidationError>>(TRX_STATUS_CHECK_RESPONSE.NO_ACTION, validationErrorsList);
                        //    log.Info("credit card transaction done from this POS is up to date.");
                        //}
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex);
                throw;
            }
            log.LogMethodExit(keyValuePair);
            return keyValuePair;

        }
        public async virtual Task<PaymentTransactionDTO> SendLastTransactionStatusCheckRequestAsync(TransactionPaymentDTO transactionPaymentsDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.LogMethodExit();
            return null;
        }
        /// <summary>
        /// Print credit card receipt
        /// </summary>
        /// <param name="transactionPaymentDTOList"> Transaction payments dto list for priting the receipt</param>
        public virtual void PrintCCReceipt(List<TransactionPaymentDTO> transactionPaymentDTOList)
        {
            log.LogMethodEntry(transactionPaymentDTOList);
            PaymentTransactionDTO paymentTransactionDTO = null;
            foreach (TransactionPaymentDTO transactionPaymentsDTO in transactionPaymentDTOList)
            {
                PaymentModeContainerDTO paymentModeContainerDTO = PaymentModeViewContainerList.GetPaymentModeContainerDTO(ExecutionContext.SiteId, transactionPaymentsDTO.PaymentModeId);
                if (transactionPaymentsDTO != null
                    && transactionPaymentsDTO.PaymentTransactionDTOList != null
                    && transactionPaymentsDTO.PaymentTransactionDTOList
                       .OrderByDescending(x => x.ResponseID).FirstOrDefault().ResponseID != -1
                    )
                {
                    paymentTransactionDTO = transactionPaymentsDTO.PaymentTransactionDTOList
                                            .OrderByDescending(x => x.ResponseID).FirstOrDefault();

                    if (paymentTransactionDTO != null)
                    {
                        log.LogVariableState("CCTransactionsPGWDTO", paymentTransactionDTO);
                        try
                        {
                            if (!string.IsNullOrEmpty(paymentTransactionDTO.MerchantCopy) && paymentModeContainerDTO.PrintMerchantReceipt && !isUnattended)
                            {
                                log.Debug("Printing merchant copy");
                                paymentTransactionDTO.MerchantCopy = paymentTransactionDTO.MerchantCopy.Replace("@invoiceNo", transactionPaymentsDTO.TransactionId.ToString());
                                if (Print(paymentTransactionDTO.MerchantCopy))
                                {
                                    log.Debug("Printing merchant copy is completed");
                                    paymentTransactionDTO.MerchantCopy = null;
                                }
                            }
                            if (!string.IsNullOrEmpty(paymentTransactionDTO.CustomerCopy) && paymentModeContainerDTO.PrintCustomerReceipt)
                            {
                                log.Debug("Printing customer copy");
                                paymentTransactionDTO.CustomerCopy = paymentTransactionDTO.CustomerCopy.Replace("@invoiceNo", transactionPaymentsDTO.TransactionId.ToString());
                                if (Print(paymentTransactionDTO.CustomerCopy))
                                {
                                    log.Debug("Printing customer copy is completed");
                                    paymentTransactionDTO.CustomerCopy = null;
                                }
                            }
                            log.LogVariableState("Saving CCTransactionsPGWDTO", paymentTransactionDTO);
                        }
                        catch (Exception ex)
                        {
                            log.Error("Error in printing.", ex);
                        }
                    }
                    log.LogMethodExit();
                }
            }
        }

        /// <summary>
        /// Makes payment.
        /// </summary>
        /// <param name="transactionPaymentsDTO">TransactionPayments DTO</param>
        /// <returns></returns>
        public virtual async Task<PaymentTransactionDTO> MakePayment(TransactionPaymentDTO transactionPaymentDTO,
                                                                     List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                                     IProgress<PaymentProgressReport> progress,
                                                                     CancellationToken cancellationToken,
                                                                     TransactionDTO transactionDTO)
        {
            //log.LogMethodEntry(transactionPaymentDTO, preAuthTransactionPaymentDTO, progress);
            //log.LogMethodExit(null);
            //return null;
            log.LogMethodEntry(transactionPaymentDTO, paymentTransactionDTOList);//NonGateway changes
            PaymentTransactionDTO paymentTransactionDTO = BuildPaymentTransactionResponseDTO(transactionPaymentDTO);
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        private PaymentTransactionDTO BuildPaymentTransactionResponseDTO(TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO);

            PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO(-1, transactionPaymentDTO.TransactionId, transactionPaymentDTO.Guid, string.Empty,
                string.Empty, string.Empty,
                -1, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty,
                DateTime.Now, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, null, null, null, transactionPaymentDTO.Guid, true, PaymentTransactionStatuses.SUCCESS.ToString(), "", "", "", transactionPaymentDTO.Amount, -1);
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;

        }

        /// <summary>
        /// Reverts the payment.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public virtual async Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO refundTransactionPaymentsDTO, PaymentTransactionDTO originalPaymentTransactionDTO, List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress, System.Threading.CancellationToken cancellationToken)
        {
            //log.LogMethodEntry(refundTransactionPaymentsDTO, originalPaymentTransactionDTO);
            //log.LogMethodExit(null);
            //return null;
            log.LogMethodEntry(refundTransactionPaymentsDTO, originalPaymentTransactionDTO);
            originalPaymentTransactionDTO = BuildPaymentTransactionResponseDTO(refundTransactionPaymentsDTO);
            log.LogMethodExit(originalPaymentTransactionDTO);
            return originalPaymentTransactionDTO;
        }


        public virtual async Task<PaymentTransactionDTO> ReversePayment(TransactionPaymentDTO reversedTransactionPaymentDTO, IProgress<PaymentProgressReport> progress,
                                                           CancellationToken cancellationToken)
        {
            log.LogMethodEntry(reversedTransactionPaymentDTO);
            PaymentTransactionDTO originalPaymentTransactionDTO = BuildPaymentTransactionResponseDTO(reversedTransactionPaymentDTO);
            originalPaymentTransactionDTO.Status = PaymentTransactionStatuses.ERROR.ToString();
            originalPaymentTransactionDTO.TextResponse = MessageViewContainerList.GetMessage(ExecutionContext, "Payment Reverse is not supported.");
            log.LogMethodExit(originalPaymentTransactionDTO);
            return originalPaymentTransactionDTO;
        }

        /// <summary>
        /// Pays tip.
        /// </summary>
        /// <param name="transactionPaymentsDTO">Payment Transaction which is authorized or settled</param>
        /// <param name="paymentTransactionDTO">Payment Transaction DTO of approved payment</param>
        /// <returns>Updated Transaction Payment DTO</returns>
        public virtual async Task<PaymentTransactionDTO> PayTip(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO paymentTransactionDTO,List<PaymentTransactionDTO> paymentTransactionDTOList, IProgress<PaymentProgressReport> progress,
                                                                     CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentTransactionDTO);
            paymentTransactionDTO = BuildPaymentTransactionResponseDTO(transactionPaymentsDTO);
            paymentTransactionDTO.TipAmount = transactionPaymentsDTO.TipAmount.ToString();
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        /// <summary>
        /// Performs settlement.
        /// </summary>
        /// <param name="transactionPaymentsDTO">transactionPaymentsDTO to be settled.</param>
        /// <param name="paymentTransactionDTO">Authorization Payment Transaction DTO to be passed for performing Settlement</param>
        /// <param name="IsForcedSettlement"></param>
        /// <returns></returns>
        public virtual async Task<PaymentTransactionDTO> PerformSettlement(TransactionPaymentDTO transactionPaymentsDTO,
                                                                PaymentTransactionDTO paymentTransactionDTO,
                                                                List<PaymentTransactionDTO> paymentTransactionDTOList,
                                                                IProgress<PaymentProgressReport> progress,
                                                                CancellationToken cancellationToken,
                                                                bool IsForcedSettlement = false)
        {
            log.LogMethodEntry(transactionPaymentsDTO, paymentTransactionDTO, progress, cancellationToken, IsForcedSettlement);
            log.LogMethodExit(null);
            return null;
        }

        /// <summary>
        /// Checks if printing of last Transaction is supported
        /// This is used by PCEFTPOS Payment Gateway
        /// </summary>
        public virtual bool IsPrintLastTransactionSupported
        {
            get
            {
                return false;
            }

        }

        /// <summary>
        /// Checks whether the printer is required.
        /// This is used by QuestPaymentGateway in Kiosk
        /// </summary>
        public virtual bool IsPrinterRequired
        {
            get
            {
                return false;
            }

        }

        /// <summary>
        /// Checks whether Partial Payment is Approved .
        /// This is used by QuestPaymentGateway in Kiosk
        /// </summary>
        public virtual bool IsPartiallyApproved
        {
            get
            {
                return false;
            }

        }


        /// <summary>
        /// This indicates that the tip amount overriding is possible in the payment gateway or not.
        /// This is set to true in specific paymentgateway in which modification of tip is allowed
        /// </summary>
        public virtual bool IsTipAdjustmentAllowed
        {
            get
            {
                return false;
            }

        }
        public virtual bool IsTipEnabled(TransactionPaymentDTO transactionPaymentDTO, List<PaymentTransactionDTO> paymentTransactionDTOList)
        {
            log.LogMethodEntry(transactionPaymentDTO, paymentTransactionDTOList);
            log.LogMethodExit();
            return false;

        }
        /// <summary>
        /// Prints the last Transaction
        /// This been used by PCEFTPOS
        /// </summary> 
        public virtual void PrintLastTransaction()
        {
            log.LogMethodEntry();
            log.LogMethodExit(null);
        }

        /// <summary>
        /// It checks for the begin Sale
        /// This been used by NCR PaymentGateway in Kiosk
        /// </summary> 
        public virtual void BeginOrder()
        {
            log.LogMethodEntry();
            log.LogMethodExit(null);
        }

        /// <summary>
        /// It checks for the End Sale
        /// This been used by NCR PaymentGateway in Kiosk
        /// </summary> 
        public virtual void EndOrder()
        {
            log.LogMethodEntry();
            log.LogMethodExit(null);
        }
        protected virtual void PrintCreditCardReceipt(TransactionPaymentDTO transactionPaymentsDTO, PaymentTransactionDTO ccTransactionsPGWDTO)
        {
            log.LogMethodEntry();
            string customerCopy;
            string merchantCopy;
            customerCopy = GetReceiptText(transactionPaymentsDTO, ccTransactionsPGWDTO, false);
            merchantCopy = GetReceiptText(transactionPaymentsDTO, ccTransactionsPGWDTO, true);
            transactionPaymentsDTO.Memo = customerCopy;
            transactionPaymentsDTO.Memo += merchantCopy;

            if (ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "PRINT_CUSTOMER_RECEIPT") == "Y")
            {
                Print(customerCopy);
            }
            if (ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "PRINT_MERCHANT_RECEIPT") == "Y" && !isUnattended)
            {
                Print(merchantCopy);
            }
            log.LogMethodExit();
        }
        protected virtual string GetReceiptText(TransactionPaymentDTO trxPaymentsDTO, PaymentTransactionDTO ccTransactionsPGWDTO, bool IsMerchantCopy)
        {
            log.LogMethodEntry();
            try
            {
                SiteContainerDTO siteContainerDTO = SiteViewContainerList.GetCurrentSiteContainerDTO(ExecutionContext);
                string[] addressArray = siteContainerDTO != null ? siteContainerDTO.SiteAddress.Split(',') : new string[] { };
                string receiptText = "";
                receiptText += CCGatewayUtils.AllignText(siteContainerDTO != null ? siteContainerDTO.SiteName : "", Alignment.Center);
                if (addressArray != null && addressArray.Length > 0)
                {
                    for (int i = 0; i < addressArray.Length; i++)
                    {
                        receiptText += Environment.NewLine + CCGatewayUtils.AllignText(addressArray[i] + ((i != addressArray.Length - 1) ? "," : "."), Alignment.Center);
                    }
                }
                receiptText += Environment.NewLine;
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Merchant ID") + ": ".PadLeft(6) + ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "CREDIT_CARD_STORE_ID"), Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Transaction Date") + ": ".PadLeft(1) + ccTransactionsPGWDTO.TransactionDatetime.ToString("MMM dd yyyy HH:mm"), Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Transaction Type") + ": ".PadLeft(1) + ccTransactionsPGWDTO.TranCode, Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Invoice Number") + ": ".PadLeft(3) + trxPaymentsDTO.TransactionId, Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Transaction Amount") + " : " + Convert.ToDouble(trxPaymentsDTO.Amount).ToString(ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "AMOUNT_WITH_CURRENCY_SYMBOL")), Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Tip Amount") + " : ".PadLeft(7) + Convert.ToDouble(trxPaymentsDTO.TipAmount).ToString(ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "AMOUNT_WITH_CURRENCY_SYMBOL")), Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Total") + " : ".PadLeft(12) + ((Convert.ToDouble(ccTransactionsPGWDTO.Authorize) == 0) ? (trxPaymentsDTO.Amount + trxPaymentsDTO.TipAmount) : Convert.ToDecimal(ccTransactionsPGWDTO.Authorize)).ToString(ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "AMOUNT_WITH_CURRENCY_SYMBOL")), Alignment.Left);
                receiptText += Environment.NewLine;
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, (ccTransactionsPGWDTO.RecordNo.Equals("A")) ? "APPROVED" : (ccTransactionsPGWDTO.RecordNo.Equals("B")) ? "RETRY" : "DECLINED") + "-" + ccTransactionsPGWDTO.DSIXReturnCode, Alignment.Center);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, ccTransactionsPGWDTO.TextResponse), Alignment.Center);

                receiptText += Environment.NewLine;
                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.AuthCode))
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Authorization") + ": ".PadLeft(4) + ccTransactionsPGWDTO.AuthCode, Alignment.Left);
                if (!string.IsNullOrEmpty(ccTransactionsPGWDTO.UserTraceData))
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Card Type") + ": ".PadLeft(10) + ccTransactionsPGWDTO.UserTraceData, Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Cardholder Name") + ": ".PadLeft(4) + trxPaymentsDTO.NameOnCreditCard, Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "PAN") + ": ".PadLeft(14) + trxPaymentsDTO.CreditCardNumber, Alignment.Left);
                receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Entry Mode") + ": ".PadLeft(7) + ccTransactionsPGWDTO.CaptureStatus, Alignment.Left);
                receiptText += Environment.NewLine;
                if (IsMerchantCopy)
                {
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "Cardholder Signature"), Alignment.Center);
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText("_______________________", Alignment.Center);
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, 1180), Alignment.Center);
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText("**" + MessageViewContainerList.GetMessage(ExecutionContext, "Merchant Copy") + "**", Alignment.Center);

                }
                else
                {
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText(MessageViewContainerList.GetMessage(ExecutionContext, "IMPORTANT— retain this copy for your records"), Alignment.Center);
                    receiptText += Environment.NewLine;
                    receiptText += Environment.NewLine + CCGatewayUtils.AllignText("**" + MessageViewContainerList.GetMessage(ExecutionContext, "Cardholder Copy") + " **", Alignment.Center);
                }

                receiptText += Environment.NewLine;
                receiptText += CCGatewayUtils.AllignText(" " + MessageViewContainerList.GetMessage(ExecutionContext, "Thank You"), Alignment.Center);
                log.LogMethodExit(receiptText);
                return receiptText;
            }
            catch (Exception ex)
            {
                log.Fatal("GetReceiptText() failed to print receipt exception:" + ex.ToString());
                return null;
            }
        }
        public bool Print(string printText)
        {
            log.LogMethodEntry(printText);
            bool status = false;
            try
            {
                using (System.Drawing.Printing.PrintDocument pd = new System.Drawing.Printing.PrintDocument())
                {
                    pd.DefaultPageSettings.PaperSize = new System.Drawing.Printing.PaperSize();//("Custom", 300, 700);
                    pd.PrintPage += (sender, e) =>
                    {
                        Font f = new Font("Arial", 9);
                        e.Graphics.DrawString(printText, f, new SolidBrush(Color.Black), new RectangleF(0, 0, pd.DefaultPageSettings.PrintableArea.Width, pd.DefaultPageSettings.PrintableArea.Height));
                    };
                    pd.Print();
                }
                status = true;
            }
            catch (Exception ex)
            {
                //utilities.EventLog.logEvent("PaymentGateway", 'I', "Receipt print failed.", printText, this.GetType().Name, 2, "", "", utilities.ParafaitEnv.LoginID, utilities.ParafaitEnv.POSMachine, null);
                log.Error("Print receipt failed." + Environment.NewLine + printText, ex);
                status = false;
            }
            log.LogMethodExit(status);
            return status;
        }

        protected async void VerifyLastPaymentRequest(TransactionPaymentDTO transactionPaymentsDTO,
                                                IProgress<PaymentProgressReport> progress,
                                                System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            if (paymentLinkEnabled && transactionPaymentsDTO != null && transactionPaymentsDTO.TransactionId > -1)
            {
                KeyValuePair<PaymentGateway.TRX_STATUS_CHECK_RESPONSE, List<utility.ValidationError>> ValidationResponse = ValidateLastTransactionStatus(transactionPaymentsDTO);
                if (ValidationResponse.Key == PaymentGateway.TRX_STATUS_CHECK_RESPONSE.WAIT)
                {
                    throw new utility.ValidationException(MessageViewContainerList.GetMessage(ExecutionContext, "Vaidation Error"), ValidationResponse.Value);
                }
                else if (ValidationResponse.Key == PaymentGateway.TRX_STATUS_CHECK_RESPONSE.SEND_STATUS_CHECK)
                {

                    PaymentTransactionDTO paymentTransactionDTO = await CheckLastTransactionStatus(transactionPaymentsDTO, progress, cancellationToken);
                }
            }
            log.LogMethodExit();
        }

        protected bool IsPaymentLinkEnbled()
        {
            log.LogMethodEntry();
            bool linkEnabled = false;

            utility.LookupsContainerDTO lookupList = LookupsViewContainerList.GetLookupsContainerDTO(ExecutionContext.SiteId, PAYMENTLINKSETUP);
            if (lookupList.LookupValuesContainerDTOList != null && lookupList.LookupValuesContainerDTOList.Count > 0)
            {
                utility.LookupValuesContainerDTO lookupValuesContainerDTO = lookupList.LookupValuesContainerDTOList.Find(x => x.LookupValue.Equals(TRANSACTIONPAYMENTLINKURL));
                if (lookupValuesContainerDTO != null
                    && string.IsNullOrWhiteSpace(lookupValuesContainerDTO.Description) == false)
                {
                    linkEnabled = true;
                }
            }
            //LookupValuesList lookupValuesList = new LookupValuesList(utilities.ExecutionContext);
            //List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>> searchParam = new List<KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>>();
            //searchParam.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_NAME, PAYMENTLINKSETUP));
            //searchParam.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.LOOKUP_VALUE, TRANSACTIONPAYMENTLINKURL));
            //searchParam.Add(new KeyValuePair<LookupValuesDTO.SearchByLookupValuesParameters, string>(LookupValuesDTO.SearchByLookupValuesParameters.SITE_ID, utilities.ExecutionContext.GetSiteId().ToString()));

            //List<LookupValuesDTO> lookupValuesDTOList = lookupValuesList.GetAllLookupValues(searchParam, null);
            //if (lookupValuesDTOList != null && lookupValuesDTOList.Any() && string.IsNullOrWhiteSpace(lookupValuesDTOList[0].Description) == false)
            //{
            //    linkEnabled = true;
            //}
            log.LogMethodExit(linkEnabled);
            return linkEnabled;
        }

        /// <summary>
        /// Makes payment.
        /// </summary>
        /// <param name="transactionPaymentsDTO">TransactionPayments DTO</param>
        /// <returns></returns>
        public virtual async Task<PaymentTransactionDTO> MakePaymentForRecurringBilling(TransactionPaymentDTO transactionPaymentsDTO)
        {
            log.LogMethodEntry(transactionPaymentsDTO);
            log.LogMethodExit(null);
            throw new NotImplementedException();
        }

        public async virtual Task<List<TransactionPaymentDTO>> ValidatePayments(TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry();
            ITransactionUseCases transactionUseCases = TransactionUseCaseFactory.GetTransactionUseCases(ExecutionContext, Guid.NewGuid().ToString());
            List<TransactionPaymentDTO> result = await transactionUseCases.ValidatePayments(transactionPaymentDTO.TransactionId, new List<TransactionPaymentDTO>() { transactionPaymentDTO });
            log.LogMethodExit(result);
            return result;
        }

    }

    /// <summary>
    /// Represents payment gateway error that occur during application execution. 
    /// </summary>
    public class PaymentGatewayException : Exception
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Initializes a new instance of PaymentGatewayException class with a specified error message.
        /// </summary>
        /// <param name="message">message</param>
        public PaymentGatewayException(string message)
        : base(message)
        {
            log.LogMethodEntry(message);
            log.LogMethodExit(null);
        }
    }
}
