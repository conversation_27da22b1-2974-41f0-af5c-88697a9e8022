﻿/********************************************************************************************
* Project Name - Web Payments
 * Description - Web Payment Use Cases
 *
 **************
 ** Version Log
  **************
  * Version     Date Modified By Remarks
 *********************************************************************************************
 *2.160.0     16-Jun-2023     Nitin Created
 *********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Semnox.Parafait.PaymentGateway;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.Transaction.V2;

namespace Semnox.Parafait.WebPayments
{
    public interface IWebPaymentsUseCases
    {
        Task<WebPaymentDTO> StartWebPayment(int transactionId, TransactionPaymentDTO transactionPaymentDTO);

        Task<WebPaymentDTO> CompleteWebPayment(int transactionId, CompleteWebPaymentDTO completeWebPaymentDTO);

        Task<WebPaymentDTO> ReverseWebPayment(int transactionId, ReverseWebPaymentDTO reverseWebPaymentDTO);

        Task<HostedPaymentResponseDTO> ProcessGatewayResponse(string paymentGateway, string paymentGatewayResponse, string caller);

        Task<PaymentSessionDTO> GetPaymentSession(PaymentRequestDTO paymentRequestDTO);

    }
}
