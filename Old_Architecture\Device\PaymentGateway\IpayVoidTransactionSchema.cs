﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Serialization;

// 
// This source code was auto-generated by wsdl, Version=4.0.30319.33440.
// 


/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Web.Services.WebServiceBindingAttribute(Name = "Service1Soap", Namespace = "https://www.mobile88.com")]
public partial class Service1 : System.Web.Services.Protocols.SoapHttpClientProtocol
{

    private System.Threading.SendOrPostCallback VoidTransactionOperationCompleted;

    private System.Threading.SendOrPostCallback VoidTransactionV2OperationCompleted;

    private System.Threading.SendOrPostCallback VoidTransactionv3OperationCompleted;

    private System.Threading.SendOrPostCallback EncryptOperationCompleted;

    private System.Threading.SendOrPostCallback SecurityOperationCompleted;

    private System.Threading.SendOrPostCallback SecuritySHA2OperationCompleted;

    /// <remarks/>
    public Service1()
    {
        this.Url = "https://payment.ipay88.com.my/ePayment/WebService/Voidapi/VoidFunction.asmx";
    }

    /// <remarks/>
    public event VoidTransactionCompletedEventHandler VoidTransactionCompleted;

    /// <remarks/>
    public event VoidTransactionV2CompletedEventHandler VoidTransactionV2Completed;

    /// <remarks/>
    public event VoidTransactionv3CompletedEventHandler VoidTransactionv3Completed;

    /// <remarks/>
    public event EncryptCompletedEventHandler EncryptCompleted;

    /// <remarks/>
    public event SecurityCompletedEventHandler SecurityCompleted;

    /// <remarks/>
    public event SecuritySHA2CompletedEventHandler SecuritySHA2Completed;

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/VoidTransaction", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    public string VoidTransaction(string merchantcode, string cctransid, string amount, string currency, string signature)
    {
        object[] results = this.Invoke("VoidTransaction", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature});
        return ((string)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginVoidTransaction(string merchantcode, string cctransid, string amount, string currency, string signature, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("VoidTransaction", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature}, callback, asyncState);
    }

    /// <remarks/>
    public string EndVoidTransaction(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((string)(results[0]));
    }

    /// <remarks/>
    public void VoidTransactionAsync(string merchantcode, string cctransid, string amount, string currency, string signature)
    {
        this.VoidTransactionAsync(merchantcode, cctransid, amount, currency, signature, null);
    }

    /// <remarks/>
    public void VoidTransactionAsync(string merchantcode, string cctransid, string amount, string currency, string signature, object userState)
    {
        if ((this.VoidTransactionOperationCompleted == null))
        {
            this.VoidTransactionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnVoidTransactionOperationCompleted);
        }
        this.InvokeAsync("VoidTransaction", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature}, this.VoidTransactionOperationCompleted, userState);
    }

    private void OnVoidTransactionOperationCompleted(object arg)
    {
        if ((this.VoidTransactionCompleted != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.VoidTransactionCompleted(this, new VoidTransactionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/VoidTransactionV2", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    public VoidReturn VoidTransactionV2(string merchantcode, string cctransid, string amount, string currency, string signature, string terminalID, string version, string signatureType)
    {
        object[] results = this.Invoke("VoidTransactionV2", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature,
                    terminalID,
                    version,
                    signatureType});
        return ((VoidReturn)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginVoidTransactionV2(string merchantcode, string cctransid, string amount, string currency, string signature, string terminalID, string version, string signatureType, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("VoidTransactionV2", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature,
                    terminalID,
                    version,
                    signatureType}, callback, asyncState);
    }

    /// <remarks/>
    public VoidReturn EndVoidTransactionV2(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((VoidReturn)(results[0]));
    }

    /// <remarks/>
    public void VoidTransactionV2Async(string merchantcode, string cctransid, string amount, string currency, string signature, string terminalID, string version, string signatureType)
    {
        this.VoidTransactionV2Async(merchantcode, cctransid, amount, currency, signature, terminalID, version, signatureType, null);
    }

    /// <remarks/>
    public void VoidTransactionV2Async(string merchantcode, string cctransid, string amount, string currency, string signature, string terminalID, string version, string signatureType, object userState)
    {
        if ((this.VoidTransactionV2OperationCompleted == null))
        {
            this.VoidTransactionV2OperationCompleted = new System.Threading.SendOrPostCallback(this.OnVoidTransactionV2OperationCompleted);
        }
        this.InvokeAsync("VoidTransactionV2", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature,
                    terminalID,
                    version,
                    signatureType}, this.VoidTransactionV2OperationCompleted, userState);
    }

    private void OnVoidTransactionV2OperationCompleted(object arg)
    {
        if ((this.VoidTransactionV2Completed != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.VoidTransactionV2Completed(this, new VoidTransactionV2CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/VoidTransactionv3", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    public string VoidTransactionv3(string merchantcode, string cctransid, string amount, string currency, string signature, string refNo)
    {
        object[] results = this.Invoke("VoidTransactionv3", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature,
                    refNo});
        return ((string)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginVoidTransactionv3(string merchantcode, string cctransid, string amount, string currency, string signature, string refNo, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("VoidTransactionv3", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature,
                    refNo}, callback, asyncState);
    }

    /// <remarks/>
    public string EndVoidTransactionv3(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((string)(results[0]));
    }

    /// <remarks/>
    public void VoidTransactionv3Async(string merchantcode, string cctransid, string amount, string currency, string signature, string refNo)
    {
        this.VoidTransactionv3Async(merchantcode, cctransid, amount, currency, signature, refNo, null);
    }

    /// <remarks/>
    public void VoidTransactionv3Async(string merchantcode, string cctransid, string amount, string currency, string signature, string refNo, object userState)
    {
        if ((this.VoidTransactionv3OperationCompleted == null))
        {
            this.VoidTransactionv3OperationCompleted = new System.Threading.SendOrPostCallback(this.OnVoidTransactionv3OperationCompleted);
        }
        this.InvokeAsync("VoidTransactionv3", new object[] {
                    merchantcode,
                    cctransid,
                    amount,
                    currency,
                    signature,
                    refNo}, this.VoidTransactionv3OperationCompleted, userState);
    }

    private void OnVoidTransactionv3OperationCompleted(object arg)
    {
        if ((this.VoidTransactionv3Completed != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.VoidTransactionv3Completed(this, new VoidTransactionv3CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/Encrypt", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    public string Encrypt(string toEncrypt)
    {
        object[] results = this.Invoke("Encrypt", new object[] {
                    toEncrypt});
        return ((string)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginEncrypt(string toEncrypt, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("Encrypt", new object[] {
                    toEncrypt}, callback, asyncState);
    }

    /// <remarks/>
    public string EndEncrypt(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((string)(results[0]));
    }

    /// <remarks/>
    public void EncryptAsync(string toEncrypt)
    {
        this.EncryptAsync(toEncrypt, null);
    }

    /// <remarks/>
    public void EncryptAsync(string toEncrypt, object userState)
    {
        if ((this.EncryptOperationCompleted == null))
        {
            this.EncryptOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEncryptOperationCompleted);
        }
        this.InvokeAsync("Encrypt", new object[] {
                    toEncrypt}, this.EncryptOperationCompleted, userState);
    }

    private void OnEncryptOperationCompleted(object arg)
    {
        if ((this.EncryptCompleted != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.EncryptCompleted(this, new EncryptCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/Security", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    public string Security(string toEncrypt)
    {
        object[] results = this.Invoke("Security", new object[] {
                    toEncrypt});
        return ((string)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginSecurity(string toEncrypt, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("Security", new object[] {
                    toEncrypt}, callback, asyncState);
    }

    /// <remarks/>
    public string EndSecurity(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((string)(results[0]));
    }

    /// <remarks/>
    public void SecurityAsync(string toEncrypt)
    {
        this.SecurityAsync(toEncrypt, null);
    }

    /// <remarks/>
    public void SecurityAsync(string toEncrypt, object userState)
    {
        if ((this.SecurityOperationCompleted == null))
        {
            this.SecurityOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSecurityOperationCompleted);
        }
        this.InvokeAsync("Security", new object[] {
                    toEncrypt}, this.SecurityOperationCompleted, userState);
    }

    private void OnSecurityOperationCompleted(object arg)
    {
        if ((this.SecurityCompleted != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.SecurityCompleted(this, new SecurityCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://www.mobile88.com/SecuritySHA2", RequestNamespace = "https://www.mobile88.com", ResponseNamespace = "https://www.mobile88.com", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
    public string SecuritySHA2(string toEncrypt)
    {
        object[] results = this.Invoke("SecuritySHA2", new object[] {
                    toEncrypt});
        return ((string)(results[0]));
    }

    /// <remarks/>
    public System.IAsyncResult BeginSecuritySHA2(string toEncrypt, System.AsyncCallback callback, object asyncState)
    {
        return this.BeginInvoke("SecuritySHA2", new object[] {
                    toEncrypt}, callback, asyncState);
    }

    /// <remarks/>
    public string EndSecuritySHA2(System.IAsyncResult asyncResult)
    {
        object[] results = this.EndInvoke(asyncResult);
        return ((string)(results[0]));
    }

    /// <remarks/>
    public void SecuritySHA2Async(string toEncrypt)
    {
        this.SecuritySHA2Async(toEncrypt, null);
    }

    /// <remarks/>
    public void SecuritySHA2Async(string toEncrypt, object userState)
    {
        if ((this.SecuritySHA2OperationCompleted == null))
        {
            this.SecuritySHA2OperationCompleted = new System.Threading.SendOrPostCallback(this.OnSecuritySHA2OperationCompleted);
        }
        this.InvokeAsync("SecuritySHA2", new object[] {
                    toEncrypt}, this.SecuritySHA2OperationCompleted, userState);
    }

    private void OnSecuritySHA2OperationCompleted(object arg)
    {
        if ((this.SecuritySHA2Completed != null))
        {
            System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
            this.SecuritySHA2Completed(this, new SecuritySHA2CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
        }
    }

    /// <remarks/>
    public new void CancelAsync(object userState)
    {
        base.CancelAsync(userState);
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace = "https://www.mobile88.com")]
public partial class VoidReturn
{

    private string versionField;

    private string merchantCodeField;

    private string cCTransIdField;

    private string currencyField;

    private string amountField;

    private string statusField;

    private string errDescField;

    private string rRNField;

    private string terminalIDField;

    private string signatureField;

    private string signatureTypeField;

    private string refNoField;

    /// <remarks/>
    public string Version
    {
        get
        {
            return this.versionField;
        }
        set
        {
            this.versionField = value;
        }
    }

    /// <remarks/>
    public string MerchantCode
    {
        get
        {
            return this.merchantCodeField;
        }
        set
        {
            this.merchantCodeField = value;
        }
    }

    /// <remarks/>
    public string CCTransId
    {
        get
        {
            return this.cCTransIdField;
        }
        set
        {
            this.cCTransIdField = value;
        }
    }

    /// <remarks/>
    public string Currency
    {
        get
        {
            return this.currencyField;
        }
        set
        {
            this.currencyField = value;
        }
    }

    /// <remarks/>
    public string Amount
    {
        get
        {
            return this.amountField;
        }
        set
        {
            this.amountField = value;
        }
    }

    /// <remarks/>
    public string status
    {
        get
        {
            return this.statusField;
        }
        set
        {
            this.statusField = value;
        }
    }

    /// <remarks/>
    public string ErrDesc
    {
        get
        {
            return this.errDescField;
        }
        set
        {
            this.errDescField = value;
        }
    }

    /// <remarks/>
    public string RRN
    {
        get
        {
            return this.rRNField;
        }
        set
        {
            this.rRNField = value;
        }
    }

    /// <remarks/>
    public string TerminalID
    {
        get
        {
            return this.terminalIDField;
        }
        set
        {
            this.terminalIDField = value;
        }
    }

    /// <remarks/>
    public string Signature
    {
        get
        {
            return this.signatureField;
        }
        set
        {
            this.signatureField = value;
        }
    }

    /// <remarks/>
    public string SignatureType
    {
        get
        {
            return this.signatureTypeField;
        }
        set
        {
            this.signatureTypeField = value;
        }
    }

    /// <remarks/>
    public string refNo
    {
        get
        {
            return this.refNoField;
        }
        set
        {
            this.refNoField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void VoidTransactionCompletedEventHandler(object sender, VoidTransactionCompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class VoidTransactionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal VoidTransactionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public string Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((string)(this.results[0]));
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void VoidTransactionV2CompletedEventHandler(object sender, VoidTransactionV2CompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class VoidTransactionV2CompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal VoidTransactionV2CompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public VoidReturn Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((VoidReturn)(this.results[0]));
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void VoidTransactionv3CompletedEventHandler(object sender, VoidTransactionv3CompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class VoidTransactionv3CompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal VoidTransactionv3CompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public string Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((string)(this.results[0]));
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void EncryptCompletedEventHandler(object sender, EncryptCompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class EncryptCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal EncryptCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public string Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((string)(this.results[0]));
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void SecurityCompletedEventHandler(object sender, SecurityCompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class SecurityCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal SecurityCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public string Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((string)(this.results[0]));
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
public delegate void SecuritySHA2CompletedEventHandler(object sender, SecuritySHA2CompletedEventArgs e);

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("wsdl", "4.0.30319.33440")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
public partial class SecuritySHA2CompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
{

    private object[] results;

    internal SecuritySHA2CompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
            base(exception, cancelled, userState)
    {
        this.results = results;
    }

    /// <remarks/>
    public string Result
    {
        get
        {
            this.RaiseExceptionIfNecessary();
            return ((string)(this.results[0]));
        }
    }
}
