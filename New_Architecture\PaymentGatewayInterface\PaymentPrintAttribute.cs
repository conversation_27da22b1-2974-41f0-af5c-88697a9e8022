﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class PaymentPrintAttribute 
    {
        Dictionary<string, string> attributes = new Dictionary<string, string>();

         protected void SetAttribute(string key, string value)
        {
            if (attributes.ContainsKey(key))
            {
                attributes[key] = value;
            }
            else
            {
                attributes.Add(key, value);
            }
        }
        public string GetAttribute(string key)
        {
            if (attributes.ContainsKey(key) == false)
            {
                throw new Exception("Key: " + key + " not found");

            }
            return attributes[key];
        }

        internal bool ContainsKey(string key)
        {
            return attributes.ContainsKey(key);
        }
    }
    

}
