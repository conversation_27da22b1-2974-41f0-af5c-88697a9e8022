﻿<?xml version="1.0"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="Parafait.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  <connectionStrings>
    <add name="ParafaitUtils.Properties.Settings.ParafaitConnectionString"
      connectionString="Data Source=MLR-LT001\SQLEXPRESS;Initial Catalog=parafait;Persist Security Info=True;User ID=parafait;Password=*****;pezz4aLNZ5rhAMN04bUczw==*****;;Connect Timeout=30" />
    <add name="Parafait.Properties.Settings.ParafaitConnectionString"
      connectionString="Data Source=MLR-LT001\SQLEXPRESS;Initial Catalog=parafait;Persist Security Info=True;User ID=parafait;Password=*****;pezz4aLNZ5rhAMN04bUczw==*****;;Connect Timeout=30"
      providerName="System.Data.SqlClient" />
    <add name="logger.Properties.Settings.ParafaitConnectionString"
      connectionString="Data Source=MLR-LT001\SQLEXPRESS;Initial Catalog=Parafait;Integrated Security=True"
      providerName="System.Data.SqlClient" />
  </connectionStrings>
  <userSettings>

    <Parafait.Properties.Settings>
      <setting name="CardScanPortNumber" serializeAs="String">
        <value>3</value>
      </setting>
    </Parafait.Properties.Settings>
  </userSettings>

  <!--<system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="DigitalSignageServiceSoap">
          <security mode="Transport" />
        </binding>
        <binding name="DigitalSignageServiceSoap1" />
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="https://localhost/DigitalSignageService.asmx"
          binding="basicHttpBinding" bindingConfiguration="DigitalSignageServiceSoap"
          contract="Service.DigitalSignageServiceSoap" name="DigitalSignageServiceSoap" />
    </client>
  </system.serviceModel>-->

  <startup>

    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/>
  </startup>
</configuration>
