﻿/********************************************************************************************
 * Project Name -  WPCyberSource Hosted Payment Gateway                                                                     
 * Description  -  Class to handle the payment of WPCyberSource Hosted Payment Gateway - Callback for Angular
 ********************************************************************************************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 ********************************************************************************************
 *2.150.1     13-Jan-2023    Nitin Pai                       Created for Website 
 ********************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Semnox.Core.Utilities;
using Semnox.Parafait.PaymentGatewayInterface;
using Semnox.Parafait.Transaction.V2;
using Semnox.Parafait.ViewContainer;

namespace Semnox.Parafait.PaymentGateway.WorldPayCyberSource
{
    public class WPCyberSourceCallbackHostedPaymentGateway : WPCyberSourceHostedPaymentGateway
    {
        internal override string paymentGatewayName { get { return "WPCyberSourceCallbackHostedPayment"; } }

        public WPCyberSourceCallbackHostedPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, System.Threading.CancellationToken cancellationToken)
            : base(executionContext, isUnattended, cancellationToken)
        {
        }
    }

    public class WPCyberSourceHostedPaymentGateway : WebHostedPaymentGateway
    {
        internal readonly Semnox.Parafait.logging.Logger log;
        internal virtual string paymentGatewayName { get { return "WPCyberSourceHostedPayment"; } }
        private Dictionary<string, string> configParameters = new Dictionary<string, string>();
        private Dictionary<string, string> responseTextCollection = new Dictionary<string, string>();

        private const string SCHEME = "https://";
        private const string ALGORITHM = "HmacSHA256";
        private const string LOCALE = "en-GB";
        private const string TX_TYPE = "sale";
        private const string PA_TX_MODE = "S"; // S=>E-commerce, R=>Retail
        private const string PA_CHALLENGE_CODE = "04"; // PA: Payer Authentication

        private enum TxType
        {
            SALE,//0
            TxStatusCheck,//1
            REFUND,//2
            VOID,//3
            TxSearch,//4
        }

        private enum TxResponse
        {
            SUCCESS = 100,
            INVALID_REQUEST = 102,
            PARTIAL_AMOUNT_APPROVED = 110,
            DUPLICATE_TRANSACTION = 104,
            GENERAL_SYSTEM_FAILURE = 150,
            SERVER_TIMEOUT = 151,
            SERVICE_TIMEOUT = 152,
            ISSUING_BANK_UNAVAILABLE = 207,
            PROCESSOR_FAILURE = 236,
        };


        public WPCyberSourceHostedPaymentGateway(Semnox.Core.Utilities.ExecutionContext executionContext, bool isUnattended, System.Threading.CancellationToken cancellationToken)
            : base(executionContext, isUnattended, cancellationToken)
        {
            log = LogManager.GetLogger(executionContext, System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
            log.LogMethodEntry(executionContext, writeToLogDelegate);
            this.InitConfigurations();
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls11 | System.Net.SecurityProtocolType.Tls12 | SecurityProtocolType.Ssl3;
            System.Net.ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(delegate { return true; });//certificate validation procedure for the SSL/TLS secure channel
            log.LogMethodExit(null);
        }

        private void InitConfigurations()
        {
            log.LogMethodEntry();
            StringBuilder errMsg = new StringBuilder();
            this.WebPaymentGatewayConfiguration = new WebPaymentGatewayConfiguration();

            // Payment Configs
            this.WebPaymentGatewayConfiguration.PartnerId = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PARTNER_ID");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.PartnerId))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_PARTNER_ID", ExecutionContext.GetSiteId().ToString() }));
            }
            this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL = ParafaitDefaultContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_BASE_URL");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_BASE_URL", ExecutionContext.GetSiteId().ToString() }));
            }
            this.WebPaymentGatewayConfiguration.MerchantKey = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.MerchantKey))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_MERCHANT_KEY", ExecutionContext.GetSiteId().ToString() }));
            }
            this.WebPaymentGatewayConfiguration.GatewayUserNamePubKey = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayUserNamePubKey))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_PUBLISHABLE_KEY", ExecutionContext.GetSiteId().ToString() }));
            }
            this.WebPaymentGatewayConfiguration.GatewayPasswordSecretKey = SystemOptionContainerList.GetSystemOption(ExecutionContext.SiteId, "Hosted Payment keys", "WPCyberSource secret key");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayPasswordSecretKey))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in system options. Site : &2", new string[] { "WPCyberSource secret key", ExecutionContext.GetSiteId().ToString() }));
            }

            // REST CONFIGS
            this.WebPaymentGatewayConfiguration.MerchantId = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.MerchantId))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_MERCHANT_ID", ExecutionContext.GetSiteId().ToString() }));
            }
            this.WebPaymentGatewayConfiguration.GatewayAPIURL = ParafaitDefaultContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_API_URL");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayAPIURL))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }
            this.WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey = ParafaitDefaultContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_SECRET_KEY");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_SECRET_KEY", ExecutionContext.GetSiteId().ToString() }));
            }
            this.WebPaymentGatewayConfiguration.GatewayAPIPasswordSecretKey = ParafaitDefaultContainerList.GetParafaitDefault(ExecutionContext, "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY");
            if (string.IsNullOrWhiteSpace(this.WebPaymentGatewayConfiguration.GatewayAPIPasswordSecretKey))
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 value in configuration. Site : &2", new string[] { "HOSTED_PAYMENT_GATEWAY_PUBLIC_KEY", ExecutionContext.GetSiteId().ToString() }));
            }

            this.WebPaymentGatewayConfiguration.IsPinCodeValidationRequired = ParafaitDefaultViewContainerList.GetParafaitDefault(ExecutionContext, "ENABLE_ADDRESS_VALIDATION") == "Y" ? true : false;

            configParameters.Clear();
            LoadConfigParams();

            log.Debug("Building lookups");
            LookupsContainerDTO lookupValuesList = LookupsViewContainerList.GetLookupsContainerDTO(ExecutionContext.GetSiteId(), "WEB_PAYMENT_CONFIGURATION");
            if (lookupValuesList == null)
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup. Site : &2", new string[] { "WEB_PAYMENT_CONFIGURATION", ExecutionContext.GetSiteId().ToString() }));
                log.Error(errMsg.ToString());
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, errMsg.ToString()));
            }
            List<LookupValuesContainerDTO> lookupValuesDTOlist = lookupValuesList.LookupValuesContainerDTOList;
            if (lookupValuesDTOlist == null || !lookupValuesDTOlist.Any())
            {
                log.Error("WEB_PAYMENT_CONFIGURATION lookup not found.");
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, "WEB_PAYMENT_CONFIGURATION lookup not found."));
            }
            log.Debug("Built lookups");

            String apiSite = "";
            String webSite = "";
            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").Count() > 0)
            {
                apiSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_API").First().Description;
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "ANGULAR_PAYMENT_API", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").Count() > 0)
            {
                webSite = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB").First().Description;
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "ANGULAR_PAYMENT_WEB", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").Count() > 0)
            {
                String linkPage = lookupValuesDTOlist.Where(x => x.LookupValue == "ANGULAR_PAYMENT_WEB_PAGE_LINK").First().Description;
                linkPage = linkPage.Replace("@gateway", paymentGatewayName);
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink = webSite + linkPage;
            }
            else
            {
                this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink = webSite + "/payment/wpcybersourcehostedpayment?payload=@payLoad&siteId=@siteId&posMachine=@posMachine";
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.SuccessURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentGatewayName);
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "SUCCESS_RESPONSE_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.FailedURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentGatewayName);
                //log.Debug("failureResponseAPIURL " + failureResponseAPIURL);
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "FAILURE_RESPONSE_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.CancelURL = apiSite + lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_RESPONSE_API_URL").First().Description.Replace("@gateway", paymentGatewayName);
                //log.Debug("cancelResponseAPIURL " + cancelResponseAPIURL);
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "CANCEL_RESPONSE_API_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.PaymentSucceededURL = lookupValuesDTOlist.Where(x => x.LookupValue == "SUCCESS_REDIRECT_URL").First().Description.Replace("@gateway", paymentGatewayName);
                //log.Debug("PaymentSucceededURL " + callbackResponseAPIURL);
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "SUCCESS_REDIRECT_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.PaymentFailedURL = lookupValuesDTOlist.Where(x => x.LookupValue == "FAILURE_REDIRECT_URL").First().Description.Replace("@gateway", paymentGatewayName);
                //log.Debug("callbackResponseAPIURL " + callbackResponseAPIURL);
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "FAILURE_REDIRECT_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            if (lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").Count() > 0)
            {
                this.WebPaymentGatewayConfiguration.PaymentCancelledURL = lookupValuesDTOlist.Where(x => x.LookupValue == "CANCEL_REDIRECT_URL").First().Description.Replace("@gateway", paymentGatewayName);
                //log.Debug("callbackResponseAPIURL " + callbackResponseAPIURL);
            }
            else
            {
                errMsg.AppendLine(MessageViewContainerList.GetMessage(ExecutionContext, "Please enter &1 lookup value in WEB_PAYMENT_CONFIGURATION. Site : &2", new string[] { "CANCEL_REDIRECT_URL", ExecutionContext.GetSiteId().ToString() }));
            }

            String error = errMsg.ToString();
            if (!string.IsNullOrWhiteSpace(error))
            {
                log.Error(errMsg);
                throw new PaymentGatewayConfigurationException(MessageViewContainerList.GetMessage(ExecutionContext, error));
            }
            log.LogMethodExit(this.WebPaymentGatewayConfiguration);
        }

        public override HostedPaymentRequestDTO CreateGatewayPaymentRequest(CreateHostedPaymentRequestDTO createHostedPaymentRequestDTO)
        {
            log.LogMethodEntry(createHostedPaymentRequestDTO);
            HostedPaymentRequestDTO HostedPaymentRequestDTO = GetHostedPaymentRequestDTO(createHostedPaymentRequestDTO, this.paymentGatewayName);
            try
            {
                HostedPaymentRequestDTO.CurrencyCode = createHostedPaymentRequestDTO.CurrencyCode;
                if (String.IsNullOrWhiteSpace(HostedPaymentRequestDTO.CurrencyCode))
                    HostedPaymentRequestDTO.CurrencyCode = "GBP";

                WPCyberSourceCommandHandler commandHandler = new WPCyberSourceCommandHandler(log);
                string signed_date_time = commandHandler.getUTCDateTime();
                string merchant_defined_data1_siteId = Convert.ToString(HostedPaymentRequestDTO.SiteId);
                string merchant_defined_data2_paymentModeId = Convert.ToString(HostedPaymentRequestDTO.PaymentModeId);

                string signed_field_names = "access_key,profile_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,payer_authentication_transaction_mode,payer_authentication_challenge_code,partner_solution_id,merchant_defined_data1,merchant_defined_data2";
                string signature = commandHandler.getSignature(TX_TYPE, HostedPaymentRequestDTO.TransactionPaymentGuid, signed_field_names, signed_date_time,
                    HostedPaymentRequestDTO.TransactionPaymentGuid, HostedPaymentRequestDTO.Amount, HostedPaymentRequestDTO.CurrencyCode, 
                    WebPaymentGatewayConfiguration.IsPinCodeValidationRequired.ToString(),
                    PA_TX_MODE, PA_CHALLENGE_CODE, WebPaymentGatewayConfiguration.MerchantKey,
                    WebPaymentGatewayConfiguration.GatewayUserNamePubKey, LOCALE, WebPaymentGatewayConfiguration.GatewayPasswordSecretKey,
                    WebPaymentGatewayConfiguration.PartnerId, merchant_defined_data1_siteId, merchant_defined_data2_paymentModeId);

                HostedPaymentRequestDTO.GatewayRequestHTML = commandHandler.prepareForm(signed_field_names, HostedPaymentRequestDTO.TransactionPaymentGuid, signed_date_time,
                    LOCALE, WebPaymentGatewayConfiguration.IsPinCodeValidationRequired.ToString(),
                    TX_TYPE, HostedPaymentRequestDTO.TransactionPaymentGuid,
                    HostedPaymentRequestDTO.Amount, HostedPaymentRequestDTO.CurrencyCode, signature, PA_TX_MODE,
                    PA_CHALLENGE_CODE, WebPaymentGatewayConfiguration.MerchantKey, 
                    WebPaymentGatewayConfiguration.GatewayUserNamePubKey, WebPaymentGatewayConfiguration.WebsitePaymentPageURL, 
                    WebPaymentGatewayConfiguration.PartnerId, merchant_defined_data1_siteId, merchant_defined_data2_paymentModeId);

                HostedPaymentRequestDTO.WebsitePaymentPageURL = this.WebPaymentGatewayConfiguration.WebsitePaymentPageURL;
                HostedPaymentRequestDTO.WebsitePaymentPageLink = this.WebPaymentGatewayConfiguration.WebsitePaymentPageLink
                                                                .Replace("@payLoad", Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(HostedPaymentRequestDTO.GatewayRequestHTML)))
                                                                .Replace("@siteId", HostedPaymentRequestDTO.SiteId.ToString())
                                                                .Replace("@posMachine", HostedPaymentRequestDTO.POSMachine.ToString());
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

            log.LogMethodExit(HostedPaymentRequestDTO);
            return HostedPaymentRequestDTO;
        }

        private WorldPayResponseDTO GetResposeObj(string gatewayResponse)
        {
            //log.LogMethodEntry(gatewayResponse);
            WorldPayResponseDTO responseObj = null;
            //utf8=%E2%9C%93&auth_cv_result=2&req_locale=en-gb&req_payer_authentication_indicator=01&req_card_type_selection_indicator=1&payer_authentication_enroll_veres_enrolled=U&req_bill_to_surname=Pai&req_card_expiry_date=12-2023&card_type_name=Visa&auth_amount=15.00&auth_response=0&bill_trans_ref_no=155728&req_payment_method=card&req_payer_authentication_merchant_name=Test+Company&auth_time=2023-01-16T125802Z&payer_authentication_enroll_e_commerce_indicator=internet&transaction_id=6738738818486230204002&req_card_type=001&payer_authentication_transaction_id=mntJCHI2jJQJTY2PRUH0&req_payer_authentication_transaction_mode=S&req_merchant_defined_data1=155728&req_merchant_defined_data2=636&auth_avs_code=U&auth_code=15&payer_authentication_specification_version=2.1.0&req_bill_to_address_country=IN&auth_cv_result_raw=3&req_profile_id=8F564CA8-C495-40DC-8DE6-6B478F5073EF&req_partner_solution_id=RVA8YQK8&signed_date_time=2023-01-16T12%3A58%3A02Z&req_bill_to_address_line1=mangalore&req_card_number=xxxxxxxxxxxx3705&signature=AO10gF6Friey8H3a5pcmpkzvIYHAJ%2BAI4l39oFnvxNk%3D&req_bill_to_address_city=Mangalore&req_bill_to_address_postal_code=575001&reason_code=100&req_bill_to_forename=Nitin&req_payer_authentication_acs_window_size=03&request_token=Axj%2F%2FwSTbU7JlAt3Rj5iABIMxatW7JwojlScBjsBURypOAx3nQu7heRDJpJl6MWZ7cQYOJNtTsmUC3dGPmIAzw0a&req_amount=15&req_bill_to_email=nitin.pai%40semnox.com&payer_authentication_reason_code=100&auth_avs_code_raw=00&req_payer_authentication_challenge_code=04&req_currency=GBP&decision=ACCEPT&message=Request+was+processed+successfully.&signed_field_names=transaction_id%2Cdecision%2Creq_access_key%2Creq_profile_id%2Creq_transaction_uuid%2Creq_transaction_type%2Creq_reference_number%2Creq_amount%2Creq_currency%2Creq_locale%2Creq_payment_method%2Creq_bill_to_forename%2Creq_bill_to_surname%2Creq_bill_to_email%2Creq_bill_to_address_line1%2Creq_bill_to_address_city%2Creq_bill_to_address_country%2Creq_bill_to_address_postal_code%2Creq_card_number%2Creq_card_type%2Creq_card_type_selection_indicator%2Creq_card_expiry_date%2Ccard_type_name%2Creq_merchant_defined_data1%2Creq_merchant_defined_data2%2Creq_partner_solution_id%2Creq_payer_authentication_acs_window_size%2Creq_payer_authentication_indicator%2Creq_payer_authentication_challenge_code%2Creq_payer_authentication_transaction_mode%2Creq_payer_authentication_merchant_name%2Cmessage%2Creason_code%2Cauth_avs_code%2Cauth_avs_code_raw%2Cauth_response%2Cauth_amount%2Cauth_code%2Cauth_cv_result%2Cauth_cv_result_raw%2Cauth_time%2Crequest_token%2Cbill_trans_ref_no%2Cpayer_authentication_reason_code%2Cpayer_authentication_enroll_e_commerce_indicator%2Cpayer_authentication_specification_version%2Cpayer_authentication_transaction_id%2Cpayer_authentication_enroll_veres_enrolled%2Csigned_field_names%2Csigned_date_time&req_transaction_uuid=7c904494-2e0f-43b1-9ec2-34dfd8bfb3f1&req_transaction_type=sale&req_access_key=248d9649918d38c2b1deaeca0253c7e1&req_reference_number=155728

            string[] responseArray = gatewayResponse.Split('&');
            StringBuilder jsonString = new StringBuilder();
            jsonString.Append("{");
            for (int i = 0; i < responseArray.Length; i++)
            {
                if (i > 0)
                    jsonString.Append(",");

                string[] keyValue = responseArray[i].Split('=');
                jsonString.Append("\"");
                jsonString.Append(keyValue[0]);
                jsonString.Append("\":\"");

                string temp = keyValue[1];
                //log.Debug("Before " + keyValue[0] + " : " + temp);
                temp = Uri.UnescapeDataString(temp);
                if (keyValue[0].Equals("payer_authentication_proof_xml"))
                {
                    log.Debug("Validating payer_authentication_proof_xml");
                    temp = temp.Replace("+", " ");
                    temp = temp.Replace("xhtml xml", "xhtml+xml");
                }
                else if (!keyValue[0].Equals("signature") && !keyValue[0].Equals("request_token"))
                {
                    temp = temp.Replace("+", " "); // Limitation of Uri.UnescapeDataString. Has to be done separately.
                }
                //log.Debug("After " + keyValue[0] + " : " + temp);
                jsonString.Append(temp);
                jsonString.Append("\"");
            }
            jsonString.Append("}");
            //log.Debug("Converted JSON " + jsonString.ToString());
            responseObj = JsonConvert.DeserializeObject<WorldPayResponseDTO>(jsonString.ToString());
            log.LogMethodExit(responseObj);
            return responseObj;
        }

        public override HostedPaymentResponseDTO ProcessGatewayResponse(String paymentGatewayResponse)
        {
            log.LogMethodEntry(paymentGatewayResponse);
            HostedPaymentResponseDTO HostedPaymentResponseDTO = GetHostedPaymentResponseDTO();
            PaymentTransactionDTO PaymentTransactionDTO = new PaymentTransactionDTO();
            bool isPaymentSuccess = false;

            //Dictionary<string, string> response = new Dictionary<string, string>();
            WPCyberSourceCommandHandler commandHandler = new WPCyberSourceCommandHandler(log);
            try
            {
                loadResponseText();
                // proceed with processing
                WorldPayResponseDTO responseObj = GetResposeObj(paymentGatewayResponse);
                if (responseObj != null)
                {
                    //log.Debug(responseObj); // using this method to print the full object
                    HostedPaymentResponseDTO.TransactionPaymentGuid = responseObj.req_reference_number;
                    log.Debug("Payment Identifier:" + HostedPaymentResponseDTO.TransactionPaymentGuid); 
                    
                    // verify signature
                    bool result = commandHandler.verifySignature(responseObj, WebPaymentGatewayConfiguration.GatewayPasswordSecretKey);
                    if (!result)
                    {
                        log.Info("Payment signature verification failed!");
                        throw new PaymentGatewayProcessingException("Payment signature verification failed!");
                    }
                    if (Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.SUCCESS)
                    {
                        String reasonCode = String.IsNullOrEmpty(responseObj.reason_code) ? "" : responseObj.reason_code;
                        log.Info("Reason code:" + reasonCode);
                        PaymentTransactionDTO.DSIXReturnCode = reasonCode;
                        PaymentTransactionDTO.AuthCode = responseObj.auth_code;
                        PaymentTransactionDTO.Authorize = responseObj.auth_amount;
                        PaymentTransactionDTO.Purchase = responseObj.req_amount;
                        Decimal approvedAmount = 0.0M;
                        Decimal.TryParse(responseObj.auth_amount, out approvedAmount);
                        PaymentTransactionDTO.Amount = approvedAmount;
                        PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                        PaymentTransactionDTO.RefNo = responseObj.transaction_id; // paymentId
                        PaymentTransactionDTO.RecordNo = responseObj.req_reference_number; // payment guid
                        PaymentTransactionDTO.TextResponse = "SUCCESS " + responseObj.message;
                        PaymentTransactionDTO.NameOnCreditCard = responseObj.req_bill_to_forename +" " + responseObj.req_bill_to_surname;
                        PaymentTransactionDTO.AcctNo = responseObj.req_card_number;
                        PaymentTransactionDTO.CardType = responseObj.req_card_type;
                        PaymentTransactionDTO.CreditCardExpiry = responseObj.req_card_expiry_date;
                        //PaymentTransactionDTO.AcqRefData += "|Name:" + customerName + "|EXP:" + ccexpiry + "|ZIP:" /*+ pin*/;
                        //PaymentTransactionDTO.ProcessData = profileid;
                        PaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                        PaymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                    }
                    else
                    {
                        log.Debug("Failed Reason Code is " + responseObj.reason_code);
                        // something wrong with the Tx
                        if (Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.PARTIAL_AMOUNT_APPROVED || 
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.DUPLICATE_TRANSACTION || 
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.GENERAL_SYSTEM_FAILURE || 
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.SERVER_TIMEOUT || 
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.SERVICE_TIMEOUT || 
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.PROCESSOR_FAILURE || 
                            Convert.ToInt32(responseObj.reason_code) == (int)TxResponse.ISSUING_BANK_UNAVAILABLE)
                        {
                            log.Info("in list of handled Reason code:" + responseObj.reason_code.ToString());
                            // check Tx details; If captured, then VOID
                            // build Tx Search requestDTO
                            TxSearchRequestDTO searchRequestDTO = new TxSearchRequestDTO
                            {
                                query = "clientReferenceInformation.code:" + responseObj.req_reference_number,
                                sort = "id:desc",
                            };
                            TxSearchResponseDTO txSearchResponse = commandHandler.CreateTxSearch(searchRequestDTO, configParameters);
                            log.Info("txSearchResponse:" + txSearchResponse.ToString());
                            TxStatusDTO txStatus = commandHandler.GetTxStatusFromSearchResponse(txSearchResponse);
                            log.Info("txStatus:" + txStatus.ToString());

                            // if any Tx has been applied at PG; then first update cCTransactionsPGWDTO
                            if (txStatus.TxType == "SALE")
                            {
                                log.Debug("Partial payment has been approved");
                                log.Info("Reason code:" + txStatus.reasonCode.ToString());
                                PaymentTransactionDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
                                PaymentTransactionDTO.AuthCode = txStatus.AuthCode;
                                PaymentTransactionDTO.Authorize = txStatus.Authorize;
                                PaymentTransactionDTO.Purchase = txStatus.Purchase;
                                Decimal approvedAmount = 0.0M;
                                Decimal.TryParse(txStatus.Authorize, out approvedAmount);
                                PaymentTransactionDTO.Amount = approvedAmount;
                                PaymentTransactionDTO.TransactionDatetime = txStatus.TransactionDatetime;
                                PaymentTransactionDTO.RefNo = txStatus.paymentId; // paymentId
                                PaymentTransactionDTO.RecordNo = txStatus.RefNo; // payment guid
                                PaymentTransactionDTO.TextResponse = "SUCCESS " + txStatus.TextResponse;

                                //PaymentTransactionDTO.NameOnCreditCard = customerName;
                                PaymentTransactionDTO.AcctNo = !String.IsNullOrEmpty(txStatus.AcctNo) ? txStatus.AcctNo : String.Empty;
                                //PaymentTransactionDTO.CustomerCardProfileId = profileid;
                                //PaymentTransactionDTO.CreditCardExpiry = resultJson["expiry"];
                                //PaymentTransactionDTO.AcqRefData += "|Name:" + customerName + "|EXP:" + ccexpiry + "|ZIP:" /*+ pin*/;
                                //PaymentTransactionDTO.ProcessData = profileid;

                                PaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.SALE.ToString();
                                PaymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();
                            }
                            else if (txStatus.TxType == "NA")
                            {
                                log.Debug("No Payment has been found");
                                PaymentTransactionDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
                                PaymentTransactionDTO.AuthCode = txStatus.AuthCode;
                                PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                                PaymentTransactionDTO.RefNo = txStatus.RefNo; // paymentId
                                PaymentTransactionDTO.RecordNo = txStatus.RecordNo; // payment guid
                                PaymentTransactionDTO.TextResponse = "FAILED " + txStatus.TextResponse;
                                PaymentTransactionDTO.AcctNo = string.Empty;
                                PaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                                PaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                            }
                        }
                        else
                        {
                            log.Debug("In list of unhandled reason codes");
                            PaymentTransactionDTO.DSIXReturnCode = String.IsNullOrEmpty(responseObj.reason_code) ? "" : responseObj.reason_code;
                            PaymentTransactionDTO.AuthCode = responseObj.auth_code;
                            PaymentTransactionDTO.Authorize = "0";
                            PaymentTransactionDTO.Purchase = "0";
                            PaymentTransactionDTO.Amount = 0.0M;
                            PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                            PaymentTransactionDTO.RefNo = responseObj.transaction_id; // paymentId
                            PaymentTransactionDTO.RecordNo = responseObj.req_reference_number; // payment guid
                            PaymentTransactionDTO.TextResponse = "FAILED " + responseObj.message;
                            PaymentTransactionDTO.AcctNo = string.Empty;
                            PaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                            PaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                        }
                    }
                }
                else
                {
                    log.Debug("responseObj is null");
                    PaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                    HostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                    PaymentTransactionDTO.Purchase = "0";
                    PaymentTransactionDTO.Authorize = "0";
                    PaymentTransactionDTO.Amount = 0.0M;
                    PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                    PaymentTransactionDTO.DSIXReturnCode = "Invalid response received.";
                    PaymentTransactionDTO.TextResponse = "Invalid response received.";
                }
            }
            catch (PaymentGatewayProcessingException ex)
            {
                log.Debug("Returning validation exception " + ex);
                PaymentTransactionDTO.RecordNo = "C";
                PaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                HostedPaymentResponseDTO.PaymentTransactionStatus = PaymentTransactionStatuses.ERROR;
                PaymentTransactionDTO.Purchase = "0";
                PaymentTransactionDTO.Authorize = "0";
                PaymentTransactionDTO.Amount = 0.0M;
                PaymentTransactionDTO.TransactionDatetime = ServerDateTime.Now;
                PaymentTransactionDTO.DSIXReturnCode = ex.Message;
                PaymentTransactionDTO.TextResponse = ex.Message;
            }
            catch (Exception ex)
            {
                log.Error("Exception in process gateway response", ex);
                throw;
            }

            HostedPaymentResponseDTO.PaymentTransactionDTO = PaymentTransactionDTO;
            log.LogMethodExit(HostedPaymentResponseDTO);
            return HostedPaymentResponseDTO;
        }

        public override string GetPaymentIdentifier(String paymentGatewayResponse)
        {
            log.LogMethodEntry(paymentGatewayResponse);
            String paymentGatewayIdentifier = string.Empty;

            try
            {
                WorldPayResponseDTO responseObj = GetResposeObj(paymentGatewayResponse);
                log.Debug(responseObj.req_reference_number + ":" + responseObj.transaction_id);
                paymentGatewayIdentifier = responseObj.req_reference_number;
            }
            catch (Exception ex)
            {
                log.Error("Error while getting trxId and captchToken" + ex);
            }

            log.LogMethodExit(paymentGatewayIdentifier);
            return paymentGatewayIdentifier;
        }

        private PaymentTransactionDTO BuildPaymentTransactionResponseDTO(TransactionPaymentDTO transactionPaymentDTO)
        {
            log.LogMethodEntry(transactionPaymentDTO);
            PaymentTransactionDTO paymentTransactionDTO = new PaymentTransactionDTO(-1, transactionPaymentDTO.TransactionId, transactionPaymentDTO.Guid, string.Empty,
                string.Empty, string.Empty,
                -1, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty,
                DateTime.Now, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, string.Empty, null, null, null, transactionPaymentDTO.Guid, true, PaymentTransactionStatuses.SUCCESS.ToString(), "", "", "", transactionPaymentDTO.Amount,-1);
            log.LogMethodExit(paymentTransactionDTO);
            return paymentTransactionDTO;
        }

        /// <summary>
        /// Reverts the payment.
        /// </summary>
        /// <param name="transactionPaymentsDTO"></param>
        /// <returns></returns>
        public virtual async Task<PaymentTransactionDTO> RefundAmount(TransactionPaymentDTO refundTransactionPaymentsDTO, PaymentTransactionDTO originalPaymentTransactionDTO, IProgress<PaymentProgressReport> progress, System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(refundTransactionPaymentsDTO);

            PaymentTransactionDTO refundPaymentTransactionDTO = new PaymentTransactionDTO();
            string refundTrxId = refundPaymentTransactionDTO.Guid;

            try
            {
                DateTime originalPaymentDate = originalPaymentTransactionDTO.TransactionDatetime;

                // Define Business Start and End Time
                DateTime bussStartTime = ServerDateTime.Now.Date.AddHours(Convert.ToInt32(ParafaitDefaultContainerList.GetParafaitDefault(ExecutionContext, "BUSINESS_DAY_START_TIME")));
                DateTime bussEndTime = bussStartTime.AddDays(1);
                if (ServerDateTime.Now < bussStartTime)
                {
                    bussStartTime = bussStartTime.AddDays(-1);
                    bussEndTime = bussStartTime.AddDays(1);
                }

                // Decide Void vs Refund basis the Date
                if ((originalPaymentDate >= bussStartTime) && (originalPaymentDate <= bussEndTime))
                {
                    // same day: VOID
                    log.Info("Same day: Void");
                    WPCyberSourceCommandHandler worldpayCommandHandler = new WPCyberSourceCommandHandler(log);
                    WorldPayRequestDTO worldPayRequestDTO = worldpayCommandHandler.getRequestDTO(originalPaymentTransactionDTO.RefNo);
                    log.Debug("getRequestDTO- worldPayRequestDTO: " + worldPayRequestDTO);
                    VoidRequestDTO voidRequestDTO = null;
                    voidRequestDTO = new VoidRequestDTO
                    {
                        clientReferenceInformation = new Clientreferenceinformation
                        {
                            code = refundTrxId, // ccRequestId
                        },
                    };
                    VoidResponseDTO voidResponseDTO;
                    voidResponseDTO = worldpayCommandHandler.CreateVoid(worldPayRequestDTO, voidRequestDTO, configParameters);
                    log.Debug("voidResponseDTO: " + voidResponseDTO);

                    if (voidResponseDTO != null && voidResponseDTO.status == "VOIDED")
                    {
                        log.Debug("Void Success");
                        refundPaymentTransactionDTO.ResponseOrigin = originalPaymentTransactionDTO.ResponseID.ToString();
                        refundPaymentTransactionDTO.DSIXReturnCode = voidResponseDTO.status;
                        //PaymentTransactionDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        refundPaymentTransactionDTO.AuthCode = voidResponseDTO.status;
                        refundPaymentTransactionDTO.Authorize = voidResponseDTO.voidAmountDetails.voidAmount;
                        refundPaymentTransactionDTO.Purchase = originalPaymentTransactionDTO.Authorize;
                        refundPaymentTransactionDTO.TransactionDatetime = voidResponseDTO.submitTimeUtc;
                        refundPaymentTransactionDTO.RefNo = voidResponseDTO.id; // paymentId
                        refundPaymentTransactionDTO.RecordNo = voidResponseDTO.clientReferenceInformation.code; // payment guid
                        refundPaymentTransactionDTO.TextResponse = "VOID SUCCESS " + voidResponseDTO.status;

                        refundPaymentTransactionDTO.AcctNo = originalPaymentTransactionDTO.AcctNo;
                        refundPaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.VOID.ToString();
                        refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();

                    }
                    else
                    {
                        log.Debug("Void Failed " + (voidResponseDTO != null ? voidResponseDTO.status : "void object not found"));
                        refundPaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.VOID.ToString();
                        refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                    }
                }
                else
                {
                    // Next Day: Refund
                    log.Info("Next Day: Refund");
                    WPCyberSourceCommandHandler worldpayCommandHandler = new WPCyberSourceCommandHandler(log);
                    WorldPayRequestDTO worldPayRequestDTO = worldpayCommandHandler.getRequestDTO(originalPaymentTransactionDTO.RefNo);
                    log.Debug("getRequestDTO- worldPayRequestDTO: " + worldPayRequestDTO);
                    RefundResponseDTO refundResponseDTO = null;
                    RefundRequestDTO refundRequestDTO = null;
                    refundRequestDTO = new RefundRequestDTO
                    {
                        clientReferenceInformation = new Clientreferenceinformation
                        {
                            code = refundTrxId, // ccRequestId
                        },
                        orderInformation = new Orderinformation
                        {
                            amountDetails = new Amountdetails
                            {
                                // get the amount to be refunded from the refundTransactionPaymentsDTO
                                totalAmount = Convert.ToString(refundTransactionPaymentsDTO.Amount),
                                //currency = CURRENCY_CODE,
                            }
                        },
                    };
                    refundResponseDTO = worldpayCommandHandler.CreateRefund(worldPayRequestDTO, refundRequestDTO, configParameters);
                    log.Debug("refundResponseDTO: " + refundResponseDTO);

                    if (refundResponseDTO != null && refundResponseDTO.status == "PENDING")
                    {
                        log.Debug("Refund Success");
                        refundPaymentTransactionDTO.ResponseOrigin = originalPaymentTransactionDTO.ResponseID.ToString();
                        refundPaymentTransactionDTO.DSIXReturnCode = refundResponseDTO.status;
                        //PaymentTransactionDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
                        refundPaymentTransactionDTO.AuthCode = refundResponseDTO.status;
                        refundPaymentTransactionDTO.Authorize = refundResponseDTO.refundAmountDetails.refundAmount;
                        refundPaymentTransactionDTO.Purchase = originalPaymentTransactionDTO.Authorize;
                        refundPaymentTransactionDTO.TransactionDatetime = refundResponseDTO.submitTimeUtc;
                        refundPaymentTransactionDTO.RefNo = refundResponseDTO.id; // paymentId
                        refundPaymentTransactionDTO.RecordNo = refundResponseDTO.clientReferenceInformation.code; // payment guid
                        refundPaymentTransactionDTO.TextResponse = "REFUND SUCCESS " + refundResponseDTO.status;

                        refundPaymentTransactionDTO.AcctNo = originalPaymentTransactionDTO.AcctNo;
                        refundPaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.SUCCESS.ToString();

                    }
                    else
                    {
                        log.Debug("Refund Failed " + (refundResponseDTO != null ? refundResponseDTO.status : "refund object not found"));
                        refundPaymentTransactionDTO.TranCode = PaymentGatewayTransactionType.REFUND.ToString();
                        refundPaymentTransactionDTO.Status = PaymentTransactionStatuses.FAILED.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw;
            }

            log.LogMethodExit(refundPaymentTransactionDTO);
            return refundPaymentTransactionDTO;
        }


        public virtual async Task<PaymentTransactionDTO> ReversePayment(TransactionPaymentDTO reversedTransactionPaymentDTO, IProgress<PaymentProgressReport> progress,
                                                           System.Threading.CancellationToken cancellationToken)
        {
            log.LogMethodEntry(reversedTransactionPaymentDTO);
            PaymentTransactionDTO originalPaymentTransactionDTO = BuildPaymentTransactionResponseDTO(reversedTransactionPaymentDTO);
            originalPaymentTransactionDTO.Status = PaymentTransactionStatuses.ERROR.ToString();
            originalPaymentTransactionDTO.TextResponse = MessageViewContainerList.GetMessage(ExecutionContext, "Payment Reverse is not supported.");
            log.LogMethodExit(originalPaymentTransactionDTO);
            return originalPaymentTransactionDTO;
        }

        public override HostedPaymentResponseDTO GetPaymentStatus(string transactionPaymentGuid)
        {
            log.LogMethodEntry(transactionPaymentGuid);
            HostedPaymentResponseDTO HostedPaymentResponseDTO = new HostedPaymentResponseDTO();

            //Dictionary<string, Object> dict = new Dictionary<string, Object>();
            //dynamic resData;
            //try
            //{
            //    WPCyberSourceCommandHandler commandHandler = new WPCyberSourceCommandHandler(log);
            //    if (string.IsNullOrWhiteSpace(transactionPaymentGuid))
            //    {
            //        log.Error("No Transaction id passed");
            //        throw new Exception("No Transaction id passed");
            //    }

            //    // build Tx Search requestDTO
            //    TxSearchRequestDTO searchRequestDTO = commandHandler.GetTxSearchRequestDTO(transactionPaymentGuid);
            //    log.Debug("GetTxSearchRequestDTO- searchRequestDTO: " + searchRequestDTO);
            //    TxSearchResponseDTO txSearchResponseDTO = commandHandler.CreateTxSearch(searchRequestDTO, configParameters);
            //    log.Debug("CreateTxSearch- txSearchResponseDTO: " + txSearchResponseDTO);

            //    if (txSearchResponseDTO != null && txSearchResponseDTO.totalCount > 0)
            //    {
            //        log.Info("Total count of txSearchResponse: " + txSearchResponseDTO.totalCount.ToString());
            //        TxStatusDTO txStatus = commandHandler.GetTxStatusFromSearchResponse(txSearchResponseDTO);
            //        log.Debug("GetTxStatusFromSearchResponse- txStatus: " + txStatus);
            //        if (txStatus.reasonCode != -2 && txStatus.reasonCode != -1)
            //        {
            //            //Tx found
            //            // Tx is either Sale/VOID/REFUND
            //            log.Info("Tx Status reasonCode: " + txStatus.reasonCode.ToString());

            //            // check if sale/void/refund Tx present
            //            // if yes then proceed
            //            if (txStatus.TxType == "SALE")
            //            {
            //                CCRequestPGWListBL cCRequestPGWListBL = new CCRequestPGWListBL();
            //                List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>> searchParametersPGW = new List<KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>>();
            //                searchParametersPGW.Add(new KeyValuePair<CCRequestPGWDTO.SearchByParameters, string>(CCRequestPGWDTO.SearchByParameters.INVOICE_NUMBER, trxId));
            //                CCRequestPGWDTO cCRequestsPGWDTO = cCRequestPGWListBL.GetLastestCCRequestPGWDTO(searchParametersPGW);

            //                if (txStatus.reasonCode == 100)
            //                {
            //                    log.Info("CC Transactions found with reasonCode 100");
            //                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
            //                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
            //                    cCTransactionsPGWDTO.AuthCode = txStatus.AuthCode;
            //                    cCTransactionsPGWDTO.Authorize = txStatus.Authorize;
            //                    cCTransactionsPGWDTO.Purchase = txStatus.Purchase;
            //                    cCTransactionsPGWDTO.TransactionDatetime = txStatus.TransactionDatetime;
            //                    cCTransactionsPGWDTO.RefNo = txStatus.RecordNo; //paymentId
            //                    cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
            //                    cCTransactionsPGWDTO.AcctNo = txStatus.AcctNo;

            //                    cCTransactionsPGWDTO.TextResponse = txStatus.TextResponse;
            //                    cCTransactionsPGWDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
            //                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

            //                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
            //                    ccTransactionsPGWBL.Save();

            //                    dict.Add("status", "1");
            //                    dict.Add("message", "success");
            //                    dict.Add("retref", txStatus.paymentId);
            //                    dict.Add("amount", txStatus.Authorize);

            //                    dict.Add("orderId", trxId);
            //                    dict.Add("acctNo", txStatus.AcctNo);
            //                }
            //                else
            //                {
            //                    log.Info("CC Transactions found with reasonCode other than 100");
            //                    CCTransactionsPGWDTO cCTransactionsPGWDTO = new CCTransactionsPGWDTO();
            //                    cCTransactionsPGWDTO.InvoiceNo = cCRequestsPGWDTO.RequestID.ToString();
            //                    cCTransactionsPGWDTO.Authorize = !String.IsNullOrEmpty(txStatus.Authorize) ? txStatus.Authorize : String.Empty;
            //                    cCTransactionsPGWDTO.Purchase = txStatus.Purchase;
            //                    cCTransactionsPGWDTO.TransactionDatetime = utilities.getServerTime();
            //                    cCTransactionsPGWDTO.RefNo = txStatus.RecordNo; //paymentId
            //                    cCTransactionsPGWDTO.RecordNo = trxId; //parafait TrxId
            //                    cCTransactionsPGWDTO.AcctNo = !String.IsNullOrEmpty(txStatus.AcctNo) ? txStatus.AcctNo : String.Empty;
            //                    cCTransactionsPGWDTO.TextResponse = txStatus.TextResponse;
            //                    cCTransactionsPGWDTO.DSIXReturnCode = txStatus.reasonCode.ToString();
            //                    cCTransactionsPGWDTO.TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();

            //                    CCTransactionsPGWBL ccTransactionsPGWBL = new CCTransactionsPGWBL(cCTransactionsPGWDTO, utilities.ExecutionContext);
            //                    ccTransactionsPGWBL.Save();

            //                    dict.Add("status", "0");
            //                    dict.Add("message", "Transaction found with reasonCode other than 100");
            //                    dict.Add("retref", txStatus.paymentId);
            //                    dict.Add("amount", txStatus.Authorize);
            //                    dict.Add("orderId", trxId);
            //                    dict.Add("acctNo", txStatus.AcctNo);
            //                }

            //                resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
            //                log.LogMethodExit(resData);
            //                return resData;
            //            }
            //        }
            //    }

            //    // cancel the Tx in Parafait DB
            //    dict.Add("status", "0");
            //    dict.Add("message", "no transaction found");
            //    dict.Add("orderId", trxId);
            //    resData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);

            //    log.LogMethodExit(resData);
            //    return resData;
            //}
            //catch (Exception ex)
            //{
            //    log.Error(ex.Message);
            //    throw new Exception(ex.Message);
            //}

            log.LogMethodExit(HostedPaymentResponseDTO);
            return HostedPaymentResponseDTO;
        }

        private void LoadConfigParams()
        {
            try
            {
                if(!configParameters.ContainsKey("SCHEME"))
                    configParameters.Add("SCHEME", SCHEME);

                if (!configParameters.ContainsKey("REST_SECRET_KEY"))
                    configParameters.Add("REST_SECRET_KEY", WebPaymentGatewayConfiguration.GatewayAPIUserNamePubKey);

                if (!configParameters.ContainsKey("PUBLIC_KEY"))
                    configParameters.Add("PUBLIC_KEY", WebPaymentGatewayConfiguration.GatewayAPIPasswordSecretKey);

                if (!configParameters.ContainsKey("ALGORITHM"))
                    configParameters.Add("ALGORITHM", ALGORITHM);

                if (!configParameters.ContainsKey("MERCHANT_ID"))
                    configParameters.Add("MERCHANT_ID", WebPaymentGatewayConfiguration.MerchantKey);

                if (!configParameters.ContainsKey("HOST_URL"))
                    configParameters.Add("HOST_URL", WebPaymentGatewayConfiguration.GatewayAPIURL);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        private void loadResponseText()
        {
            try
            {
                responseTextCollection.Add("100", "Successful transaction.");
                responseTextCollection.Add("102", "One or more fields in the request contain invalid data.");
                responseTextCollection.Add("104", "The access_key and transaction_uuid fields for this authorization request match the access_key and transaction_uuid fields of another authorization request that you sent within the past 15 minutes.");
                responseTextCollection.Add("110", "Only a partial amount was approved.");
                responseTextCollection.Add("150", "General system failure.");
                responseTextCollection.Add("151", "The request was received but a server timeout occurred.");
                responseTextCollection.Add("152", "The request was received, but a service timeout occurred.");
                responseTextCollection.Add("200", "The authorization request was approved by the issuing bank but declined because it did not pass the Address Verification System (AVS) check.");
                responseTextCollection.Add("201", "The issuing bank has questions about the request.");
                responseTextCollection.Add("202", "Expired card.");
                responseTextCollection.Add("203", "General decline of the card.");
                responseTextCollection.Add("204", "Insufficient funds in the account.");
                responseTextCollection.Add("205", "Stolen or lost card.");
                responseTextCollection.Add("207", "Issuing bank unavailable.");
                responseTextCollection.Add("208", "Inactive card or card not authorized for card-not-present transactions.");
                responseTextCollection.Add("210", "The card has reached the credit limit.");
                responseTextCollection.Add("211", "Invalid CVN.");
                responseTextCollection.Add("221", "The customer matched an entry on the processor’s negative file.");
                responseTextCollection.Add("222", "Account frozen.");
                responseTextCollection.Add("230", "The authorization request was approved by the issuing bank but declined because it did not pass the CVN check.");
                responseTextCollection.Add("231", "Invalid account number.");
                responseTextCollection.Add("232", "The card type is not accepted by the payment processor.");
                responseTextCollection.Add("233", "General decline by the processor.");
                responseTextCollection.Add("234", "There is a problem with the information in your account.");
                responseTextCollection.Add("236", "Processor failure.");
                responseTextCollection.Add("240", "The card type sent is invalid or does not correlate with the payment card number.");
                responseTextCollection.Add("475", "The cardholder is enrolled for payer authentication.");
                responseTextCollection.Add("476", "Payer authentication could not be authenticated.");
                responseTextCollection.Add("481", "Transaction declined based on your payment settings for the profile.");
                responseTextCollection.Add("520", "The authorization request was approved by the issuing bank but declined based on your legacy Smart Authorization settings.");
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }

        private string getResponseText(string responseCode)
        {
            string responseText = "";
            try
            {
                if (responseTextCollection.ContainsKey(responseCode))
                {
                    responseTextCollection.TryGetValue(responseCode, out responseText);
                }
                else
                {
                    responseText = "Other";
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            return responseText;
        }
    }
}
