<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns="http://securetransport.dw/rcservice/soap" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xs="http://www.w3.org/2001/XMLSchema" name="rcService" targetNamespace="http://securetransport.dw/rcservice/soap" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xs:schema xmlns:target="http://securetransport.dw/rcservice/soap" attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://securetransport.dw/rcservice/soap">
      <xs:complexType name="ReqClientIDType">
        <xs:sequence>
          <xs:element name="DID" type="xs:string" />
          <xs:element name="App" type="xs:string" />
          <xs:element name="Auth" type="xs:string" />
          <xs:element name="ClientRef" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PayloadType">
        <xs:simpleContent>
          <xs:extension base="xs:string">
            <xs:attribute name="Encoding">
              <xs:simpleType>
                <xs:restriction base="xs:string">
                  <xs:enumeration value="xml_escape" />
                  <xs:enumeration value="cdata" />
                </xs:restriction>
              </xs:simpleType>
            </xs:attribute>
          </xs:extension>
        </xs:simpleContent>
      </xs:complexType>
      <xs:complexType name="RespClientIDType">
        <xs:sequence>
          <xs:element name="DID" type="xs:string" />
          <xs:element name="ClientRef" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="StatusType">
        <xs:simpleContent>
          <xs:extension base="xs:string">
            <xs:attribute name="StatusCode" type="xs:string" use="required" />
          </xs:extension>
        </xs:simpleContent>
      </xs:complexType>
      <xs:simpleType name="ServiceIDType">
        <xs:restriction base="xs:string" />
      </xs:simpleType>
      <xs:simpleType name="SessionContextType">
        <xs:restriction base="xs:string" />
      </xs:simpleType>
      <xs:simpleType name="ReturnCodeType">
        <xs:restriction base="xs:string" />
      </xs:simpleType>
      <xs:complexType name="TransactionType">
        <xs:sequence>
          <xs:element name="ServiceID" type="target:ServiceIDType" />
          <xs:element name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TransactionResponseType">
        <xs:sequence>
          <xs:element name="ReturnCode" type="target:ReturnCodeType" />
          <xs:element minOccurs="0" name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="InitiateSessionType">
        <xs:sequence>
          <xs:element name="ServiceID" type="target:ServiceIDType" />
          <xs:element minOccurs="0" name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="InitiateSessionResponseType">
        <xs:sequence>
          <xs:element name="SessionContext" type="target:SessionContextType" />
          <xs:element name="ReturnCode" type="target:ReturnCodeType" />
          <xs:element minOccurs="0" name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SessionTransactionType">
        <xs:sequence>
          <xs:element name="SessionContext" type="target:SessionContextType" />
          <xs:element name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="SessionTransactionResponseType">
        <xs:sequence>
          <xs:element name="ReturnCode" type="target:ReturnCodeType" />
          <xs:element name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TerminateSessionType">
        <xs:sequence>
          <xs:element name="SessionContext" type="target:SessionContextType" />
          <xs:element minOccurs="0" name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="TerminateSessionResponseType">
        <xs:sequence>
          <xs:element name="ReturnCode" type="target:ReturnCodeType" />
          <xs:element minOccurs="0" name="Payload" type="target:PayloadType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RequestType">
        <xs:sequence>
          <xs:element name="ReqClientID" type="target:ReqClientIDType" />
          <xs:choice>
            <xs:element name="Transaction" type="target:TransactionType" />
            <xs:element name="InitiateSession" type="target:InitiateSessionType" />
            <xs:element name="SessionTransaction" type="target:SessionTransactionType" />
            <xs:element name="TerminateSession" type="target:TerminateSessionType" />
          </xs:choice>
        </xs:sequence>
        <xs:attribute fixed="3" name="Version" type="xs:string" use="required" />
        <xs:attribute name="ClientTimeout">
          <xs:simpleType>
            <xs:restriction base="xs:integer">
              <xs:minExclusive value="0" />
            </xs:restriction>
          </xs:simpleType>
        </xs:attribute>
      </xs:complexType>
      <xs:complexType name="ResponseType">
        <xs:sequence>
          <xs:element name="RespClientID" type="target:RespClientIDType" />
          <xs:element name="Status" type="target:StatusType" />
          <xs:choice>
            <xs:element minOccurs="0" name="TransactionResponse" type="target:TransactionResponseType" />
            <xs:element minOccurs="0" name="InitiateSessionResponse" type="target:InitiateSessionResponseType" />
            <xs:element minOccurs="0" name="SessionTransactionResponse" type="target:SessionTransactionResponseType" />
            <xs:element minOccurs="0" name="TerminateSessionResponse" type="target:TerminateSessionResponseType" />
          </xs:choice>
        </xs:sequence>
        <xs:attribute fixed="3" name="Version" type="xs:string" use="required" />
      </xs:complexType>
      <xs:element name="Transaction" type="target:TransactionType" />
      <xs:element name="TransactionResponse" type="target:TransactionResponseType" />
      <xs:element name="Request" type="target:RequestType" />
      <xs:element name="Response" type="target:ResponseType" />
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="rcRequestMsg">
    <wsdl:part name="body" element="Request" />
  </wsdl:message>
  <wsdl:message name="rcResponseMsg">
    <wsdl:part name="body" element="Response" />
  </wsdl:message>
  <wsdl:portType name="rcPortType">
    <wsdl:operation name="rcTransaction">
      <wsdl:input name="rcRequest" message="rcRequestMsg" />
      <wsdl:output name="rcResponse" message="rcResponseMsg" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="rcSoapBinding" type="rcPortType">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="rcTransaction">
      <soap:operation soapAction="http://securetransport.dw/rcservice" />
      <wsdl:input name="rcRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="rcResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="rcService">
    <wsdl:port name="rcServicePort" binding="rcSoapBinding">
      <soap:address location="https://stg.dw.us.fdcnet.biz/rc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>