﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.Device.PaymentGateway
{
    class AuthorizeQwikCilverResponseDTO
    {
        public string AuthToken { get; set; }
        public int BatchId { get; set; }
        public DateTime DateAtServer { get; set; }
        public string MerchantName { get; set; }
        public string OutletAddress1 { get; set; }
        public string OutletAddress2 { get; set; }
        public string OutletCity { get; set; }
        public string OutletState { get; set; }
        public string OutletPinCode { get; set; }
        public string OutletTelephone { get; set; }
        public bool MaskCardNumber { get; set; }
        public bool PrintMerchantCopy { get; set; }
        public bool InvoiceNumberMandatory { get; set; }
        public bool NumericUserPwd { get; set; }
        public bool IntegerAmounts { get; set; }
        public string CultureName { get; set; }
        public string CurrencySymbol { get; set; }
        public int CurrencyPosition { get; set; }
        public int CurrencyDecimalDigits { get; set; }
        public int DisplayUnitForPoints { get; set; }
        public object ReceiptFooterLine1 { get; set; }
        public string ReceiptFooterLine2 { get; set; }
        public string ReceiptFooterLine3 { get; set; }
        public string ReceiptFooterLine4 { get; set; }
        public int MerchantId { get; set; }
        public bool TransactionStatus { get; set; }
        public long TransactionId { get; set; }
        public string TransactionType { get; set; }
        public string Notes { get; set; }
        public string ApprovalCode { get; set; }
        public int ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorDescription { get; set; }
        public bool Result { get; set; }
    }
}
