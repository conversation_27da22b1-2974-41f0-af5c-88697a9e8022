﻿/********************************************************************************************
 * Project Name - Unified checkout CyberSource Hosted Payment Gateway                                                                     
 * Description  -  Class to handle the payment of CyberSource Hosted Payment Gateway - Callback for Angular
 ********************************************************************************************
 **Version Log
  *Version     Date          Modified By                     Remarks          
 ********************************************************************************************
 *2.152.0     13-March-2024    Yashodhara C H             Created for Website 
 ********************************************************************************************/

using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using Newtonsoft.Json;

namespace Semnox.Parafait.Device.PaymentGateway.HostedPayment.UCCyberSource
{
    public class UCCyberSourceCommandHandler
    {
        private static readonly Semnox.Parafait.logging.Logger log = new Semnox.Parafait.logging.Logger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);
        private string _rest_secret_key, _public_key, _merchant_id, _host_url, _payment_request_url;

        const string SCHEME = "https://";
        const string ALGORITHM = "HmacSHA256";

        public UCCyberSourceCommandHandler( string REST_SECRET_KEY, string PUBLIC_KEY, string MERCHANT_ID,
            string HOST_URL, string PAYMENT_REQUEST_URL)
        {
            log.LogMethodEntry();
            this._rest_secret_key = REST_SECRET_KEY;
            this._public_key = PUBLIC_KEY;
            this._merchant_id = MERCHANT_ID;
            this._host_url = HOST_URL;
            this._payment_request_url = PAYMENT_REQUEST_URL;
            log.LogMethodExit();
        }

        // Create Tx Search when we dont receive timely response of SALE Tx
        public UCCyberSourceRequestDTO CreateCheckout(CaptureContextRequestDTO captureContextRequestDTO)
        {
            log.LogMethodEntry(captureContextRequestDTO);
            UCCyberSourceRequestDTO response = null;
            string HOST = this._host_url;
            string BASE_URL = SCHEME + HOST;
            try
            {
                if (!string.IsNullOrEmpty(BASE_URL))
                {
                    string API_URL = BASE_URL + "/up/v1/capture-contexts";
                    if (captureContextRequestDTO != null)
                    {
                        response = MakeRequest(captureContextRequestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(response);
            return response;
        }

        private UCCyberSourceRequestDTO MakeRequest(CaptureContextRequestDTO captureContextRequestDTO, string API_URL)
        {
            log.LogMethodEntry(captureContextRequestDTO, API_URL);

            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "post /up/v1/capture-contexts";

                string host = this._host_url;
                string MerchantId = this._merchant_id;
                string SecretKey = this._rest_secret_key;
                string KeyId = this._public_key;

                string JsonObj = JsonConvert.SerializeObject(captureContextRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });

                log.Debug("Reached JsonObj:   " + JsonObj);

                //string JsonObj = JsonConvert.SerializeObject(uCCyberSourceRequestDTO);

                var Digest = GenerateDigest(JsonObj);
                var signatureParams = "host: " + host + "\nv-c-date: " + requestDate + "\n(request-target): " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("signatureParams: " + signatureParams);
                var SignatureHash = GenerateSignatureFromParams(signatureParams.ToString(), SecretKey);

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "POST";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + ALGORITHM + "\", headers=\"host v-c-date (request-target) digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.Host = host;
                httpWebRequest.ContentType = "application/json";
                log.Debug("TxSearch httpWebRequest headers: " + httpWebRequest.Headers);

                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }
                UCCyberSourceResponseDTO uCCyberSourceResponseDTO = null;
                try
                {
                    httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                    using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                    {
                        responseFromServer = streamReader.ReadToEnd();
                    }

                    uCCyberSourceResponseDTO = DeserializeServerResponse(responseFromServer);
                }
                catch (WebException webEx)
                {
                    // Log the details of the WebException
                    log.Error(webEx + "WebException during API call: {Message}"+ webEx.Message);

                    // You can also log additional details like HttpStatusCode, if needed
                    HttpWebResponse httpErrorResponse = (HttpWebResponse)webEx.Response;
                    if (httpErrorResponse != null)
                    {
                        using (var streamReader = new StreamReader(httpErrorResponse.GetResponseStream()))
                        {
                            responseFromServer = streamReader.ReadToEnd();
                        }

                        log.Error("Error response: " + responseFromServer);
                        log.Error($"HTTP Status Code: {httpErrorResponse.StatusCode}");
                    }

                    // Handle the error or rethrow the exception as needed
                    throw; // Rethrow the exception or handle it according to your application's logic
                }

                UCCyberSourceRequestDTO uCCyberSourceRequestDTO = new UCCyberSourceRequestDTO
                {
                    captureContext = responseFromServer,
                    clientLibrary = uCCyberSourceResponseDTO.data.clientLibrary,
                    postURL = this._payment_request_url
                };
                log.LogMethodExit(uCCyberSourceRequestDTO);
                return uCCyberSourceRequestDTO;

            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }


        public PaymentStatusType GetPaymentStatus(string resultCode)
        {
            log.LogMethodEntry(resultCode);
            Dictionary<string, PaymentStatusType> resultCodeStatus = new Dictionary<string, PaymentStatusType>
            {
                { "100",PaymentStatusType.SUCCESS },
                {"101",PaymentStatusType.FAILED }
            };

            log.LogMethodExit(resultCodeStatus);
            return resultCodeStatus[resultCode];
        }

        public TxSearchRequestDTO GetTxSearchRequestDTO(string ccRequestId)
        {
            try
            {
                TxSearchRequestDTO searchRequestDTO = new TxSearchRequestDTO
                {
                    query = "clientReferenceInformation.code:" + ccRequestId,
                    sort = "id:desc",
                };
                return searchRequestDTO;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public TxSearchResponseDTO CreateTxSearch(TxSearchRequestDTO searchRequestDTO)
        {
            log.LogMethodEntry(searchRequestDTO);
            TxSearchResponseDTO response = null;
            string HOST = this._host_url;
            string BASE_URL = SCHEME + HOST;
            try
            {
                if (!string.IsNullOrEmpty(BASE_URL))
                {
                    string API_URL = BASE_URL + "/tss/v2/searches";
                    if (searchRequestDTO != null)
                    {
                        response = MakeSearchRequest(searchRequestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(response);
            return response;
        }

        private TxSearchResponseDTO MakeSearchRequest(TxSearchRequestDTO searchRequestDTO, string API_URL)
        {
            log.LogMethodEntry(searchRequestDTO, API_URL);
            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "post /tss/v2/searches";

                string host = this._host_url;
                //string host = GetConfigParams(configParameters, "HOST_PRODUCTION");
                string MerchantId = this._merchant_id;
                string SecretKey = this._rest_secret_key;
                string KeyId = this._public_key;

                string JsonObj = JsonConvert.SerializeObject(searchRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });

                var Digest = GenerateDigest(JsonObj);

                var signatureParams = "host: " + host + "\n(request-target): " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("signatureParams: " + signatureParams);
                var SignatureHash = GenerateSignatureFromParams(signatureParams, SecretKey);

                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "POST";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + ALGORITHM + "\", headers=\"host (request-target) digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("TxSearch httpWebRequest headers: " + httpWebRequest.Headers);

                //var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    responseFromServer = streamReader.ReadToEnd();
                }

                // deserialize the response received
                TxSearchResponseDTO response = deserializeTxSearchResponse(responseFromServer);
                log.Debug("TxSearch responseFromServer:" + responseFromServer);

                log.LogMethodExit(response);
                return response;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public TxStatusDTO GetTxStatusFromSearchResponse(TxSearchResponseDTO response)
        {
            log.LogMethodEntry(response);
            TxStatusDTO txStatusDTO = null;
            if (response != null)
            {
                try
                {
                    if (response.totalCount > 0)
                    {
                        List<Transactionsummary> transactionSummaries = response._embedded.transactionSummaries;
                        var transactions = from transaction in transactionSummaries
                                           from app in transaction.applicationInformation.applications
                                           where app.name == "ics_bill" && app.reasonCode == "100" // second condition added on 09 Nov 2022
                                           select transaction;
                        if (transactions.Count() > 0)
                        {
                            // check for void/refund
                            var voidTx = from transaction_void in transactionSummaries
                                         from app_void in transaction_void.applicationInformation.applications
                                         where app_void.name == "ics_void"
                                         select transaction_void;
                            var refundTx = from transaction_refund in transactionSummaries
                                           from app_refund in transaction_refund.applicationInformation.applications
                                           where app_refund.name == "ics_credit"
                                           select transaction_refund;
                            if (voidTx.Count() > 0)
                            {
                                //void found
                                log.Debug("Void already done");
                                txStatusDTO = new TxStatusDTO
                                {
                                    reasonCode = -2,
                                    status = "TX NOT FOUND",
                                    TxType = "NA"
                                };
                            }
                            else if (refundTx.Count() > 0)
                            {
                                //refund found
                                log.Debug("Refund already done");
                                txStatusDTO = new TxStatusDTO
                                {
                                    reasonCode = -2,
                                    status = "TX NOT FOUND",
                                    TxType = "NA"
                                };
                            }
                            else
                            {
                                // proceed with sale
                                foreach (var tx in transactions)
                                {
                                    if (tx.applicationInformation.reasonCode == 100)
                                    {
                                        txStatusDTO = new TxStatusDTO();
                                        txStatusDTO.reasonCode = Convert.ToInt32(tx.applicationInformation.reasonCode);
                                        txStatusDTO.paymentId = tx.id;
                                        txStatusDTO.InvoiceNo = tx.clientReferenceInformation.code.ToString();
                                        txStatusDTO.AuthCode = tx.processorInformation.approvalCode.ToString();
                                        txStatusDTO.Authorize = tx.orderInformation.amountDetails.totalAmount.ToString();
                                        txStatusDTO.Purchase = txStatusDTO.Authorize;
                                        txStatusDTO.TransactionDatetime = tx.submitTimeUtc;
                                        txStatusDTO.AcctNo = string.Concat("**** **** **** ", tx.paymentInformation.card.suffix.ToString());
                                        txStatusDTO.RecordNo = tx.id;
                                        var app = from application in tx.applicationInformation.applications
                                                  where application.name == "ics_bill"
                                                  select application;
                                        foreach (var application_obj in app)
                                        {
                                            if (application_obj.rMessage != null)
                                            {
                                                txStatusDTO.TextResponse = application_obj.rMessage.ToString();
                                            }
                                        }

                                        txStatusDTO.TxType = "SALE";
                                    }
                                    else
                                    {
                                        // reasoncode other than 100
                                        txStatusDTO = new TxStatusDTO();
                                        txStatusDTO.reasonCode = Convert.ToInt32(tx.applicationInformation.reasonCode);
                                        txStatusDTO.InvoiceNo = tx.clientReferenceInformation.code.ToString();
                                        txStatusDTO.TxType = "SALE";
                                        var app = from application in tx.applicationInformation.applications
                                                  where application.name == "ics_bill"
                                                  select application;
                                        if (app.Count() > 0)
                                        {
                                            foreach (var application_obj in app)
                                            {
                                                if (application_obj.rMessage != null)
                                                {
                                                    txStatusDTO.TextResponse = application_obj.rMessage.ToString();
                                                }
                                            }
                                        }
                                        else
                                        {
                                            txStatusDTO.TextResponse = String.Empty;

                                        }

                                    }
                                }
                            }
                        }
                        else
                        {
                            txStatusDTO = new TxStatusDTO
                            {
                                reasonCode = -2,
                                status = "TX NOT FOUND",
                                TxType = "NA"
                            };
                        }
                    }
                    else
                    {
                        txStatusDTO = new TxStatusDTO
                        {
                            reasonCode = -2,
                            status = "TX NOT FOUND",
                            TxType = "NA"
                        };
                    }
                }
                catch (Exception ex)
                {
                    log.Error(ex.Message);
                    throw new Exception(ex.Message);
                }

            }
            else
            {
                txStatusDTO = new TxStatusDTO
                {
                    reasonCode = -2,
                    status = "TX NOT FOUND",
                    TxType = "NA"
                };
            }
            log.LogMethodExit(txStatusDTO);
            return txStatusDTO;
        }

        public VoidResponseDTO CreateVoid(UCCyberSourceRefundRequestDTO requestDTO, VoidRequestDTO voidRequestDTO)
        {
            log.LogMethodEntry(requestDTO, voidRequestDTO);
            VoidResponseDTO response = null;
            string HOST = this._host_url;
            string BASE_URL = SCHEME + HOST;
            try
            {
                if (!string.IsNullOrEmpty(BASE_URL))
                {
                    string API_URL = BASE_URL + "/pts/v2/payments/" + requestDTO.paymentId + "/voids";
                    if (requestDTO != null)
                    {
                        response = MakeVoidRequest(requestDTO, voidRequestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(response);
            return response;
        }

        private VoidResponseDTO MakeVoidRequest(UCCyberSourceRefundRequestDTO requestDTO, VoidRequestDTO voidRequestDTO, string API_URL)
        {
            log.LogMethodEntry(requestDTO, voidRequestDTO, API_URL);
            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "post /pts/v2/payments/" + requestDTO.paymentId + "/voids";

                string host = this._host_url;
                //string host = GetConfigParams(configParameters, "HOST_PRODUCTION");
                string MerchantId = this._merchant_id;
                string SecretKey = this._rest_secret_key;
                string KeyId = this._public_key;

                string JsonObj = JsonConvert.SerializeObject(voidRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });

                var Digest = GenerateDigest(JsonObj);

                var SignatureParm = "host: " + host + "\n(request-target): " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("SignatureParm: " + SignatureParm);
                var SignatureHash = GenerateSignatureFromParams(SignatureParm, SecretKey);

                //var httpWebRequest = (HttpWebRequest)WebRequest.Create("https://apitest.cybersource.com/pts/v2/payments/6584950941846702404006/refunds");
                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "POST";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + ALGORITHM + "\", headers=\"host (request-target) digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("Void httpWebRequest headers: " + httpWebRequest.Headers);

                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    responseFromServer = streamReader.ReadToEnd();
                }

                // deserialize the response received
                VoidResponseDTO response = deserializeVoidResponse(responseFromServer);
                log.Info(responseFromServer);

                log.LogMethodEntry(response);
                return response;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        private VoidResponseDTO deserializeVoidResponse(string response)
        {
            log.LogMethodEntry(response);
            try
            {
                log.LogMethodExit(response);
                return JsonConvert.DeserializeObject<VoidResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }
        public UCCyberSourceRefundRequestDTO getRequestDTO(string paymentId)
        {
            try
            {
                UCCyberSourceRefundRequestDTO requestDTO = null;
                requestDTO = new UCCyberSourceRefundRequestDTO
                {
                    paymentId = paymentId,
                };

                return requestDTO;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public RefundResponseDTO CreateRefund(UCCyberSourceRefundRequestDTO requestDTO, RefundRequestDTO refundRequestDTO)
        {
            log.LogMethodEntry(requestDTO, refundRequestDTO);
            RefundResponseDTO response = null;
            string HOST = this._host_url;
            string BASE_URL = SCHEME + HOST;
            try
            {
                if (!string.IsNullOrEmpty(BASE_URL))
                {
                    string API_URL = BASE_URL + "/pts/v2/payments/" + requestDTO.paymentId + "/refunds";
                    log.Info(API_URL);
                    if (requestDTO != null)
                    {
                        response = MakeRefundRequest(requestDTO, refundRequestDTO, API_URL);
                    }
                    else
                    {
                        throw new Exception("Request was null");
                    }
                }
                else
                {
                    throw new Exception("Base Url was null");
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(response);
            return response;
        }

        private RefundResponseDTO MakeRefundRequest(UCCyberSourceRefundRequestDTO requestDTO, RefundRequestDTO refundRequestDTO, string API_URL)
        {
            log.LogMethodEntry(requestDTO, refundRequestDTO, API_URL);
            try
            {
                string requestDate = getFormattedRequestDate();
                string requestTarget = "post /pts/v2/payments/" + requestDTO.paymentId + "/refunds";

                string host = this._host_url;
                string MerchantId = this._merchant_id;
                string SecretKey = this._rest_secret_key;
                string KeyId = this._public_key;
                //SecretKey = "Ga+dQUuctbdudg7Pzw8yvzh6yttlwF6TjKfbCj8sQ5c=";
                //string KeyId = "3d249232-bfe6-46ff-87dc-332ed320f9a9";
                //string MerchantId = "cwpdsemnoxtest001";
                //string requestTarget = "post /pts/v2/payments/6584950941846702404006/refunds";

                //string JsonObj = "{\"clientReferenceInformation\":{\"code\":\"1658494933318\"},\"orderInformation\":{\"amountDetails\":{\"totalAmount\":\"10\",\"currency\":\"GBP\"}}}";
                string JsonObj = JsonConvert.SerializeObject(refundRequestDTO, Formatting.Indented, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore });

                var Digest = GenerateDigest(JsonObj);

                var SignatureParm = "host: " + host + "\n(request-target): " + requestTarget + "\ndigest: " + Digest + "\nv-c-merchant-id: " + MerchantId;
                log.Debug("SignatureParm: " + SignatureParm);
                var SignatureHash = GenerateSignatureFromParams(SignatureParm, SecretKey);

                //var httpWebRequest = (HttpWebRequest)WebRequest.Create("https://apitest.cybersource.com/pts/v2/payments/6584950941846702404006/refunds");
                var httpWebRequest = (HttpWebRequest)WebRequest.Create(API_URL);
                httpWebRequest.Method = "POST";

                httpWebRequest.Headers.Add("v-c-merchant-id", MerchantId);
                httpWebRequest.Headers.Add("v-c-date", requestDate);
                httpWebRequest.Headers.Add("Digest", Digest);
                httpWebRequest.Headers.Add("Signature", "keyid=\"" + KeyId + "\", algorithm=\"" + ALGORITHM + "\", headers=\"host (request-target) digest v-c-merchant-id\", signature=\"" + SignatureHash + "\"");
                httpWebRequest.ContentType = "application/json";
                log.Debug("Refund httpWebRequest headers: " + httpWebRequest.Headers);

                //var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                HttpWebResponse httpResponse;
                string responseFromServer = "";
                using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    streamWriter.Write(JsonObj);
                    streamWriter.Flush();
                    streamWriter.Close();
                }

                httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();

                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    responseFromServer = streamReader.ReadToEnd();
                }

                // deserialize the response received
                RefundResponseDTO response = deserializeRefundResponse(responseFromServer);
                log.LogMethodExit(response);
                return response;
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        private RefundResponseDTO deserializeRefundResponse(string response)
        {
            try
            {
                return JsonConvert.DeserializeObject<RefundResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }

        private TxSearchResponseDTO deserializeTxSearchResponse(string response)
        {
            try
            {
                return JsonConvert.DeserializeObject<TxSearchResponseDTO>(response);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }
        private UCCyberSourceResponseDTO DeserializeServerResponse(dynamic responseFromServer)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var responseObj = tokenHandler.ReadJwtToken(responseFromServer) as JwtSecurityToken;

                log.Debug("UCCyberSource Response:" + responseObj);
                var clientLibrary = responseObj.Claims.FirstOrDefault(x => x.Value.Contains("clientLibrary")).Value;
                return JsonConvert.DeserializeObject<UCCyberSourceResponseDTO>(clientLibrary);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
        }

        public static string GenerateDigest(string jsonRestBody)
        {
            log.LogMethodEntry();
            var digest = "";
            var bodyText = jsonRestBody;
            try
            {
                using (var sha256hash = SHA256.Create())
                {
                    byte[] payloadBytes = sha256hash
                        .ComputeHash(Encoding.UTF8.GetBytes(bodyText));
                    digest = Convert.ToBase64String(payloadBytes);
                    digest = "SHA-256=" + digest;
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            log.LogMethodExit(digest);
            return digest;
        }

        private static string GenerateSignatureFromParams(string signatureParams, string secretKey)
        {
            log.LogMethodEntry();
            try
            {
                byte[] sigBytes = Encoding.UTF8.GetBytes(signatureParams);
                byte[] decodedSecret = Convert.FromBase64String(secretKey);
                HMACSHA256 hmacSha256 = new HMACSHA256(decodedSecret);
                byte[] messageHash = hmacSha256.ComputeHash(sigBytes);
                log.LogMethodExit(Convert.ToBase64String(messageHash));
                return Convert.ToBase64String(messageHash);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }

        }

        private string GetConfigParams(Dictionary<string, string> configParameters, string key)
        {
            string configvalue = "";
            try
            {
                if (configParameters.ContainsKey(key))
                {
                    configParameters.TryGetValue(key, out configvalue);
                }
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
                throw new Exception(ex.Message);
            }
            return configvalue;
        }

        private string getFormattedRequestDate()
        {
            log.LogMethodEntry();
            log.LogMethodExit(DateTime.Now.ToUniversalTime().ToString("r"));
            return DateTime.Now.ToUniversalTime().ToString("r");
        }
    }
}
