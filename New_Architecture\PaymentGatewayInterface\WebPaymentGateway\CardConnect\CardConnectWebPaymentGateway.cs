﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Security;
using System.IO;
using System.Net;
using System.Threading;
using Newtonsoft.Json.Linq;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    public class CardConnectWebPaymentGateway : PaymentGateway, IPaymentGateway
    {
        private readonly Semnox.Parafait.logging.Logger log;

        public override bool IsRefundSupported
        {
            get
            {
                return true;
            }
        }
        public override bool IsStatusCheckSupported
        {
            get
            {
                return true;
            }
        }
        public override bool CanCreateMultipleInstances
        {
            get
            {
                return true;
            }
        }

        public override bool RedirectResponseToWebsite
        {
            get
            {
                return false;
            }
        }

        private string merchantId;
        private string username;
        private string password;
        private string apiPostUrl;
        private string successURL;
        private string callbackURL;
        private string currencyCode;
        private string paymentModeId;
        private string isCaptchEnabled;
        private string googleRecaptchaSecretKey;
        private string googleRecaptchaClientId;
        private string googleRecaptchaURL;
        private string addressValidationEnabled;
        private string isPaayEnabled;
        private string showPAAYChallenge;
        private string threedsPaayUrl;
        private string threedsPaayApiKey;
        private string RSATokenizePublicKey;
        private string RSADataPublicKey;
        private string RSADataPrivateKey;
        private string siteId;
        private int paymentTrxWindowTime;
        protected dynamic resultJson;

        private static readonly Dictionary<string, string> paayResponseCodes = new Dictionary<string, string>
        {
            { "01", "Attempted; Liability is still shifted. Network stepped in for the authentication (Mastercard)" },
            { "02", "Authenticated (Mastercard) Liability is still shifted." },
            { "00", "Not Authenticated (Mastercard)" },
            { "05", "Authenticated(Visa/Amex/Discover) Liability is still shifted." },
            { "06", "Attempted; Liability is still shifted. Network stepped in for the authentication (Visa/Amex/Discover)" },
            { "07", "Not Authenticated (Visa/Amex/Discover)" }
        };

        public CardConnectWebPaymentGateway(PaymentConfiguration paymentConfiguration, PaymentMessages paymentMessages, Semnox.Parafait.logging.Logger logger) : base(paymentConfiguration, paymentMessages)
        {
            log = logger;
            log.LogMethodEntry();

            merchantId = paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID");
            username = paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_USER_NAME");
            password = paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_PASSWORD");
            //Check if the URL ends with '/' if not add '/' at the end
            apiPostUrl = (paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_BASE_URL") ?? "").Trim();
            apiPostUrl = apiPostUrl.EndsWith("/") ? apiPostUrl : apiPostUrl + "/";

            successURL = paymentConfiguration.GetConfiguration("SUCCESS_RESPONSE_API_URL");
            callbackURL = paymentConfiguration.GetConfiguration("CALLBACK_RESPONSE_API_URL");
            currencyCode = paymentConfiguration.GetConfiguration("CURRENCY_CODE");
            paymentModeId = paymentConfiguration.GetConfiguration("PAYMENT_MODE_ID");
            siteId = paymentConfiguration.GetConfiguration("SITE_ID");

            string trxWindowTime = paymentConfiguration.GetConfiguration("HOSTED_PAYMENT_TRX_WINDOW_TIME");
            if (!int.TryParse(trxWindowTime, out paymentTrxWindowTime))
            {
                paymentTrxWindowTime = 15;
            }

            isCaptchEnabled = paymentConfiguration.GetConfiguration("ENABLE_GOOGLE_RECAPTCHA");
            googleRecaptchaSecretKey = paymentConfiguration.GetConfiguration("GOOGLE_RECAPTCHA_SECRET_KEY");
            googleRecaptchaClientId = paymentConfiguration.GetConfiguration("GOOGLE_RECAPTCHA_CLIENT_ID") ?? "";
            googleRecaptchaURL = paymentConfiguration.GetConfiguration("GOOGLE_RECAPTCHA_URL") ?? "";
            addressValidationEnabled = paymentConfiguration.GetConfiguration("ENABLE_ADDRESS_VALIDATION");

            isPaayEnabled = paymentConfiguration.GetConfiguration("IS_PAAY_ENABLED");
            showPAAYChallenge = paymentConfiguration.GetConfiguration("SHOW_PAAY_CHALLENGE");
            threedsPaayUrl = paymentConfiguration.GetConfiguration("THREEDS_PAAY_URL");
            threedsPaayApiKey = paymentConfiguration.GetConfiguration("THREEDS_PAAY_API_KEY");

            RSATokenizePublicKey = paymentConfiguration.GetConfiguration("RSA_TOKENIZE_PUBLIC_KEY");
            RSADataPublicKey = paymentConfiguration.GetConfiguration("RSA_DATA_PUBLIC_KEY");
            RSADataPrivateKey = paymentConfiguration.GetConfiguration("RSA_DATA_PRIVATE_KEY");

            log.Debug($"Payment mode id: {paymentModeId}");
            log.Debug($"CardConnect API URL: {apiPostUrl}");
            log.Debug($"Currency Code: {currencyCode}");
            log.Debug($"Transaction window time: {paymentTrxWindowTime}");
            log.Debug($"Callback URL: {callbackURL}");
            log.Debug($"Success URL: {successURL}");
            log.Debug($"Captcha Enabled: {isCaptchEnabled}");
            log.Debug($"ReCaptcha URL: {googleRecaptchaURL}");
            log.Debug($"Pin Code Validation Enabled: {addressValidationEnabled}");
            log.Debug($"PAAY Enabled: {isPaayEnabled}");
            log.Debug($"Threeds Paay URL: {threedsPaayUrl}");

            log.LogMethodExit(null);
        }

        public override void ValidateConfiguration()
        {
            log.LogMethodEntry("START - Configuration Validation");

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID")))
            {
                log.Error("CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CARD_CONNECT_HOSTED_PAYMENT_MERCHANT_ID" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_USER_NAME")))
            {
                log.Error("CARD_CONNECT_HOSTED_PAYMENT_USER_NAME is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CARD_CONNECT_HOSTED_PAYMENT_USER_NAME" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_PASSWORD")))
            {
                log.Error("CARD_CONNECT_HOSTED_PAYMENT_PASSWORD is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CARD_CONNECT_HOSTED_PAYMENT_PASSWORD" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CARD_CONNECT_HOSTED_PAYMENT_BASE_URL")))
            {
                log.Error("CARD_CONNECT_HOSTED_PAYMENT_BASE_URL is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CARD_CONNECT_HOSTED_PAYMENT_BASE_URL" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("SUCCESS_RESPONSE_API_URL")))
            {
                log.Error("SUCCESS_RESPONSE_API_URL is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "SUCCESS_RESPONSE_API_URL" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CALLBACK_RESPONSE_API_URL")))
            {
                log.Error("CALLBACK_RESPONSE_API_URL is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CALLBACK_RESPONSE_API_URL" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("CURRENCY_CODE")))
            {
                log.Error("CURRENCY_CODE is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "CURRENCY_CODE" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ENABLE_GOOGLE_RECAPTCHA")))
            {
                log.Error("ENABLE_GOOGLE_RECAPTCHA is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ENABLE_GOOGLE_RECAPTCHA" + " SiteId: " + siteId);
            }

            if (paymentConfiguration.GetConfiguration("ENABLE_GOOGLE_RECAPTCHA")?.ToUpper() == "Y")
            {
                if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("GOOGLE_RECAPTCHA_SECRET_KEY")))
                {
                    log.Error("GOOGLE_RECAPTCHA_SECRET_KEY is not set.");
                    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "GOOGLE_RECAPTCHA_SECRET_KEY" + " SiteId: " + siteId);
                }
                if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("GOOGLE_RECAPTCHA_CLIENT_ID")))
                {
                    log.Error("GOOGLE_RECAPTCHA_CLIENT_ID is not set.");
                    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "GOOGLE_RECAPTCHA_CLIENT_ID" + " SiteId: " + siteId);
                }
                if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("GOOGLE_RECAPTCHA_URL")))
                {
                    log.Error("GOOGLE_RECAPTCHA_URL is not set.");
                    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "GOOGLE_RECAPTCHA_URL" + " SiteId: " + siteId);
                }
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("ENABLE_ADDRESS_VALIDATION")))
            {
                log.Error("ENABLE_ADDRESS_VALIDATION is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "ENABLE_ADDRESS_VALIDATION" + " SiteId: " + siteId);
            }

            if (paymentConfiguration.GetConfiguration("IS_PAAY_ENABLED")?.ToUpper() == "Y")
            {
                if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("SHOW_PAAY_CHALLENGE")))
                {
                    log.Error("SHOW_PAAY_CHALLENGE is not set.");
                    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "SHOW_PAAY_CHALLENGE");
                }
                if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("THREEDS_PAAY_URL")))
                {
                    log.Error("THREEDS_PAAY_URL is not set.");
                    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "THREEDS_PAAY_URL" + " SiteId: " + siteId);
                }
                if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("THREEDS_PAAY_API_KEY")))
                {
                    log.Error("THREEDS_PAAY_API_KEY is not set.");
                    throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "THREEDS_PAAY_API_KEY" + " SiteId: " + siteId);
                }
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("RSA_TOKENIZE_PUBLIC_KEY")))
            {
                log.Error("RSA_TOKENIZE_PUBLIC_KEY is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "RSA_TOKENIZE_PUBLIC_KEY" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("RSA_DATA_PUBLIC_KEY")))
            {
                log.Error("RSA_DATA_PUBLIC_KEY is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "RSA_DATA_PUBLIC_KEY" + " SiteId: " + siteId);
            }

            if (string.IsNullOrWhiteSpace(paymentConfiguration.GetConfiguration("RSA_DATA_PRIVATE_KEY")))
            {
                log.Error("RSA_DATA_PRIVATE_KEY is not set.");
                throw new ConfigurationNotSetException(paymentMessages.GetMessage(5873) + "RSA_DATA_PRIVATE_KEY" + " SiteId: " + siteId);
            }

            log.LogMethodExit("END - Completed configuration validation");
        }

        public override async Task<PaymentSessionDTO> CreatePaymentSessionDTO(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);

            PaymentSessionDTO paymentSessionDTO = new PaymentSessionDTO
            {
                RequestId = paymentRequestDTO.RequestIdentifier,
                IntRequestId = paymentRequestDTO.IntRequestIdentifier,
                PaymentGatewayName = PaymentGateways.CardConnectCallbackHostedPayment.ToString()
            };

            log.Debug("Creating MountedPaymentSessionJSON");
            paymentSessionDTO.MountedPaymentSessionJSON = JsonConvert.SerializeObject(SetPostParameters(paymentRequestDTO));
            log.Debug("Completed MountedPaymentSessionJSON creation");

            log.LogMethodExit(paymentSessionDTO);
            return await Task.FromResult(paymentSessionDTO);
        }

        private IDictionary<string, string> SetPostParameters(PaymentRequestDTO paymentRequestDTO)
        {
            log.LogMethodEntry(paymentRequestDTO);
            IDictionary<string, string> postParamsList = new Dictionary<string, string>();

            postParamsList.Add("merchantId", "@merchantId");
            postParamsList.Add("currencyCode", this.currencyCode);
            postParamsList.Add("amount", String.Format("{0:0.00}", paymentRequestDTO.Amount));

            postParamsList.Add("transactionId", Convert.ToBase64String(
                                                Encoding.UTF8.GetBytes(paymentRequestDTO.RequestIdentifier + ":" + paymentRequestDTO.RequestDate.Ticks.ToString())));
            postParamsList.Add("paymentModeId", paymentModeId);
            postParamsList.Add("requestId", paymentRequestDTO.RequestIdentifier);
            postParamsList.Add("api_post_url", this.apiPostUrl);

            if (paymentRequestDTO.SubscriptionAuthorizationMode == "N")
            {
                postParamsList.Add("capture", "Y");
                postParamsList.Add("ecomind", "Y");
                postParamsList.Add("cof", "C");
                postParamsList.Add("cofscheduled", "N");
            }
            else if (paymentRequestDTO.SubscriptionAuthorizationMode == "I")
            {
                postParamsList.Add("capture", "Y");
                postParamsList.Add("ecomind", "R");
                postParamsList.Add("profile", "Y");
                postParamsList.Add("cof", "M");
                if (paymentRequestDTO.Amount != 0)
                {
                    postParamsList.Add("cofscheduled", "Y");
                }
                else
                {
                    postParamsList.Add("cofscheduled", "N");
                }
            }
            else if (paymentRequestDTO.SubscriptionAuthorizationMode == "P")
            {
                postParamsList.Add("capture", "Y");
                postParamsList.Add("ecomind", "R");
                postParamsList.Add("profile", (paymentRequestDTO.PaymentGatewayCustomerDTO != null ?
                                                    paymentRequestDTO.PaymentGatewayCustomerDTO.CustomerIdentifier: ""));
                postParamsList.Add("cof", "M");
                if (paymentRequestDTO.Amount != 0)
                {
                    postParamsList.Add("cofscheduled", "Y");
                }
                else
                {
                    postParamsList.Add("cofscheduled", "N");
                }
            }
            postParamsList.Add("postalValidation", addressValidationEnabled);
            postParamsList.Add("requestGuid", paymentRequestDTO.RequestIdentifier);
            postParamsList.Add("isRecaptchaEnabled", isCaptchEnabled);
            postParamsList.Add("clientID", googleRecaptchaClientId);
            postParamsList.Add("CallbackURL", callbackURL);
            postParamsList.Add("ResponseURL", successURL);

            postParamsList.Add("orderid", paymentRequestDTO.RequestIdentifier);
            postParamsList.Add("isPaayEnabled", isPaayEnabled);
            postParamsList.Add("threedsPaayUrl", threedsPaayUrl);
            postParamsList.Add("threedsPaayApiKey", threedsPaayApiKey);
            postParamsList.Add("tokenizePublicKey", RSATokenizePublicKey);
            postParamsList.Add("dataPublicKey", RSADataPublicKey);
            postParamsList.Add("showPAAYChallenge", showPAAYChallenge);
            postParamsList.Add("userEmailId", !string.IsNullOrWhiteSpace(paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerEmail) ? paymentRequestDTO.PaymentGatewayCustomerDTO.CustomerEmail : "");
            postParamsList.Add("userPhoneNumber", !string.IsNullOrWhiteSpace(paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerPhone) ? paymentRequestDTO.PaymentGatewayCustomerDTO?.CustomerPhone : "");

            log.LogMethodExit(postParamsList);
            return postParamsList;
        }

        public async override Task<string> GetPaymentIdentifier(string paymentResponse)
        {
            log.LogMethodEntry(paymentResponse);
            String paymentGatewayIdentifier = string.Empty;

            if (string.IsNullOrWhiteSpace(paymentResponse))
            {
                log.Error($"Payment failed. Payment response is null or whitespace");
                throw new PaymentResponseNullException(paymentMessages.GetMessage(5984));
            }

            try
            {
                string decryptedResponse = DecryptGatewayResponse(paymentResponse);
                dynamic response = JsonConvert.DeserializeObject(decryptedResponse);

                log.Debug("Extracting PaymentIdentifier from gateway response.");
                string fields = response.userfields;
                dynamic userfields = JsonConvert.DeserializeObject(fields);

                // Decode and extract the transaction ID
                String trxIdString = userfields.transactionId;
                trxIdString = Encoding.UTF8.GetString(Convert.FromBase64String(trxIdString));

                // Extract the identifier portion before the colon
                if (!String.IsNullOrWhiteSpace(trxIdString))
                {
                    paymentGatewayIdentifier = trxIdString.Substring(0, trxIdString.IndexOf(":"));
                }
                log.Debug("Extracted PaymentIdentifier from gateway response: " + paymentGatewayIdentifier);
            }
            catch (Exception ex)
            {
                log.Error("Payemnt response: " + paymentResponse);
                log.Error("Error while extracting PaymentIdentifier.", ex);
                throw new ResponseParsingFailedException(paymentMessages.GetMessage(6087), ex);
            }

            log.LogMethodExit(paymentGatewayIdentifier);

            return await Task.FromResult<string>(paymentGatewayIdentifier);
        }

        public override async Task<PaymentResponseDTO> ProcessPaymentResponse(PaymentGatewayResponseDTO paymentGatewayResponseDTO)
        {
            log.LogMethodEntry(paymentGatewayResponseDTO);
            string gatewayResponse = paymentGatewayResponseDTO.GatewayResponse;

            if (string.IsNullOrWhiteSpace(gatewayResponse))
            {
                log.Error($"Payment failed: Payment response is null or whitespace.");
                throw new PaymentResponseNullException(paymentMessages.GetMessage(5984)); // PaymentResponse is Null
            }

            string invoiceNo = "";
            string recordNo = "";
            string refNo = "";
            string authCode = "";
            decimal amount = 0;
            string authorize = "";
            string tranCode = "";
            string status = "";
            string textResponse = "";
            string dSIXReturnCode = "";
            string acctNo = "";
            string creditCardName = "";
            string creditCardExpiry = "";
            string cardType = "";
            string purchase = "";
            string profileid = "";
            string acctid = "";
            DateTime transactionDateTime = paymentGatewayResponseDTO.SiteDateTime;

            string captureStatus = "";
            string acqRefData = "";
            string merchantCopy = "";
            string userTraceData = "";
            string secureflag = "";
            string securedstid = "";
            string securevalue = "";

            try
            {
                gatewayResponse = DecryptGatewayResponse(gatewayResponse);
                log.Debug("Gateway response: " + gatewayResponse);
                gatewayResponse = gatewayResponse.Replace("@merchantId", this.merchantId);

                dynamic response = JsonConvert.DeserializeObject(gatewayResponse);

                String tempAccount = response.account;
                String tempToken = response.token;
                if (tempAccount.Length < 16 || !tempAccount.StartsWith("9") || !tempAccount.Equals(tempToken))
                {
                    log.Error("The response contains invalid information " + response);
                    log.Error("Invalid Card number" + tempAccount);
                    throw new InvalidCardDetailsException(paymentMessages.GetMessage(5428)); //"Invalid card number"
                }
                else
                {
                    log.Debug("Card number validated.");
                }

                string paymentIdentifier = "";
                string captchToken = "";
                string paayMessage = "";
                Int64 ticks = 0;
                try
                {
                    log.Debug("Extracting payment identifier and captcha token.");
                    string fields = response.userfields;
                    dynamic userfields = JsonConvert.DeserializeObject(fields);
                    captchToken = userfields.captchToken;
                    String trxIdString = userfields.transactionId;

                    paayMessage = userfields?.paayMessage?.ToString() ?? "";

                    trxIdString = Encoding.UTF8.GetString(Convert.FromBase64String(trxIdString));
                    if (!String.IsNullOrWhiteSpace(trxIdString))
                    {
                        paymentIdentifier = trxIdString.Substring(0, trxIdString.IndexOf(":"));
                        ticks = Convert.ToInt64(trxIdString.Substring(trxIdString.IndexOf(":") + 1));
                    }
                }
                catch (Exception ex)
                {
                    log.Error("Error while extracting trxId and captchToken" + ex);
                    log.Error("Gateway response: " + JsonConvert.SerializeObject(response));
                    throw new PaymentResponseProcessingException(paymentMessages.GetMessage(6181), ex); //Error while extracting trxId and captchToken from gateway response
                }

                log.Debug($"Extracted PaymentIdentifier: {paymentIdentifier}, Ticks: {ticks}");

                Int64 TrxWindow = Convert.ToInt64(paymentGatewayResponseDTO.SiteDateTime.Subtract(new TimeSpan(0, this.paymentTrxWindowTime, 0)).Ticks);
                if (ticks < TrxWindow)
                {
                    log.Error($"Payment timeout:Transaction timestamp is older than the allowed transaction window. SiteDateTime: {paymentGatewayResponseDTO.SiteDateTime}, TrxWindow limit (in minutes): {this.paymentTrxWindowTime}");
                    throw new PaymentTimeoutException(paymentMessages.GetMessage(6172)); //Payment rejected due to timeout. Payment timestamp is older than the allowed time window
                }
                else
                {
                    log.Debug("Payment time is within the trx window limit");
                }

                if (this.isCaptchEnabled == "Y")
                {
                    log.Debug("Captcha is enabled. Validating captcha token...");
                    if (!ValidateReCaptchaToken(captchToken))
                    {
                        log.Error($"Captcha validation failed. Token: {(string.IsNullOrWhiteSpace(captchToken) ? "NULL/EMPTY" : captchToken)}");
                        throw new CaptchaValidationException(paymentMessages.GetMessage(6173)); //Captcha validation failed
                    }
                    log.Debug("Captcha token validated successfully.");
                }
                else
                {
                    log.Debug("Captcha is disabled");
                }


                //Add OrderId key to auth request payload with paymentIdentifier value
                response.orderid = paymentIdentifier;
                gatewayResponse = JsonConvert.SerializeObject(response);

                //api call for card connect authorization 
                string url = this.apiPostUrl + "cardconnect/rest/auth";
                log.Debug("Initiating authorization request to CardConnect auth API: " + url + " with Payload: " + gatewayResponse);
                
                resultJson = ExecuteAPIRequest(url, gatewayResponse, "PUT");
                log.Debug("Received response from CardConnect auth API" + resultJson);

                if (resultJson["retref"] != null)
                {
                    refNo = resultJson["retref"];
                }
                if (resultJson["account"] != null)
                {
                    acctNo = resultJson["account"];
                    cardType = GetCardTypeHelper((acctNo.Length > 4) ? acctNo.Substring(1, 2) : "");
                    acctNo = GetMaskedCardNumber(acctNo);
                }
                if (resultJson["amount"] != null)
                {
                    string amountStr = Convert.ToString(resultJson["amount"]);
                    decimal.TryParse(amountStr, out amount);
                    authorize = amount.ToString();
                }
                if (resultJson["profileid"] != null)
                {
                    profileid = resultJson["profileid"];
                }
                if (resultJson["expiry"] != null)
                {
                    creditCardExpiry = resultJson["expiry"];
                }
                if (resultJson["acctid"] != null)
                {
                    acctid = resultJson["acctid"];
                }
                if (resultJson["authcode"] != null)
                {
                    authCode = resultJson["authcode"];
                }

                invoiceNo = response.orderid ?? "";
                tranCode = PaymentGatewayTransactionType.SALE.ToString();
                purchase = response.amount ?? "";
                creditCardName = "";

                dynamic secureValues = resultJson["secureValues"] != null ? resultJson["secureValues"] : null;

                if (secureValues != null)
                {
                    secureflag = secureValues.secureflag;
                    securedstid = secureValues.securedstid;
                    securevalue = secureValues.securevalue;
                }
                
                acqRefData = (!string.IsNullOrEmpty(secureflag) && paayResponseCodes.ContainsKey(secureflag)) ? paayResponseCodes[secureflag] : "";
                captureStatus = securevalue;
                userTraceData = securedstid;
                merchantCopy = paayMessage;

                string paymentResponseStatus = resultJson["respstat"] ?? "";
                string paymentResponseText = resultJson["resptext"] ?? "";
                PaymentTransactionStatuses paymentStatusType = MapPaymentStatus(paymentResponseStatus, PaymentGatewayTransactionType.SALE);

                if (paymentStatusType == PaymentTransactionStatuses.SUCCESS)
                {
                    log.Debug("Payment processed successfully.");
                    recordNo = "A";
                    dSIXReturnCode = "SUCCESS";
                    textResponse = paymentResponseText;
                    status = PaymentTransactionStatuses.SUCCESS.ToString();
                }
                else
                {
                    log.Debug("Payment failed.");
                    recordNo = "C";
                    dSIXReturnCode = "FAILED";
                    textResponse = paymentResponseText;
                    status = PaymentTransactionStatuses.FAILED.ToString();
                }
            }
            catch (Exception ex)
            {
                log.Error("Payment respone: " + gatewayResponse);
                log.Error("Exception in process gateway response", ex);
                throw new PaymentResponseProcessingException(paymentMessages.GetMessage(6148), ex); //Exception in process gateway response
            }

            // Construct PaymentResponseDTO
            PaymentResponseDTO paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, cardType, tranCode, refNo, purchase, authorize, transactionDateTime, authCode, null, null, userTraceData, captureStatus, acqRefData, null, null, merchantCopy, null, status, creditCardName, null, creditCardExpiry, amount);

            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        public async override Task<PaymentResponseDTO> StatusCheck(StatusCheckRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken, string errorMsg)
        {
            log.LogMethodEntry(paymentRequestDTO);
            PaymentResponseDTO paymentResponseDTO;

            string acctNo = "XXXXXXXXXXXXXXXX";
            string invoiceNo = "";
            string status = "";
            decimal amount = 0.00m;
            string recordNo = "";
            string refNo = "";
            string authCode = "";
            string authorize = "";
            string purchase = "";
            string tranCode = "";
            string textResponse = "";
            string dSIXReturnCode = "";
            DateTime transactionDatetime = paymentRequestDTO.RequestDate;
            string responseOrigin = "";
            string acqRefData = "";
            string cardType = "";

            try
            {
                if (string.IsNullOrWhiteSpace(paymentRequestDTO.RequestIdentifier))
                {
                    log.Error("No Transaction id provided");
                    throw new TransactionIdNullException(paymentMessages.GetMessage(5944));
                }

                //Inquiy API call to CardConnect 
                string url = this.apiPostUrl + "cardconnect/rest/inquireByOrderid" + "/" + paymentRequestDTO.RequestIdentifier + "/" + this.merchantId + "/1";
                log.Info("Initiating request to CardConnect status check API: " + url);
                var resultJson = ExecuteAPIRequest(url, "", "GET");

                string strResultJson = JsonConvert.SerializeObject(resultJson);
                log.Debug("Raw response of payment status search: " + strResultJson);

                bool isResponseArrayType = false;
                try
                {
                    log.Info("Identifying the response type...");
                    JToken resultJsonToken = JToken.Parse(strResultJson);
                    log.Debug("Response type: " + resultJsonToken.Type);
                    isResponseArrayType = resultJsonToken.Type == JTokenType.Array;
                }
                catch (Exception ex)
                {
                    log.Error("Defaulting response type to object type. Error: " + ex);
                }

                CardConnectStatusCheckResponseDTO paymentSearchResponseDTO = null;
                if (isResponseArrayType)
                {
                    List<CardConnectStatusCheckResponseDTO> paymentSearchResponseDTOList = JsonConvert.DeserializeObject<List<CardConnectStatusCheckResponseDTO>>(strResultJson);
                    log.Debug("Raw payment status search from cardconnect api: " + paymentSearchResponseDTOList.ToString());
                    // Filter by entrymode == "ECommerce"
                    List<CardConnectStatusCheckResponseDTO> ecommerceTransactions = paymentSearchResponseDTOList
                                                                                .Where(trx => trx.entrymode.ToUpper() == "ECOMMERCE")
                                                                                .ToList();
                    log.Debug("Filtered ECOMMERCE transactions: " + JsonConvert.SerializeObject(ecommerceTransactions));

                    if (!ecommerceTransactions.Any())
                    {
                        log.Error("No ECommerce transactions found in status check response.");
                        throw new PaymentTransactionNotFoundException(paymentMessages.GetMessage(6174)); //No valid ECommerce transactions found in status check response
                    }

                    paymentSearchResponseDTO = paymentSearchResponseDTOList.OrderByDescending(trx => trx.capturedate).First();
                    log.Debug("Latest payment status search from cardconnect api: " + JsonConvert.SerializeObject(paymentSearchResponseDTO));
                }
                else
                {
                    paymentSearchResponseDTO = JsonConvert.DeserializeObject<CardConnectStatusCheckResponseDTO>(strResultJson);
                    log.Debug("Payment status search from cardconnect api: " + paymentSearchResponseDTO.ToString());
                }

                if (paymentSearchResponseDTO == null)
                {
                    log.Error("No payment response data received from status check API.");
                    throw new StatusCheckResponseNullException(paymentMessages.GetMessage(6175)); // No payment response data received from status check API
                }

                string rawPaymentStatus = paymentSearchResponseDTO?.respstat ?? "";
                log.Debug("Raw payment status: " + rawPaymentStatus);
                PaymentTransactionStatuses mappedPaymentStatus = MapPaymentStatus(rawPaymentStatus, PaymentGatewayTransactionType.STATUSCHECK);
                log.Debug("Value of mapped PaymentStatus: " + mappedPaymentStatus.ToString());

                log.Info("Check if payment applied");
                if (paymentSearchResponseDTO.refundable?.ToUpper() == "Y")
                {
                    log.Info("Transaction can be refunded.");
                }
                else
                {
                    log.Debug("Transaction cannot be refunded");
                    mappedPaymentStatus = PaymentTransactionStatuses.FAILED;
                }

               
                if (!string.IsNullOrEmpty(paymentSearchResponseDTO?.account))
                {
                    string rawAccountNumber = paymentSearchResponseDTO.account;
                    cardType = GetCardTypeHelper((rawAccountNumber.Length > 4) ? rawAccountNumber.Substring(1, 2) : "");
                    acctNo = GetMaskedCardNumber(rawAccountNumber);
                }

                invoiceNo = paymentRequestDTO.RequestIdentifier;
                refNo = paymentSearchResponseDTO.retref;
                authCode = paymentSearchResponseDTO.authcode;
                status = mappedPaymentStatus.ToString();
                recordNo = paymentSearchResponseDTO.respstat;
                dSIXReturnCode = paymentSearchResponseDTO.resptext;
                textResponse = paymentSearchResponseDTO.resptext;
                tranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString();
                authorize = paymentSearchResponseDTO.amount;
                decimal.TryParse(paymentSearchResponseDTO.amount, out amount);
                purchase = paymentRequestDTO.Amount.ToString();
                responseOrigin = "";
                acqRefData = paymentSearchResponseDTO.token;

                paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, cardType, tranCode, refNo, purchase, authorize, transactionDatetime, authCode, null, responseOrigin, null, null, acqRefData, null, null, null, null, status, null, null, null, amount);

            }
            catch (Exception ex)
            {
                log.Error("Status Check request DTO: " + paymentRequestDTO.ToString());
                log.Error("Error performing GetPaymentStatus. Error message: " + ex);

                paymentResponseDTO = new PaymentResponseDTO
                {
                    InvoiceNo = paymentRequestDTO.RequestIdentifier,
                    Purchase = "0",
                    Authorize = "0",
                    Amount = 0.0M,
                    TransactionDatetime = paymentRequestDTO.RequestDate,
                    TextResponse = "Error performing GetPaymentStatus!",
                    DSIXReturnCode = ex.Message,
                    Status = PaymentTransactionStatuses.ERROR.ToString(),
                    TranCode = PaymentGatewayTransactionType.STATUSCHECK.ToString()
                };
            }


            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        public async override Task<PaymentResponseDTO> Refund(RefundRequestDTO paymentRequestDTO, IProgress<PaymentProgressReport> progress, CancellationToken cancellationToken)
        {
            log.LogMethodEntry();
            PaymentResponseDTO paymentResponseDTO;

            string invoiceNo = "";
            string recordNo = "";
            string refNo = "";
            string authCode = "";
            decimal amount = 0;
            string authorize = "0";
            string purchase = "0";
            string tranCode = "";
            DateTime transactionDatetime = paymentRequestDTO.RequestDate;
            string status = "";
            string textResponse = "";
            string dSIXReturnCode = "";
            string acqRefData = "";
            string acctNo = "";

            try
            {
                Dictionary<string, Object> dict = new Dictionary<string, Object>();
                dict.Add("merchid", this.merchantId);
                dict.Add("retref", paymentRequestDTO.PaymentResponses.RefNo);
                dict.Add("amount", paymentRequestDTO.Amount);

                string postData = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.Indented);
                log.Debug("Refund request payload: " + postData);

                //Refund API call for card connect authorization 
                string url = this.apiPostUrl + "cardconnect/rest/refund";
                log.Info("Initiating request to CardConnect refund API: " + url);
                dynamic resultJson = ExecuteAPIRequest(url, postData, "PUT");

                if (resultJson["retref"] != null)
                {
                    refNo = resultJson["retref"];
                }
                if (resultJson["amount"] != null)
                {
                    authorize = resultJson["amount"];
                }
                if (resultJson["resptext"] != null)
                {
                    dSIXReturnCode = resultJson["resptext"];
                    textResponse = resultJson["resptext"];
                }
                if (resultJson["authcode"] != null)
                {
                    authCode = resultJson["authcode"];
                }

                invoiceNo = paymentRequestDTO.RequestIdentifier;
                amount = paymentRequestDTO.Amount;
                purchase = paymentRequestDTO.Amount.ToString();
                acctNo = paymentRequestDTO.PaymentResponses?.AcctNo ?? "";
                tranCode = PaymentGatewayTransactionType.REFUND.ToString();
                acqRefData = paymentRequestDTO.PaymentResponses?.AcqRefData ?? "";

                string paymentResponseStatus = resultJson["respstat"] ?? "";
                string paymentResponseText = resultJson["resptext"] ?? "";
                PaymentTransactionStatuses paymentStatusType = MapPaymentStatus(paymentResponseStatus, PaymentGatewayTransactionType.REFUND);

                if (paymentStatusType == PaymentTransactionStatuses.SUCCESS)
                {
                    log.Debug("Refund successful.");
                    recordNo = "A";
                    dSIXReturnCode = "REFUND SUCCESS";
                    textResponse = paymentResponseText;
                    status = PaymentTransactionStatuses.SUCCESS.ToString();
                }
                else
                {
                    log.Debug("Refund failed.");
                    recordNo = "C";
                    dSIXReturnCode = "REFUND FAILED";
                    textResponse = paymentResponseText;
                    status = PaymentTransactionStatuses.FAILED.ToString();
                }
            }
            catch (Exception ex)
            {
                log.Error("Refund request DTO: " + paymentRequestDTO.ToString());
                log.Error("Error performing Refund. Error message: " + ex);
                recordNo = "C";
                dSIXReturnCode = ex.Message;
                textResponse = "Error performing Refund!";
                status = PaymentTransactionStatuses.ERROR.ToString();
            }

            paymentResponseDTO = new PaymentResponseDTO(invoiceNo, null, recordNo, dSIXReturnCode, textResponse, acctNo, null, tranCode, refNo, purchase, authorize, transactionDatetime,
                                                        authCode, null, null, null, null, acqRefData, null, null, null, null, status, null, null, null, amount);

            log.LogMethodExit(paymentResponseDTO);
            return await Task.FromResult(paymentResponseDTO);
        }

        private bool ValidateReCaptchaToken(string token)
        {
            log.LogMethodEntry(token);
            if (String.IsNullOrEmpty(googleRecaptchaClientId) || String.IsNullOrEmpty(googleRecaptchaURL) || string.IsNullOrEmpty(token))
            {
                log.Error("Google captch settings are not present ");
                throw new Exception("Payment Failed.");
            }
            try
            {
                using (WebClient webClient = new WebClient())
                {
                    ServicePointManager.Expect100Continue = true;
                    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                    webClient.Headers[HttpRequestHeader.ContentType] = "application/x-www-form-urlencoded";
                    string result = webClient.DownloadString(string.Format(googleRecaptchaURL, googleRecaptchaSecretKey, token));
                    log.Debug("Google recaptcha result: " + result);

                    dynamic response = JsonConvert.DeserializeObject(result);
                    bool captchResult = response.success;
                    log.LogMethodExit(captchResult);
                    return captchResult;
                }
            }
            catch (WebException webException)
            {
                log.Error(webException);
                if (webException.Response != null)
                {
                    using (HttpWebResponse errorResponse = (HttpWebResponse)webException.Response)
                    {
                        using (StreamReader streamReader = new StreamReader(errorResponse.GetResponseStream()))
                        {
                            string error = streamReader.ReadToEnd();
                            log.Error("Google Captch failed " + error);
                            log.LogMethodExit(error);
                            return false;
                        }
                    }
                }
                log.LogMethodExit();
                throw;
            }
        }

        private PaymentTransactionStatuses MapPaymentStatus(string rawPaymentGatewayStatus, PaymentGatewayTransactionType pgwTrxType)
        {
            log.LogMethodEntry(rawPaymentGatewayStatus, pgwTrxType);
            PaymentTransactionStatuses defaultStatus = PaymentTransactionStatuses.FAILED;
            PaymentTransactionStatuses paymentTransactionStatus = defaultStatus;

            if (string.IsNullOrWhiteSpace(rawPaymentGatewayStatus))
            {
                log.Warn($"Raw payment gateway status is null or empty for TrxType: {pgwTrxType}. Defaulting to {defaultStatus}.");
                log.LogMethodExit(defaultStatus);
                return defaultStatus;
            }

            try
            {
                Dictionary<string, PaymentTransactionStatuses> pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase);

                switch (pgwTrxType)
                {
                    case PaymentGatewayTransactionType.SALE:
                    case PaymentGatewayTransactionType.STATUSCHECK:
                    case PaymentGatewayTransactionType.REFUND:
                        pgwStatusMappingDict = new Dictionary<string, PaymentTransactionStatuses>(StringComparer.OrdinalIgnoreCase)
                        {
                            { "Approved", PaymentTransactionStatuses.SUCCESS },
                            { "Retry", PaymentTransactionStatuses.FAILED },
                            { "Declined", PaymentTransactionStatuses.FAILED },
                            { "A", PaymentTransactionStatuses.SUCCESS },
                            { "B", PaymentTransactionStatuses.FAILED },
                            { "C", PaymentTransactionStatuses.FAILED }
                        };
                        break;
                    default:
                        log.Error("No case found for the provided PaymentGatewayTransactionType: " + pgwTrxType);
                        break;
                }

                log.Info("Begin of map payment status");
                bool isPaymentStatusExist = pgwStatusMappingDict.TryGetValue(rawPaymentGatewayStatus, out paymentTransactionStatus);
                log.Debug("Value of transformed payment status: " + paymentTransactionStatus.ToString());
                log.Info("End of map payment status");

                if (!isPaymentStatusExist)
                {
                    log.Error($"Provided raw payment gateway response: {rawPaymentGatewayStatus} is not mentioned in salePaymentStatusMappingDict. Defaulting payment status to failed.");
                    paymentTransactionStatus = PaymentTransactionStatuses.FAILED;
                }

            }
            catch (Exception ex)
            {
                log.Error("Error getting transformed payment status. Defaulting payment status to failed." + ex);
                paymentTransactionStatus = defaultStatus;
            }

            log.LogMethodExit(paymentTransactionStatus);
            return paymentTransactionStatus;
        }

        /// <summary>
        /// Decrypts the gateway response only if it contains encrypted fields otherwise returns gateway response as it is
        /// </summary>
        private string DecryptGatewayResponse(string gatewayResponse)
        {
            log.LogMethodEntry();

            if (string.IsNullOrWhiteSpace(gatewayResponse))
            {
                log.Error($"Payment response is null or whitespace.");
                throw new PaymentResponseNullException(paymentMessages.GetMessage(5984)); // PaymentResponse is Null
            }

            log.Debug("gatewayResponse: " + gatewayResponse);
            dynamic response = JsonConvert.DeserializeObject(gatewayResponse);

            // Decrypt if response contains encrypted fields
            if (response.encOrderNo != null)
            {
                log.Debug("GatewayResponse is encrypted. Starting decryption...");

                string encAESKey = response.encryptedAESKey;
                string tempEncGatewayResponse = response.encUserData;

                // RSA Key decryption
                RSAParameters rsaParameters = ConvertPemToRSAParameters(RSADataPrivateKey, log);
                byte[] decryptedAESKey = DecryptAESKey(encAESKey, rsaParameters, log);

                // AES Decryption
                string decryptedGatewayResponse = DecryptUserData(decryptedAESKey, tempEncGatewayResponse, log);
                log.Debug("Decryption Completed.");

                log.LogMethodExit("Returning decrypted gateway response.");
                return decryptedGatewayResponse;
            }

            log.Debug("GatewayResponse is not encrypted. Returning original response.");
            log.LogMethodExit("Returning plain gateway response.");
            return gatewayResponse;
        }

        /// <summary>
        ///Converts a PEM-encoded RSA private key string to an RSAParameters
        /// </summary>
        public static RSAParameters ConvertPemToRSAParameters(string pemKey, Semnox.Parafait.logging.Logger log)
        {
            if (string.IsNullOrWhiteSpace(pemKey))
            {
                throw new Exception("The PEM key string cannot be null or empty.");
            }
            using (var reader = new System.IO.StringReader(pemKey))
            {
                var pemReader = new PemReader(reader);
                var keyObject = pemReader.ReadObject();

                var privateKeyParams = keyObject as RsaPrivateCrtKeyParameters;
                if (privateKeyParams != null)
                {
                    return DotNetUtilities.ToRSAParameters(privateKeyParams);
                }
                else
                {
                    log.Error("The provided key is not a valid RSA private key.");
                    throw new InvalidCastException("The provided is not a valid RSA private key.");
                }
            }
        }

        /// <summary>
        /// Decrypts the AES key using RSA encryption
        /// </summary>
        public static byte[] DecryptAESKey(string encryptedAESKey, RSAParameters rsaParameters, Semnox.Parafait.logging.Logger log)
        {
            if (string.IsNullOrWhiteSpace(encryptedAESKey))
            {
                throw new Exception("no string passed for decryption");
            }
            if (rsaParameters.Modulus == null || rsaParameters.Exponent == null)
            {
                throw new Exception("Invalid RSA parameters provided for decryption.");
            }
            try
            {
                // Decode the Base64 encoded AES key
                byte[] encryptedAESKeyBytes = Convert.FromBase64String(encryptedAESKey);

                using (var rsa = new RSACryptoServiceProvider())
                {
                    rsa.ImportParameters(rsaParameters);

                    log.Debug($"RSA Key Size: {rsa.KeySize} bits");
                    log.Debug($"Encrypted AES Key Size: {encryptedAESKeyBytes.Length * 8} bits");

                    // Decrypt the AES key
                    byte[] decryptedAESKeyBytes = rsa.Decrypt(encryptedAESKeyBytes, false);

                    log.Debug($"Encrypted AES Key Size: {decryptedAESKeyBytes.Length * 8} bits");
                    return decryptedAESKeyBytes;

                }
            }
            catch (Exception ex)
            {
                log.Error("Error during RSA decryption: " + ex.Message);
                throw new Exception($"Error during RSA decryption: {ex.Message}");
            }
        }

        /// <summary>
        /// Decrypts the user data using AES encryption
        /// </summary>
        public static string DecryptUserData(byte[] aesKey, string encryptedString, Semnox.Parafait.logging.Logger log)
        //public static string DecryptUserData(byte[] aesKey, string encryptedString)
        {
            string result = string.Empty;
            try
            {
                byte[] cipherBytes = Convert.FromBase64String(encryptedString);
                string aesKeyString = Encoding.UTF8.GetString(aesKey);
                using (Aes encryptor = Aes.Create())
                {
                    var salt = cipherBytes.Take(16).ToArray();
                    var iv = cipherBytes.Skip(16).Take(16).ToArray();
                    var encrypted = cipherBytes.Skip(32).ToArray();
                    Rfc2898DeriveBytes pdb = new Rfc2898DeriveBytes(aesKeyString, salt, 100);
                    encryptor.Key = pdb.GetBytes(32);
                    encryptor.Padding = PaddingMode.PKCS7;
                    encryptor.Mode = CipherMode.CBC;
                    encryptor.IV = iv;
                    using (MemoryStream ms = new MemoryStream(encrypted))
                    {
                        using (CryptoStream cs = new CryptoStream(ms, encryptor.CreateDecryptor(), CryptoStreamMode.Read))
                        {
                            using (var reader = new StreamReader(cs, Encoding.UTF8))
                            {
                                result = reader.ReadToEnd();
                            }
                        }
                    }
                }
                return result;

            }
            catch (Exception ex)
            {
                log.Error("Error while decrypting user data: " + ex.Message);
                return result;
            }
        }

        /// <summary>
        /// Method for http post and get
        /// </summary>
        /// <param name="url"></param>
        /// <param name="postData"></param>
        /// <param name="method"></param>
        /// <returns></returns>
        private dynamic ExecuteAPIRequest(string url, string postData, string method)
        {
            try
            {

                HttpWebRequest req = (HttpWebRequest)WebRequest.Create(url);
                byte[] data = Encoding.ASCII.GetBytes(postData);
                req.Method = method; // Post method
                req.ContentType = "application/json";
                String apiKey = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(this.username + ":" + this.password));
                req.Headers.Add("Authorization", "Basic" + apiKey);


                if (!(method == "GET"))
                {
                    req.Accept = "application/json";
                    req.ContentLength = data.Length;
                    Stream requestStream = req.GetRequestStream();
                    requestStream.Write(data, 0, data.Length);
                    requestStream.Close();
                }


                WebResponse rsp = req.GetResponse();
                StreamReader responseStream = new StreamReader(rsp.GetResponseStream());
                string resultXml = responseStream.ReadToEnd();

                resultJson = JsonConvert.DeserializeObject(resultXml);
                log.LogMethodExit();

                return resultJson;

            }
            catch (Exception ex)
            {
                log.Error("An error occured while making an HTTP API request");
                log.Error("URL: " + url + " , Method: " + method + " , Request body: " + postData);
                log.Error(ex);
                throw;
            }
        }
    }
}
