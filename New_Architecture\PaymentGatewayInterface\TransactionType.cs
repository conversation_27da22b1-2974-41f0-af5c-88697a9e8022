using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Semnox.Parafait.PaymentGatewayInterface
{
    internal enum TransactionType
    {
        INIT,
        GET_CONFIG,
        SET_CONFIG,
        SALE,
        VOID,
        REFUND,
        PRE_AUTH,
        BATCH_CLOSE,
        IND_REFUND,
        CO<PERSON><PERSON><PERSON><PERSON>,
        INDEPENDENT_REFUND,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        CAPTURE,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        GETTERMINALINFO


    }
    public enum PaymentGatewayTransactionType
    {
        TATokenRequest,
        SALE,
        REFUND,
        AUTH<PERSON><PERSON>ZATION,
        VOID,
        CAPTURE,
        PARING,
        TIPADJUST,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        STAT<PERSON><PERSON><PERSON><PERSON>_CLOSE,
        STATUSCHECK_CANCEL,
        STATUS<PERSON><PERSON><PERSON>_OVERRIDE,
        DONATION
    }
}
